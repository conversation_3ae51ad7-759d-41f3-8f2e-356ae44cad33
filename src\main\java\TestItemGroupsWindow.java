import javax.swing.JOptionPane;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * اختبار نافذة إدارة مجموعات الأصناف
 */
public class TestItemGroupsWindow {

    public static void main(String[] args) {
        try {
            // تطبيق Look and Feel
            UIManager.setLookAndFeel("javax.swing.plaf.nimbus.NimbusLookAndFeel");

            // تشغيل النافذة
            SwingUtilities.invokeLater(() -> {
                try {
                    System.out.println("🚀 تشغيل نافذة إدارة مجموعات الأصناف...");

                    ItemGroupsManagementWindow window = new ItemGroupsManagementWindow();
                    window.setVisible(true);

                    System.out.println("✅ تم فتح النافذة بنجاح");

                } catch (Exception e) {
                    System.err.println("❌ خطأ في فتح النافذة: " + e.getMessage());
                    e.printStackTrace();

                    JOptionPane.showMessageDialog(null, "خطأ في فتح النافذة:\n" + e.getMessage(),
                            "خطأ", JOptionPane.ERROR_MESSAGE);
                }
            });

        } catch (Exception e) {
            System.err.println("❌ خطأ في تهيئة النظام: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
