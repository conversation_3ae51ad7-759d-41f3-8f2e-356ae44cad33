import java.sql.*;

public class CheckPackageErrors {
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            System.out.println("🔗 الاتصال بـ SHIP_ERP...");
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            System.out.println("✅ تم الاتصال");
            
            // فحص أخطاء Package
            System.out.println("🔍 فحص أخطاء Package...");
            
            String sql = """
                SELECT line, position, text 
                FROM USER_ERRORS 
                WHERE name = 'ERP_ITEM_GROUPS' 
                AND type = 'PACKAGE BODY'
                ORDER BY line, position
            """;
            
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            boolean hasErrors = false;
            while (rs.next()) {
                hasErrors = true;
                System.out.println("❌ خطأ في السطر " + rs.getInt("line") + 
                    " الموضع " + rs.getInt("position") + ": " + rs.getString("text"));
            }
            
            if (!hasErrors) {
                System.out.println("✅ لا توجد أخطاء في Package");
            }
            
            // فحص حالة Package
            System.out.println("\n🔍 فحص حالة Package...");
            String statusSql = """
                SELECT object_name, object_type, status 
                FROM USER_OBJECTS 
                WHERE object_name = 'ERP_ITEM_GROUPS'
                ORDER BY object_type
            """;
            
            ResultSet statusRs = stmt.executeQuery(statusSql);
            while (statusRs.next()) {
                System.out.println("📋 " + statusRs.getString("object_type") + 
                    " - " + statusRs.getString("object_name") + 
                    " - حالة: " + statusRs.getString("status"));
            }
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
