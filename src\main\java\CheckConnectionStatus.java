import java.sql.*;

/**
 * فحص حالة الاتصال بقاعدة البيانات
 */
public class CheckConnectionStatus {
    
    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("   فحص حالة الاتصال بقاعدة البيانات");
        System.out.println("   Database Connection Status Check");
        System.out.println("====================================");
        System.out.println();
        
        // فحص تحميل مكتبات Oracle
        System.out.println("1. فحص مكتبات Oracle:");
        System.out.println("===================");
        
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            System.out.println("✅ Oracle JDBC Driver محمل بنجاح");
        } catch (ClassNotFoundException e) {
            System.out.println("❌ Oracle JDBC Driver غير محمل!");
            System.out.println("السبب: " + e.getMessage());
            return;
        }
        
        // فحص الاتصال بقاعدة البيانات
        System.out.println();
        System.out.println("2. اختبار الاتصال بقاعدة البيانات:");
        System.out.println("===============================");
        
        String url = "*************************************";
        String username = "ysdba2";
        String password = "ys123";
        
        Connection conn = null;
        try {
            System.out.println("🔄 محاولة الاتصال...");
            System.out.println("URL: " + url);
            System.out.println("User: " + username);
            
            conn = DriverManager.getConnection(url, username, password);
            
            if (conn != null && !conn.isClosed()) {
                System.out.println("✅ تم الاتصال بنجاح!");
                
                // فحص معلومات قاعدة البيانات
                DatabaseMetaData metaData = conn.getMetaData();
                System.out.println();
                System.out.println("معلومات قاعدة البيانات:");
                System.out.println("- اسم قاعدة البيانات: " + metaData.getDatabaseProductName());
                System.out.println("- إصدار قاعدة البيانات: " + metaData.getDatabaseProductVersion());
                System.out.println("- اسم المستخدم: " + metaData.getUserName());
                
                // اختبار استعلام بسيط
                System.out.println();
                System.out.println("3. اختبار استعلام بسيط:");
                System.out.println("====================");
                
                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery("SELECT SYSDATE FROM DUAL");
                
                if (rs.next()) {
                    System.out.println("✅ الاستعلام نجح!");
                    System.out.println("التاريخ والوقت الحالي: " + rs.getTimestamp(1));
                }
                
                rs.close();
                stmt.close();
                
                // فحص وجود جداول IAS
                System.out.println();
                System.out.println("4. فحص وجود جداول IAS:");
                System.out.println("=====================");
                
                String checkTablesQuery = """
                    SELECT table_name, num_rows 
                    FROM all_tables 
                    WHERE owner = 'IAS20251' 
                    AND table_name IN ('IAS_ITM_MST', 'IAS_ITM_DTL')
                    ORDER BY table_name
                """;
                
                PreparedStatement pstmt = conn.prepareStatement(checkTablesQuery);
                ResultSet tablesRs = pstmt.executeQuery();
                
                boolean tablesFound = false;
                while (tablesRs.next()) {
                    tablesFound = true;
                    String tableName = tablesRs.getString("table_name");
                    String numRows = tablesRs.getString("num_rows");
                    System.out.println("✅ جدول " + tableName + " موجود - عدد الصفوف: " + 
                        (numRows != null ? numRows : "غير محدد"));
                }
                
                if (!tablesFound) {
                    System.out.println("⚠️ لم يتم العثور على جداول IAS");
                }
                
                tablesRs.close();
                pstmt.close();
                
            } else {
                System.out.println("❌ فشل في الاتصال!");
            }
            
        } catch (SQLException e) {
            System.out.println("❌ خطأ في الاتصال:");
            System.out.println("كود الخطأ: " + e.getErrorCode());
            System.out.println("رسالة الخطأ: " + e.getMessage());
            
            if (e.getErrorCode() == 1017) {
                System.out.println("💡 السبب: اسم المستخدم أو كلمة المرور خاطئة");
            } else if (e.getErrorCode() == 17002) {
                System.out.println("💡 السبب: خادم Oracle غير متاح أو غير مشغل");
            } else if (e.getErrorCode() == 17056) {
                System.out.println("💡 السبب: مشكلة في ترميز الأحرف - تحتاج orai18n.jar");
            }
            
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                    System.out.println();
                    System.out.println("🔒 تم إغلاق الاتصال");
                } catch (SQLException e) {
                    System.out.println("⚠️ خطأ في إغلاق الاتصال: " + e.getMessage());
                }
            }
        }
        
        System.out.println();
        System.out.println("====================================");
        System.out.println("   انتهى فحص حالة الاتصال");
        System.out.println("====================================");
    }
}
