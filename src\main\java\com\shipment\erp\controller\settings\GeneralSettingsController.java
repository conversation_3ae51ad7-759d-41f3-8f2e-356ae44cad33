package com.shipment.erp.controller.settings;

import com.shipment.erp.ShipERPApplication;
import com.shipment.erp.service.SystemSettingsService;
import com.shipment.erp.service.CurrencyService;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.stage.DirectoryChooser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.net.URL;
import java.util.ResourceBundle;

/**
 * Controller للإعدادات العامة
 */
public class GeneralSettingsController implements Initializable {

    private static final Logger logger = LoggerFactory.getLogger(GeneralSettingsController.class);

    @FXML private TextField companyNameField;
    @FXML private TextArea companyAddressField;
    @FXML private TextField companyPhoneField;
    @FXML private TextField companyEmailField;
    @FXML private TextField taxNumberField;
    @FXML private TextField commercialRegisterField;
    
    @FXML private ComboBox<String> defaultCurrencyCombo;
    @FXML private Spinner<Integer> decimalPlacesSpinner;
    @FXML private ComboBox<String> dateFormatCombo;
    
    @FXML private CheckBox backupEnabledCheck;
    @FXML private Spinner<Integer> backupIntervalSpinner;
    @FXML private TextField backupDirectoryField;
    
    @FXML private Button saveButton;
    @FXML private Button cancelButton;
    @FXML private Button resetButton;

    private SystemSettingsService settingsService;
    private CurrencyService currencyService;
    private ResourceBundle resources;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        this.resources = resources;
        
        try {
            // الحصول على Services من Spring Context
            settingsService = ShipERPApplication.getBean(SystemSettingsService.class);
            currencyService = ShipERPApplication.getBean(CurrencyService.class);
            
            setupUI();
            loadSettings();
            
            logger.info("تم تهيئة شاشة الإعدادات العامة بنجاح");
            
        } catch (Exception e) {
            logger.error("خطأ في تهيئة شاشة الإعدادات العامة", e);
            showError("خطأ في تهيئة الشاشة: " + e.getMessage());
        }
    }

    /**
     * إعداد واجهة المستخدم
     */
    private void setupUI() {
        // إعداد Spinner للمنازل العشرية
        decimalPlacesSpinner.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(0, 6, 2));
        
        // إعداد Spinner لفترة النسخ الاحتياطي
        backupIntervalSpinner.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(1, 168, 24));
        
        // إعداد ComboBox لتنسيق التاريخ
        dateFormatCombo.getItems().addAll(
            "dd/MM/yyyy",
            "MM/dd/yyyy", 
            "yyyy-MM-dd",
            "dd-MM-yyyy"
        );
        dateFormatCombo.setValue("dd/MM/yyyy");
        
        // تحميل العملات في ComboBox
        loadCurrencies();
    }

    /**
     * تحميل العملات المتاحة
     */
    private void loadCurrencies() {
        try {
            defaultCurrencyCombo.getItems().clear();
            // سيتم تحميل العملات من قاعدة البيانات لاحقاً
            defaultCurrencyCombo.getItems().addAll("SAR", "USD", "EUR");
            defaultCurrencyCombo.setValue("SAR");
        } catch (Exception e) {
            logger.error("خطأ في تحميل العملات", e);
        }
    }

    /**
     * تحميل الإعدادات من قاعدة البيانات
     */
    private void loadSettings() {
        try {
            // تحميل إعدادات الشركة
            companyNameField.setText(settingsService.getSettingValue("COMPANY_NAME", ""));
            companyAddressField.setText(settingsService.getSettingValue("COMPANY_ADDRESS", ""));
            companyPhoneField.setText(settingsService.getSettingValue("COMPANY_PHONE", ""));
            companyEmailField.setText(settingsService.getSettingValue("COMPANY_EMAIL", ""));
            taxNumberField.setText(settingsService.getSettingValue("TAX_NUMBER", ""));
            commercialRegisterField.setText(settingsService.getSettingValue("COMMERCIAL_REGISTER", ""));
            
            // تحميل إعدادات النظام
            String defaultCurrency = settingsService.getSettingValue("DEFAULT_CURRENCY", "SAR");
            defaultCurrencyCombo.setValue(defaultCurrency);
            
            Integer decimalPlaces = settingsService.getIntegerSetting("DECIMAL_PLACES", 2);
            decimalPlacesSpinner.getValueFactory().setValue(decimalPlaces);
            
            String dateFormat = settingsService.getSettingValue("DATE_FORMAT", "dd/MM/yyyy");
            dateFormatCombo.setValue(dateFormat);
            
            // تحميل إعدادات النسخ الاحتياطي
            Boolean backupEnabled = settingsService.getBooleanSetting("BACKUP_ENABLED", true);
            backupEnabledCheck.setSelected(backupEnabled);
            
            Integer backupInterval = settingsService.getIntegerSetting("BACKUP_INTERVAL_HOURS", 24);
            backupIntervalSpinner.getValueFactory().setValue(backupInterval);
            
            String backupDirectory = settingsService.getSettingValue("BACKUP_DIRECTORY", "backup");
            backupDirectoryField.setText(backupDirectory);
            
            logger.info("تم تحميل الإعدادات بنجاح");
            
        } catch (Exception e) {
            logger.error("خطأ في تحميل الإعدادات", e);
            showError("خطأ في تحميل الإعدادات: " + e.getMessage());
        }
    }

    /**
     * معالج حفظ الإعدادات
     */
    @FXML
    private void handleSave() {
        try {
            // التحقق من صحة البيانات
            if (!validateInput()) {
                return;
            }
            
            // حفظ إعدادات الشركة
            settingsService.updateSettingValue("COMPANY_NAME", companyNameField.getText().trim());
            settingsService.updateSettingValue("COMPANY_ADDRESS", companyAddressField.getText().trim());
            settingsService.updateSettingValue("COMPANY_PHONE", companyPhoneField.getText().trim());
            settingsService.updateSettingValue("COMPANY_EMAIL", companyEmailField.getText().trim());
            settingsService.updateSettingValue("TAX_NUMBER", taxNumberField.getText().trim());
            settingsService.updateSettingValue("COMMERCIAL_REGISTER", commercialRegisterField.getText().trim());
            
            // حفظ إعدادات النظام
            settingsService.updateSettingValue("DEFAULT_CURRENCY", defaultCurrencyCombo.getValue());
            settingsService.updateIntegerSetting("DECIMAL_PLACES", decimalPlacesSpinner.getValue());
            settingsService.updateSettingValue("DATE_FORMAT", dateFormatCombo.getValue());
            
            // حفظ إعدادات النسخ الاحتياطي
            settingsService.updateBooleanSetting("BACKUP_ENABLED", backupEnabledCheck.isSelected());
            settingsService.updateIntegerSetting("BACKUP_INTERVAL_HOURS", backupIntervalSpinner.getValue());
            settingsService.updateSettingValue("BACKUP_DIRECTORY", backupDirectoryField.getText().trim());
            
            showSuccess("تم حفظ الإعدادات بنجاح");
            logger.info("تم حفظ الإعدادات العامة بنجاح");
            
        } catch (Exception e) {
            logger.error("خطأ في حفظ الإعدادات", e);
            showError("خطأ في حفظ الإعدادات: " + e.getMessage());
        }
    }

    /**
     * معالج إلغاء التغييرات
     */
    @FXML
    private void handleCancel() {
        loadSettings(); // إعادة تحميل الإعدادات الأصلية
    }

    /**
     * معالج إعادة تعيين الإعدادات للقيم الافتراضية
     */
    @FXML
    private void handleReset() {
        Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
        confirmDialog.setTitle("تأكيد إعادة التعيين");
        confirmDialog.setHeaderText(null);
        confirmDialog.setContentText("هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟");
        
        confirmDialog.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                resetToDefaults();
            }
        });
    }

    /**
     * معالج تصفح مجلد النسخ الاحتياطي
     */
    @FXML
    private void handleBrowseBackupDirectory() {
        DirectoryChooser directoryChooser = new DirectoryChooser();
        directoryChooser.setTitle("اختيار مجلد النسخ الاحتياطي");
        
        // تعيين المجلد الحالي إذا كان موجوداً
        String currentPath = backupDirectoryField.getText();
        if (!currentPath.isEmpty()) {
            File currentDir = new File(currentPath);
            if (currentDir.exists() && currentDir.isDirectory()) {
                directoryChooser.setInitialDirectory(currentDir);
            }
        }
        
        File selectedDirectory = directoryChooser.showDialog(saveButton.getScene().getWindow());
        if (selectedDirectory != null) {
            backupDirectoryField.setText(selectedDirectory.getAbsolutePath());
        }
    }

    /**
     * التحقق من صحة البيانات المدخلة
     */
    private boolean validateInput() {
        // التحقق من اسم الشركة
        if (companyNameField.getText().trim().isEmpty()) {
            showError("اسم الشركة مطلوب");
            companyNameField.requestFocus();
            return false;
        }
        
        // التحقق من البريد الإلكتروني إذا تم إدخاله
        String email = companyEmailField.getText().trim();
        if (!email.isEmpty() && !isValidEmail(email)) {
            showError("البريد الإلكتروني غير صحيح");
            companyEmailField.requestFocus();
            return false;
        }
        
        return true;
    }

    /**
     * التحقق من صحة البريد الإلكتروني
     */
    private boolean isValidEmail(String email) {
        return email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    }

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     */
    private void resetToDefaults() {
        companyNameField.setText("");
        companyAddressField.setText("");
        companyPhoneField.setText("");
        companyEmailField.setText("");
        taxNumberField.setText("");
        commercialRegisterField.setText("");
        
        defaultCurrencyCombo.setValue("SAR");
        decimalPlacesSpinner.getValueFactory().setValue(2);
        dateFormatCombo.setValue("dd/MM/yyyy");
        
        backupEnabledCheck.setSelected(true);
        backupIntervalSpinner.getValueFactory().setValue(24);
        backupDirectoryField.setText("backup");
    }

    /**
     * عرض رسالة نجاح
     */
    private void showSuccess(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("نجح");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * عرض رسالة خطأ
     */
    private void showError(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("خطأ");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
