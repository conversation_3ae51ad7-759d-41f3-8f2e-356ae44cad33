@echo off
chcp 65001 > nul
title تشخيص نظام Ship ERP

echo ====================================
echo    تشخيص نظام Ship ERP
echo    Ship ERP System Diagnostics
echo ====================================
echo.

echo [1] فحص وجود الملفات الأساسية...
echo Checking essential files...

if exist "CompleteSystemTest.java" (
    echo ✓ CompleteSystemTest.java موجود
) else (
    echo ✗ CompleteSystemTest.java مفقود!
    goto :error
)

if exist "EnhancedShipERP.java" (
    echo ✓ EnhancedShipERP.java موجود
) else (
    echo ✗ EnhancedShipERP.java مفقود!
)

if exist "EnhancedMainWindow.java" (
    echo ✓ EnhancedMainWindow.java موجود
) else (
    echo ✗ EnhancedMainWindow.java مفقود!
)

echo.
echo [2] فحص إصدار Java...
echo Checking Java version...

java -version 2>&1 | findstr "version"
if errorlevel 1 (
    echo ✗ Java غير مثبت أو غير متوفر!
    goto :error
) else (
    echo ✓ Java متوفر
)

echo.
echo [3] اختبار التجميع...
echo Testing compilation...

javac -encoding UTF-8 CompleteSystemTest.java 2>compile_test.txt
if errorlevel 1 (
    echo ✗ فشل في التجميع!
    echo تفاصيل الخطأ:
    type compile_test.txt
    goto :error
) else (
    echo ✓ التجميع نجح
    del compile_test.txt 2>nul
)

echo.
echo [4] اختبار التشغيل السريع...
echo Testing quick run...

timeout /t 2 /nobreak > nul
echo تشغيل النظام لمدة 5 ثوان...

start /min java CompleteSystemTest
timeout /t 5 /nobreak > nul
taskkill /f /im java.exe > nul 2>&1

echo ✓ اختبار التشغيل مكتمل

echo.
echo [5] فحص الملفات المساعدة...
echo Checking helper files...

if exist "SettingsManager.class" (
    echo ✓ SettingsManager.class موجود
) else (
    echo ! SettingsManager.class مفقود - قد يحتاج تجميع
)

if exist "UIUtils.class" (
    echo ✓ UIUtils.class موجود
) else (
    echo ! UIUtils.class مفقود - قد يحتاج تجميع
)

echo.
echo ====================================
echo    نتيجة التشخيص
echo    Diagnosis Result
echo ====================================
echo ✓ النظام يبدو سليماً ويجب أن يعمل بشكل طبيعي
echo ✓ System appears healthy and should work normally
echo.
echo استخدم أحد ملفات التشغيل التالية:
echo Use one of the following run files:
echo   1. test_system.bat     (مفصل)
echo   2. run_simple.bat      (مبسط)
echo   3. run_system.ps1      (PowerShell)
echo.
goto :end

:error
echo.
echo ====================================
echo    خطأ في التشخيص
echo    Diagnosis Error
echo ====================================
echo ✗ تم اكتشاف مشاكل في النظام
echo ✗ System issues detected
echo.
echo يرجى التحقق من:
echo Please check:
echo   1. تثبيت Java بشكل صحيح
echo   2. وجود جميع ملفات النظام
echo   3. الأذونات المطلوبة
echo.

:end
echo اضغط أي مفتاح للخروج...
pause > nul
