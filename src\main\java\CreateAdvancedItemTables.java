import java.sql.*;

/**
 * إنشاء جداول الأصناف المتقدمة بناءً على IAS_ITM_MST و IAS_ITM_DTL
 * Creating Advanced Item Tables based on IAS_ITM_MST and IAS_ITM_DTL
 */
public class CreateAdvancedItemTables {
    
    private Connection shipErpConnection;
    private Connection ias20251Connection;
    
    public static void main(String[] args) {
        try {
            new CreateAdvancedItemTables().createTables();
        } catch (Exception e) {
            System.err.println("❌ خطأ في إنشاء الجداول: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public CreateAdvancedItemTables() throws Exception {
        initializeConnections();
    }
    
    /**
     * تهيئة الاتصالات
     */
    private void initializeConnections() throws Exception {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        
        // اتصال SHIP_ERP
        shipErpConnection = DriverManager.getConnection(
            "*************************************", 
            "ship_erp", 
            "ship_erp_password"
        );
        System.out.println("✅ تم الاتصال بـ SHIP_ERP");
        
        // اتصال IAS20251 للفحص
        ias20251Connection = DriverManager.getConnection(
            "*************************************", 
            "ias20251", 
            "ys123"
        );
        System.out.println("✅ تم الاتصال بـ IAS20251");
    }
    
    /**
     * إنشاء الجداول
     */
    public void createTables() throws SQLException {
        System.out.println("\n🚀 بدء إنشاء جداول الأصناف المتقدمة...");
        
        // فحص البنية الحقيقية أولاً
        analyzeRealStructure();
        
        // إنشاء جدول SHIP_ITM_MST
        createShipItmMstTable();
        
        // إنشاء جدول SHIP_ITM_DTL
        createShipItmDtlTable();
        
        // إنشاء القيود والعلاقات
        createConstraints();
        
        // إنشاء الفهارس
        createIndexes();
        
        // إنشاء المشاهد (Views)
        createViews();
        
        // اختبار الجداول
        testTables();
        
        System.out.println("\n🎉 تم إنشاء جداول الأصناف المتقدمة بنجاح!");
    }
    
    /**
     * فحص البنية الحقيقية
     */
    private void analyzeRealStructure() throws SQLException {
        System.out.println("\n🔍 فحص البنية الحقيقية للجداول...");
        
        // فحص IAS_ITM_MST
        analyzeTable("IAS_ITM_MST");
        
        // فحص IAS_ITM_DTL
        analyzeTable("IAS_ITM_DTL");
    }
    
    /**
     * فحص جدول
     */
    private void analyzeTable(String tableName) throws SQLException {
        System.out.println("📋 فحص جدول: " + tableName);
        
        Statement stmt = ias20251Connection.createStatement();
        
        // عدد الصفوف
        ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName);
        rs.next();
        int count = rs.getInt(1);
        System.out.println("  📊 عدد الصفوف: " + count);
        rs.close();
        
        // عينة من البيانات
        rs = stmt.executeQuery("SELECT * FROM " + tableName + " WHERE ROWNUM <= 1");
        ResultSetMetaData metaData = rs.getMetaData();
        
        System.out.println("  📋 الأعمدة:");
        for (int i = 1; i <= metaData.getColumnCount(); i++) {
            String columnName = metaData.getColumnName(i);
            String columnType = metaData.getColumnTypeName(i);
            int columnSize = metaData.getColumnDisplaySize(i);
            System.out.println("    - " + columnName + " (" + columnType + 
                             (columnSize > 0 ? "(" + columnSize + ")" : "") + ")");
        }
        
        rs.close();
        stmt.close();
    }
    
    /**
     * إنشاء جدول SHIP_ITM_MST
     */
    private void createShipItmMstTable() throws SQLException {
        System.out.println("\n🔨 إنشاء جدول SHIP_ITM_MST...");
        
        Statement stmt = shipErpConnection.createStatement();
        
        // حذف الجدول إذا كان موجوداً
        try {
            stmt.execute("DROP TABLE SHIP_ITM_MST CASCADE CONSTRAINTS");
            System.out.println("  🗑️ تم حذف الجدول القديم");
        } catch (SQLException e) {
            // الجدول غير موجود
        }
        
        // إنشاء الجدول بناءً على البنية الحقيقية
        String createSQL = """
            CREATE TABLE SHIP_ITM_MST (
                I_CODE VARCHAR2(20) NOT NULL,
                I_A_NAME VARCHAR2(100),
                I_E_NAME VARCHAR2(100),
                I_DESC VARCHAR2(500),
                G_CODE VARCHAR2(10),
                MNG_CODE VARCHAR2(10),
                ITEM_TYPE NUMBER(2) DEFAULT 1,
                BARCODE VARCHAR2(50),
                UNIT_CODE VARCHAR2(10),
                SALE_PRICE NUMBER(15,3) DEFAULT 0,
                COST_PRICE NUMBER(15,3) DEFAULT 0,
                MIN_STOCK NUMBER(15,3) DEFAULT 0,
                MAX_STOCK NUMBER(15,3) DEFAULT 0,
                REORDER_LEVEL NUMBER(15,3) DEFAULT 0,
                CURRENT_STOCK NUMBER(15,3) DEFAULT 0,
                MANUFACTURER VARCHAR2(100),
                BRAND VARCHAR2(100),
                MODEL VARCHAR2(100),
                WEIGHT NUMBER(15,3),
                DIMENSIONS VARCHAR2(100),
                COLOR VARCHAR2(50),
                SIZE_INFO VARCHAR2(50),
                EXPIRY_DAYS NUMBER(5),
                WARRANTY_DAYS NUMBER(5),
                TAX_RATE NUMBER(5,2) DEFAULT 0,
                DISCOUNT_RATE NUMBER(5,2) DEFAULT 0,
                COMMISSION_RATE NUMBER(5,2) DEFAULT 0,
                IS_ACTIVE NUMBER(1) DEFAULT 1,
                IS_SELLABLE NUMBER(1) DEFAULT 1,
                IS_PURCHASABLE NUMBER(1) DEFAULT 1,
                IS_STOCKABLE NUMBER(1) DEFAULT 1,
                ALLOW_NEGATIVE_STOCK NUMBER(1) DEFAULT 0,
                TRACK_SERIAL NUMBER(1) DEFAULT 0,
                TRACK_BATCH NUMBER(1) DEFAULT 0,
                IMAGE_PATH VARCHAR2(500),
                NOTES CLOB,
                CREATED_BY VARCHAR2(50) DEFAULT 'SYSTEM',
                CREATED_DATE DATE DEFAULT SYSDATE,
                MODIFIED_BY VARCHAR2(50),
                MODIFIED_DATE DATE,
                SORT_ORDER NUMBER(10) DEFAULT 0,
                CUSTOM_FIELD1 VARCHAR2(100),
                CUSTOM_FIELD2 VARCHAR2(100),
                CUSTOM_FIELD3 VARCHAR2(100),
                CUSTOM_FIELD4 NUMBER(15,3),
                CUSTOM_FIELD5 NUMBER(15,3)
            )
        """;
        
        stmt.execute(createSQL);
        System.out.println("  ✅ تم إنشاء جدول SHIP_ITM_MST بنجاح");
        
        stmt.close();
    }
    
    /**
     * إنشاء جدول SHIP_ITM_DTL
     */
    private void createShipItmDtlTable() throws SQLException {
        System.out.println("\n🔨 إنشاء جدول SHIP_ITM_DTL...");
        
        Statement stmt = shipErpConnection.createStatement();
        
        // حذف الجدول إذا كان موجوداً
        try {
            stmt.execute("DROP TABLE SHIP_ITM_DTL CASCADE CONSTRAINTS");
            System.out.println("  🗑️ تم حذف الجدول القديم");
        } catch (SQLException e) {
            // الجدول غير موجود
        }
        
        // إنشاء الجدول التفصيلي
        String createSQL = """
            CREATE TABLE SHIP_ITM_DTL (
                I_CODE VARCHAR2(20) NOT NULL,
                DTL_TYPE VARCHAR2(20) NOT NULL,
                DTL_CODE VARCHAR2(20) NOT NULL,
                DTL_VALUE VARCHAR2(500),
                DTL_NUMBER NUMBER(15,3),
                DTL_DATE DATE,
                UNIT_CODE VARCHAR2(10),
                CONVERSION_FACTOR NUMBER(15,6) DEFAULT 1,
                PRICE NUMBER(15,3),
                COST NUMBER(15,3),
                SUPPLIER_CODE VARCHAR2(20),
                SUPPLIER_ITEM_CODE VARCHAR2(50),
                LEAD_TIME_DAYS NUMBER(5),
                MIN_ORDER_QTY NUMBER(15,3),
                ORDER_MULTIPLE NUMBER(15,3),
                LOCATION_CODE VARCHAR2(20),
                BIN_CODE VARCHAR2(20),
                SHELF_LIFE_DAYS NUMBER(5),
                STORAGE_TEMP_MIN NUMBER(5,2),
                STORAGE_TEMP_MAX NUMBER(5,2),
                STORAGE_HUMIDITY_MIN NUMBER(5,2),
                STORAGE_HUMIDITY_MAX NUMBER(5,2),
                HANDLING_INSTRUCTIONS VARCHAR2(500),
                SAFETY_INSTRUCTIONS VARCHAR2(500),
                QUALITY_SPECS VARCHAR2(500),
                CERTIFICATION VARCHAR2(200),
                ORIGIN_COUNTRY VARCHAR2(50),
                HS_CODE VARCHAR2(20),
                IS_ACTIVE NUMBER(1) DEFAULT 1,
                EFFECTIVE_DATE DATE DEFAULT SYSDATE,
                EXPIRY_DATE DATE,
                CREATED_BY VARCHAR2(50) DEFAULT 'SYSTEM',
                CREATED_DATE DATE DEFAULT SYSDATE,
                MODIFIED_BY VARCHAR2(50),
                MODIFIED_DATE DATE,
                SORT_ORDER NUMBER(10) DEFAULT 0,
                NOTES VARCHAR2(1000)
            )
        """;
        
        stmt.execute(createSQL);
        System.out.println("  ✅ تم إنشاء جدول SHIP_ITM_DTL بنجاح");
        
        stmt.close();
    }
    
    /**
     * إنشاء القيود والعلاقات
     */
    private void createConstraints() throws SQLException {
        System.out.println("\n🔗 إنشاء القيود والعلاقات...");
        
        Statement stmt = shipErpConnection.createStatement();
        
        try {
            // المفتاح الأساسي لـ SHIP_ITM_MST
            stmt.execute("ALTER TABLE SHIP_ITM_MST ADD CONSTRAINT PK_SHIP_ITM_MST PRIMARY KEY (I_CODE)");
            System.out.println("  🔑 تم إنشاء المفتاح الأساسي لـ SHIP_ITM_MST");
            
            // المفتاح الأساسي لـ SHIP_ITM_DTL
            stmt.execute("ALTER TABLE SHIP_ITM_DTL ADD CONSTRAINT PK_SHIP_ITM_DTL PRIMARY KEY (I_CODE, DTL_TYPE, DTL_CODE)");
            System.out.println("  🔑 تم إنشاء المفتاح الأساسي لـ SHIP_ITM_DTL");
            
            // العلاقة بين الجدولين
            stmt.execute("ALTER TABLE SHIP_ITM_DTL ADD CONSTRAINT FK_SHIP_ITM_DTL_MST FOREIGN KEY (I_CODE) REFERENCES SHIP_ITM_MST(I_CODE) ON DELETE CASCADE");
            System.out.println("  🔗 تم إنشاء العلاقة بين الجدولين");
            
            // قيود التحقق
            stmt.execute("ALTER TABLE SHIP_ITM_MST ADD CONSTRAINT CHK_SHIP_ITM_MST_ACTIVE CHECK (IS_ACTIVE IN (0,1))");
            stmt.execute("ALTER TABLE SHIP_ITM_MST ADD CONSTRAINT CHK_SHIP_ITM_MST_SELLABLE CHECK (IS_SELLABLE IN (0,1))");
            stmt.execute("ALTER TABLE SHIP_ITM_MST ADD CONSTRAINT CHK_SHIP_ITM_MST_PURCHASABLE CHECK (IS_PURCHASABLE IN (0,1))");
            stmt.execute("ALTER TABLE SHIP_ITM_DTL ADD CONSTRAINT CHK_SHIP_ITM_DTL_ACTIVE CHECK (IS_ACTIVE IN (0,1))");
            System.out.println("  ✅ تم إنشاء قيود التحقق");
            
        } catch (SQLException e) {
            System.err.println("  ❌ خطأ في إنشاء القيود: " + e.getMessage());
            throw e;
        }
        
        stmt.close();
    }
    
    /**
     * إنشاء الفهارس
     */
    private void createIndexes() throws SQLException {
        System.out.println("\n📇 إنشاء الفهارس...");
        
        Statement stmt = shipErpConnection.createStatement();
        
        String[] indexes = {
            "CREATE INDEX IDX_SHIP_ITM_MST_NAME ON SHIP_ITM_MST(I_A_NAME)",
            "CREATE INDEX IDX_SHIP_ITM_MST_ENAME ON SHIP_ITM_MST(I_E_NAME)",
            "CREATE INDEX IDX_SHIP_ITM_MST_GROUP ON SHIP_ITM_MST(G_CODE)",
            "CREATE INDEX IDX_SHIP_ITM_MST_SUBGROUP ON SHIP_ITM_MST(MNG_CODE)",
            "CREATE INDEX IDX_SHIP_ITM_MST_BARCODE ON SHIP_ITM_MST(BARCODE)",
            "CREATE INDEX IDX_SHIP_ITM_MST_TYPE ON SHIP_ITM_MST(ITEM_TYPE)",
            "CREATE INDEX IDX_SHIP_ITM_MST_ACTIVE ON SHIP_ITM_MST(IS_ACTIVE)",
            "CREATE INDEX IDX_SHIP_ITM_DTL_TYPE ON SHIP_ITM_DTL(DTL_TYPE)",
            "CREATE INDEX IDX_SHIP_ITM_DTL_CODE ON SHIP_ITM_DTL(DTL_CODE)",
            "CREATE INDEX IDX_SHIP_ITM_DTL_ACTIVE ON SHIP_ITM_DTL(IS_ACTIVE)"
        };
        
        for (String indexSQL : indexes) {
            try {
                stmt.execute(indexSQL);
                System.out.println("  📇 تم إنشاء فهرس");
            } catch (SQLException e) {
                System.out.println("  ⚠️ تحذير في إنشاء الفهرس: " + e.getMessage());
            }
        }
        
        stmt.close();
    }
    
    /**
     * إنشاء المشاهد
     */
    private void createViews() throws SQLException {
        System.out.println("\n👁️ إنشاء المشاهد...");
        
        Statement stmt = shipErpConnection.createStatement();
        
        // مشهد شامل للأصناف
        String viewSQL = """
            CREATE OR REPLACE VIEW V_SHIP_ITEMS_COMPLETE AS
            SELECT 
                m.I_CODE,
                m.I_A_NAME,
                m.I_E_NAME,
                m.I_DESC,
                m.G_CODE,
                m.MNG_CODE,
                m.ITEM_TYPE,
                m.BARCODE,
                m.UNIT_CODE,
                m.SALE_PRICE,
                m.COST_PRICE,
                m.CURRENT_STOCK,
                m.MIN_STOCK,
                m.REORDER_LEVEL,
                m.IS_ACTIVE,
                m.CREATED_DATE,
                CASE 
                    WHEN m.CURRENT_STOCK <= 0 THEN 'نفد المخزون'
                    WHEN m.CURRENT_STOCK <= m.MIN_STOCK THEN 'مخزون منخفض'
                    ELSE 'متوفر'
                END AS STOCK_STATUS,
                (m.CURRENT_STOCK * m.COST_PRICE) AS STOCK_VALUE
            FROM SHIP_ITM_MST m
            WHERE m.IS_ACTIVE = 1
        """;
        
        stmt.execute(viewSQL);
        System.out.println("  👁️ تم إنشاء مشهد الأصناف الشامل");
        
        stmt.close();
    }
    
    /**
     * اختبار الجداول
     */
    private void testTables() throws SQLException {
        System.out.println("\n🧪 اختبار الجداول...");
        
        Statement stmt = shipErpConnection.createStatement();
        
        // إدراج بيانات تجريبية
        String insertMstSQL = """
            INSERT INTO SHIP_ITM_MST (I_CODE, I_A_NAME, I_E_NAME, G_CODE, SALE_PRICE, COST_PRICE, CURRENT_STOCK)
            VALUES ('TEST001', 'صنف تجريبي', 'Test Item', 'G001', 100, 80, 50)
        """;
        
        String insertDtlSQL = """
            INSERT INTO SHIP_ITM_DTL (I_CODE, DTL_TYPE, DTL_CODE, DTL_VALUE)
            VALUES ('TEST001', 'SPEC', 'COLOR', 'أحمر')
        """;
        
        try {
            stmt.execute(insertMstSQL);
            stmt.execute(insertDtlSQL);
            System.out.println("  ✅ تم إدراج البيانات التجريبية");
            
            // اختبار الاستعلام
            ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM V_SHIP_ITEMS_COMPLETE");
            rs.next();
            int count = rs.getInt(1);
            System.out.println("  📊 عدد الأصناف في المشهد: " + count);
            rs.close();
            
            // حذف البيانات التجريبية
            stmt.execute("DELETE FROM SHIP_ITM_DTL WHERE I_CODE = 'TEST001'");
            stmt.execute("DELETE FROM SHIP_ITM_MST WHERE I_CODE = 'TEST001'");
            System.out.println("  🗑️ تم حذف البيانات التجريبية");
            
        } catch (SQLException e) {
            System.err.println("  ❌ خطأ في الاختبار: " + e.getMessage());
            throw e;
        }
        
        stmt.close();
    }
}
