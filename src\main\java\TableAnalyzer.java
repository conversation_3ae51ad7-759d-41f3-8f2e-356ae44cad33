import java.sql.*;
import java.util.Locale;
import java.util.Properties;

/**
 * أداة تحليل جدول محدد
 * Table Analyzer Tool
 */
public class TableAnalyzer {
    
    public static void main(String[] args) {
        TableAnalyzer analyzer = new TableAnalyzer();
        analyzer.analyzeMeasurementTable();
    }
    
    /**
     * تحليل جدول MEASUREMENT
     */
    public void analyzeMeasurementTable() {
        // إصلاح regex
        fixRegexIssue();
        
        String url = "*************************************";
        String username = "ias20251";
        String password = "ys123";
        
        try (Connection connection = DriverManager.getConnection(url, username, password)) {
            
            System.out.println("🔍 تحليل جدول MEASUREMENT في قاعدة البيانات IAS20251");
            System.out.println("=" + "=".repeat(60));
            
            // فحص وجود الجدول
            if (!checkTableExists(connection, "MEASUREMENT")) {
                System.out.println("❌ جدول MEASUREMENT غير موجود");
                
                // البحث عن جداول مشابهة
                searchSimilarTables(connection);
                return;
            }
            
            // تحليل بنية الجدول
            analyzeTableStructure(connection, "MEASUREMENT");
            
            // عرض عينة من البيانات
            showSampleData(connection, "MEASUREMENT");
            
            // إنشاء سكريبت إنشاء الجدول
            generateCreateScript(connection, "MEASUREMENT");
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في الاتصال: " + e.getMessage());
        }
    }
    
    private void fixRegexIssue() {
        try {
            Locale.setDefault(Locale.ENGLISH);
            System.setProperty("user.language", "en");
            System.setProperty("user.country", "US");
            System.setProperty("file.encoding", "UTF-8");
        } catch (Exception e) {
            // تجاهل
        }
    }
    
    private boolean checkTableExists(Connection connection, String tableName) throws SQLException {
        String query = "SELECT COUNT(*) as count FROM all_tables WHERE owner = 'IAS20251' AND table_name = ?";
        
        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, tableName);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("count") > 0;
                }
            }
        }
        return false;
    }
    
    private void searchSimilarTables(Connection connection) throws SQLException {
        System.out.println("\n🔍 البحث عن جداول مشابهة...");
        
        String query = """
            SELECT table_name 
            FROM all_tables 
            WHERE owner = 'IAS20251' 
            AND (table_name LIKE '%MEASURE%' 
                 OR table_name LIKE '%UNIT%' 
                 OR table_name LIKE '%UOM%'
                 OR table_name LIKE '%ITM%')
            ORDER BY table_name
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(query);
             ResultSet rs = stmt.executeQuery()) {
            
            System.out.println("\nالجداول المشابهة الموجودة:");
            int count = 0;
            while (rs.next()) {
                String tableName = rs.getString("table_name");
                System.out.println("  - " + tableName);
                count++;
            }
            
            if (count == 0) {
                System.out.println("  لا توجد جداول مشابهة");
            }
        }
    }
    
    private void analyzeTableStructure(Connection connection, String tableName) throws SQLException {
        System.out.println("\n📋 بنية جدول " + tableName + ":");
        System.out.println("-".repeat(80));
        
        String query = """
            SELECT 
                column_name,
                data_type,
                data_length,
                data_precision,
                data_scale,
                nullable,
                data_default,
                column_id
            FROM all_tab_columns 
            WHERE owner = 'IAS20251' AND table_name = ?
            ORDER BY column_id
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, tableName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                System.out.printf("%-25s %-20s %-10s %-8s %-15s\n", 
                    "اسم العمود", "نوع البيانات", "الطول", "NULL", "القيمة الافتراضية");
                System.out.println("-".repeat(80));
                
                int columnCount = 0;
                while (rs.next()) {
                    String columnName = rs.getString("column_name");
                    String dataType = rs.getString("data_type");
                    String dataLength = rs.getString("data_length");
                    String dataPrecision = rs.getString("data_precision");
                    String dataScale = rs.getString("data_scale");
                    String nullable = rs.getString("nullable");
                    String dataDefault = rs.getString("data_default");
                    
                    // تنسيق نوع البيانات
                    String typeInfo = dataType;
                    if (dataPrecision != null) {
                        typeInfo += "(" + dataPrecision;
                        if (dataScale != null && !dataScale.equals("0")) {
                            typeInfo += "," + dataScale;
                        }
                        typeInfo += ")";
                    } else if (dataLength != null && !dataType.equals("DATE")) {
                        typeInfo += "(" + dataLength + ")";
                    }
                    
                    String defaultValue = dataDefault != null ? 
                        (dataDefault.length() > 12 ? dataDefault.substring(0, 12) + "..." : dataDefault) : "-";
                    
                    System.out.printf("%-25s %-20s %-10s %-8s %-15s\n",
                        columnName,
                        typeInfo,
                        dataLength != null ? dataLength : "-",
                        "Y".equals(nullable) ? "نعم" : "لا",
                        defaultValue
                    );
                    
                    columnCount++;
                }
                
                System.out.println("\n📊 إجمالي الأعمدة: " + columnCount);
            }
        }
    }
    
    private void showSampleData(Connection connection, String tableName) throws SQLException {
        System.out.println("\n📄 عينة من البيانات:");
        System.out.println("-".repeat(80));
        
        // عدد الصفوف أولاً
        String countQuery = "SELECT COUNT(*) as total FROM IAS20251." + tableName;
        try (PreparedStatement stmt = connection.prepareStatement(countQuery);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                int total = rs.getInt("total");
                System.out.println("📊 إجمالي الصفوف: " + total);
            }
        }
        
        // عينة من البيانات
        String sampleQuery = "SELECT * FROM IAS20251." + tableName + " WHERE ROWNUM <= 5";
        
        try (PreparedStatement stmt = connection.prepareStatement(sampleQuery);
             ResultSet rs = stmt.executeQuery()) {
            
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            // عرض أسماء الأعمدة
            for (int i = 1; i <= columnCount; i++) {
                System.out.printf("%-15s ", metaData.getColumnName(i));
            }
            System.out.println();
            System.out.println("-".repeat(columnCount * 16));
            
            // عرض البيانات
            int rowCount = 0;
            while (rs.next() && rowCount < 5) {
                for (int i = 1; i <= columnCount; i++) {
                    Object value = rs.getObject(i);
                    String displayValue = value != null ? value.toString() : "NULL";
                    if (displayValue.length() > 12) {
                        displayValue = displayValue.substring(0, 12) + "...";
                    }
                    System.out.printf("%-15s ", displayValue);
                }
                System.out.println();
                rowCount++;
            }
            
            if (rowCount == 0) {
                System.out.println("(الجدول فارغ)");
            }
        }
    }
    
    private void generateCreateScript(Connection connection, String tableName) throws SQLException {
        System.out.println("\n🏗️ سكريبت إنشاء الجدول:");
        System.out.println("-".repeat(80));
        
        StringBuilder createScript = new StringBuilder();
        createScript.append("-- سكريبت إنشاء جدول وحدات القياس\n");
        createScript.append("-- مبني على جدول ").append(tableName).append(" من قاعدة البيانات IAS20251\n\n");
        createScript.append("CREATE TABLE ERP_MEASUREMENT_UNITS (\n");
        
        String query = """
            SELECT 
                column_name,
                data_type,
                data_length,
                data_precision,
                data_scale,
                nullable,
                column_id
            FROM all_tab_columns 
            WHERE owner = 'IAS20251' AND table_name = ?
            ORDER BY column_id
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, tableName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                boolean first = true;
                
                while (rs.next()) {
                    if (!first) {
                        createScript.append(",\n");
                    }
                    
                    String columnName = rs.getString("column_name");
                    String dataType = rs.getString("data_type");
                    String dataLength = rs.getString("data_length");
                    String dataPrecision = rs.getString("data_precision");
                    String dataScale = rs.getString("data_scale");
                    String nullable = rs.getString("nullable");
                    
                    // تنسيق نوع البيانات
                    String typeInfo = dataType;
                    if (dataPrecision != null) {
                        typeInfo += "(" + dataPrecision;
                        if (dataScale != null && !dataScale.equals("0")) {
                            typeInfo += "," + dataScale;
                        }
                        typeInfo += ")";
                    } else if (dataLength != null && !dataType.equals("DATE")) {
                        typeInfo += "(" + dataLength + ")";
                    }
                    
                    String nullConstraint = "Y".equals(nullable) ? "" : " NOT NULL";
                    
                    createScript.append("    ").append(columnName).append(" ").append(typeInfo).append(nullConstraint);
                    
                    first = false;
                }
                
                createScript.append("\n);\n\n");
                createScript.append("-- إضافة تعليقات\n");
                createScript.append("COMMENT ON TABLE ERP_MEASUREMENT_UNITS IS 'جدول وحدات القياس';\n");
                
                System.out.println(createScript.toString());
            }
        }
    }
}
