@echo off
echo ====================================
echo    Oracle JDBC Problem SOLVED
echo ====================================
echo.

echo Checking Oracle libraries...
if not exist "lib\ojdbc11.jar" (
    echo ERROR: Oracle JDBC library missing!
    echo Running LibraryDownloader...
    java LibraryDownloader
    if errorlevel 1 (
        echo FAILED to download libraries!
        pause
        exit /b 1
    )
)

if not exist "lib\orai18n.jar" (
    echo ERROR: Oracle Internationalization library missing!
    echo This library is required for Arabic character support (AR8MSWIN1256)
    echo Running LibraryDownloader...
    java LibraryDownloader
    if errorlevel 1 (
        echo FAILED to download libraries!
        pause
        exit /b 1
    )
)

echo SUCCESS: Oracle libraries found (ojdbc11.jar + orai18n.jar)
echo.

echo Compiling with Oracle libraries...
javac -encoding UTF-8 -cp "lib/*;." *.java
if errorlevel 1 (
    echo FAILED to compile!
    pause
    exit /b 1
)

echo SUCCESS: Compilation completed
echo.

echo Starting system with Oracle JDBC (English locale to fix regex issue)...
echo.

java -Duser.language=en -Duser.country=US -Dfile.encoding=UTF-8 -cp "lib/*;." CompleteSystemTest

echo.
echo System finished.
pause
