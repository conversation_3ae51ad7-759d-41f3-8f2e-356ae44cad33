import java.sql.*;
import java.util.Locale;
import java.util.Properties;

/**
 * اختبار الاتصال مع إصلاح مشكلة regex
 * Fixed Connection Test with regex fix
 */
public class FixedConnectionTest {
    
    public static void main(String[] args) {
        System.out.println("🔄 بدء اختبار الاتصال مع إصلاح regex...");
        System.out.println("=" + "=".repeat(50));
        
        // إصلاح مشكلة regex قبل أي شيء
        fixRegexIssue();
        
        // إعدادات الاتصال
        String url = "*************************************";
        String username = "ias20251";
        String password = "ys123";
        
        System.out.println("📋 إعدادات الاتصال:");
        System.out.println("   URL: " + url);
        System.out.println("   المستخدم: " + username);
        System.out.println("   كلمة المرور: " + password);
        System.out.println();
        
        // اختبار الاتصال
        testConnection(url, username, password);
    }
    
    /**
     * إصلاح مشكلة regex
     */
    private static void fixRegexIssue() {
        System.out.println("🔧 إصلاح مشكلة regex...");
        
        try {
            // تعيين Locale إلى الإنجليزية
            Locale.setDefault(Locale.ENGLISH);
            System.out.println("✅ تم تعيين Locale إلى الإنجليزية");
            
            // تعيين خصائص النظام لتجنب مشاكل regex
            System.setProperty("user.language", "en");
            System.setProperty("user.country", "US");
            System.setProperty("file.encoding", "UTF-8");
            
            // خصائص Oracle محددة
            System.setProperty("oracle.jdbc.autoCommitSpecCompliant", "false");
            System.setProperty("oracle.jdbc.timezoneAsRegion", "false");
            
            System.out.println("✅ تم تعيين خصائص النظام");
            
        } catch (Exception e) {
            System.out.println("⚠️ تحذير في إصلاح regex: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * اختبار الاتصال
     */
    private static void testConnection(String url, String username, String password) {
        System.out.println("🔗 اختبار الاتصال...");
        
        try {
            // تحميل تعريف Oracle
            Class.forName("oracle.jdbc.driver.OracleDriver");
            System.out.println("✅ تم تحميل تعريف Oracle");
            
            // إعداد خصائص الاتصال
            Properties props = new Properties();
            props.setProperty("user", username);
            props.setProperty("password", password);
            props.setProperty("oracle.jdbc.ReadTimeout", "30000");
            props.setProperty("oracle.net.CONNECT_TIMEOUT", "30000");
            
            // محاولة الاتصال
            System.out.println("🔄 جاري الاتصال...");
            Connection connection = DriverManager.getConnection(url, props);
            
            if (connection != null && !connection.isClosed()) {
                System.out.println("✅ نجح الاتصال!");
                
                // اختبار استعلام بسيط
                try (Statement stmt = connection.createStatement();
                     ResultSet rs = stmt.executeQuery("SELECT 1 FROM DUAL")) {
                    
                    if (rs.next()) {
                        System.out.println("✅ استعلام DUAL نجح");
                    }
                }
                
                // معلومات قاعدة البيانات
                DatabaseMetaData metaData = connection.getMetaData();
                System.out.println("📋 معلومات قاعدة البيانات:");
                System.out.println("   المنتج: " + metaData.getDatabaseProductName());
                System.out.println("   الإصدار: " + metaData.getDatabaseProductVersion());
                System.out.println("   المستخدم: " + metaData.getUserName());
                
                // اختبار الوصول للجداول
                testTableAccess(connection);
                
                connection.close();
                System.out.println("✅ تم إغلاق الاتصال");
                
            } else {
                System.out.println("❌ فشل في إنشاء الاتصال");
            }
            
        } catch (ClassNotFoundException e) {
            System.out.println("❌ فشل في تحميل تعريف Oracle: " + e.getMessage());
        } catch (SQLException e) {
            System.out.println("❌ خطأ في الاتصال:");
            System.out.println("   الرمز: " + e.getErrorCode());
            System.out.println("   الرسالة: " + e.getMessage());
            
            // تشخيص أخطاء شائعة
            diagnoseSQLError(e);
            
        } catch (Exception e) {
            System.out.println("❌ خطأ عام: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
        System.out.println("🏁 انتهى الاختبار");
    }
    
    /**
     * اختبار الوصول للجداول
     */
    private static void testTableAccess(Connection connection) {
        System.out.println("📋 اختبار الوصول للجداول...");
        
        try {
            // اختبار الوصول لجدول IAS_ITM_MST
            String query = "SELECT COUNT(*) as total FROM IAS20251.IAS_ITM_MST WHERE ROWNUM <= 1";
            
            try (PreparedStatement stmt = connection.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next()) {
                    System.out.println("✅ تم الوصول لجدول IAS_ITM_MST بنجاح");
                    
                    // اختبار عدد السجلات
                    String countQuery = "SELECT COUNT(*) as total FROM IAS20251.IAS_ITM_MST";
                    try (PreparedStatement countStmt = connection.prepareStatement(countQuery);
                         ResultSet countRs = countStmt.executeQuery()) {
                        
                        if (countRs.next()) {
                            int total = countRs.getInt("total");
                            System.out.println("📊 عدد الأصناف في IAS_ITM_MST: " + total);
                        }
                    }
                }
                
            } catch (SQLException e) {
                System.out.println("❌ فشل في الوصول لجدول IAS_ITM_MST: " + e.getMessage());
                
                // اختبار بديل - البحث عن الجداول المتاحة
                listAvailableTables(connection);
            }
            
        } catch (Exception e) {
            System.out.println("❌ خطأ في اختبار الجداول: " + e.getMessage());
        }
    }
    
    /**
     * عرض الجداول المتاحة
     */
    private static void listAvailableTables(Connection connection) {
        try {
            System.out.println("🔍 البحث عن الجداول المتاحة...");
            
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet tables = metaData.getTables(null, "IAS20251", "%", new String[]{"TABLE"});
            
            System.out.println("📋 الجداول المتاحة في schema IAS20251:");
            int count = 0;
            while (tables.next() && count < 20) {
                String tableName = tables.getString("TABLE_NAME");
                System.out.println("   - " + tableName);
                count++;
            }
            
            if (count == 0) {
                System.out.println("   لا توجد جداول في schema IAS20251");
                
                // البحث في schema الحالي
                ResultSet userTables = metaData.getTables(null, null, "%", new String[]{"TABLE"});
                System.out.println("📋 الجداول في schema الحالي:");
                count = 0;
                while (userTables.next() && count < 10) {
                    String tableName = userTables.getString("TABLE_NAME");
                    String schemaName = userTables.getString("TABLE_SCHEM");
                    System.out.println("   - " + schemaName + "." + tableName);
                    count++;
                }
            }
            
        } catch (SQLException e) {
            System.out.println("❌ فشل في الحصول على قائمة الجداول: " + e.getMessage());
        }
    }
    
    /**
     * تشخيص أخطاء SQL
     */
    private static void diagnoseSQLError(SQLException e) {
        int errorCode = e.getErrorCode();
        
        switch (errorCode) {
            case 17002:
                System.out.println("💡 مشكلة في الشبكة أو عنوان الخادم غير صحيح");
                break;
            case 1017:
                System.out.println("💡 اسم المستخدم أو كلمة المرور خطأ");
                break;
            case 12505:
                System.out.println("💡 اسم الخدمة (SID) خطأ - تأكد من أن 'orcl' صحيح");
                break;
            case 12541:
                System.out.println("💡 لا يوجد listener على العنوان المحدد");
                break;
            case 942:
                System.out.println("💡 الجدول أو المشهد غير موجود");
                break;
            default:
                System.out.println("💡 خطأ غير معروف - راجع إعدادات قاعدة البيانات");
        }
    }
}
