import java.sql.*;
import java.util.*;
import java.util.concurrent.Executor;

/**
 * اتصال وهمي لاختبار النظام بدون قاعدة بيانات حقيقية
 * Mock Connection for testing without real database
 */
public class MockConnection implements Connection {
    
    private boolean closed = false;
    private boolean autoCommit = true;
    private Map<String, List<Map<String, Object>>> mockTables;
    
    public MockConnection() {
        initializeMockData();
    }
    
    /**
     * تهيئة البيانات الوهمية
     */
    private void initializeMockData() {
        mockTables = new HashMap<>();
        
        // جدول ITEMS
        List<Map<String, Object>> itemsData = new ArrayList<>();
        
        Map<String, Object> item1 = new HashMap<>();
        item1.put("ITEM_ID", 1);
        item1.put("ITEM_CODE", "PHONE001");
        item1.put("ITEM_NAME_AR", "هاتف سامسونج جالاكسي S23");
        item1.put("ITEM_NAME_EN", "Samsung Galaxy S23");
        item1.put("DESCRIPTION", "هاتف ذكي متطور");
        item1.put("CATEGORY_CODE", "ELECTRONICS");
        item1.put("UNIT_CODE", "PCS");
        item1.put("SALES_PRICE", 2500.00);
        item1.put("COST_PRICE", 2000.00);
        item1.put("CURRENT_STOCK", 50.0);
        item1.put("MIN_STOCK", 10.0);
        item1.put("IS_ACTIVE", "Y");
        item1.put("BARCODE", "1234567890123");
        item1.put("SUPPLIER", "شركة التقنية المتقدمة");
        item1.put("MANUFACTURER", "سامسونج");
        itemsData.add(item1);
        
        Map<String, Object> item2 = new HashMap<>();
        item2.put("ITEM_ID", 2);
        item2.put("ITEM_CODE", "LAPTOP001");
        item2.put("ITEM_NAME_AR", "لابتوب ديل انسبايرون");
        item2.put("ITEM_NAME_EN", "Dell Inspiron Laptop");
        item2.put("DESCRIPTION", "لابتوب للاستخدام المكتبي");
        item2.put("CATEGORY_CODE", "ELECTRONICS");
        item2.put("UNIT_CODE", "PCS");
        item2.put("SALES_PRICE", 3500.00);
        item2.put("COST_PRICE", 3000.00);
        item2.put("CURRENT_STOCK", 25.0);
        item2.put("MIN_STOCK", 5.0);
        item2.put("IS_ACTIVE", "Y");
        item2.put("BARCODE", "1234567890124");
        item2.put("SUPPLIER", "شركة الحاسوب");
        item2.put("MANUFACTURER", "ديل");
        itemsData.add(item2);
        
        Map<String, Object> item3 = new HashMap<>();
        item3.put("ITEM_ID", 3);
        item3.put("ITEM_CODE", "SHIRT001");
        item3.put("ITEM_NAME_AR", "قميص قطني رجالي");
        item3.put("ITEM_NAME_EN", "Men's Cotton Shirt");
        item3.put("DESCRIPTION", "قميص قطني عالي الجودة");
        item3.put("CATEGORY_CODE", "CLOTHING");
        item3.put("UNIT_CODE", "PCS");
        item3.put("SALES_PRICE", 150.00);
        item3.put("COST_PRICE", 100.00);
        item3.put("CURRENT_STOCK", 100.0);
        item3.put("MIN_STOCK", 20.0);
        item3.put("IS_ACTIVE", "Y");
        item3.put("BARCODE", "1234567890125");
        item3.put("SUPPLIER", "مصنع الملابس");
        item3.put("MANUFACTURER", "ماركة محلية");
        itemsData.add(item3);
        
        mockTables.put("ITEMS", itemsData);
        
        // جدول PRODUCTS
        List<Map<String, Object>> productsData = new ArrayList<>();
        
        Map<String, Object> product1 = new HashMap<>();
        product1.put("PRODUCT_ID", 1);
        product1.put("PRODUCT_CODE", "PROD001");
        product1.put("PRODUCT_NAME", "منتج تجريبي 1");
        product1.put("PRODUCT_DESC", "وصف المنتج التجريبي الأول");
        product1.put("CATEGORY", "CAT1");
        product1.put("UNIT", "PCS");
        product1.put("PRICE", 100.00);
        product1.put("COST", 80.00);
        product1.put("QTY_ON_HAND", 50.0);
        product1.put("MIN_QTY", 10.0);
        product1.put("ACTIVE_FLAG", "Y");
        product1.put("UPC_CODE", "9876543210123");
        product1.put("VENDOR", "مورد تجريبي");
        productsData.add(product1);
        
        Map<String, Object> product2 = new HashMap<>();
        product2.put("PRODUCT_ID", 2);
        product2.put("PRODUCT_CODE", "PROD002");
        product2.put("PRODUCT_NAME", "منتج تجريبي 2");
        product2.put("PRODUCT_DESC", "وصف المنتج التجريبي الثاني");
        product2.put("CATEGORY", "CAT2");
        product2.put("UNIT", "KG");
        product2.put("PRICE", 200.00);
        product2.put("COST", 150.00);
        product2.put("QTY_ON_HAND", 30.0);
        product2.put("MIN_QTY", 5.0);
        product2.put("ACTIVE_FLAG", "Y");
        product2.put("UPC_CODE", "9876543210124");
        product2.put("VENDOR", "مورد آخر");
        productsData.add(product2);
        
        mockTables.put("PRODUCTS", productsData);
        
        // جدول ITEM_MASTER
        List<Map<String, Object>> itemMasterData = new ArrayList<>();
        
        Map<String, Object> master1 = new HashMap<>();
        master1.put("MASTER_ID", 1);
        master1.put("ITEM_NO", "MASTER001");
        master1.put("ITEM_DESC", "صنف رئيسي 1");
        master1.put("ITEM_TYPE", "FINISHED");
        master1.put("UNIT_OF_MEASURE", "EA");
        master1.put("STANDARD_COST", 75.00);
        master1.put("LIST_PRICE", 120.00);
        master1.put("ON_HAND_QTY", 80.0);
        master1.put("REORDER_POINT", 15.0);
        master1.put("STATUS", "A");
        itemMasterData.add(master1);
        
        mockTables.put("ITEM_MASTER", itemMasterData);
    }
    
    @Override
    public Statement createStatement() throws SQLException {
        if (closed) throw new SQLException("Connection is closed");
        return new MockStatement(this);
    }
    
    @Override
    public PreparedStatement prepareStatement(String sql) throws SQLException {
        if (closed) throw new SQLException("Connection is closed");
        return new MockPreparedStatement(this, sql);
    }
    
    @Override
    public DatabaseMetaData getMetaData() throws SQLException {
        if (closed) throw new SQLException("Connection is closed");
        return new MockDatabaseMetaData();
    }
    
    @Override
    public boolean isClosed() throws SQLException {
        return closed;
    }
    
    @Override
    public void close() throws SQLException {
        closed = true;
    }
    
    @Override
    public void commit() throws SQLException {
        if (closed) throw new SQLException("Connection is closed");
        // لا حاجة لعمل شيء في الاتصال الوهمي
    }
    
    @Override
    public void rollback() throws SQLException {
        if (closed) throw new SQLException("Connection is closed");
        // لا حاجة لعمل شيء في الاتصال الوهمي
    }
    
    @Override
    public void setAutoCommit(boolean autoCommit) throws SQLException {
        if (closed) throw new SQLException("Connection is closed");
        this.autoCommit = autoCommit;
    }
    
    @Override
    public boolean getAutoCommit() throws SQLException {
        if (closed) throw new SQLException("Connection is closed");
        return autoCommit;
    }
    
    /**
     * الحصول على البيانات الوهمية للجدول
     */
    public List<Map<String, Object>> getMockTableData(String tableName) {
        return mockTables.getOrDefault(tableName.toUpperCase(), new ArrayList<>());
    }
    
    /**
     * الحصول على أسماء الجداول الوهمية
     */
    public Set<String> getMockTableNames() {
        return mockTables.keySet();
    }
    
    // باقي الدوال المطلوبة لواجهة Connection
    @Override public CallableStatement prepareCall(String sql) throws SQLException { throw new UnsupportedOperationException(); }
    @Override public String nativeSQL(String sql) throws SQLException { return sql; }
    @Override public boolean isReadOnly() throws SQLException { return false; }
    @Override public void setReadOnly(boolean readOnly) throws SQLException {}
    @Override public String getCatalog() throws SQLException { return "MOCK_CATALOG"; }
    @Override public void setCatalog(String catalog) throws SQLException {}
    @Override public int getTransactionIsolation() throws SQLException { return TRANSACTION_READ_COMMITTED; }
    @Override public void setTransactionIsolation(int level) throws SQLException {}
    @Override public SQLWarning getWarnings() throws SQLException { return null; }
    @Override public void clearWarnings() throws SQLException {}
    @Override public Statement createStatement(int resultSetType, int resultSetConcurrency) throws SQLException { return createStatement(); }
    @Override public PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency) throws SQLException { return prepareStatement(sql); }
    @Override public CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency) throws SQLException { throw new UnsupportedOperationException(); }
    @Override public Map<String, Class<?>> getTypeMap() throws SQLException { return new HashMap<>(); }
    @Override public void setTypeMap(Map<String, Class<?>> map) throws SQLException {}
    @Override public int getHoldability() throws SQLException { return ResultSet.HOLD_CURSORS_OVER_COMMIT; }
    @Override public void setHoldability(int holdability) throws SQLException {}
    @Override public Savepoint setSavepoint() throws SQLException { throw new UnsupportedOperationException(); }
    @Override public Savepoint setSavepoint(String name) throws SQLException { throw new UnsupportedOperationException(); }
    @Override public void rollback(Savepoint savepoint) throws SQLException { throw new UnsupportedOperationException(); }
    @Override public void releaseSavepoint(Savepoint savepoint) throws SQLException { throw new UnsupportedOperationException(); }
    @Override public Statement createStatement(int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException { return createStatement(); }
    @Override public PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException { return prepareStatement(sql); }
    @Override public CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException { throw new UnsupportedOperationException(); }
    @Override public PreparedStatement prepareStatement(String sql, int autoGeneratedKeys) throws SQLException { return prepareStatement(sql); }
    @Override public PreparedStatement prepareStatement(String sql, int[] columnIndexes) throws SQLException { return prepareStatement(sql); }
    @Override public PreparedStatement prepareStatement(String sql, String[] columnNames) throws SQLException { return prepareStatement(sql); }
    @Override public Clob createClob() throws SQLException { throw new UnsupportedOperationException(); }
    @Override public Blob createBlob() throws SQLException { throw new UnsupportedOperationException(); }
    @Override public NClob createNClob() throws SQLException { throw new UnsupportedOperationException(); }
    @Override public SQLXML createSQLXML() throws SQLException { throw new UnsupportedOperationException(); }
    @Override public boolean isValid(int timeout) throws SQLException { return !closed; }
    @Override public void setClientInfo(String name, String value) throws SQLClientInfoException {}
    @Override public String getClientInfo(String name) throws SQLException { return null; }
    @Override public Properties getClientInfo() throws SQLException { return new Properties(); }
    @Override public void setClientInfo(Properties properties) throws SQLClientInfoException {}
    @Override public Array createArrayOf(String typeName, Object[] elements) throws SQLException { throw new UnsupportedOperationException(); }
    @Override public Struct createStruct(String typeName, Object[] attributes) throws SQLException { throw new UnsupportedOperationException(); }
    @Override public String getSchema() throws SQLException { return "MOCK_SCHEMA"; }
    @Override public void setSchema(String schema) throws SQLException {}
    @Override public void abort(Executor executor) throws SQLException {}
    @Override public void setNetworkTimeout(Executor executor, int milliseconds) throws SQLException {}
    @Override public int getNetworkTimeout() throws SQLException { return 0; }
    @Override public <T> T unwrap(Class<T> iface) throws SQLException { throw new UnsupportedOperationException(); }
    @Override public boolean isWrapperFor(Class<?> iface) throws SQLException { return false; }
}
