import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * فحص جدول comments لمعرفة تسميات الحقول الحقيقية
 */
public class CheckFieldComments {

    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");

            Connection conn = DriverManager.getConnection("*************************************",
                    "ship_erp", "ship_erp_password");
            System.out.println("✅ تم الاتصال بـ SHIP_ERP");

            // فحص بنية جدول comments
            checkCommentsTableStructure(conn);

            // عرض عينة من محتوى جدول comments
            showCommentsSample(conn);

            // فحص تعليقات جدول IAS_ITM_MST
            checkTableComments(conn, "IAS_ITM_MST");

            // فحص تعليقات جدول IAS_ITM_DTL
            checkTableComments(conn, "IAS_ITM_DTL");

            // فحص الحقول المهمة للنافذة
            checkImportantFieldsComments(conn);

            conn.close();

        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * فحص بنية جدول comment
     */
    private static void checkCommentTableStructure(Connection conn) throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("📋 بنية جدول COMMENT");
        System.out.println("=".repeat(80));

        String sql = """
                    SELECT
                        COLUMN_NAME,
                        DATA_TYPE,
                        DATA_LENGTH,
                        NULLABLE
                    FROM USER_TAB_COLUMNS
                    WHERE TABLE_NAME = 'COMMENT'
                    ORDER BY COLUMN_ID
                """;

        Statement stmt = conn.createStatement();
        ResultSet rs = stmt.executeQuery(sql);

        System.out.printf("%-20s %-15s %-10s %-8s\n", "COLUMN_NAME", "DATA_TYPE", "LENGTH",
                "NULL?");
        System.out.println("-".repeat(60));

        while (rs.next()) {
            String columnName = rs.getString("COLUMN_NAME");
            String dataType = rs.getString("DATA_TYPE");
            int dataLength = rs.getInt("DATA_LENGTH");
            String nullable = rs.getString("NULLABLE");

            System.out.printf("%-20s %-15s %-10d %-8s\n", columnName, dataType, dataLength,
                    nullable);
        }

        rs.close();
        stmt.close();
    }

    /**
     * فحص تعليقات جدول محدد
     */
    private static void checkTableComments(Connection conn, String tableName) throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("📝 تعليقات جدول: " + tableName);
        System.out.println("=".repeat(80));

        // فحص الأعمدة الموجودة في جدول comment
        Statement stmt = conn.createStatement();
        ResultSet rs = stmt.executeQuery("SELECT * FROM COMMENT WHERE ROWNUM <= 1");
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();

        System.out.println("📊 أعمدة جدول COMMENT:");
        for (int i = 1; i <= columnCount; i++) {
            System.out.println("  " + i + ". " + metaData.getColumnName(i));
        }
        rs.close();

        // البحث عن تعليقات الجدول المحدد
        String searchSql = "";

        // جرب أعمدة مختلفة للبحث
        String[] possibleColumns = {"TABLE_NAME", "OWNER", "OBJECT_NAME", "NAME"};
        String[] commentColumns = {"COMMENTS", "COMMENT", "DESCRIPTION", "DESC"};
        String[] fieldColumns = {"COLUMN_NAME", "FIELD_NAME", "FIELD", "COL_NAME"};

        for (String tableCol : possibleColumns) {
            for (String commentCol : commentColumns) {
                for (String fieldCol : fieldColumns) {
                    try {
                        searchSql = String.format(
                                "SELECT %s, %s FROM COMMENT WHERE UPPER(%s) = UPPER('%s') ORDER BY %s",
                                fieldCol, commentCol, tableCol, tableName, fieldCol);

                        PreparedStatement pstmt = conn.prepareStatement(searchSql);
                        ResultSet searchRs = pstmt.executeQuery();

                        if (searchRs.next()) {
                            System.out.println("\n✅ تم العثور على التعليقات باستخدام:");
                            System.out.println("   الجدول: " + tableCol + " = " + tableName);
                            System.out.println("   الحقل: " + fieldCol);
                            System.out.println("   التعليق: " + commentCol);
                            System.out.println();

                            System.out.printf("%-30s %-50s\n", "اسم الحقل", "التعليق");
                            System.out.println("-".repeat(85));

                            // إعادة تشغيل الاستعلام لعرض النتائج
                            searchRs.close();
                            pstmt.close();

                            pstmt = conn.prepareStatement(searchSql);
                            searchRs = pstmt.executeQuery();

                            while (searchRs.next()) {
                                String fieldName = searchRs.getString(1);
                                String comment = searchRs.getString(2);

                                if (fieldName != null && comment != null) {
                                    System.out.printf("%-30s %-50s\n", fieldName,
                                            comment.length() > 50 ? comment.substring(0, 47) + "..."
                                                    : comment);
                                }
                            }

                            searchRs.close();
                            pstmt.close();
                            return; // تم العثور على النتائج
                        }

                        searchRs.close();
                        pstmt.close();

                    } catch (SQLException e) {
                        // تجاهل الأخطاء وجرب التركيبة التالية
                    }
                }
            }
        }

        System.out.println("⚠️ لم يتم العثور على تعليقات للجدول: " + tableName);
        System.out.println("💡 جرب فحص محتوى جدول COMMENT يدوياً");

        stmt.close();
    }

    /**
     * فحص تعليقات الحقول المهمة للنافذة
     */
    private static void checkImportantFieldsComments(Connection conn) throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("🎯 البحث عن الحقول المهمة في النافذة");
        System.out.println("=".repeat(80));

        String[] importantFields = {"I_CODE", "I_NAME", "I_E_NAME", "I_DESC", "G_CODE",
                "PRIMARY_COST", "INIT_PRIMARY_COST", "INACTIVE", "SERVICE_ITM", "CASH_SALE",
                "ITM_UNT", "P_SIZE", "BARCODE"};

        Statement stmt = conn.createStatement();

        for (String field : importantFields) {
            System.out.println("\n🔍 البحث عن: " + field);

            // جرب استعلامات مختلفة للبحث
            String[] searchQueries =
                    {"SELECT * FROM COMMENT WHERE UPPER(COLUMN_NAME) = UPPER('" + field + "')",
                            "SELECT * FROM COMMENT WHERE UPPER(FIELD_NAME) = UPPER('" + field
                                    + "')",
                            "SELECT * FROM COMMENT WHERE UPPER(FIELD) = UPPER('" + field + "')",
                            "SELECT * FROM COMMENT WHERE COLUMN_NAME LIKE '%" + field + "%'",
                            "SELECT * FROM COMMENT WHERE COMMENTS LIKE '%" + field + "%'"};

            boolean found = false;
            for (String query : searchQueries) {
                try {
                    ResultSet rs = stmt.executeQuery(query);
                    if (rs.next()) {
                        found = true;
                        System.out.println("  ✅ تم العثور عليه:");

                        ResultSetMetaData metaData = rs.getMetaData();
                        int columnCount = metaData.getColumnCount();

                        for (int i = 1; i <= columnCount; i++) {
                            String columnName = metaData.getColumnName(i);
                            String value = rs.getString(i);
                            if (value != null && !value.trim().isEmpty()) {
                                System.out.println("    " + columnName + ": " + value);
                            }
                        }
                        break;
                    }
                    rs.close();
                } catch (SQLException e) {
                    // تجاهل الأخطاء
                }
            }

            if (!found) {
                System.out.println("  ❌ لم يتم العثور عليه");
            }
        }

        stmt.close();
    }

    /**
     * عرض عينة من محتوى جدول comment
     */
    private static void showCommentSample(Connection conn) throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("📄 عينة من محتوى جدول COMMENT");
        System.out.println("=".repeat(80));

        Statement stmt = conn.createStatement();
        ResultSet rs = stmt.executeQuery("SELECT * FROM COMMENT WHERE ROWNUM <= 10");

        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();

        // عرض أسماء الأعمدة
        for (int i = 1; i <= columnCount; i++) {
            System.out.printf("%-20s ", metaData.getColumnName(i));
        }
        System.out.println();
        System.out.println("-".repeat(columnCount * 21));

        // عرض البيانات
        while (rs.next()) {
            for (int i = 1; i <= columnCount; i++) {
                String value = rs.getString(i);
                if (value != null && value.length() > 18) {
                    value = value.substring(0, 15) + "...";
                }
                System.out.printf("%-20s ", value != null ? value : "NULL");
            }
            System.out.println();
        }

        rs.close();
        stmt.close();
    }
}
