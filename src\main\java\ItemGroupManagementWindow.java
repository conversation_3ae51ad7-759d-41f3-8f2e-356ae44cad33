import java.awt.BorderLayout;
import java.awt.ComponentOrientation;
import java.awt.FlowLayout;
import java.awt.Font;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSeparator;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.ListSelectionModel;
import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة إدارة مجموعات الأصناف الجديدة الأبعاد: 1377×782 بكسل نوع: تبويبات حسب جداول IAS20251
 */
public class ItemGroupManagementWindow extends JFrame {

    private Connection connection;
    private JTabbedPane tabbedPane;

    // نماذج الجداول
    private DefaultTableModel mainGroupModel;
    private DefaultTableModel mainSubGroupModel;
    private DefaultTableModel subGroupModel;
    private DefaultTableModel assistantGroupModel;
    private DefaultTableModel detailGroupModel;

    // الجداول
    private JTable mainGroupTable;
    private JTable mainSubGroupTable;
    private JTable subGroupTable;
    private JTable assistantGroupTable;
    private JTable detailGroupTable;

    public ItemGroupManagementWindow() {
        initializeConnection();
        initializeUI();
        loadAllData();
    }

    /**
     * تهيئة الاتصال بقاعدة البيانات
     */
    private void initializeConnection() {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            connection = DriverManager.getConnection("*************************************",
                    "ship_erp", "ship123");
            System.out.println("✅ تم الاتصال بقاعدة البيانات SHIP_ERP");
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "خطأ في الاتصال بقاعدة البيانات: " + e.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
            e.printStackTrace();
        }
    }

    /**
     * تهيئة واجهة المستخدم
     */
    private void initializeUI() {
        setTitle("إدارة مجموعات الأصناف");
        setSize(1377, 782);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);

        // تطبيق اللغة العربية
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إنشاء التبويبات
        tabbedPane = new JTabbedPane();
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إضافة التبويبات
        createMainGroupTab();
        createMainSubGroupTab();
        createSubGroupTab();
        createAssistantGroupTab();
        createDetailGroupTab();

        add(tabbedPane, BorderLayout.CENTER);

        // شريط الحالة
        JPanel statusPanel = createStatusPanel();
        add(statusPanel, BorderLayout.SOUTH);
    }

    /**
     * إنشاء تبويب المجموعات الرئيسية بنية جدول GROUP_DETAILS من IAS20251
     */
    private void createMainGroupTab() {
        // أعمدة جدول GROUP_DETAILS
        String[] columns = {"G_CODE", "G_A_NAME", "G_E_NAME", "TAX_PRCNT_DFLT", "ROL_LMT_QTY",
                "G_I_CODE", "SYNCHRNZ_TO_WEB_FLG", "USE_SAL_PRICE_AS_PUR_PRICE", "ALLOW_DISC_FLG",
                "ALLOW_DISC_PI_FLG", "G_ORDR", "IS_ACTIVE"};

        mainGroupModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        mainGroupTable = new JTable(mainGroupModel);
        setupTableProperties(mainGroupTable);

        JPanel panel = createTabPanel("المجموعات الرئيسية", mainGroupTable, "main");
        tabbedPane.addTab("المجموعات الرئيسية", panel);
    }

    /**
     * إنشاء تبويب المجموعات الفرعية بنية جدول IAS_MAINSUB_GRP_DTL من IAS20251
     */
    private void createMainSubGroupTab() {
        String[] columns = {"G_CODE", "MNG_CODE", "MNG_A_NAME", "MNG_E_NAME", "MNG_I_CODE",
                "SYNCHRNZ_TO_WEB_FLG", "MNG_ORDR", "IS_ACTIVE"};

        mainSubGroupModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        mainSubGroupTable = new JTable(mainSubGroupModel);
        setupTableProperties(mainSubGroupTable);

        JPanel panel = createTabPanel("المجموعات الفرعية", mainSubGroupTable, "mainsub");
        tabbedPane.addTab("المجموعات الفرعية", panel);
    }

    /**
     * إنشاء تبويب المجموعات تحت فرعية بنية جدول IAS_SUB_GRP_DTL من IAS20251
     */
    private void createSubGroupTab() {
        String[] columns = {"G_CODE", "MNG_CODE", "SUBG_CODE", "SUBG_A_NAME", "SUBG_E_NAME",
                "SUBG_I_CODE", "SYNCHRNZ_TO_WEB_FLG", "SUBG_ORDR", "IS_ACTIVE"};

        subGroupModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        subGroupTable = new JTable(subGroupModel);
        setupTableProperties(subGroupTable);

        JPanel panel = createTabPanel("المجموعات تحت فرعية", subGroupTable, "sub");
        tabbedPane.addTab("المجموعات تحت فرعية", panel);
    }

    /**
     * إنشاء تبويب المجموعات المساعدة بنية جدول IAS_ASSISTANT_GROUP من IAS20251
     */
    private void createAssistantGroupTab() {
        String[] columns = {"G_CODE", "MNG_CODE", "SUBG_CODE", "ASSISTANT_NO", "ASSISTANT_A_NAME",
                "ASSISTANT_E_NAME", "ASSISTANT_I_CODE", "SYNCHRNZ_TO_WEB_FLG", "ASSISTANT_ORDR",
                "IS_ACTIVE"};

        assistantGroupModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        assistantGroupTable = new JTable(assistantGroupModel);
        setupTableProperties(assistantGroupTable);

        JPanel panel = createTabPanel("المجموعات المساعدة", assistantGroupTable, "assistant");
        tabbedPane.addTab("المجموعات المساعدة", panel);
    }

    /**
     * إنشاء تبويب المجموعات التفصيلية بنية جدول IAS_DETAIL_GROUP من IAS20251
     */
    private void createDetailGroupTab() {
        String[] columns = {"G_CODE", "MNG_CODE", "SUBG_CODE", "ASSISTANT_NO", "DETAIL_NO",
                "DETAIL_A_NAME", "DETAIL_E_NAME", "DETAIL_I_CODE", "SYNCHRNZ_TO_WEB_FLG",
                "DETAIL_ORDR", "IS_ACTIVE"};

        detailGroupModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        detailGroupTable = new JTable(detailGroupModel);
        setupTableProperties(detailGroupTable);

        JPanel panel = createTabPanel("المجموعات التفصيلية", detailGroupTable, "detail");
        tabbedPane.addTab("المجموعات التفصيلية", panel);
    }

    /**
     * إعداد خصائص الجدول
     */
    private void setupTableProperties(JTable table) {
        table.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        table.setAutoResizeMode(JTable.AUTO_RESIZE_ALL_COLUMNS);
        table.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        table.setRowHeight(25);

        // تنسيق الخط
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        table.setFont(arabicFont);
        table.getTableHeader().setFont(new Font("Tahoma", Font.BOLD, 12));

        // محاذاة النص
        DefaultTableCellRenderer rightRenderer = new DefaultTableCellRenderer();
        rightRenderer.setHorizontalAlignment(SwingConstants.RIGHT);

        for (int i = 0; i < table.getColumnCount(); i++) {
            table.getColumnModel().getColumn(i).setCellRenderer(rightRenderer);
        }
    }

    /**
     * إنشاء لوحة التبويب
     */
    private JPanel createTabPanel(String title, JTable table, String type) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط الأدوات
        JPanel toolbarPanel = createToolbar(type);
        panel.add(toolbarPanel, BorderLayout.NORTH);

        // الجدول مع شريط التمرير
        JScrollPane scrollPane = new JScrollPane(table);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء شريط الأدوات
     */
    private JPanel createToolbar(String type) {
        JPanel toolbar = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        toolbar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // أزرار الأدوات
        JButton addBtn = new JButton("إضافة");
        JButton editBtn = new JButton("تعديل");
        JButton deleteBtn = new JButton("حذف");
        JButton refreshBtn = new JButton("تحديث");
        JButton importBtn = new JButton("استيراد");

        // إضافة الأحداث
        addBtn.addActionListener(e -> addRecord(type));
        editBtn.addActionListener(e -> editRecord(type));
        deleteBtn.addActionListener(e -> deleteRecord(type));
        refreshBtn.addActionListener(e -> refreshData(type));
        importBtn.addActionListener(e -> importData(type));

        toolbar.add(addBtn);
        toolbar.add(editBtn);
        toolbar.add(deleteBtn);
        toolbar.add(new JSeparator(SwingConstants.VERTICAL));
        toolbar.add(refreshBtn);
        toolbar.add(importBtn);

        return toolbar;
    }

    /**
     * إنشاء شريط الحالة
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel statusLabel = new JLabel("جاهز");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        panel.add(statusLabel, BorderLayout.WEST);

        return panel;
    }

    /**
     * تحميل جميع البيانات
     */
    private void loadAllData() {
        loadMainGroupData();
        loadMainSubGroupData();
        loadSubGroupData();
        loadAssistantGroupData();
        loadDetailGroupData();
    }

    /**
     * تحميل بيانات المجموعات الرئيسية
     */
    private void loadMainGroupData() {
        try {
            mainGroupModel.setRowCount(0);
            String sql = "SELECT * FROM ERP_GROUP_DETAILS ORDER BY G_CODE";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                Object[] row = {rs.getString("G_CODE"), rs.getString("G_A_NAME"),
                        rs.getString("G_E_NAME"), rs.getObject("TAX_PRCNT_DFLT"),
                        rs.getObject("ROL_LMT_QTY"), rs.getString("G_I_CODE"),
                        rs.getInt("SYNCHRNZ_TO_WEB_FLG") == 1 ? "نعم" : "لا",
                        rs.getInt("USE_SAL_PRICE_AS_PUR_PRICE") == 1 ? "نعم" : "لا",
                        rs.getInt("ALLOW_DISC_FLG") == 1 ? "نعم" : "لا",
                        rs.getInt("ALLOW_DISC_PI_FLG") == 1 ? "نعم" : "لا", rs.getObject("G_ORDR"),
                        rs.getInt("IS_ACTIVE") == 1 ? "نعم" : "لا"};
                mainGroupModel.addRow(row);
            }

            System.out.println("✅ تم تحميل " + mainGroupModel.getRowCount() + " مجموعة رئيسية");

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات الرئيسية: " + e.getMessage());
        }
    }

    /**
     * تحميل بيانات المجموعات الفرعية
     */
    private void loadMainSubGroupData() {
        try {
            mainSubGroupModel.setRowCount(0);
            String sql = "SELECT * FROM ERP_MAINSUB_GRP_DTL ORDER BY G_CODE, MNG_CODE";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                Object[] row = {rs.getString("G_CODE"), rs.getString("MNG_CODE"),
                        rs.getString("MNG_A_NAME"), rs.getString("MNG_E_NAME"),
                        rs.getString("MNG_I_CODE"),
                        rs.getInt("SYNCHRNZ_TO_WEB_FLG") == 1 ? "نعم" : "لا",
                        rs.getObject("MNG_ORDR"), rs.getInt("IS_ACTIVE") == 1 ? "نعم" : "لا"};
                mainSubGroupModel.addRow(row);
            }

            System.out.println("✅ تم تحميل " + mainSubGroupModel.getRowCount() + " مجموعة فرعية");

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات الفرعية: " + e.getMessage());
        }
    }

    /**
     * تحميل بيانات المجموعات تحت فرعية
     */
    private void loadSubGroupData() {
        try {
            subGroupModel.setRowCount(0);
            String sql = "SELECT * FROM ERP_SUB_GRP_DTL ORDER BY G_CODE, MNG_CODE, SUBG_CODE";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                Object[] row = {rs.getString("G_CODE"), rs.getString("MNG_CODE"),
                        rs.getString("SUBG_CODE"), rs.getString("SUBG_A_NAME"),
                        rs.getString("SUBG_E_NAME"), rs.getString("SUBG_I_CODE"),
                        rs.getInt("SYNCHRNZ_TO_WEB_FLG") == 1 ? "نعم" : "لا",
                        rs.getObject("SUBG_ORDR"), rs.getInt("IS_ACTIVE") == 1 ? "نعم" : "لا"};
                subGroupModel.addRow(row);
            }

            System.out.println("✅ تم تحميل " + subGroupModel.getRowCount() + " مجموعة تحت فرعية");

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات تحت فرعية: " + e.getMessage());
        }
    }

    /**
     * تحميل بيانات المجموعات المساعدة
     */
    private void loadAssistantGroupData() {
        try {
            assistantGroupModel.setRowCount(0);
            String sql =
                    "SELECT * FROM ERP_ASSISTANT_GROUP ORDER BY G_CODE, MNG_CODE, SUBG_CODE, ASSISTANT_NO";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                Object[] row = {rs.getString("G_CODE"), rs.getString("MNG_CODE"),
                        rs.getString("SUBG_CODE"), rs.getString("ASSISTANT_NO"),
                        rs.getString("ASSISTANT_A_NAME"), rs.getString("ASSISTANT_E_NAME"),
                        rs.getString("ASSISTANT_I_CODE"),
                        rs.getInt("SYNCHRNZ_TO_WEB_FLG") == 1 ? "نعم" : "لا",
                        rs.getObject("ASSISTANT_ORDR"), rs.getInt("IS_ACTIVE") == 1 ? "نعم" : "لا"};
                assistantGroupModel.addRow(row);
            }

            System.out
                    .println("✅ تم تحميل " + assistantGroupModel.getRowCount() + " مجموعة مساعدة");

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات المساعدة: " + e.getMessage());
        }
    }

    /**
     * تحميل بيانات المجموعات التفصيلية
     */
    private void loadDetailGroupData() {
        try {
            detailGroupModel.setRowCount(0);
            String sql =
                    "SELECT * FROM ERP_DETAIL_GROUP ORDER BY G_CODE, MNG_CODE, SUBG_CODE, ASSISTANT_NO, DETAIL_NO";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                Object[] row = {rs.getString("G_CODE"), rs.getString("MNG_CODE"),
                        rs.getString("SUBG_CODE"), rs.getString("ASSISTANT_NO"),
                        rs.getString("DETAIL_NO"), rs.getString("DETAIL_A_NAME"),
                        rs.getString("DETAIL_E_NAME"), rs.getString("DETAIL_I_CODE"),
                        rs.getInt("SYNCHRNZ_TO_WEB_FLG") == 1 ? "نعم" : "لا",
                        rs.getObject("DETAIL_ORDR"), rs.getInt("IS_ACTIVE") == 1 ? "نعم" : "لا"};
                detailGroupModel.addRow(row);
            }

            System.out.println("✅ تم تحميل " + detailGroupModel.getRowCount() + " مجموعة تفصيلية");

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات التفصيلية: " + e.getMessage());
        }
    }

    /**
     * إضافة سجل جديد
     */
    private void addRecord(String type) {
        JOptionPane.showMessageDialog(this, "سيتم إضافة " + getTypeDisplayName(type) + " جديدة",
                "إضافة", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * تعديل سجل
     */
    private void editRecord(String type) {
        JOptionPane.showMessageDialog(this, "سيتم تعديل " + getTypeDisplayName(type), "تعديل",
                JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * حذف سجل
     */
    private void deleteRecord(String type) {
        int result = JOptionPane.showConfirmDialog(this,
                "هل أنت متأكد من حذف " + getTypeDisplayName(type) + "؟", "تأكيد الحذف",
                JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            JOptionPane.showMessageDialog(this, "تم حذف " + getTypeDisplayName(type), "تم الحذف",
                    JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * تحديث البيانات
     */
    private void refreshData(String type) {
        switch (type) {
            case "main" -> loadMainGroupData();
            case "mainsub" -> loadMainSubGroupData();
            case "sub" -> loadSubGroupData();
            case "assistant" -> loadAssistantGroupData();
            case "detail" -> loadDetailGroupData();
        }
    }

    /**
     * استيراد البيانات
     */
    private void importData(String type) {
        try {
            int count = 0;
            String functionName = "";

            switch (type) {
                case "main" -> {
                    functionName = "PKG_ITEM_GROUP_IMPORT.IMPORT_MAIN_GROUPS";
                    count = executeImportFunction(functionName);
                }
                case "mainsub" -> {
                    functionName = "PKG_ITEM_GROUP_IMPORT.IMPORT_MAIN_SUB_GROUPS";
                    count = executeImportFunction(functionName);
                }
                case "sub" -> {
                    functionName = "PKG_ITEM_GROUP_IMPORT.IMPORT_SUB_GROUPS";
                    count = executeImportFunction(functionName);
                }
                case "assistant" -> {
                    count = 0; // مبسط للآن
                }
                case "detail" -> {
                    count = 0; // مبسط للآن
                }
            }

            if (count > 0) {
                JOptionPane.showMessageDialog(this,
                        "تم استيراد " + getTypeDisplayName(type)
                                + " بنجاح!\nعدد السجلات المستوردة: " + count,
                        "نجح الاستيراد", JOptionPane.INFORMATION_MESSAGE);
                refreshData(type);
            } else {
                JOptionPane.showMessageDialog(this, "لا توجد بيانات جديدة للاستيراد", "معلومات",
                        JOptionPane.INFORMATION_MESSAGE);
            }

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في الاستيراد: " + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * تنفيذ دالة الاستيراد
     */
    private int executeImportFunction(String functionName) throws SQLException {
        String sql = "SELECT " + functionName + " FROM DUAL";
        Statement stmt = connection.createStatement();
        ResultSet rs = stmt.executeQuery(sql);

        if (rs.next()) {
            return rs.getInt(1);
        }
        return 0;
    }

    /**
     * الحصول على اسم النوع للعرض
     */
    private String getTypeDisplayName(String type) {
        return switch (type) {
            case "main" -> "المجموعة الرئيسية";
            case "mainsub" -> "المجموعة الفرعية";
            case "sub" -> "المجموعة تحت فرعية";
            case "assistant" -> "المجموعة المساعدة";
            case "detail" -> "المجموعة التفصيلية";
            default -> "المجموعة";
        };
    }
}
