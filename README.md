# نظام إدارة الشحنات المتكامل
## Ship ERP System

نظام إدارة شحنات متكامل وشامل ومتقدم مطور بلغة Java مع دعم كامل للغة العربية ومحاذاة النصوص RTL.

## الميزات الرئيسية

### 🏢 نظام الإعدادات العامة
- **المتغيرات العامة للبرنامج**: إعدادات شاملة ومتقدمة للنظام
- **إعداد السنة المالية**: إدارة السنوات المالية وفتح سنوات جديدة
- **تهيئة العملات**: إدارة العملات وأسعار الصرف
- **بيانات الشركة والفروع**: إدارة معلومات الشركة والفروع
- **إدارة المستخدمين**: إضافة وتعديل المستخدمين
- **صلاحيات المستخدمين**: نظام صلاحيات متقدم ومرن

### 📦 الأنظمة الفرعية (قيد التطوير)
- **نظام إدارة الأصناف**
- **نظام إدارة الموردين**
- **نظام متابعة وتتبع الشحنات**
- **نظام الإدخالات الجمركية**
- **نظام إدارة التكاليف**

## المتطلبات التقنية

### البيئة المطلوبة
- **Java**: JDK 17 أو أحدث
- **Maven**: 3.6 أو أحدث
- **Oracle Database**: 11g أو أحدث (يُفضل 21c)
- **نظام التشغيل**: Windows 10/11

### التقنيات المستخدمة
- **JavaFX**: للواجهة الرسومية
- **Hibernate**: لإدارة قاعدة البيانات
- **Spring Framework**: للحقن والإدارة
- **Oracle Database**: قاعدة البيانات الرئيسية
- **HikariCP**: لإدارة اتصالات قاعدة البيانات
- **Logback**: للتسجيل والمراقبة

## التثبيت والإعداد

### 1. تثبيت المتطلبات الأساسية

#### تثبيت Java JDK 17
```bash
# تحميل من: https://adoptium.net/
# إضافة JAVA_HOME إلى متغيرات البيئة
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-17.0.x-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%
```

#### تثبيت Apache Maven
```bash
# تحميل من: https://maven.apache.org/download.cgi
# إضافة إلى PATH
set M2_HOME=C:\apache-maven-3.9.x
set PATH=%M2_HOME%\bin;%PATH%
```

#### تثبيت Oracle Database
```bash
# تحميل Oracle XE من الموقع الرسمي
# إنشاء مستخدم قاعدة البيانات:
# Username: ship_erp
# Password: ship_erp_password
```

### 2. إعداد المشروع

#### استنساخ المشروع
```bash
git clone <repository-url>
cd ship-erp-system
```

#### تثبيت التبعيات
```bash
mvn clean install
```

#### إعداد قاعدة البيانات
```sql
-- إنشاء مستخدم قاعدة البيانات
CREATE USER ship_erp IDENTIFIED BY ship_erp_password;
GRANT CONNECT, RESOURCE, DBA TO ship_erp;
GRANT UNLIMITED TABLESPACE TO ship_erp;
```

#### تحديث إعدادات قاعدة البيانات
```properties
# في ملف src/main/resources/application.properties
database.url=***********************************
database.username=ship_erp
database.password=ship_erp_password
```

### 3. تشغيل التطبيق

#### باستخدام Maven
```bash
mvn javafx:run
```

#### باستخدام JAR
```bash
mvn clean package
java -jar target/ship-erp-system-1.0.0.jar
```

## هيكل المشروع

```
ship-erp-system/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/shipment/erp/
│   │   │       ├── model/          # نماذج البيانات
│   │   │       ├── service/        # خدمات الأعمال
│   │   │       ├── repository/     # طبقة الوصول للبيانات
│   │   │       ├── controller/     # تحكم الواجهة
│   │   │       ├── view/           # واجهات المستخدم
│   │   │       ├── config/         # إعدادات التطبيق
│   │   │       ├── security/       # الأمان والصلاحيات
│   │   │       └── util/           # أدوات مساعدة
│   │   └── resources/
│   │       ├── styles/             # ملفات CSS
│   │       ├── fxml/               # ملفات FXML
│   │       ├── images/             # الصور والأيقونات
│   │       ├── fonts/              # الخطوط العربية
│   │       └── messages_ar.properties  # النصوص العربية
│   └── test/                       # اختبارات الوحدة
├── docs/                           # الوثائق
├── scripts/                        # سكريبتات قاعدة البيانات
└── pom.xml                         # إعدادات Maven
```

## دعم اللغة العربية و RTL

### الخطوط المدعومة
- **Noto Sans Arabic**: الخط الافتراضي
- **Tahoma**: خط احتياطي
- **Arial Unicode MS**: دعم إضافي

### إعدادات RTL
```css
.root {
    -fx-node-orientation: right-to-left;
    -fx-text-alignment: right;
}
```

### الترميز
- جميع الملفات محفوظة بترميز UTF-8
- دعم كامل للنصوص العربية
- محاذاة صحيحة للنصوص والحقول

## الأمان والصلاحيات

### تشفير كلمات المرور
- استخدام BCrypt لتشفير كلمات المرور
- مستوى أمان عالي

### نظام الصلاحيات
- صلاحيات مرنة على مستوى الوحدات
- تحكم في القراءة والكتابة والحذف
- تسجيل جميع العمليات للمراجعة

## التسجيل والمراقبة

### ملفات السجل
- `ship-erp.log`: السجل العام
- `ship-erp-error.log`: سجل الأخطاء
- `ship-erp-audit.log`: سجل التدقيق

### مستويات التسجيل
- **DEBUG**: للتطوير
- **INFO**: المعلومات العامة
- **WARN**: التحذيرات
- **ERROR**: الأخطاء

## النسخ الاحتياطي

### النسخ التلقائي
- نسخ احتياطي تلقائي كل 24 ساعة
- الاحتفاظ بالنسخ لمدة 30 يوم
- ضغط الملفات لتوفير المساحة

## التطوير والمساهمة

### معايير الكود
- استخدام Java 17 features
- اتباع Google Java Style Guide
- توثيق شامل للكود
- اختبارات وحدة شاملة

### إضافة ميزات جديدة
1. إنشاء branch جديد
2. تطوير الميزة مع الاختبارات
3. إنشاء Pull Request
4. مراجعة الكود

## الدعم والمساعدة

### الوثائق
- دليل المستخدم: `docs/user-guide.md`
- دليل المطور: `docs/developer-guide.md`
- API Documentation: `docs/api/`

### الإبلاغ عن المشاكل
- استخدام GitHub Issues
- تقديم وصف مفصل للمشكلة
- إرفاق ملفات السجل إذا لزم الأمر

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الإصدارات

### الإصدار 1.0.0 (الحالي)
- نظام الإعدادات العامة
- إدارة المستخدمين والصلاحيات
- دعم كامل للعربية و RTL
- قاعدة بيانات Oracle

### الإصدارات القادمة
- نظام إدارة الأصناف
- نظام إدارة الموردين
- نظام متابعة الشحنات
- التقارير المتقدمة
