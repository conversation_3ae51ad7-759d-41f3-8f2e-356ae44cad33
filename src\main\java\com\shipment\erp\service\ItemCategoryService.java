package com.shipment.erp.service;

import com.shipment.erp.model.ItemCategory;
import java.util.List;
import java.util.Optional;

/**
 * خدمة مجموعات الأصناف
 * Item Category Service Interface
 */
public interface ItemCategoryService extends BaseService<ItemCategory, Long> {
    
    /**
     * البحث عن مجموعة بالكود
     * Find category by code
     */
    Optional<ItemCategory> findByCode(String code);
    
    /**
     * البحث عن مجموعة بالاسم العربي
     * Find category by Arabic name
     */
    Optional<ItemCategory> findByNameAr(String nameAr);
    
    /**
     * الحصول على المجموعات الجذرية
     * Get root categories
     */
    List<ItemCategory> findRootCategories();
    
    /**
     * الحصول على المجموعات الفرعية
     * Get subcategories
     */
    List<ItemCategory> findSubCategories(ItemCategory parentCategory);
    
    /**
     * الحصول على جميع المجموعات النشطة
     * Get all active categories
     */
    List<ItemCategory> findActiveCategories();
    
    /**
     * البحث في المجموعات
     * Search categories
     */
    List<ItemCategory> searchCategories(String searchText);
    
    /**
     * إنشاء مجموعة جديدة
     * Create new category
     */
    ItemCategory createCategory(ItemCategory category) throws Exception;
    
    /**
     * تحديث مجموعة
     * Update category
     */
    ItemCategory updateCategory(ItemCategory category) throws Exception;
    
    /**
     * حذف مجموعة
     * Delete category
     */
    void deleteCategory(Long categoryId) throws Exception;
    
    /**
     * تفعيل/إلغاء تفعيل مجموعة
     * Activate/Deactivate category
     */
    void toggleCategoryStatus(Long categoryId) throws Exception;
    
    /**
     * نقل مجموعة إلى مجموعة أب جديدة
     * Move category to new parent
     */
    void moveCategory(Long categoryId, Long newParentId) throws Exception;
    
    /**
     * التحقق من صحة بيانات المجموعة
     * Validate category data
     */
    void validateCategory(ItemCategory category) throws Exception;
    
    /**
     * التحقق من إمكانية حذف المجموعة
     * Check if category can be deleted
     */
    boolean canDeleteCategory(Long categoryId);
    
    /**
     * الحصول على شجرة المجموعات
     * Get category tree
     */
    List<CategoryTreeNode> getCategoryTree();
    
    /**
     * الحصول على مسار المجموعة
     * Get category path
     */
    String getCategoryPath(Long categoryId);
    
    /**
     * الحصول على إحصائيات المجموعات
     * Get category statistics
     */
    CategoryStatistics getCategoryStatistics();
    
    /**
     * فئة عقدة شجرة المجموعات
     * Category tree node class
     */
    class CategoryTreeNode {
        private ItemCategory category;
        private List<CategoryTreeNode> children;
        private int itemCount;
        
        public CategoryTreeNode() {}
        
        public CategoryTreeNode(ItemCategory category) {
            this.category = category;
        }
        
        // Getters and setters
        public ItemCategory getCategory() { return category; }
        public void setCategory(ItemCategory category) { this.category = category; }
        
        public List<CategoryTreeNode> getChildren() { return children; }
        public void setChildren(List<CategoryTreeNode> children) { this.children = children; }
        
        public int getItemCount() { return itemCount; }
        public void setItemCount(int itemCount) { this.itemCount = itemCount; }
    }
    
    /**
     * فئة إحصائيات المجموعات
     * Category statistics class
     */
    class CategoryStatistics {
        private long totalCategories;
        private long activeCategories;
        private long rootCategories;
        private long leafCategories;
        private int maxLevel;
        
        public CategoryStatistics() {}
        
        public CategoryStatistics(long totalCategories, long activeCategories, 
                                long rootCategories, long leafCategories, int maxLevel) {
            this.totalCategories = totalCategories;
            this.activeCategories = activeCategories;
            this.rootCategories = rootCategories;
            this.leafCategories = leafCategories;
            this.maxLevel = maxLevel;
        }
        
        // Getters and setters
        public long getTotalCategories() { return totalCategories; }
        public void setTotalCategories(long totalCategories) { this.totalCategories = totalCategories; }
        
        public long getActiveCategories() { return activeCategories; }
        public void setActiveCategories(long activeCategories) { this.activeCategories = activeCategories; }
        
        public long getRootCategories() { return rootCategories; }
        public void setRootCategories(long rootCategories) { this.rootCategories = rootCategories; }
        
        public long getLeafCategories() { return leafCategories; }
        public void setLeafCategories(long leafCategories) { this.leafCategories = leafCategories; }
        
        public int getMaxLevel() { return maxLevel; }
        public void setMaxLevel(int maxLevel) { this.maxLevel = maxLevel; }
    }
}
