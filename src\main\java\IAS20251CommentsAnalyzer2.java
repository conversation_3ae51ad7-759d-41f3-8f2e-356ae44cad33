import java.sql.*;
import java.util.HashMap;
import java.util.Map;

/**
 * تحليل التعليقات في جداول المستخدم ias20251 وربطها بجدول comments في ship_erp
 */
public class IAS20251CommentsAnalyzer2 {
    
    private static Connection shipErpConn;
    private static Connection ias20251Conn;
    private static Map<Integer, String> commentsMap = new HashMap<>();
    
    public static void main(String[] args) {
        try {
            // الاتصال بقاعدتي البيانات
            connectToDatabases();
            
            // تحميل جدول التعليقات من ship_erp
            loadCommentsMap();
            
            // فحص التعليقات في جداول المستخدم ias20251
            analyzeIAS20251TableComments();
            
            // فحص التعليقات في الحقول
            analyzeIAS20251FieldComments();
            
            // إنشاء تقرير الاستيراد
            generateImportReport();
            
            // إغلاق الاتصالات
            closeConnections();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * الاتصال بقاعدتي البيانات
     */
    private static void connectToDatabases() throws Exception {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        
        // الاتصال بـ ship_erp
        shipErpConn = DriverManager.getConnection(
            "*************************************", 
            "ship_erp", 
            "ship_erp_password"
        );
        System.out.println("✅ تم الاتصال بـ SHIP_ERP");
        
        // الاتصال بالمستخدم ias20251
        ias20251Conn = DriverManager.getConnection(
            "*************************************", 
            "ias20251", 
            "ys123"
        );
        System.out.println("✅ تم الاتصال بالمستخدم IAS20251");
    }
    
    /**
     * تحميل جدول التعليقات من ship_erp
     */
    private static void loadCommentsMap() throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("📋 تحميل جدول التعليقات من SHIP_ERP");
        System.out.println("=".repeat(80));
        
        Statement stmt = shipErpConn.createStatement();
        ResultSet rs = stmt.executeQuery("SELECT COMMENTS_NO, COMMENTS_NAME FROM COMMENTS ORDER BY COMMENTS_NO");
        
        int count = 0;
        while (rs.next()) {
            int commentNo = rs.getInt("COMMENTS_NO");
            String commentName = rs.getString("COMMENTS_NAME");
            commentsMap.put(commentNo, commentName);
            count++;
        }
        
        rs.close();
        stmt.close();
        
        System.out.println("📊 تم تحميل " + count + " تعليق من جدول COMMENTS");
    }
    
    /**
     * فحص التعليقات في جداول المستخدم ias20251
     */
    private static void analyzeIAS20251TableComments() throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("🔍 فحص التعليقات في جداول المستخدم IAS20251");
        System.out.println("=".repeat(80));
        
        // فحص تعليقات الجداول
        Statement stmt = ias20251Conn.createStatement();
        ResultSet rs = stmt.executeQuery(
            "SELECT TABLE_NAME, COMMENTS " +
            "FROM USER_TAB_COMMENTS " +
            "WHERE COMMENTS IS NOT NULL " +
            "ORDER BY TABLE_NAME"
        );
        
        System.out.println("📋 تعليقات الجداول:");
        System.out.printf("%-30s %-20s %-50s\n", "اسم الجدول", "رقم التعليق", "معنى التعليق");
        System.out.println("-".repeat(100));
        
        while (rs.next()) {
            String tableName = rs.getString("TABLE_NAME");
            String comments = rs.getString("COMMENTS");
            
            if (comments != null && !comments.trim().isEmpty()) {
                try {
                    int commentNo = Integer.parseInt(comments.trim());
                    String commentMeaning = commentsMap.getOrDefault(commentNo, "غير موجود");
                    System.out.printf("%-30s %-20d %-50s\n", tableName, commentNo, 
                        commentMeaning.length() > 48 ? commentMeaning.substring(0, 45) + "..." : commentMeaning);
                } catch (NumberFormatException e) {
                    System.out.printf("%-30s %-20s %-50s\n", tableName, comments, "ليس رقم");
                }
            }
        }
        
        rs.close();
        stmt.close();
    }
    
    /**
     * فحص التعليقات في الحقول
     */
    private static void analyzeIAS20251FieldComments() throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("🔍 فحص التعليقات في حقول جداول IAS_ITM_MST و IAS_ITM_DTL");
        System.out.println("=".repeat(80));
        
        String[] tables = {"IAS_ITM_MST", "IAS_ITM_DTL"};
        
        for (String tableName : tables) {
            System.out.println("\n📋 جدول: " + tableName);
            System.out.printf("%-30s %-15s %-50s\n", "اسم الحقل", "رقم التعليق", "معنى التعليق");
            System.out.println("-".repeat(95));
            
            Statement stmt = ias20251Conn.createStatement();
            ResultSet rs = stmt.executeQuery(
                "SELECT COLUMN_NAME, COMMENTS " +
                "FROM USER_COL_COMMENTS " +
                "WHERE TABLE_NAME = '" + tableName + "' " +
                "AND COMMENTS IS NOT NULL " +
                "ORDER BY COLUMN_NAME"
            );
            
            int fieldCount = 0;
            while (rs.next()) {
                String columnName = rs.getString("COLUMN_NAME");
                String comments = rs.getString("COMMENTS");
                
                if (comments != null && !comments.trim().isEmpty()) {
                    try {
                        int commentNo = Integer.parseInt(comments.trim());
                        String commentMeaning = commentsMap.getOrDefault(commentNo, "غير موجود");
                        System.out.printf("%-30s %-15d %-50s\n", columnName, commentNo, 
                            commentMeaning.length() > 48 ? commentMeaning.substring(0, 45) + "..." : commentMeaning);
                        fieldCount++;
                    } catch (NumberFormatException e) {
                        System.out.printf("%-30s %-15s %-50s\n", columnName, comments, "ليس رقم");
                    }
                }
            }
            
            rs.close();
            stmt.close();
            
            if (fieldCount == 0) {
                System.out.println("❌ لا توجد تعليقات في حقول هذا الجدول");
            } else {
                System.out.println("📊 إجمالي الحقول التي لها تعليقات: " + fieldCount);
            }
        }
    }
    
    /**
     * إنشاء تقرير الاستيراد
     */
    private static void generateImportReport() throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("📊 تقرير الاستيراد - ربط التعليقات");
        System.out.println("=".repeat(80));
        
        // فحص الحقول المهمة في IAS_ITM_MST
        String[] importantFields = {
            "I_CODE", "I_NAME", "I_E_NAME", "I_DESC", "G_CODE", 
            "PRIMARY_COST", "INACTIVE", "SERVICE_ITM", "CASH_SALE"
        };
        
        System.out.println("🎯 الحقول المهمة للاستيراد في IAS_ITM_MST:");
        System.out.printf("%-20s %-65s\n", "اسم الحقل", "التعليق");
        System.out.println("-".repeat(85));
        
        for (String fieldName : importantFields) {
            String commentInfo = getFieldComment("IAS_ITM_MST", fieldName);
            System.out.printf("%-20s %-65s\n", fieldName, commentInfo);
        }
        
        // فحص الحقول المهمة في IAS_ITM_DTL
        String[] dtlFields = {"ITM_UNT", "P_SIZE", "BARCODE", "MAIN_UNIT", "SALE_UNIT"};
        
        System.out.println("\n🎯 الحقول المهمة للاستيراد في IAS_ITM_DTL:");
        System.out.printf("%-20s %-65s\n", "اسم الحقل", "التعليق");
        System.out.println("-".repeat(85));
        
        for (String fieldName : dtlFields) {
            String commentInfo = getFieldComment("IAS_ITM_DTL", fieldName);
            System.out.printf("%-20s %-65s\n", fieldName, commentInfo);
        }
        
        // إحصائيات عامة
        System.out.println("\n📈 إحصائيات:");
        System.out.println("  📋 إجمالي التعليقات المتاحة: " + commentsMap.size());
        
        // عدد الحقول التي لها تعليقات
        int fieldsWithComments = countFieldsWithComments("IAS_ITM_MST") + countFieldsWithComments("IAS_ITM_DTL");
        System.out.println("  🔗 إجمالي الحقول التي لها تعليقات: " + fieldsWithComments);
        
        System.out.println("\n💡 توصيات للاستيراد:");
        System.out.println("  1. استخدم التعليقات لفهم معنى البيانات عند الاستيراد");
        System.out.println("  2. قم بربط أرقام التعليقات بجدول comments في ship_erp");
        System.out.println("  3. أنشئ mapping للحقول بناءً على التعليقات");
    }
    
    /**
     * الحصول على تعليق حقل محدد
     */
    private static String getFieldComment(String tableName, String fieldName) {
        try {
            Statement stmt = ias20251Conn.createStatement();
            ResultSet rs = stmt.executeQuery(
                "SELECT COMMENTS " +
                "FROM USER_COL_COMMENTS " +
                "WHERE TABLE_NAME = '" + tableName + "' " +
                "AND COLUMN_NAME = '" + fieldName + "'"
            );
            
            if (rs.next()) {
                String comments = rs.getString("COMMENTS");
                if (comments != null && !comments.trim().isEmpty()) {
                    try {
                        int commentNo = Integer.parseInt(comments.trim());
                        String commentMeaning = commentsMap.getOrDefault(commentNo, "غير موجود");
                        rs.close();
                        stmt.close();
                        return commentNo + " - " + commentMeaning;
                    } catch (NumberFormatException e) {
                        rs.close();
                        stmt.close();
                        return comments + " (ليس رقم)";
                    }
                }
            }
            
            rs.close();
            stmt.close();
            return "لا يوجد تعليق";
            
        } catch (SQLException e) {
            return "خطأ: " + e.getMessage();
        }
    }
    
    /**
     * عد الحقول التي لها تعليقات في جدول محدد
     */
    private static int countFieldsWithComments(String tableName) {
        try {
            Statement stmt = ias20251Conn.createStatement();
            ResultSet rs = stmt.executeQuery(
                "SELECT COUNT(*) " +
                "FROM USER_COL_COMMENTS " +
                "WHERE TABLE_NAME = '" + tableName + "' " +
                "AND COMMENTS IS NOT NULL"
            );
            
            rs.next();
            int count = rs.getInt(1);
            rs.close();
            stmt.close();
            return count;
            
        } catch (SQLException e) {
            return 0;
        }
    }
    
    /**
     * إغلاق الاتصالات
     */
    private static void closeConnections() throws SQLException {
        if (shipErpConn != null) shipErpConn.close();
        if (ias20251Conn != null) ias20251Conn.close();
        System.out.println("\n✅ تم إغلاق جميع الاتصالات");
    }
    
    /**
     * الحصول على معنى تعليق برقمه
     */
    public static String getCommentMeaning(int commentNo) {
        return commentsMap.getOrDefault(commentNo, "تعليق غير موجود");
    }
}
