package com.shipment.erp.util;

import java.util.regex.Pattern;
import java.util.List;
import java.util.ArrayList;

/**
 * أداة للتحقق من صحة البيانات
 */
public class ValidationUtil {

    // أنماط التحقق
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );
    
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^[+]?[0-9]{10,15}$"
    );
    
    private static final Pattern ARABIC_TEXT_PATTERN = Pattern.compile(
        "^[\\u0600-\\u06FF\\u0750-\\u077F\\u08A0-\\u08FF\\uFB50-\\uFDFF\\uFE70-\\uFEFF\\s]+$"
    );
    
    private static final Pattern ENGLISH_TEXT_PATTERN = Pattern.compile(
        "^[a-zA-Z\\s]+$"
    );
    
    private static final Pattern ALPHANUMERIC_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9]+$"
    );
    
    private static final Pattern NUMERIC_PATTERN = Pattern.compile(
        "^[0-9]+$"
    );
    
    private static final Pattern DECIMAL_PATTERN = Pattern.compile(
        "^[0-9]+(\\.[0-9]+)?$"
    );

    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public static boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email.trim()).matches();
    }

    /**
     * التحقق من صحة رقم الهاتف
     */
    public static boolean isValidPhone(String phone) {
        if (phone == null) return false;
        String cleanPhone = phone.replaceAll("[\\s()-]", "");
        return PHONE_PATTERN.matcher(cleanPhone).matches();
    }

    /**
     * التحقق من النص العربي
     */
    public static boolean isArabicText(String text) {
        return text != null && ARABIC_TEXT_PATTERN.matcher(text.trim()).matches();
    }

    /**
     * التحقق من النص الإنجليزي
     */
    public static boolean isEnglishText(String text) {
        return text != null && ENGLISH_TEXT_PATTERN.matcher(text.trim()).matches();
    }

    /**
     * التحقق من النص الأبجدي الرقمي
     */
    public static boolean isAlphanumeric(String text) {
        return text != null && ALPHANUMERIC_PATTERN.matcher(text.trim()).matches();
    }

    /**
     * التحقق من الرقم
     */
    public static boolean isNumeric(String text) {
        return text != null && NUMERIC_PATTERN.matcher(text.trim()).matches();
    }

    /**
     * التحقق من الرقم العشري
     */
    public static boolean isDecimal(String text) {
        return text != null && DECIMAL_PATTERN.matcher(text.trim()).matches();
    }

    /**
     * التحقق من عدم كون النص فارغ
     */
    public static boolean isNotEmpty(String text) {
        return text != null && !text.trim().isEmpty();
    }

    /**
     * التحقق من طول النص
     */
    public static boolean isValidLength(String text, int minLength, int maxLength) {
        if (text == null) return false;
        int length = text.trim().length();
        return length >= minLength && length <= maxLength;
    }

    /**
     * التحقق من الحد الأدنى للطول
     */
    public static boolean hasMinLength(String text, int minLength) {
        return text != null && text.trim().length() >= minLength;
    }

    /**
     * التحقق من الحد الأقصى للطول
     */
    public static boolean hasMaxLength(String text, int maxLength) {
        return text == null || text.trim().length() <= maxLength;
    }

    /**
     * التحقق من الرقم في نطاق معين
     */
    public static boolean isInRange(Number number, Number min, Number max) {
        if (number == null) return false;
        double value = number.doubleValue();
        double minValue = min != null ? min.doubleValue() : Double.MIN_VALUE;
        double maxValue = max != null ? max.doubleValue() : Double.MAX_VALUE;
        return value >= minValue && value <= maxValue;
    }

    /**
     * التحقق من الرقم الموجب
     */
    public static boolean isPositive(Number number) {
        return number != null && number.doubleValue() > 0;
    }

    /**
     * التحقق من الرقم غير السالب
     */
    public static boolean isNonNegative(Number number) {
        return number != null && number.doubleValue() >= 0;
    }

    /**
     * التحقق من صحة الرقم الضريبي السعودي
     */
    public static boolean isValidSaudiTaxNumber(String taxNumber) {
        if (taxNumber == null || taxNumber.length() != 15) {
            return false;
        }
        
        // يجب أن يبدأ بـ 3 وينتهي بـ 3
        if (!taxNumber.startsWith("3") || !taxNumber.endsWith("3")) {
            return false;
        }
        
        // يجب أن يكون جميع الأرقام
        return isNumeric(taxNumber);
    }

    /**
     * التحقق من صحة السجل التجاري السعودي
     */
    public static boolean isValidSaudiCommercialRegister(String crNumber) {
        if (crNumber == null) return false;
        
        // إزالة المسافات والرموز
        String cleanCR = crNumber.replaceAll("[\\s-]", "");
        
        // يجب أن يكون 10 أرقام
        return cleanCR.length() == 10 && isNumeric(cleanCR);
    }

    /**
     * التحقق من صحة رقم الهوية السعودي
     */
    public static boolean isValidSaudiNationalId(String nationalId) {
        if (nationalId == null || nationalId.length() != 10) {
            return false;
        }
        
        if (!isNumeric(nationalId)) {
            return false;
        }
        
        // خوارزمية التحقق من صحة رقم الهوية السعودي
        int sum = 0;
        for (int i = 0; i < 9; i++) {
            int digit = Character.getNumericValue(nationalId.charAt(i));
            if (i % 2 == 0) {
                digit *= 2;
                if (digit > 9) {
                    digit = digit / 10 + digit % 10;
                }
            }
            sum += digit;
        }
        
        int checkDigit = (10 - (sum % 10)) % 10;
        return checkDigit == Character.getNumericValue(nationalId.charAt(9));
    }

    /**
     * تنظيف النص من المسافات الزائدة
     */
    public static String cleanText(String text) {
        if (text == null) return null;
        return text.trim().replaceAll("\\s+", " ");
    }

    /**
     * تنظيف رقم الهاتف
     */
    public static String cleanPhoneNumber(String phone) {
        if (phone == null) return null;
        return phone.replaceAll("[\\s()-]", "");
    }

    /**
     * تنسيق رقم الهاتف السعودي
     */
    public static String formatSaudiPhoneNumber(String phone) {
        String cleanPhone = cleanPhoneNumber(phone);
        if (cleanPhone == null) return null;
        
        // إضافة رمز البلد إذا لم يكن موجوداً
        if (cleanPhone.startsWith("05")) {
            cleanPhone = "+966" + cleanPhone.substring(1);
        } else if (cleanPhone.startsWith("5") && cleanPhone.length() == 9) {
            cleanPhone = "+966" + cleanPhone;
        }
        
        return cleanPhone;
    }

    /**
     * فئة نتيجة التحقق
     */
    public static class ValidationResult {
        private boolean valid = true;
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();

        public boolean isValid() {
            return valid && errors.isEmpty();
        }

        public void addError(String error) {
            this.valid = false;
            this.errors.add(error);
        }

        public void addWarning(String warning) {
            this.warnings.add(warning);
        }

        public List<String> getErrors() {
            return new ArrayList<>(errors);
        }

        public List<String> getWarnings() {
            return new ArrayList<>(warnings);
        }

        public String getErrorsAsString() {
            return String.join("\n", errors);
        }

        public String getWarningsAsString() {
            return String.join("\n", warnings);
        }

        public boolean hasErrors() {
            return !errors.isEmpty();
        }

        public boolean hasWarnings() {
            return !warnings.isEmpty();
        }

        public void merge(ValidationResult other) {
            if (other != null) {
                this.errors.addAll(other.errors);
                this.warnings.addAll(other.warnings);
                if (!other.valid) {
                    this.valid = false;
                }
            }
        }
    }

    /**
     * منشئ التحقق المتسلسل
     */
    public static class ValidationBuilder {
        private ValidationResult result = new ValidationResult();

        public ValidationBuilder notEmpty(String value, String fieldName) {
            if (!isNotEmpty(value)) {
                result.addError(fieldName + " مطلوب");
            }
            return this;
        }

        public ValidationBuilder email(String value, String fieldName) {
            if (isNotEmpty(value) && !isValidEmail(value)) {
                result.addError(fieldName + " غير صحيح");
            }
            return this;
        }

        public ValidationBuilder phone(String value, String fieldName) {
            if (isNotEmpty(value) && !isValidPhone(value)) {
                result.addError(fieldName + " غير صحيح");
            }
            return this;
        }

        public ValidationBuilder length(String value, int min, int max, String fieldName) {
            if (isNotEmpty(value) && !isValidLength(value, min, max)) {
                result.addError(fieldName + " يجب أن يكون بين " + min + " و " + max + " حرف");
            }
            return this;
        }

        public ValidationBuilder numeric(String value, String fieldName) {
            if (isNotEmpty(value) && !isNumeric(value)) {
                result.addError(fieldName + " يجب أن يكون رقم");
            }
            return this;
        }

        public ValidationBuilder positive(Number value, String fieldName) {
            if (value != null && !isPositive(value)) {
                result.addError(fieldName + " يجب أن يكون رقم موجب");
            }
            return this;
        }

        public ValidationBuilder custom(boolean condition, String errorMessage) {
            if (!condition) {
                result.addError(errorMessage);
            }
            return this;
        }

        public ValidationResult build() {
            return result;
        }
    }

    /**
     * إنشاء منشئ التحقق
     */
    public static ValidationBuilder validate() {
        return new ValidationBuilder();
    }
}
