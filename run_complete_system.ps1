# تشغيل النظام الكامل - PowerShell Script
Write-Host "🚀 تشغيل النظام الكامل..." -ForegroundColor Green
Write-Host ""

# الانتقال إلى المجلد الصحيح
Set-Location "e:\ship_erp\java\src\main\java"

Write-Host "📋 التحقق من ملفات المكتبات..." -ForegroundColor Yellow

# التحقق من وجود الملفات
$requiredFiles = @(
    "lib\ojdbc11.jar",
    "lib\orai18n.jar", 
    "lib\h2-2.2.224.jar"
)

$allFilesExist = $true
foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "❌ ملف $file غير موجود" -ForegroundColor Red
        $allFilesExist = $false
    } else {
        Write-Host "✅ $file موجود" -ForegroundColor Green
    }
}

if (-not $allFilesExist) {
    Write-Host "❌ بعض المكتبات مفقودة" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "✅ جميع المكتبات موجودة" -ForegroundColor Green
Write-Host ""

Write-Host "🔄 تشغيل النظام الكامل..." -ForegroundColor Cyan

# تشغيل النظام
$javaCommand = 'java "-Duser.language=en" "-Duser.country=US" "-Dfile.encoding=UTF-8" -cp "lib/ojdbc11.jar;lib/orai18n.jar;lib/h2-2.2.224.jar;." CompleteSystemTest'

try {
    Invoke-Expression $javaCommand
    Write-Host ""
    Write-Host "✅ تم إنهاء النظام بنجاح" -ForegroundColor Green
} catch {
    Write-Host ""
    Write-Host "❌ خطأ في تشغيل النظام: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "اضغط Enter للخروج"
