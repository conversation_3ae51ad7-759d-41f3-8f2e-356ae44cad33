import javax.swing.*;
import java.awt.*;
import java.sql.*;

/**
 * نافذة إضافة مجموعة تحت فرعية جديدة
 * Add Sub Group Dialog
 */
public class AddSubGroupDialog extends JDialog {
    
    private Connection connection;
    private boolean saved = false;
    
    // مكونات الواجهة
    private JComboBox<String> mainGroupCombo;
    private JComboBox<String> mainSubGroupCombo;
    private JTextField subCodeField;
    private JTextField arabicNameField;
    private JTextField englishNameField;
    private JTextField imageCodeField;
    private JTextField orderField;
    private JCheckBox webSyncCheckBox;
    private JCheckBox activeCheckBox;
    
    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 13);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 13);
    
    public AddSubGroupDialog(Frame parent, Connection connection) {
        super(parent, "إضافة مجموعة تحت فرعية جديدة", true);
        this.connection = connection;
        
        initializeComponents();
        setupLayout();
        loadMainGroups();
        setDefaultValues();
        
        setSize(450, 450);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }
    
    private void initializeComponents() {
        // إنشاء الحقول
        mainGroupCombo = new JComboBox<>();
        mainSubGroupCombo = new JComboBox<>();
        subCodeField = new JTextField(15);
        arabicNameField = new JTextField(25);
        englishNameField = new JTextField(25);
        imageCodeField = new JTextField(15);
        orderField = new JTextField(10);
        
        // إنشاء صناديق الاختيار
        webSyncCheckBox = new JCheckBox("مزامنة الويب");
        activeCheckBox = new JCheckBox("نشط");
        
        // تطبيق الخط العربي
        JComponent[] components = {mainGroupCombo, mainSubGroupCombo, subCodeField, arabicNameField, 
                                  englishNameField, imageCodeField, orderField, webSyncCheckBox, activeCheckBox};
        for (JComponent component : components) {
            component.setFont(arabicFont);
            component.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        }
        
        // إضافة مستمعين
        mainGroupCombo.addActionListener(e -> loadMainSubGroups());
        mainSubGroupCombo.addActionListener(e -> generateSubCode());
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;
        
        int row = 0;
        
        // المجموعة الرئيسية
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("المجموعة الرئيسية *:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(mainGroupCombo, gbc);
        row++;
        
        // المجموعة الفرعية
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("المجموعة الفرعية *:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(mainSubGroupCombo, gbc);
        row++;
        
        // كود المجموعة تحت فرعية
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("كود المجموعة تحت فرعية *:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(subCodeField, gbc);
        row++;
        
        // الاسم العربي
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("الاسم العربي *:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(arabicNameField, gbc);
        row++;
        
        // الاسم الإنجليزي
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("الاسم الإنجليزي:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(englishNameField, gbc);
        row++;
        
        // كود الصورة
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("كود الصورة:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(imageCodeField, gbc);
        row++;
        
        // الترتيب
        gbc.gridx = 1; gbc.gridy = row;
        mainPanel.add(createLabel("الترتيب:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(orderField, gbc);
        row++;
        
        // صناديق الاختيار
        gbc.gridx = 0; gbc.gridy = row; gbc.gridwidth = 2;
        mainPanel.add(webSyncCheckBox, gbc);
        row++;
        
        gbc.gridy = row;
        mainPanel.add(activeCheckBox, gbc);
        
        add(mainPanel, BorderLayout.CENTER);
        add(createButtonPanel(), BorderLayout.SOUTH);
    }
    
    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicBoldFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }
    
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        JButton saveButton = new JButton("💾 حفظ");
        saveButton.setFont(arabicBoldFont);
        saveButton.setBackground(new Color(39, 174, 96));
        saveButton.setForeground(Color.WHITE);
        saveButton.setPreferredSize(new Dimension(100, 35));
        saveButton.addActionListener(e -> saveSubGroup());
        
        JButton cancelButton = new JButton("❌ إلغاء");
        cancelButton.setFont(arabicBoldFont);
        cancelButton.setBackground(new Color(231, 76, 60));
        cancelButton.setForeground(Color.WHITE);
        cancelButton.setPreferredSize(new Dimension(100, 35));
        cancelButton.addActionListener(e -> dispose());
        
        panel.add(saveButton);
        panel.add(cancelButton);
        
        return panel;
    }
    
    private void loadMainGroups() {
        try {
            String sql = "SELECT G_CODE, G_A_NAME FROM ERP_GROUP_DETAILS WHERE IS_ACTIVE = 1 ORDER BY G_CODE";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            mainGroupCombo.removeAllItems();
            mainGroupCombo.addItem("-- اختر المجموعة الرئيسية --");
            
            while (rs.next()) {
                String code = rs.getString("G_CODE");
                String name = rs.getString("G_A_NAME");
                mainGroupCombo.addItem(code + " - " + name);
            }
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات الرئيسية: " + e.getMessage());
        }
    }
    
    private void loadMainSubGroups() {
        mainSubGroupCombo.removeAllItems();
        mainSubGroupCombo.addItem("-- اختر المجموعة الفرعية --");
        
        if (mainGroupCombo.getSelectedIndex() <= 0) {
            return;
        }
        
        try {
            String selectedItem = (String) mainGroupCombo.getSelectedItem();
            String mainGroupCode = selectedItem.split(" - ")[0];
            
            String sql = "SELECT MNG_CODE, MNG_A_NAME FROM ERP_MAINSUB_GRP_DTL WHERE G_CODE = ? AND IS_ACTIVE = 1 ORDER BY MNG_CODE";
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, mainGroupCode);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                String code = rs.getString("MNG_CODE");
                String name = rs.getString("MNG_A_NAME");
                mainSubGroupCombo.addItem(code + " - " + name);
            }
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات الفرعية: " + e.getMessage());
        }
    }
    
    private void generateSubCode() {
        if (mainSubGroupCombo.getSelectedIndex() <= 0) {
            subCodeField.setText("");
            return;
        }
        
        try {
            String selectedItem = (String) mainSubGroupCombo.getSelectedItem();
            String mainSubGroupCode = selectedItem.split(" - ")[0];
            
            String sql = """
                SELECT ? || LPAD(NVL(MAX(TO_NUMBER(SUBSTR(SUBG_CODE, LENGTH(?) + 1))), 0) + 1, 2, '0') 
                FROM ERP_SUB_GRP_DTL 
                WHERE MNG_CODE = ? AND SUBG_CODE LIKE ? || '%'
            """;
            
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, mainSubGroupCode);
            pstmt.setString(2, mainSubGroupCode);
            pstmt.setString(3, mainSubGroupCode);
            pstmt.setString(4, mainSubGroupCode);
            
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                String nextCode = rs.getString(1);
                subCodeField.setText(nextCode);
            } else {
                subCodeField.setText(mainSubGroupCode + "01");
            }
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في توليد كود المجموعة تحت فرعية: " + e.getMessage());
        }
    }
    
    private void setDefaultValues() {
        orderField.setText("1");
        activeCheckBox.setSelected(true);
        webSyncCheckBox.setSelected(false);
    }
    
    private void saveSubGroup() {
        if (!validateInput()) {
            return;
        }
        
        try {
            String mainGroupItem = (String) mainGroupCombo.getSelectedItem();
            String mainGroupCode = mainGroupItem.split(" - ")[0];
            
            String mainSubGroupItem = (String) mainSubGroupCombo.getSelectedItem();
            String mainSubGroupCode = mainSubGroupItem.split(" - ")[0];
            
            String sql = """
                INSERT INTO ERP_SUB_GRP_DTL 
                (G_CODE, MNG_CODE, SUBG_CODE, SUBG_A_NAME, SUBG_E_NAME, SUBG_I_CODE, 
                 SYNCHRNZ_TO_WEB_FLG, SUBG_ORDR, IS_ACTIVE, CREATED_BY, CREATED_DATE) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'USER', SYSDATE)
            """;
            
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, mainGroupCode);
            pstmt.setString(2, mainSubGroupCode);
            pstmt.setString(3, subCodeField.getText().trim());
            pstmt.setString(4, arabicNameField.getText().trim());
            pstmt.setString(5, englishNameField.getText().trim());
            pstmt.setString(6, imageCodeField.getText().trim());
            pstmt.setInt(7, webSyncCheckBox.isSelected() ? 1 : 0);
            
            String orderText = orderField.getText().trim();
            if (orderText.isEmpty()) {
                pstmt.setNull(8, Types.INTEGER);
            } else {
                pstmt.setInt(8, Integer.parseInt(orderText));
            }
            
            pstmt.setInt(9, activeCheckBox.isSelected() ? 1 : 0);
            
            pstmt.executeUpdate();
            connection.commit();
            
            saved = true;
            JOptionPane.showMessageDialog(this, 
                "تم حفظ المجموعة تحت فرعية بنجاح!", 
                "نجح الحفظ", JOptionPane.INFORMATION_MESSAGE);
            
            dispose();
            
        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
            
            JOptionPane.showMessageDialog(this, 
                "خطأ في حفظ المجموعة تحت فرعية:\n" + e.getMessage(), 
                "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private boolean validateInput() {
        if (mainGroupCombo.getSelectedIndex() <= 0) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار المجموعة الرئيسية", "خطأ", JOptionPane.ERROR_MESSAGE);
            return false;
        }
        
        if (mainSubGroupCombo.getSelectedIndex() <= 0) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار المجموعة الفرعية", "خطأ", JOptionPane.ERROR_MESSAGE);
            return false;
        }
        
        if (subCodeField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "كود المجموعة تحت فرعية مطلوب", "خطأ", JOptionPane.ERROR_MESSAGE);
            return false;
        }
        
        if (arabicNameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "الاسم العربي مطلوب", "خطأ", JOptionPane.ERROR_MESSAGE);
            return false;
        }
        
        return true;
    }
    
    public boolean isSaved() {
        return saved;
    }
}
