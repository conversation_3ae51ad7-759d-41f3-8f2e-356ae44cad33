import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

public class CreateTableDirectly {
    public static void main(String[] args) {
        try {
            // تحميل Oracle JDBC driver
            try {
                Class.forName("oracle.jdbc.OracleDriver");
                System.out.println("✅ تم تحميل Oracle JDBC driver بنجاح");
            } catch (ClassNotFoundException e) {
                System.err.println("❌ فشل في تحميل Oracle JDBC driver: " + e.getMessage());
                System.err.println("تأكد من وجود ملف ojdbc11.jar في classpath");
                throw e;
            }

            // الاتصال بقاعدة البيانات
            Connection conn = DriverManager.getConnection("*************************************",
                    "ship_erp", "ship_erp_password");

            // إيقاف auto-commit
            conn.setAutoCommit(false);

            System.out.println("✅ متصل بقاعدة البيانات SHIP_ERP");

            // حذف الجدول إذا كان موجود
            try {
                Statement stmt = conn.createStatement();
                stmt.execute("DROP TABLE ERP_MEASUREMENT CASCADE CONSTRAINTS");
                System.out.println("🗑️ تم حذف الجدول القديم");
            } catch (Exception e) {
                // الجدول غير موجود
            }

            // إنشاء الجدول
            String createSQL = """
                        CREATE TABLE ERP_MEASUREMENT (
                            MEASURE_CODE VARCHAR2(10) NOT NULL,
                            MEASURE VARCHAR2(100) NOT NULL,
                            MEASURE_F_NM VARCHAR2(100),
                            MEASURE_CODE_GB VARCHAR2(10),
                            MEASURE_TYPE NUMBER(1) DEFAULT 1,
                            MEASURE_WT_TYPE NUMBER(2),
                            MEASURE_WT_CONN NUMBER(1) DEFAULT 0,
                            DFLT_SIZE NUMBER(22,4),
                            ALLOW_UPD NUMBER(1) DEFAULT 1,
                            UNT_SALE_TYP NUMBER(2) DEFAULT 3,
                            AD_U_ID NUMBER(5),
                            AD_DATE DATE DEFAULT SYSDATE,
                            UP_U_ID NUMBER(5),
                            UP_DATE DATE,
                            UP_CNT NUMBER(10) DEFAULT 0,
                            AD_TRMNL_NM VARCHAR2(50),
                            UP_TRMNL_NM VARCHAR2(50),
                            CONSTRAINT PK_ERP_MEASUREMENT PRIMARY KEY (MEASURE_CODE)
                        )
                    """;

            Statement stmt = conn.createStatement();
            stmt.execute(createSQL);
            System.out.println("✅ تم إنشاء جدول ERP_MEASUREMENT");

            // إدراج البيانات
            String insertSQL =
                    """
                                INSERT INTO ERP_MEASUREMENT
                                (MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB, MEASURE_TYPE, ALLOW_UPD, UNT_SALE_TYP, AD_U_ID)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                            """;

            PreparedStatement pstmt = conn.prepareStatement(insertSQL);

            // البيانات التجريبية
            Object[][] data = {{"PIECE", "قطعة", "Piece", "PCE", 1, 1, 3, 1},
                    {"KG", "كيلوجرام", "Kilogram", "KGM", 2, 1, 2, 1},
                    {"GRAM", "جرام", "Gram", "GRM", 2, 1, 2, 1},
                    {"LITER", "لتر", "Liter", "LTR", 3, 1, 3, 1},
                    {"METER", "متر", "Meter", "MTR", 4, 1, 3, 1},
                    {"BOX", "صندوق", "Box", "BX", 1, 1, 1, 1},
                    {"DOZEN", "دزينة", "Dozen", "DZN", 1, 1, 1, 1}};

            for (Object[] row : data) {
                pstmt.setString(1, (String) row[0]);
                pstmt.setString(2, (String) row[1]);
                pstmt.setString(3, (String) row[2]);
                pstmt.setString(4, (String) row[3]);
                pstmt.setInt(5, (Integer) row[4]);
                pstmt.setInt(6, (Integer) row[5]);
                pstmt.setInt(7, (Integer) row[6]);
                pstmt.setInt(8, (Integer) row[7]);
                pstmt.executeUpdate();
            }

            conn.commit();
            System.out.println("✅ تم إدراج " + data.length + " وحدة قياس");

            // التحقق من البيانات
            ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM ERP_MEASUREMENT");
            if (rs.next()) {
                System.out.println("📊 عدد السجلات في الجدول: " + rs.getInt(1));
            }

            conn.close();
            System.out.println("🎉 تم إنشاء الجدول بنجاح!");

        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
