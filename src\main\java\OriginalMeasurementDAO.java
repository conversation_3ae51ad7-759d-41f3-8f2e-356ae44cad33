import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * DA<PERSON> للتعامل مع جدول MEASUREMENT الأصلي Data Access Object for Original MEASUREMENT table
 */
public class OriginalMeasurementDAO {

    private Connection connection;

    public OriginalMeasurementDAO(Connection connection) {
        this.connection = connection;
    }

    /**
     * إنشاء جدول MEASUREMENT بنفس بنية الجدول الأصلي في Oracle
     */
    public void createTable() throws SQLException {
        // حذف الجدول إذا كان موجوداً
        try {
            String dropSQL = "DROP TABLE ERP_MEASUREMENT CASCADE CONSTRAINTS";
            try (Statement stmt = connection.createStatement()) {
                stmt.execute(dropSQL);
                System.out.println("ℹ️ تم حذف الجدول الموجود مسبقاً");
            }
        } catch (SQLException e) {
            // تجاهل خطأ عدم وجود الجدول
        }

        String createTableSQL = """
                    CREATE TABLE ERP_MEASUREMENT (
                        MEASURE_CODE VARCHAR2(10) NOT NULL,
                        MEASURE VARCHAR2(100) NOT NULL,
                        MEASURE_F_NM VARCHAR2(100),
                        MEASURE_CODE_GB VARCHAR2(10),
                        MEASURE_TYPE NUMBER(1) DEFAULT 1,
                        MEASURE_WT_TYPE NUMBER(2),
                        MEASURE_WT_CONN NUMBER(1) DEFAULT 0,
                        DFLT_SIZE NUMBER(22,4),
                        ALLOW_UPD NUMBER(1) DEFAULT 1,
                        UNT_SALE_TYP NUMBER(2) DEFAULT 3,
                        AD_U_ID NUMBER(5),
                        AD_DATE DATE DEFAULT SYSDATE,
                        UP_U_ID NUMBER(5),
                        UP_DATE DATE,
                        UP_CNT NUMBER(10) DEFAULT 0,
                        AD_TRMNL_NM VARCHAR2(50),
                        UP_TRMNL_NM VARCHAR2(50),
                        CONSTRAINT PK_ERP_MEASUREMENT PRIMARY KEY (MEASURE_CODE)
                    )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.execute(createTableSQL);
            System.out.println("✅ تم إنشاء جدول ERP_MEASUREMENT بنجاح");
        }

        // إنشاء الفهارس
        createIndexes();
    }

    /**
     * إنشاء الفهارس
     */
    private void createIndexes() throws SQLException {
        String[] indexes = {"CREATE INDEX IDX_ERP_MEASURE_NAME ON ERP_MEASUREMENT(MEASURE)",
                "CREATE INDEX IDX_ERP_MEASURE_TYPE ON ERP_MEASUREMENT(MEASURE_TYPE)",
                "CREATE INDEX IDX_ERP_MEASURE_ALLOW_UPD ON ERP_MEASUREMENT(ALLOW_UPD)"};

        try (Statement stmt = connection.createStatement()) {
            for (String index : indexes) {
                try {
                    stmt.execute(index);
                } catch (SQLException e) {
                    // تجاهل خطأ الفهرس الموجود
                    if (!e.getMessage().contains("already exists")) {
                        throw e;
                    }
                }
            }
        }
    }

    /**
     * إدراج بيانات تجريبية
     */
    public void insertSampleData() throws SQLException {
        // فحص وجود البيانات
        String checkQuery = "SELECT COUNT(*) FROM ERP_MEASUREMENT";
        try (Statement stmt = connection.createStatement();
                ResultSet rs = stmt.executeQuery(checkQuery)) {

            if (rs.next() && rs.getInt(1) > 0) {
                return; // البيانات موجودة مسبقاً
            }
        }

        // إدراج البيانات التجريبية
        String insertSQL = """
                    INSERT INTO ERP_MEASUREMENT (
                        MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB,
                        MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE,
                        ALLOW_UPD, UNT_SALE_TYP, AD_U_ID
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """;

        Object[][] sampleData = {{"PIECE", "قطعة", "Piece", "PCE", 1, null, 0, null, 1, 3, 1},
                {"KG", "كيلوجرام", "Kilogram", "KGM", 2, 1, 0, 1.0, 1, 2, 1},
                {"GRAM", "جرام", "Gram", "GRM", 2, 1, 0, 0.001, 1, 2, 1},
                {"TON", "طن", "Ton", "TNE", 2, 1, 0, 1000.0, 1, 2, 1},
                {"LITER", "لتر", "Liter", "LTR", 3, null, 0, 1.0, 1, 3, 1},
                {"METER", "متر", "Meter", "MTR", 4, null, 0, 1.0, 1, 3, 1},
                {"CM", "سنتيمتر", "Centimeter", "CMT", 4, null, 0, 0.01, 1, 3, 1},
                {"SQM", "متر مربع", "Square Meter", "MTK", 5, null, 0, 1.0, 1, 3, 1},
                {"BOX", "صندوق", "Box", "BX", 1, null, 0, null, 1, 1, 1},
                {"DOZEN", "دزينة", "Dozen", "DZN", 1, null, 0, 12.0, 1, 1, 1},
                {"PACK", "عبوة", "Pack", "PK", 1, null, 0, null, 1, 1, 1},
                {"BOTTLE", "زجاجة", "Bottle", "BTL", 1, null, 0, null, 1, 1, 1},
                {"CAN", "علبة", "Can", "CA", 1, null, 0, null, 1, 1, 1},
                {"BAG", "كيس", "Bag", "BG", 1, null, 0, null, 1, 1, 1},
                {"ROLL", "لفة", "Roll", "RO", 1, null, 0, null, 1, 1, 1}};

        try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
            for (Object[] data : sampleData) {
                stmt.setString(1, (String) data[0]);
                stmt.setString(2, (String) data[1]);
                stmt.setString(3, (String) data[2]);
                stmt.setString(4, (String) data[3]);
                stmt.setObject(5, data[4]);
                stmt.setObject(6, data[5]);
                stmt.setObject(7, data[6]);
                stmt.setObject(8, data[7]);
                stmt.setObject(9, data[8]);
                stmt.setObject(10, data[9]);
                stmt.setObject(11, data[10]);

                stmt.executeUpdate();
            }
        }
    }

    /**
     * إضافة وحدة قياس جديدة
     */
    public void insert(OriginalMeasurement measurement) throws SQLException {
        String sql = """
                    INSERT INTO ERP_MEASUREMENT (
                        MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB,
                        MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE,
                        ALLOW_UPD, UNT_SALE_TYP, AD_U_ID, AD_DATE, AD_TRMNL_NM
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, measurement.getMeasureCode());
            stmt.setString(2, measurement.getMeasure());
            stmt.setString(3, measurement.getMeasureFNm());
            stmt.setString(4, measurement.getMeasureCodeGb());
            stmt.setObject(5, measurement.getMeasureType());
            stmt.setObject(6, measurement.getMeasureWtType());
            stmt.setObject(7, measurement.getMeasureWtConn());
            stmt.setObject(8, measurement.getDfltSize());
            stmt.setObject(9, measurement.getAllowUpd());
            stmt.setObject(10, measurement.getUntSaleTyp());
            stmt.setObject(11, measurement.getAdUId());
            stmt.setTimestamp(12,
                    measurement.getAdDate() != null
                            ? new Timestamp(measurement.getAdDate().getTime())
                            : null);
            stmt.setString(13, measurement.getAdTrmnlNm());

            stmt.executeUpdate();
        }
    }

    /**
     * تحديث وحدة قياس
     */
    public void update(OriginalMeasurement measurement) throws SQLException {
        String sql = """
                    UPDATE ERP_MEASUREMENT SET
                        MEASURE = ?, MEASURE_F_NM = ?, MEASURE_CODE_GB = ?,
                        MEASURE_TYPE = ?, MEASURE_WT_TYPE = ?, MEASURE_WT_CONN = ?,
                        DFLT_SIZE = ?, ALLOW_UPD = ?, UNT_SALE_TYP = ?,
                        UP_U_ID = ?, UP_DATE = ?, UP_CNT = COALESCE(UP_CNT, 0) + 1, UP_TRMNL_NM = ?
                    WHERE MEASURE_CODE = ?
                """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, measurement.getMeasure());
            stmt.setString(2, measurement.getMeasureFNm());
            stmt.setString(3, measurement.getMeasureCodeGb());
            stmt.setObject(4, measurement.getMeasureType());
            stmt.setObject(5, measurement.getMeasureWtType());
            stmt.setObject(6, measurement.getMeasureWtConn());
            stmt.setObject(7, measurement.getDfltSize());
            stmt.setObject(8, measurement.getAllowUpd());
            stmt.setObject(9, measurement.getUntSaleTyp());
            stmt.setObject(10, measurement.getUpUId());
            stmt.setTimestamp(11, new Timestamp(new Date().getTime()));
            stmt.setString(12, measurement.getUpTrmnlNm());
            stmt.setString(13, measurement.getMeasureCode());

            stmt.executeUpdate();
        }
    }

    /**
     * حذف وحدة قياس
     */
    public void delete(String measureCode) throws SQLException {
        String sql = "DELETE FROM ERP_MEASUREMENT WHERE MEASURE_CODE = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, measureCode);
            stmt.executeUpdate();
        }
    }

    /**
     * البحث عن وحدة قياس بالكود
     */
    public OriginalMeasurement findByCode(String measureCode) throws SQLException {
        String sql = "SELECT * FROM ERP_MEASUREMENT WHERE MEASURE_CODE = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, measureCode);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToMeasurement(rs);
                }
            }
        }

        return null;
    }

    /**
     * الحصول على جميع وحدات القياس
     */
    public List<OriginalMeasurement> findAll() throws SQLException {
        String sql = "SELECT * FROM ERP_MEASUREMENT ORDER BY MEASURE";

        List<OriginalMeasurement> measurements = new ArrayList<>();

        try (PreparedStatement stmt = connection.prepareStatement(sql);
                ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                measurements.add(mapResultSetToMeasurement(rs));
            }
        }

        return measurements;
    }

    /**
     * البحث في وحدات القياس
     */
    public List<OriginalMeasurement> search(String searchTerm) throws SQLException {
        String sql = """
                    SELECT * FROM ERP_MEASUREMENT
                    WHERE UPPER(MEASURE) LIKE UPPER(?)
                       OR UPPER(MEASURE_CODE) LIKE UPPER(?)
                       OR UPPER(MEASURE_F_NM) LIKE UPPER(?)
                    ORDER BY MEASURE
                """;

        List<OriginalMeasurement> measurements = new ArrayList<>();
        String searchPattern = "%" + searchTerm + "%";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, searchPattern);
            stmt.setString(2, searchPattern);
            stmt.setString(3, searchPattern);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    measurements.add(mapResultSetToMeasurement(rs));
                }
            }
        }

        return measurements;
    }

    /**
     * تحويل ResultSet إلى OriginalMeasurement
     */
    private OriginalMeasurement mapResultSetToMeasurement(ResultSet rs) throws SQLException {
        OriginalMeasurement measurement = new OriginalMeasurement();

        measurement.setMeasureCode(rs.getString("MEASURE_CODE"));
        measurement.setMeasure(rs.getString("MEASURE"));
        measurement.setMeasureFNm(rs.getString("MEASURE_F_NM"));
        measurement.setMeasureCodeGb(rs.getString("MEASURE_CODE_GB"));
        measurement.setMeasureType(rs.getObject("MEASURE_TYPE", Integer.class));
        measurement.setMeasureWtType(rs.getObject("MEASURE_WT_TYPE", Integer.class));
        measurement.setMeasureWtConn(rs.getObject("MEASURE_WT_CONN", Integer.class));
        measurement.setDfltSize(rs.getObject("DFLT_SIZE", Double.class));
        measurement.setAllowUpd(rs.getObject("ALLOW_UPD", Integer.class));
        measurement.setUntSaleTyp(rs.getObject("UNT_SALE_TYP", Integer.class));
        measurement.setAdUId(rs.getObject("AD_U_ID", Integer.class));
        measurement.setAdDate(rs.getTimestamp("AD_DATE"));
        measurement.setUpUId(rs.getObject("UP_U_ID", Integer.class));
        measurement.setUpDate(rs.getTimestamp("UP_DATE"));
        measurement.setUpCnt(rs.getObject("UP_CNT", Integer.class));
        measurement.setAdTrmnlNm(rs.getString("AD_TRMNL_NM"));
        measurement.setUpTrmnlNm(rs.getString("UP_TRMNL_NM"));

        return measurement;
    }

    /**
     * استيراد البيانات من جدول MEASUREMENT الأصلي في IAS20251
     */
    public int importFromOriginalTable() throws SQLException {
        // هذه الوظيفة تحتاج اتصال منفصل بقاعدة البيانات IAS20251
        // سيتم تطويرها لاحقاً عند الحاجة
        return 0;
    }
}
