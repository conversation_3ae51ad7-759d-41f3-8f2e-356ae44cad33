import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * مدير Packages قاعدة البيانات
 * Database Package Manager for System Integration
 */
public class DatabasePackageManager {
    
    private Connection connection;
    
    public DatabasePackageManager(Connection connection) {
        this.connection = connection;
    }
    
    /**
     * إدارة الاتصالات باستخدام Package
     */
    public static class ConnectionManager {
        private Connection conn;
        
        public ConnectionManager(Connection conn) {
            this.conn = conn;
        }
        
        // إضافة اتصال جديد
        public int addConnection(String connectionName, String connectionType, String hostName,
                int portNumber, String databaseName, String username, String password,
                String schemaName, String description) throws SQLException {
            
            String sql = "{ ? = call PKG_CONNECTION_MANAGEMENT.ADD_CONNECTION(?, ?, ?, ?, ?, ?, ?, ?, ?) }";
            CallableStatement cs = conn.prepareCall(sql);
            
            cs.registerOutParameter(1, Types.INTEGER);
            cs.setString(2, connectionName);
            cs.setString(3, connectionType);
            cs.setString(4, hostName);
            cs.setInt(5, portNumber);
            cs.setString(6, databaseName);
            cs.setString(7, username);
            cs.setString(8, password);
            cs.setString(9, schemaName);
            cs.setString(10, description);
            
            cs.execute();
            int connectionId = cs.getInt(1);
            cs.close();
            
            return connectionId;
        }
        
        // تحديث اتصال
        public void updateConnection(int connectionId, String connectionName, String connectionType,
                String hostName, int portNumber, String databaseName, String username,
                String password, String schemaName, String description) throws SQLException {
            
            String sql = "{ call PKG_CONNECTION_MANAGEMENT.UPDATE_CONNECTION(?, ?, ?, ?, ?, ?, ?, ?, ?, ?) }";
            CallableStatement cs = conn.prepareCall(sql);
            
            cs.setInt(1, connectionId);
            cs.setString(2, connectionName);
            cs.setString(3, connectionType);
            cs.setString(4, hostName);
            cs.setInt(5, portNumber);
            cs.setString(6, databaseName);
            cs.setString(7, username);
            cs.setString(8, password);
            cs.setString(9, schemaName);
            cs.setString(10, description);
            
            cs.execute();
            cs.close();
        }
        
        // حذف اتصال
        public void deleteConnection(int connectionId) throws SQLException {
            String sql = "{ call PKG_CONNECTION_MANAGEMENT.DELETE_CONNECTION(?) }";
            CallableStatement cs = conn.prepareCall(sql);
            cs.setInt(1, connectionId);
            cs.execute();
            cs.close();
        }
        
        // اختبار اتصال
        public String testConnection(int connectionId) throws SQLException {
            String sql = "{ ? = call PKG_CONNECTION_MANAGEMENT.TEST_CONNECTION(?) }";
            CallableStatement cs = conn.prepareCall(sql);
            
            cs.registerOutParameter(1, Types.VARCHAR);
            cs.setInt(2, connectionId);
            cs.execute();
            
            String result = cs.getString(1);
            cs.close();
            
            return result;
        }
        
        // الحصول على جميع الاتصالات
        public ResultSet getAllConnections() throws SQLException {
            String sql = "{ ? = call PKG_CONNECTION_MANAGEMENT.GET_ALL_CONNECTIONS() }";
            CallableStatement cs = conn.prepareCall(sql);
            cs.registerOutParameter(1, Types.REF_CURSOR);
            cs.execute();
            
            return (ResultSet) cs.getObject(1);
        }
    }
    
    /**
     * إدارة ربط الجداول باستخدام Package
     */
    public static class TableMappingManager {
        private Connection conn;
        
        public TableMappingManager(Connection conn) {
            this.conn = conn;
        }
        
        // إضافة ربط جدول جديد
        public int addTableMapping(int connectionId, String sourceTableName, String targetTableName,
                String mappingName, String mappingType, String whereCondition, String orderByClause,
                String description) throws SQLException {
            
            String sql = "{ ? = call PKG_TABLE_MAPPING.ADD_TABLE_MAPPING(?, ?, ?, ?, ?, ?, ?, ?) }";
            CallableStatement cs = conn.prepareCall(sql);
            
            cs.registerOutParameter(1, Types.INTEGER);
            cs.setInt(2, connectionId);
            cs.setString(3, sourceTableName);
            cs.setString(4, targetTableName);
            cs.setString(5, mappingName);
            cs.setString(6, mappingType);
            cs.setString(7, whereCondition);
            cs.setString(8, orderByClause);
            cs.setString(9, description);
            
            cs.execute();
            int mappingId = cs.getInt(1);
            cs.close();
            
            return mappingId;
        }
        
        // تحديث ربط جدول
        public void updateTableMapping(int mappingId, int connectionId, String sourceTableName,
                String targetTableName, String mappingName, String mappingType,
                String whereCondition, String orderByClause, String description) throws SQLException {
            
            String sql = "{ call PKG_TABLE_MAPPING.UPDATE_TABLE_MAPPING(?, ?, ?, ?, ?, ?, ?, ?, ?) }";
            CallableStatement cs = conn.prepareCall(sql);
            
            cs.setInt(1, mappingId);
            cs.setInt(2, connectionId);
            cs.setString(3, sourceTableName);
            cs.setString(4, targetTableName);
            cs.setString(5, mappingName);
            cs.setString(6, mappingType);
            cs.setString(7, whereCondition);
            cs.setString(8, orderByClause);
            cs.setString(9, description);
            
            cs.execute();
            cs.close();
        }
        
        // حذف ربط جدول
        public void deleteTableMapping(int mappingId) throws SQLException {
            String sql = "{ call PKG_TABLE_MAPPING.DELETE_TABLE_MAPPING(?) }";
            CallableStatement cs = conn.prepareCall(sql);
            cs.setInt(1, mappingId);
            cs.execute();
            cs.close();
        }
        
        // الحصول على جميع ربط الجداول
        public ResultSet getAllTableMappings() throws SQLException {
            String sql = "{ ? = call PKG_TABLE_MAPPING.GET_ALL_TABLE_MAPPINGS() }";
            CallableStatement cs = conn.prepareCall(sql);
            cs.registerOutParameter(1, Types.REF_CURSOR);
            cs.execute();
            
            return (ResultSet) cs.getObject(1);
        }
        
        // تحديث معلومات آخر استيراد
        public void updateLastImportInfo(int mappingId, int importCount) throws SQLException {
            String sql = "{ call PKG_TABLE_MAPPING.UPDATE_LAST_IMPORT_INFO(?, ?) }";
            CallableStatement cs = conn.prepareCall(sql);
            cs.setInt(1, mappingId);
            cs.setInt(2, importCount);
            cs.execute();
            cs.close();
        }
    }
    
    /**
     * إدارة تنفيذ الاستيراد باستخدام Package
     */
    public static class ImportExecutionManager {
        private Connection conn;
        
        public ImportExecutionManager(Connection conn) {
            this.conn = conn;
        }
        
        // تنفيذ استيراد
        public int executeImport(int mappingId, String importType, String executedBy) throws SQLException {
            String sql = "{ ? = call PKG_IMPORT_EXECUTION.EXECUTE_IMPORT(?, ?, ?) }";
            CallableStatement cs = conn.prepareCall(sql);
            
            cs.registerOutParameter(1, Types.INTEGER);
            cs.setInt(2, mappingId);
            cs.setString(3, importType);
            cs.setString(4, executedBy);
            
            cs.execute();
            int importId = cs.getInt(1);
            cs.close();
            
            return importId;
        }
        
        // التحقق من صحة البيانات
        public String validateImportData(int mappingId) throws SQLException {
            String sql = "{ ? = call PKG_IMPORT_EXECUTION.VALIDATE_IMPORT_DATA(?) }";
            CallableStatement cs = conn.prepareCall(sql);
            
            cs.registerOutParameter(1, Types.VARCHAR);
            cs.setInt(2, mappingId);
            cs.execute();
            
            String result = cs.getString(1);
            cs.close();
            
            return result;
        }
        
        // الحصول على إحصائيات الاستيراد
        public ResultSet getImportStatistics(int mappingId, int daysBack) throws SQLException {
            String sql = "{ ? = call PKG_IMPORT_EXECUTION.GET_IMPORT_STATISTICS(?, ?) }";
            CallableStatement cs = conn.prepareCall(sql);
            
            cs.registerOutParameter(1, Types.REF_CURSOR);
            cs.setInt(2, mappingId);
            cs.setInt(3, daysBack);
            cs.execute();
            
            return (ResultSet) cs.getObject(1);
        }
        
        // إلغاء استيراد
        public void cancelImport(int importId) throws SQLException {
            String sql = "{ call PKG_IMPORT_EXECUTION.CANCEL_IMPORT(?) }";
            CallableStatement cs = conn.prepareCall(sql);
            cs.setInt(1, importId);
            cs.execute();
            cs.close();
        }
    }
    
    /**
     * إدارة الإعدادات باستخدام Package
     */
    public static class ConfigurationManager {
        private Connection conn;
        
        public ConfigurationManager(Connection conn) {
            this.conn = conn;
        }
        
        // الحصول على قيمة إعداد
        public String getConfigValue(String configName) throws SQLException {
            String sql = "{ ? = call PKG_CONFIGURATION.GET_CONFIG_VALUE(?) }";
            CallableStatement cs = conn.prepareCall(sql);
            
            cs.registerOutParameter(1, Types.VARCHAR);
            cs.setString(2, configName);
            cs.execute();
            
            String result = cs.getString(1);
            cs.close();
            
            return result;
        }
        
        // تحديث قيمة إعداد
        public void setConfigValue(String configName, String configValue, String updatedBy) throws SQLException {
            String sql = "{ call PKG_CONFIGURATION.SET_CONFIG_VALUE(?, ?, ?) }";
            CallableStatement cs = conn.prepareCall(sql);
            
            cs.setString(1, configName);
            cs.setString(2, configValue);
            cs.setString(3, updatedBy);
            cs.execute();
            cs.close();
        }
        
        // إضافة إعداد جديد
        public int addConfig(String configName, String configType, String configValue,
                String configDescription, int isSystemConfig, String createdBy) throws SQLException {
            
            String sql = "{ ? = call PKG_CONFIGURATION.ADD_CONFIG(?, ?, ?, ?, ?, ?) }";
            CallableStatement cs = conn.prepareCall(sql);
            
            cs.registerOutParameter(1, Types.INTEGER);
            cs.setString(2, configName);
            cs.setString(3, configType);
            cs.setString(4, configValue);
            cs.setString(5, configDescription);
            cs.setInt(6, isSystemConfig);
            cs.setString(7, createdBy);
            
            cs.execute();
            int configId = cs.getInt(1);
            cs.close();
            
            return configId;
        }
        
        // الحصول على جميع الإعدادات
        public ResultSet getAllConfigs() throws SQLException {
            String sql = "{ ? = call PKG_CONFIGURATION.GET_ALL_CONFIGS() }";
            CallableStatement cs = conn.prepareCall(sql);
            cs.registerOutParameter(1, Types.REF_CURSOR);
            cs.execute();
            
            return (ResultSet) cs.getObject(1);
        }
        
        // الحصول على الإعدادات النشطة
        public ResultSet getActiveConfigs() throws SQLException {
            String sql = "{ ? = call PKG_CONFIGURATION.GET_ACTIVE_CONFIGS() }";
            CallableStatement cs = conn.prepareCall(sql);
            cs.registerOutParameter(1, Types.REF_CURSOR);
            cs.execute();
            
            return (ResultSet) cs.getObject(1);
        }
    }
    
    // إنشاء مديري الـ Packages
    public ConnectionManager getConnectionManager() {
        return new ConnectionManager(connection);
    }
    
    public TableMappingManager getTableMappingManager() {
        return new TableMappingManager(connection);
    }
    
    public ImportExecutionManager getImportExecutionManager() {
        return new ImportExecutionManager(connection);
    }
    
    public ConfigurationManager getConfigurationManager() {
        return new ConfigurationManager(connection);
    }
}
