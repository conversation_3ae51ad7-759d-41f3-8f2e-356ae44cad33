import java.sql.Connection;
import java.sql.DriverManager;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Scanner;

/**
 * مدير الاستيراد الشامل - نظام موحد لإدارة جميع عمليات الاستيراد يطبق المعايير الموحدة ويوفر واجهة
 * موحدة لجميع عمليات الاستيراد
 */
public class ImportManager {

    // قائمة المستوردين المتاحين
    private Map<String, Class<? extends BaseDataImporter>> availableImporters;

    // سجل عمليات الاستيراد
    private List<ImportOperation> importHistory;

    /**
     * فئة لحفظ معلومات عملية الاستيراد
     */
    public static class ImportOperation {
        private String importerName;
        private Date startTime;
        private Date endTime;
        private int totalRecords;
        private int importedRecords;
        private int errorRecords;
        private boolean successful;
        private String errorMessage;

        public ImportOperation(String importerName) {
            this.importerName = importerName;
            this.startTime = new Date();
        }

        public void complete(int total, int imported, int errors, String errorMsg) {
            this.endTime = new Date();
            this.totalRecords = total;
            this.importedRecords = imported;
            this.errorRecords = errors;
            this.successful = (errors == 0 && imported > 0);
            this.errorMessage = errorMsg;
        }

        // Getters
        public String getImporterName() {
            return importerName;
        }

        public Date getStartTime() {
            return startTime;
        }

        public Date getEndTime() {
            return endTime;
        }

        public int getTotalRecords() {
            return totalRecords;
        }

        public int getImportedRecords() {
            return importedRecords;
        }

        public int getErrorRecords() {
            return errorRecords;
        }

        public boolean isSuccessful() {
            return successful;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public long getDurationMs() {
            return endTime != null ? endTime.getTime() - startTime.getTime() : 0;
        }
    }

    /**
     * منشئ مدير الاستيراد
     */
    public ImportManager() {
        this.availableImporters = new HashMap<>();
        this.importHistory = new ArrayList<>();

        // تسجيل المستوردين المتاحين
        registerImporters();
    }

    /**
     * تسجيل المستوردين المتاحين
     */
    private void registerImporters() {
        // تسجيل مستورد بيانات الأصناف
        availableImporters.put("ITEMS", ItemsDataImporter.class);

        // يمكن إضافة مستوردين آخرين هنا
        // availableImporters.put("CUSTOMERS", CustomersDataImporter.class);
        // availableImporters.put("VENDORS", VendorsDataImporter.class);

        System.out.println("📋 تم تسجيل " + availableImporters.size() + " مستورد");
    }

    /**
     * عرض المستوردين المتاحين
     */
    public void listAvailableImporters() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("📋 المستوردين المتاحين");
        System.out.println("=".repeat(60));

        for (String importerName : availableImporters.keySet()) {
            System.out.println("  - " + importerName + ": "
                    + availableImporters.get(importerName).getSimpleName());
        }
    }

    /**
     * تنفيذ عملية استيراد محددة
     */
    public boolean executeImport(String importerName) {
        if (!availableImporters.containsKey(importerName)) {
            System.err.println("❌ المستورد غير موجود: " + importerName);
            return false;
        }

        ImportOperation operation = new ImportOperation(importerName);
        importHistory.add(operation);

        try {
            System.out.println("🚀 بدء تنفيذ المستورد: " + importerName);

            // إنشاء مثيل من المستورد
            Class<? extends BaseDataImporter> importerClass = availableImporters.get(importerName);
            BaseDataImporter importer = importerClass.getDeclaredConstructor().newInstance();

            // تنفيذ عملية الاستيراد
            importer.executeImport();

            // تسجيل النتائج
            operation.complete(importer.getTotalRecords(), importer.getImportedRecords(),
                    importer.getErrorRecords(), importer.getErrorMessages().isEmpty() ? null
                            : String.join("; ", importer.getErrorMessages()));

            System.out.println("✅ تم الانتهاء من المستورد: " + importerName);
            return operation.isSuccessful();

        } catch (Exception e) {
            operation.complete(0, 0, 1, e.getMessage());
            System.err.println("❌ خطأ في تنفيذ المستورد " + importerName + ": " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * تنفيذ جميع المستوردين
     */
    public void executeAllImports() {
        System.out.println("🚀 بدء تنفيذ جميع المستوردين...");

        int successful = 0;
        int failed = 0;

        for (String importerName : availableImporters.keySet()) {
            if (executeImport(importerName)) {
                successful++;
            } else {
                failed++;
            }
        }

        System.out.println("\n📊 ملخص تنفيذ جميع المستوردين:");
        System.out.println("  ✅ ناجح: " + successful);
        System.out.println("  ❌ فاشل: " + failed);
    }

    /**
     * عرض سجل عمليات الاستيراد
     */
    public void showImportHistory() {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("📈 سجل عمليات الاستيراد");
        System.out.println("=".repeat(80));

        if (importHistory.isEmpty()) {
            System.out.println("❌ لا توجد عمليات استيراد مسجلة");
            return;
        }

        System.out.printf("%-15s %-20s %-10s %-10s %-10s %-10s %-10s\n", "المستورد", "وقت البدء",
                "المدة", "المجموع", "مستورد", "أخطاء", "الحالة");
        System.out.println("-".repeat(80));

        for (ImportOperation op : importHistory) {
            String duration = op.getDurationMs() > 0 ? (op.getDurationMs() / 1000) + "ث" : "جاري";
            String status = op.isSuccessful() ? "✅" : "❌";

            System.out.printf("%-15s %-20s %-10s %-10d %-10d %-10d %-10s\n", op.getImporterName(),
                    op.getStartTime().toString().substring(11, 19), duration, op.getTotalRecords(),
                    op.getImportedRecords(), op.getErrorRecords(), status);
        }
    }

    /**
     * إنشاء تقرير شامل
     */
    public void generateComprehensiveReport() {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("📊 التقرير الشامل لعمليات الاستيراد");
        System.out.println("=".repeat(80));

        // إحصائيات عامة
        int totalOperations = importHistory.size();
        int successfulOperations = 0;
        int totalRecordsImported = 0;
        int totalErrors = 0;

        for (ImportOperation op : importHistory) {
            if (op.isSuccessful())
                successfulOperations++;
            totalRecordsImported += op.getImportedRecords();
            totalErrors += op.getErrorRecords();
        }

        System.out.println("📈 الإحصائيات العامة:");
        System.out.println("  📋 إجمالي العمليات: " + totalOperations);
        System.out.println("  ✅ العمليات الناجحة: " + successfulOperations);
        System.out.println("  ❌ العمليات الفاشلة: " + (totalOperations - successfulOperations));
        System.out.println("  📊 إجمالي السجلات المستوردة: " + totalRecordsImported);
        System.out.println("  ⚠️ إجمالي الأخطاء: " + totalErrors);

        if (totalOperations > 0) {
            double successRate = (double) successfulOperations / totalOperations * 100;
            System.out.println("  📈 نسبة النجاح: " + String.format("%.2f%%", successRate));
        }

        // تفاصيل كل مستورد
        System.out.println("\n📋 تفاصيل المستوردين:");
        for (String importerName : availableImporters.keySet()) {
            ImportOperation lastOp = getLastOperation(importerName);
            if (lastOp != null) {
                System.out.println("  " + importerName + ":");
                System.out.println("    - آخر تشغيل: " + lastOp.getStartTime());
                System.out.println("    - السجلات المستوردة: " + lastOp.getImportedRecords());
                System.out.println("    - الأخطاء: " + lastOp.getErrorRecords());
                System.out.println("    - الحالة: " + (lastOp.isSuccessful() ? "ناجح" : "فاشل"));
            } else {
                System.out.println("  " + importerName + ": لم يتم تشغيله بعد");
            }
        }

        // التوصيات
        System.out.println("\n💡 التوصيات:");
        if (totalErrors > 0) {
            System.out.println("  ⚠️ يوجد " + totalErrors + " خطأ، يُنصح بمراجعة السجلات");
        }
        if (successfulOperations == totalOperations && totalOperations > 0) {
            System.out.println("  🎉 جميع عمليات الاستيراد ناجحة!");
        }
        if (totalRecordsImported == 0) {
            System.out.println("  ❌ لم يتم استيراد أي سجلات، تحقق من الاتصالات والبيانات");
        }
    }

    /**
     * الحصول على آخر عملية لمستورد محدد
     */
    private ImportOperation getLastOperation(String importerName) {
        for (int i = importHistory.size() - 1; i >= 0; i--) {
            ImportOperation op = importHistory.get(i);
            if (op.getImporterName().equals(importerName)) {
                return op;
            }
        }
        return null;
    }

    /**
     * فحص حالة النظام
     */
    public void checkSystemStatus() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("🔍 فحص حالة نظام الاستيراد");
        System.out.println("=".repeat(60));

        // فحص مدير التعليقات
        try {
            CommentsManager.initialize();
            CommentsManager.printStatistics();
            System.out.println("✅ مدير التعليقات يعمل بشكل صحيح");
        } catch (Exception e) {
            System.out.println("❌ خطأ في مدير التعليقات: " + e.getMessage());
        }

        // فحص الاتصالات
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");

            // فحص الاتصال بـ ship_erp
            try (Connection conn = DriverManager.getConnection(
                    "*************************************", "ship_erp", "ship_erp_password")) {
                System.out.println("✅ الاتصال بـ ship_erp يعمل");
            }

            // فحص الاتصال بـ ias20251
            try (Connection conn = DriverManager
                    .getConnection("*************************************", "ias20251", "ys123")) {
                System.out.println("✅ الاتصال بـ ias20251 يعمل");
            }

        } catch (Exception e) {
            System.out.println("❌ خطأ في الاتصالات: " + e.getMessage());
        }

        // عرض المستوردين المتاحين
        listAvailableImporters();
    }

    /**
     * تشغيل مدير الاستيراد
     */
    public static void main(String[] args) {
        System.out.println("🚀 مدير الاستيراد الشامل - نظام موحد لاستيراد البيانات");

        ImportManager manager = new ImportManager();

        // فحص حالة النظام
        manager.checkSystemStatus();

        // عرض القائمة التفاعلية
        Scanner scanner = new Scanner(System.in);
        boolean running = true;

        while (running) {
            System.out.println("\n" + "=".repeat(50));
            System.out.println("📋 القائمة الرئيسية:");
            System.out.println("1. عرض المستوردين المتاحين");
            System.out.println("2. تنفيذ مستورد محدد");
            System.out.println("3. تنفيذ جميع المستوردين");
            System.out.println("4. عرض سجل العمليات");
            System.out.println("5. إنشاء تقرير شامل");
            System.out.println("6. فحص حالة النظام");
            System.out.println("0. خروج");
            System.out.print("اختر رقم العملية: ");

            try {
                int choice = scanner.nextInt();
                scanner.nextLine(); // استهلاك السطر الجديد

                switch (choice) {
                    case 1:
                        manager.listAvailableImporters();
                        break;
                    case 2:
                        System.out.print("أدخل اسم المستورد: ");
                        String importerName = scanner.nextLine().toUpperCase();
                        manager.executeImport(importerName);
                        break;
                    case 3:
                        manager.executeAllImports();
                        break;
                    case 4:
                        manager.showImportHistory();
                        break;
                    case 5:
                        manager.generateComprehensiveReport();
                        break;
                    case 6:
                        manager.checkSystemStatus();
                        break;
                    case 0:
                        running = false;
                        System.out.println("👋 شكراً لاستخدام مدير الاستيراد");
                        break;
                    default:
                        System.out.println("❌ خيار غير صحيح");
                }
            } catch (Exception e) {
                System.out.println("❌ خطأ في الإدخال: " + e.getMessage());
                scanner.nextLine(); // تنظيف المدخل
            }
        }

        scanner.close();
    }
}
