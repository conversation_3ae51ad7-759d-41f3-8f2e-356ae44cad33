# دليل تثبيت أدوات تطوير برنامج إدارة الشحنات

## المتطلبات الأساسية

### 1. تثبيت Java Development Kit (JDK)
```bash
# تحميل وتثبيت OpenJDK 17 أو أحدث
# من الموقع: https://adoptium.net/
# أو Oracle JDK من: https://www.oracle.com/java/technologies/downloads/

# بعد التثبيت، تأكد من إضافة JAVA_HOME إلى متغيرات البيئة:
# JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-17.0.x-hotspot
# PATH=%JAVA_HOME%\bin;%PATH%
```

### 2. تثبيت Apache Maven
```bash
# تحميل Maven من: https://maven.apache.org/download.cgi
# استخراج الملفات إلى: C:\apache-maven-3.9.x
# إضافة إلى PATH: C:\apache-maven-3.9.x\bin
```

### 3. تثبيت Oracle Database
```bash
# تحميل Oracle Database Express Edition (XE) من:
# https://www.oracle.com/database/technologies/xe-downloads.html
# أو استخدام Oracle Database 21c
```

### 4. تثبيت IDE (بيئة التطوير)
```bash
# خيارات مقترحة:
# 1. IntelliJ IDEA Community Edition (مجاني)
# 2. Eclipse IDE for Enterprise Java Developers
# 3. NetBeans IDE
```

## إنشاء مشروع Maven

### 1. إنشاء هيكل المشروع
```xml
<!-- pom.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.shipment.erp</groupId>
    <artifactId>ship-erp-system</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <name>Ship ERP System</name>
    <description>نظام إدارة الشحنات المتكامل</description>
    
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <javafx.version>********</javafx.version>
        <hibernate.version>6.2.7.Final</hibernate.version>
        <oracle.version>********</oracle.version>
    </properties>
    
    <dependencies>
        <!-- JavaFX للواجهة الرسومية -->
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-controls</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-fxml</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        
        <!-- Oracle Database Driver -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc11</artifactId>
            <version>${oracle.version}</version>
        </dependency>
        
        <!-- Hibernate ORM -->
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>${hibernate.version}</version>
        </dependency>
        
        <!-- Connection Pool -->
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>5.0.1</version>
        </dependency>
        
        <!-- Logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>2.0.7</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.4.8</version>
        </dependency>
        
        <!-- JSON Processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.15.2</version>
        </dependency>
        
        <!-- Testing -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.9.3</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            
            <plugin>
                <groupId>org.openjfx</groupId>
                <artifactId>javafx-maven-plugin</artifactId>
                <version>0.0.8</version>
                <configuration>
                    <mainClass>com.shipment.erp.ShipERPApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
```

## المكتبات المطلوبة لدعم العربية و RTL

### 1. خطوط عربية
```java
// تحميل خطوط عربية مثل:
// - Noto Sans Arabic
// - Amiri
// - Cairo
// - Tajawal
```

### 2. إعدادات RTL في JavaFX
```java
// في ملف CSS
.root {
    -fx-node-orientation: right-to-left;
}

.text-field {
    -fx-alignment: center-right;
}

.label {
    -fx-alignment: center-right;
}
```

## خطوات التثبيت

### 1. تثبيت Java
1. تحميل OpenJDK 17 من https://adoptium.net/
2. تشغيل ملف التثبيت
3. إضافة JAVA_HOME إلى متغيرات البيئة
4. إضافة %JAVA_HOME%\bin إلى PATH

### 2. تثبيت Maven
1. تحميل Maven من https://maven.apache.org/download.cgi
2. استخراج الملفات إلى C:\apache-maven-3.9.x
3. إضافة C:\apache-maven-3.9.x\bin إلى PATH
4. إنشاء متغير M2_HOME=C:\apache-maven-3.9.x

### 3. تثبيت Oracle Database
1. تحميل Oracle XE من الموقع الرسمي
2. تشغيل ملف التثبيت
3. إعداد كلمة مرور للمستخدم system
4. تشغيل خدمة Oracle

### 4. إعداد IDE
1. تحميل IntelliJ IDEA Community Edition
2. تثبيت البرنامج
3. إعداد JDK في IDE
4. تثبيت إضافات JavaFX إذا لزم الأمر

## التحقق من التثبيت
```bash
# التحقق من Java
java -version

# التحقق من Maven
mvn -version

# التحقق من Oracle (في SQL*Plus)
sqlplus system/password@localhost:1521/XE
```

## الخطوات التالية
بعد التثبيت، يمكنك:
1. إنشاء مشروع Maven جديد
2. إضافة التبعيات المطلوبة
3. إنشاء هيكل قاعدة البيانات
4. بدء تطوير الواجهات
