@echo off
chcp 65001 > nul
title نظام إدارة الشحنات المتقدم - Ship ERP System

echo ====================================
echo    نظام إدارة الشحنات المتقدم
echo    Advanced Shipping Management System
echo    Ship ERP - Complete System Test
echo ====================================
echo.

echo [1/3] جاري فحص ملفات النظام...
if not exist "CompleteSystemTest.java" (
    echo خطأ: ملف CompleteSystemTest.java غير موجود!
    pause
    exit /b 1
)

echo [2/3] جاري تجميع الملفات...
javac -encoding UTF-8 -cp . *.java 2>compile_errors.txt
if errorlevel 1 (
    echo خطأ في التجميع! تحقق من ملف compile_errors.txt
    type compile_errors.txt
    pause
    exit /b 1
)

echo تم التجميع بنجاح!
echo.

echo [3/3] جاري تشغيل النظام...
echo المعاملات المستخدمة:
echo   -Dfile.encoding=UTF-8
echo   -Duser.language=ar
echo   -Duser.country=SA
echo   -Dawt.useSystemAAFontSettings=lcd
echo   -Dswing.aatext=true
echo.

java -Dfile.encoding=UTF-8 -Duser.language=ar -Duser.country=SA -Dawt.useSystemAAFontSettings=lcd -Dswing.aatext=true CompleteSystemTest

if errorlevel 1 (
    echo.
    echo خطأ في تشغيل النظام!
    echo تحقق من:
    echo 1. وجود Java Runtime Environment
    echo 2. صحة ملفات النظام
    echo 3. الأذونات المطلوبة
) else (
    echo.
    echo تم تشغيل النظام بنجاح!
)

echo.
echo اضغط أي مفتاح للخروج...
pause > nul
