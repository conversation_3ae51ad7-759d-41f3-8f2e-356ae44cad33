import java.io.File;
import java.net.URL;
import java.net.URLClassLoader;

/**
 * أداة تشخيص شاملة لمشكلة Oracle JDBC
 * Comprehensive Oracle JDBC Problem Diagnostic Tool
 */
public class OracleProblemDiagnostic {
    
    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("   Oracle JDBC Problem Diagnostic");
        System.out.println("====================================");
        System.out.println();
        
        OracleProblemDiagnostic diagnostic = new OracleProblemDiagnostic();
        diagnostic.runFullDiagnostic();
    }
    
    /**
     * تشغيل التشخيص الشامل
     */
    public void runFullDiagnostic() {
        System.out.println("🔍 بدء التشخيص الشامل...");
        System.out.println();
        
        // 1. فحص البيئة
        checkEnvironment();
        
        // 2. فحص مجلد lib
        checkLibDirectory();
        
        // 3. فحص classpath
        checkClasspath();
        
        // 4. فحص تحميل المكتبة
        checkOracleJDBCLoading();
        
        // 5. اختبار الاتصال
        testOracleConnection();
        
        // 6. تقديم الحلول
        provideSolutions();
    }
    
    /**
     * فحص البيئة
     */
    private void checkEnvironment() {
        System.out.println("1️⃣ فحص البيئة:");
        System.out.println("   Java Version: " + System.getProperty("java.version"));
        System.out.println("   Java Home: " + System.getProperty("java.home"));
        System.out.println("   Current Directory: " + System.getProperty("user.dir"));
        System.out.println("   OS: " + System.getProperty("os.name"));
        System.out.println();
    }
    
    /**
     * فحص مجلد lib
     */
    private void checkLibDirectory() {
        System.out.println("2️⃣ فحص مجلد lib:");
        
        File libDir = new File("lib");
        if (!libDir.exists()) {
            System.out.println("   ❌ مجلد lib غير موجود!");
            System.out.println("   الحل: تشغيل java LibraryDownloader");
            return;
        }
        
        System.out.println("   ✅ مجلد lib موجود");
        
        // فحص ملفات المكتبات
        String[] requiredJars = {
            "ojdbc11.jar",
            "commons-dbcp2-2.9.0.jar",
            "commons-pool2-2.11.1.jar",
            "commons-logging-1.2.jar",
            "json-20230227.jar",
            "h2-2.2.224.jar"
        };
        
        for (String jar : requiredJars) {
            File jarFile = new File("lib/" + jar);
            if (jarFile.exists()) {
                System.out.printf("   ✅ %s (%.1f MB)%n", jar, jarFile.length() / (1024.0 * 1024.0));
            } else {
                System.out.printf("   ❌ %s مفقود%n", jar);
            }
        }
        System.out.println();
    }
    
    /**
     * فحص classpath
     */
    private void checkClasspath() {
        System.out.println("3️⃣ فحص classpath:");
        
        String classpath = System.getProperty("java.class.path");
        System.out.println("   Classpath: " + classpath);
        
        if (classpath.contains("ojdbc11.jar")) {
            System.out.println("   ✅ ojdbc11.jar موجود في classpath");
        } else {
            System.out.println("   ❌ ojdbc11.jar غير موجود في classpath");
            System.out.println("   الحل: تشغيل النظام مع: java -cp \"lib/*;.\" ClassName");
        }
        System.out.println();
    }
    
    /**
     * فحص تحميل مكتبة Oracle JDBC
     */
    private void checkOracleJDBCLoading() {
        System.out.println("4️⃣ فحص تحميل Oracle JDBC:");
        
        try {
            // محاولة تحميل الفئة
            Class.forName("oracle.jdbc.driver.OracleDriver");
            System.out.println("   ✅ تم تحميل oracle.jdbc.driver.OracleDriver بنجاح");
            
            // محاولة إنشاء instance
            oracle.jdbc.driver.OracleDriver driver = new oracle.jdbc.driver.OracleDriver();
            System.out.println("   ✅ تم إنشاء instance من OracleDriver");
            System.out.println("   إصدار التعريف: " + driver.getMajorVersion() + "." + driver.getMinorVersion());
            
        } catch (ClassNotFoundException e) {
            System.out.println("   ❌ فشل في تحميل oracle.jdbc.driver.OracleDriver");
            System.out.println("   السبب: " + e.getMessage());
            
            // محاولة تحميل المكتبة يدوياً
            tryManualLoading();
            
        } catch (Exception e) {
            System.out.println("   ❌ خطأ في إنشاء OracleDriver: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * محاولة تحميل المكتبة يدوياً
     */
    private void tryManualLoading() {
        System.out.println("   🔄 محاولة تحميل المكتبة يدوياً...");
        
        try {
            File oracleJar = new File("lib/ojdbc11.jar");
            if (oracleJar.exists()) {
                URL[] urls = {oracleJar.toURI().toURL()};
                URLClassLoader classLoader = new URLClassLoader(urls);
                
                Class<?> driverClass = classLoader.loadClass("oracle.jdbc.driver.OracleDriver");
                System.out.println("   ✅ تم تحميل المكتبة يدوياً بنجاح");
                
                classLoader.close();
            } else {
                System.out.println("   ❌ ملف ojdbc11.jar غير موجود");
            }
            
        } catch (Exception e) {
            System.out.println("   ❌ فشل التحميل اليدوي: " + e.getMessage());
        }
    }
    
    /**
     * اختبار الاتصال بـ Oracle
     */
    private void testOracleConnection() {
        System.out.println("5️⃣ اختبار الاتصال بـ Oracle:");
        
        try {
            // إعدادات الاتصال الافتراضية
            String url = "*************************************";
            String username = "ysdba2";
            String password = "ys123";
            
            System.out.println("   رابط الاتصال: " + url);
            System.out.println("   المستخدم: " + username);
            
            // محاولة الاتصال
            java.sql.Connection conn = java.sql.DriverManager.getConnection(url, username, password);
            System.out.println("   ✅ نجح الاتصال!");
            
            // اختبار استعلام
            java.sql.Statement stmt = conn.createStatement();
            java.sql.ResultSet rs = stmt.executeQuery("SELECT SYSDATE FROM DUAL");
            if (rs.next()) {
                System.out.println("   ✅ الاستعلام نجح: " + rs.getTimestamp(1));
            }
            
            rs.close();
            stmt.close();
            conn.close();
            
        } catch (Exception e) {
            System.out.println("   ❌ فشل الاتصال: " + e.getMessage());
            
            if (e.getMessage().contains("ClassNotFoundException")) {
                System.out.println("   السبب: مكتبة Oracle JDBC غير محملة");
            } else if (e.getMessage().contains("Connection refused")) {
                System.out.println("   السبب: خادم Oracle غير متاح");
            } else if (e.getMessage().contains("invalid username")) {
                System.out.println("   السبب: اسم المستخدم أو كلمة المرور خاطئة");
            }
        }
        System.out.println();
    }
    
    /**
     * تقديم الحلول
     */
    private void provideSolutions() {
        System.out.println("6️⃣ الحلول المقترحة:");
        System.out.println();
        
        System.out.println("🔧 إذا كانت المكتبات مفقودة:");
        System.out.println("   java LibraryDownloader");
        System.out.println();
        
        System.out.println("🔧 إذا كانت المكتبات موجودة لكن غير محملة:");
        System.out.println("   java -cp \"lib/*;.\" CompleteSystemTest");
        System.out.println();
        
        System.out.println("🔧 إذا كان هناك مشكلة في regex (أرقام عربية):");
        System.out.println("   java -Duser.language=en -Duser.country=US -cp \"lib/*;.\" CompleteSystemTest");
        System.out.println();
        
        System.out.println("🔧 للتجميع مع المكتبات:");
        System.out.println("   javac -encoding UTF-8 -cp \"lib/*;.\" *.java");
        System.out.println();
        
        System.out.println("🔧 استخدام ملف batch:");
        System.out.println("   .\\run_fixed.bat");
        System.out.println();
        
        System.out.println("💡 ملاحظة مهمة:");
        System.out.println("   يجب إعادة تشغيل النظام بالكامل بعد تحميل المكتبات");
        System.out.println("   لأن classpath يتم تحديده عند بدء تشغيل JVM");
        System.out.println();
        
        System.out.println("====================================");
        System.out.println("   انتهى التشخيص");
        System.out.println("====================================");
    }
}
