package com.shipment.erp.security;

import com.shipment.erp.model.User;
import com.shipment.erp.model.AuditLog;

/**
 * خدمة الأمان والتحقق من الصلاحيات
 */
public interface SecurityService {

    /**
     * التحقق من صلاحية المستخدم لوحدة معينة
     */
    boolean hasPermission(User user, String moduleName, String permissionType);

    /**
     * التحقق من صلاحية القراءة
     */
    boolean canRead(User user, String moduleName);

    /**
     * التحقق من صلاحية الكتابة
     */
    boolean canWrite(User user, String moduleName);

    /**
     * التحقق من صلاحية الحذف
     */
    boolean canDelete(User user, String moduleName);

    /**
     * التحقق من صلاحية الطباعة
     */
    boolean canPrint(User user, String moduleName);

    /**
     * التحقق من صلاحية التصدير
     */
    boolean canExport(User user, String moduleName);

    /**
     * التحقق من كون المستخدم مدير
     */
    boolean isAdmin(User user);

    /**
     * التحقق من كون المستخدم مدير نظام
     */
    boolean isSystemAdmin(User user);

    /**
     * تسجيل عملية في سجل التدقيق
     */
    void logAudit(User user, AuditLog.ActionType actionType, String tableName, Long recordId, 
                  String oldValues, String newValues, String ipAddress, String userAgent);

    /**
     * تسجيل عملية تسجيل دخول ناجح
     */
    void logSuccessfulLogin(User user, String ipAddress, String userAgent);

    /**
     * تسجيل عملية تسجيل دخول فاشل
     */
    void logFailedLogin(String username, String ipAddress, String userAgent);

    /**
     * تسجيل عملية تسجيل خروج
     */
    void logLogout(User user, String ipAddress, String userAgent);

    /**
     * التحقق من قوة كلمة المرور
     */
    PasswordStrengthResult checkPasswordStrength(String password);

    /**
     * التحقق من انتهاء صلاحية كلمة المرور
     */
    boolean isPasswordExpired(User user, int maxDaysOld);

    /**
     * التحقق من الحاجة لتغيير كلمة المرور
     */
    boolean needsPasswordChange(User user);

    /**
     * تشفير البيانات الحساسة
     */
    String encryptSensitiveData(String data);

    /**
     * فك تشفير البيانات الحساسة
     */
    String decryptSensitiveData(String encryptedData);

    /**
     * إنشاء رمز مميز للجلسة
     */
    String generateSessionToken(User user);

    /**
     * التحقق من صحة رمز الجلسة
     */
    boolean validateSessionToken(String token, User user);

    /**
     * إلغاء رمز الجلسة
     */
    void invalidateSessionToken(String token);

    /**
     * الحصول على عنوان IP الحقيقي
     */
    String getRealIpAddress(String forwardedFor, String remoteAddr);

    /**
     * التحقق من محاولات تسجيل الدخول المشبوهة
     */
    boolean isSuspiciousLoginAttempt(String username, String ipAddress);

    /**
     * حظر عنوان IP مؤقتاً
     */
    void blockIpTemporarily(String ipAddress, int minutes);

    /**
     * التحقق من حظر عنوان IP
     */
    boolean isIpBlocked(String ipAddress);

    /**
     * نتيجة فحص قوة كلمة المرور
     */
    class PasswordStrengthResult {
        private final PasswordStrength strength;
        private final int score;
        private final String feedback;

        public PasswordStrengthResult(PasswordStrength strength, int score, String feedback) {
            this.strength = strength;
            this.score = score;
            this.feedback = feedback;
        }

        public PasswordStrength getStrength() { return strength; }
        public int getScore() { return score; }
        public String getFeedback() { return feedback; }
    }

    /**
     * مستويات قوة كلمة المرور
     */
    enum PasswordStrength {
        VERY_WEAK("ضعيف جداً"),
        WEAK("ضعيف"),
        MEDIUM("متوسط"),
        STRONG("قوي"),
        VERY_STRONG("قوي جداً");

        private final String description;

        PasswordStrength(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * أنواع الصلاحيات
     */
    enum PermissionType {
        READ("READ", "قراءة"),
        WRITE("WRITE", "كتابة"),
        DELETE("DELETE", "حذف"),
        PRINT("PRINT", "طباعة"),
        EXPORT("EXPORT", "تصدير");

        private final String code;
        private final String description;

        PermissionType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() { return code; }
        public String getDescription() { return description; }
    }

    /**
     * وحدات النظام
     */
    enum SystemModule {
        SETTINGS("SETTINGS", "الإعدادات"),
        USERS("USERS", "المستخدمين"),
        COMPANIES("COMPANIES", "الشركات"),
        BRANCHES("BRANCHES", "الفروع"),
        CURRENCIES("CURRENCIES", "العملات"),
        FISCAL_YEARS("FISCAL_YEARS", "السنوات المالية"),
        ITEMS("ITEMS", "الأصناف"),
        SUPPLIERS("SUPPLIERS", "الموردين"),
        SHIPMENTS("SHIPMENTS", "الشحنات"),
        CUSTOMS("CUSTOMS", "الجمارك"),
        COSTS("COSTS", "التكاليف"),
        REPORTS("REPORTS", "التقارير");

        private final String code;
        private final String description;

        SystemModule(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() { return code; }
        public String getDescription() { return description; }
    }
}
