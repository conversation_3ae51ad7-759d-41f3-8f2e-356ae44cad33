package com.shipment.erp.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * كيان الصلاحية
 * يمثل صلاحيات الأدوار في الوحدات المختلفة
 */
@Entity
@Table(name = "PERMISSIONS", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"ROLE_ID", "MODULE_NAME"})
})
@SequenceGenerator(name = "permission_seq", sequenceName = "SEQ_PERMISSION", allocationSize = 1)
public class Permission extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ROLE_ID", nullable = false)
    @NotNull(message = "الدور مطلوب")
    private Role role;

    @Column(name = "MODULE_NAME", nullable = false, length = 100)
    @NotBlank(message = "اسم الوحدة مطلوب")
    @Size(max = 100, message = "اسم الوحدة يجب أن يكون أقل من 100 حرف")
    private String moduleName;

    @Column(name = "CAN_READ", nullable = false)
    private Boolean canRead = false;

    @Column(name = "CAN_WRITE", nullable = false)
    private Boolean canWrite = false;

    @Column(name = "CAN_DELETE", nullable = false)
    private Boolean canDelete = false;

    @Column(name = "CAN_PRINT", nullable = false)
    private Boolean canPrint = false;

    @Column(name = "CAN_EXPORT", nullable = false)
    private Boolean canExport = false;

    /**
     * Constructor افتراضي
     */
    public Permission() {
        super();
    }

    /**
     * Constructor مع الدور واسم الوحدة
     */
    public Permission(Role role, String moduleName) {
        this();
        this.role = role;
        this.moduleName = moduleName;
    }

    /**
     * Constructor مع جميع الصلاحيات
     */
    public Permission(Role role, String moduleName, boolean canRead, boolean canWrite, 
                     boolean canDelete, boolean canPrint, boolean canExport) {
        this(role, moduleName);
        this.canRead = canRead;
        this.canWrite = canWrite;
        this.canDelete = canDelete;
        this.canPrint = canPrint;
        this.canExport = canExport;
    }

    // Getters and Setters

    public Role getRole() {
        return role;
    }

    public void setRole(Role role) {
        this.role = role;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public Boolean getCanRead() {
        return canRead;
    }

    public void setCanRead(Boolean canRead) {
        this.canRead = canRead;
    }

    public Boolean getCanWrite() {
        return canWrite;
    }

    public void setCanWrite(Boolean canWrite) {
        this.canWrite = canWrite;
    }

    public Boolean getCanDelete() {
        return canDelete;
    }

    public void setCanDelete(Boolean canDelete) {
        this.canDelete = canDelete;
    }

    public Boolean getCanPrint() {
        return canPrint;
    }

    public void setCanPrint(Boolean canPrint) {
        this.canPrint = canPrint;
    }

    public Boolean getCanExport() {
        return canExport;
    }

    public void setCanExport(Boolean canExport) {
        this.canExport = canExport;
    }

    /**
     * التحقق من وجود صلاحية معينة
     */
    public boolean hasPermission(String permissionType) {
        switch (permissionType.toUpperCase()) {
            case "READ":
                return canRead;
            case "WRITE":
                return canWrite;
            case "DELETE":
                return canDelete;
            case "PRINT":
                return canPrint;
            case "EXPORT":
                return canExport;
            default:
                return false;
        }
    }

    /**
     * تعيين صلاحية معينة
     */
    public void setPermission(String permissionType, boolean value) {
        switch (permissionType.toUpperCase()) {
            case "READ":
                this.canRead = value;
                break;
            case "WRITE":
                this.canWrite = value;
                break;
            case "DELETE":
                this.canDelete = value;
                break;
            case "PRINT":
                this.canPrint = value;
                break;
            case "EXPORT":
                this.canExport = value;
                break;
        }
    }

    /**
     * منح جميع الصلاحيات
     */
    public void grantAllPermissions() {
        this.canRead = true;
        this.canWrite = true;
        this.canDelete = true;
        this.canPrint = true;
        this.canExport = true;
    }

    /**
     * إلغاء جميع الصلاحيات
     */
    public void revokeAllPermissions() {
        this.canRead = false;
        this.canWrite = false;
        this.canDelete = false;
        this.canPrint = false;
        this.canExport = false;
    }

    /**
     * التحقق من وجود أي صلاحية
     */
    public boolean hasAnyPermission() {
        return canRead || canWrite || canDelete || canPrint || canExport;
    }

    @Override
    public String toString() {
        return "Permission{" +
                "id=" + getId() +
                ", moduleName='" + moduleName + '\'' +
                ", canRead=" + canRead +
                ", canWrite=" + canWrite +
                ", canDelete=" + canDelete +
                ", canPrint=" + canPrint +
                ", canExport=" + canExport +
                '}';
    }
}
