import java.sql.*;

/**
 * فحص جدول comments لمعرفة تسميات الحقول الحقيقية
 */
public class CheckCommentsTable {
    
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            System.out.println("✅ تم الاتصال بـ SHIP_ERP");
            
            // فحص بنية جدول comments
            checkCommentsTableStructure(conn);
            
            // عرض عينة من محتوى جدول comments
            showCommentsSample(conn);
            
            // البحث عن تعليقات IAS_ITM_MST
            searchTableComments(conn, "IAS_ITM_MST");
            
            // البحث عن تعليقات IAS_ITM_DTL
            searchTableComments(conn, "IAS_ITM_DTL");
            
            // البحث عن الحقول المهمة
            searchImportantFields(conn);
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * فحص بنية جدول comments
     */
    private static void checkCommentsTableStructure(Connection conn) throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("📋 بنية جدول COMMENTS");
        System.out.println("=".repeat(80));
        
        String sql = """
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE
            FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = 'COMMENTS'
            ORDER BY COLUMN_ID
        """;
        
        Statement stmt = conn.createStatement();
        ResultSet rs = stmt.executeQuery(sql);
        
        System.out.printf("%-20s %-15s %-10s %-8s\n", "COLUMN_NAME", "DATA_TYPE", "LENGTH", "NULL?");
        System.out.println("-".repeat(60));
        
        while (rs.next()) {
            String columnName = rs.getString("COLUMN_NAME");
            String dataType = rs.getString("DATA_TYPE");
            int dataLength = rs.getInt("DATA_LENGTH");
            String nullable = rs.getString("NULLABLE");
            
            System.out.printf("%-20s %-15s %-10d %-8s\n", 
                columnName, dataType, dataLength, nullable);
        }
        
        rs.close();
        stmt.close();
    }
    
    /**
     * عرض عينة من محتوى جدول comments
     */
    private static void showCommentsSample(Connection conn) throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("📄 عينة من محتوى جدول COMMENTS (أول 10 صفوف)");
        System.out.println("=".repeat(80));
        
        Statement stmt = conn.createStatement();
        ResultSet rs = stmt.executeQuery("SELECT * FROM COMMENTS WHERE ROWNUM <= 10");
        
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();
        
        // عرض أسماء الأعمدة
        for (int i = 1; i <= columnCount; i++) {
            System.out.printf("%-25s ", metaData.getColumnName(i));
        }
        System.out.println();
        System.out.println("-".repeat(columnCount * 26));
        
        // عرض البيانات
        int rowCount = 0;
        while (rs.next() && rowCount < 10) {
            for (int i = 1; i <= columnCount; i++) {
                String value = rs.getString(i);
                if (value != null && value.length() > 23) {
                    value = value.substring(0, 20) + "...";
                }
                System.out.printf("%-25s ", value != null ? value : "NULL");
            }
            System.out.println();
            rowCount++;
        }
        
        rs.close();
        stmt.close();
    }
    
    /**
     * البحث عن تعليقات جدول محدد
     */
    private static void searchTableComments(Connection conn, String tableName) throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("🔍 البحث عن تعليقات جدول: " + tableName);
        System.out.println("=".repeat(80));
        
        // جرب استعلامات مختلفة للبحث
        String[] searchQueries = {
            "SELECT * FROM COMMENTS WHERE UPPER(TABLE_NAME) = UPPER('" + tableName + "')",
            "SELECT * FROM COMMENTS WHERE UPPER(OWNER) = UPPER('" + tableName + "')",
            "SELECT * FROM COMMENTS WHERE UPPER(OBJECT_NAME) = UPPER('" + tableName + "')",
            "SELECT * FROM COMMENTS WHERE TABLE_NAME LIKE '%" + tableName + "%'",
            "SELECT * FROM COMMENTS WHERE COMMENTS LIKE '%" + tableName + "%'"
        };
        
        Statement stmt = conn.createStatement();
        boolean found = false;
        
        for (int i = 0; i < searchQueries.length; i++) {
            try {
                System.out.println("🔍 جرب الاستعلام " + (i+1) + ":");
                ResultSet rs = stmt.executeQuery(searchQueries[i]);
                
                if (rs.next()) {
                    found = true;
                    System.out.println("✅ تم العثور على نتائج:");
                    
                    ResultSetMetaData metaData = rs.getMetaData();
                    int columnCount = metaData.getColumnCount();
                    
                    // عرض النتائج
                    do {
                        System.out.println("  📝 سجل:");
                        for (int j = 1; j <= columnCount; j++) {
                            String columnName = metaData.getColumnName(j);
                            String value = rs.getString(j);
                            if (value != null && !value.trim().isEmpty()) {
                                System.out.println("    " + columnName + ": " + value);
                            }
                        }
                        System.out.println();
                    } while (rs.next());
                    
                    rs.close();
                    break; // تم العثور على نتائج، توقف
                } else {
                    System.out.println("❌ لا توجد نتائج");
                }
                rs.close();
                
            } catch (SQLException e) {
                System.out.println("⚠️ خطأ في الاستعلام: " + e.getMessage());
            }
        }
        
        if (!found) {
            System.out.println("❌ لم يتم العثور على تعليقات للجدول: " + tableName);
        }
        
        stmt.close();
    }
    
    /**
     * البحث عن الحقول المهمة
     */
    private static void searchImportantFields(Connection conn) throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("🎯 البحث عن الحقول المهمة في النافذة");
        System.out.println("=".repeat(80));
        
        String[] importantFields = {
            "I_CODE", "I_NAME", "I_E_NAME", "I_DESC", "G_CODE", 
            "PRIMARY_COST", "INIT_PRIMARY_COST", "INACTIVE", 
            "SERVICE_ITM", "CASH_SALE", "ITM_UNT", "P_SIZE", "BARCODE"
        };
        
        Statement stmt = conn.createStatement();
        
        for (String field : importantFields) {
            System.out.println("\n🔍 البحث عن: " + field);
            
            String searchQuery = "SELECT * FROM COMMENTS WHERE UPPER(COLUMN_NAME) LIKE UPPER('%" + field + "%') OR UPPER(COMMENTS) LIKE UPPER('%" + field + "%')";
            
            try {
                ResultSet rs = stmt.executeQuery(searchQuery);
                boolean found = false;
                
                while (rs.next()) {
                    if (!found) {
                        System.out.println("  ✅ تم العثور عليه:");
                        found = true;
                    }
                    
                    ResultSetMetaData metaData = rs.getMetaData();
                    int columnCount = metaData.getColumnCount();
                    
                    System.out.println("    📝 سجل:");
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        String value = rs.getString(i);
                        if (value != null && !value.trim().isEmpty()) {
                            System.out.println("      " + columnName + ": " + value);
                        }
                    }
                }
                
                if (!found) {
                    System.out.println("  ❌ لم يتم العثور عليه");
                }
                
                rs.close();
                
            } catch (SQLException e) {
                System.out.println("  ⚠️ خطأ في البحث: " + e.getMessage());
            }
        }
        
        stmt.close();
    }
}
