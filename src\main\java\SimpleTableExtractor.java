import java.sql.*;

/**
 * استخراج بسيط وآمن لبنية الجداول
 */
public class SimpleTableExtractor {
    
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            // اتصال IAS20251
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ias20251", 
                "ys123"
            );
            System.out.println("✅ تم الاتصال بـ IAS20251");
            
            // استخراج بنية IAS_ITM_MST
            extractTableStructure(conn, "IAS_ITM_MST");
            
            // استخراج بنية IAS_ITM_DTL
            extractTableStructure(conn, "IAS_ITM_DTL");
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * استخراج بنية جدول
     */
    private static void extractTableStructure(Connection conn, String tableName) throws SQLException {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("📋 جدول: " + tableName);
        System.out.println("=".repeat(60));
        
        // استعلام مباشر للحصول على بنية الجدول
        Statement stmt = conn.createStatement();
        
        // الحصول على معلومات الأعمدة من USER_TAB_COLUMNS
        String sql = """
            SELECT 
                COLUMN_ID,
                COLUMN_NAME, 
                DATA_TYPE, 
                DATA_LENGTH, 
                DATA_PRECISION, 
                DATA_SCALE, 
                NULLABLE,
                DATA_DEFAULT
            FROM ALL_TAB_COLUMNS 
            WHERE OWNER = 'IAS20251' AND TABLE_NAME = ?
            ORDER BY COLUMN_ID
        """;
        
        PreparedStatement pstmt = conn.prepareStatement(sql);
        pstmt.setString(1, tableName);
        ResultSet rs = pstmt.executeQuery();
        
        System.out.printf("%-3s %-25s %-15s %-10s %-8s\n", "#", "COLUMN_NAME", "DATA_TYPE", "SIZE", "NULL?");
        System.out.println("-".repeat(70));
        
        int count = 0;
        while (rs.next()) {
            count++;
            int columnId = rs.getInt("COLUMN_ID");
            String columnName = rs.getString("COLUMN_NAME");
            String dataType = rs.getString("DATA_TYPE");
            int dataLength = rs.getInt("DATA_LENGTH");
            int dataPrecision = rs.getInt("DATA_PRECISION");
            int dataScale = rs.getInt("DATA_SCALE");
            String nullable = rs.getString("NULLABLE");
            String dataDefault = rs.getString("DATA_DEFAULT");
            
            String sizeInfo = "";
            if (dataType.equals("VARCHAR2") || dataType.equals("CHAR")) {
                sizeInfo = "(" + dataLength + ")";
            } else if (dataType.equals("NUMBER")) {
                if (dataPrecision > 0) {
                    if (dataScale > 0) {
                        sizeInfo = "(" + dataPrecision + "," + dataScale + ")";
                    } else {
                        sizeInfo = "(" + dataPrecision + ")";
                    }
                }
            }
            
            String nullInfo = nullable.equals("Y") ? "NULL" : "NOT NULL";
            
            System.out.printf("%-3d %-25s %-15s %-10s %-8s", 
                columnId, columnName, dataType + sizeInfo, "", nullInfo);
            
            if (dataDefault != null && !dataDefault.trim().isEmpty()) {
                System.out.print(" DEFAULT " + dataDefault.trim());
            }
            System.out.println();
        }
        
        rs.close();
        pstmt.close();
        
        System.out.println("-".repeat(70));
        System.out.println("📊 إجمالي الأعمدة: " + count);
        
        // الحصول على المفاتيح الأساسية
        sql = """
            SELECT COLUMN_NAME, CONSTRAINT_NAME
            FROM ALL_CONS_COLUMNS 
            WHERE OWNER = 'IAS20251' AND TABLE_NAME = ? 
            AND CONSTRAINT_NAME IN (
                SELECT CONSTRAINT_NAME FROM ALL_CONSTRAINTS 
                WHERE OWNER = 'IAS20251' AND TABLE_NAME = ? AND CONSTRAINT_TYPE = 'P'
            )
            ORDER BY POSITION
        """;
        
        pstmt = conn.prepareStatement(sql);
        pstmt.setString(1, tableName);
        pstmt.setString(2, tableName);
        rs = pstmt.executeQuery();
        
        System.out.println("\n🔑 المفاتيح الأساسية:");
        while (rs.next()) {
            System.out.println("  - " + rs.getString("COLUMN_NAME") + " (PK: " + rs.getString("CONSTRAINT_NAME") + ")");
        }
        rs.close();
        pstmt.close();
        
        // الحصول على المفاتيح الخارجية
        sql = """
            SELECT 
                acc.COLUMN_NAME,
                ac.CONSTRAINT_NAME,
                ac.R_CONSTRAINT_NAME,
                acc2.TABLE_NAME AS REF_TABLE,
                acc2.COLUMN_NAME AS REF_COLUMN
            FROM ALL_CONSTRAINTS ac
            JOIN ALL_CONS_COLUMNS acc ON ac.CONSTRAINT_NAME = acc.CONSTRAINT_NAME AND ac.OWNER = acc.OWNER
            JOIN ALL_CONSTRAINTS ac2 ON ac.R_CONSTRAINT_NAME = ac2.CONSTRAINT_NAME AND ac.R_OWNER = ac2.OWNER
            JOIN ALL_CONS_COLUMNS acc2 ON ac2.CONSTRAINT_NAME = acc2.CONSTRAINT_NAME AND ac2.OWNER = acc2.OWNER
            WHERE ac.OWNER = 'IAS20251' AND ac.TABLE_NAME = ? AND ac.CONSTRAINT_TYPE = 'R'
        """;
        
        pstmt = conn.prepareStatement(sql);
        pstmt.setString(1, tableName);
        rs = pstmt.executeQuery();
        
        System.out.println("\n🔗 المفاتيح الخارجية:");
        while (rs.next()) {
            System.out.println("  - " + rs.getString("COLUMN_NAME") + " → " + 
                             rs.getString("REF_TABLE") + "." + rs.getString("REF_COLUMN") + 
                             " (FK: " + rs.getString("CONSTRAINT_NAME") + ")");
        }
        rs.close();
        pstmt.close();
        
        // عرض عينة من البيانات
        System.out.println("\n📄 عينة من البيانات:");
        rs = stmt.executeQuery("SELECT * FROM " + tableName + " WHERE ROWNUM <= 2");
        
        ResultSetMetaData rsmd = rs.getMetaData();
        int colCount = rsmd.getColumnCount();
        
        // عرض أسماء الأعمدة
        for (int i = 1; i <= colCount; i++) {
            System.out.printf("%-15s ", rsmd.getColumnName(i));
        }
        System.out.println();
        System.out.println("-".repeat(colCount * 16));
        
        // عرض البيانات
        while (rs.next()) {
            for (int i = 1; i <= colCount; i++) {
                String value = rs.getString(i);
                if (value != null && value.length() > 12) {
                    value = value.substring(0, 12) + "...";
                }
                System.out.printf("%-15s ", value != null ? value : "NULL");
            }
            System.out.println();
        }
        
        rs.close();
        stmt.close();
    }
}
