@echo off
title Oracle JDBC Diagnosis

echo ====================================
echo    Oracle JDBC Quick Diagnosis
echo ====================================
echo.

echo [1/4] Checking Oracle libraries...

if exist "lib\ojdbc11.jar" (
    echo [OK] ojdbc11.jar found
) else (
    echo [ERROR] ojdbc11.jar missing!
    echo Solution: java LibraryDownloader
    set MISSING_LIBS=1
)

if exist "lib\orai18n.jar" (
    echo [OK] orai18n.jar found
) else (
    echo [ERROR] orai18n.jar missing!
    echo Solution: java LibraryDownloader
    echo Note: Required to fix ORA-17056 error
    set MISSING_LIBS=1
)

echo.

echo [2/4] Testing library loading...
if defined MISSING_LIBS (
    echo [SKIP] Libraries missing
) else (
    java -cp "lib/*;." QuickLibraryTest
)

echo.

echo [3/4] Testing Oracle connection...
if defined MISSING_LIBS (
    echo [SKIP] Libraries missing
) else (
    java -Duser.language=en -Duser.country=US -cp "lib/*;." OracleConnectionFixer
)

echo.

echo [4/4] Testing IAS tables...
if defined MISSING_LIBS (
    echo [SKIP] Libraries missing
) else (
    echo Testing IAS_ITM_MST and IAS_ITM_DTL tables...
    java -Duser.language=en -Duser.country=US -cp "lib/*;." IASTablesTest
)

echo.
echo ====================================
echo    Diagnosis Summary
echo ====================================

if defined MISSING_LIBS (
    echo [ERROR] Oracle libraries missing!
    echo.
    echo SOLUTIONS:
    echo 1. Run LibraryDownloader:
    echo    java LibraryDownloader
    echo.
    echo 2. Restart system with libraries:
    echo    java -cp "lib/*;." CompleteSystemTest
    echo.
    echo 3. Use enhanced startup file:
    echo    START_ERP_WITH_ORACLE.bat
    echo.
    echo Required files:
    echo - lib/ojdbc11.jar (Oracle JDBC Driver)
    echo - lib/orai18n.jar (Arabic charset support)
    echo.
    echo Note: orai18n.jar required to fix:
    echo ORA-17056: unsupported charset AR8MSWIN1256
    echo.
    echo Must restart system completely after loading libraries
) else (
    echo [OK] All libraries found and loaded
    echo [OK] System ready for use
    echo.
    echo To run:
    echo START_ERP_WITH_ORACLE.bat
    echo.
    echo To import from IAS tables:
    echo 1. Run the system
    echo 2. Go to: Item Management - System Integration
    echo 3. Click "Import IAS" button
    echo 4. Import 4630 items from IAS_ITM_MST and IAS_ITM_DTL
)

echo.
pause
