import java.sql.*;
import java.io.*;
import java.nio.file.*;
import java.util.*;

/**
 * أداة إنشاء قاعدة البيانات الكاملة لتطبيق ERP
 * Complete Database Creator for ERP Application
 */
public class DatabaseCreator {
    
    private DatabaseConfig dbConfig;
    private Connection connection;
    
    public DatabaseCreator(DatabaseConfig config) {
        this.dbConfig = config;
    }
    
    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("   أداة إنشاء قاعدة البيانات");
        System.out.println("   Database Creator Tool");
        System.out.println("====================================");
        System.out.println();
        
        // إنشاء إعدادات الاتصال
        DatabaseConfig config = new DatabaseConfig();
        config.setHost("localhost");
        config.setPort("1521");
        config.setServiceName("orcl");
        config.setUsername("ship_erp");
        config.setPassword("ship_erp_password");
        
        DatabaseCreator creator = new DatabaseCreator(config);
        creator.createCompleteDatabase();
    }
    
    /**
     * إنشاء قاعدة البيانات الكاملة
     */
    public void createCompleteDatabase() {
        try {
            System.out.println("🔄 الاتصال بقاعدة البيانات...");
            if (!connect()) {
                System.out.println("❌ فشل في الاتصال بقاعدة البيانات!");
                return;
            }
            
            System.out.println("✅ تم الاتصال بنجاح!");
            System.out.println();
            
            // فحص الجداول الموجودة
            System.out.println("🔍 فحص الجداول الموجودة...");
            checkExistingTables();
            System.out.println();
            
            // إنشاء الجداول
            System.out.println("🏗️ إنشاء بنية قاعدة البيانات...");
            createDatabaseStructure();
            System.out.println();
            
            // إدراج البيانات الأساسية
            System.out.println("📝 إدراج البيانات الأساسية...");
            insertBasicData();
            System.out.println();
            
            // التحقق من النتائج
            System.out.println("✅ التحقق من النتائج...");
            verifyDatabaseCreation();
            
            System.out.println();
            System.out.println("🎉 تم إنشاء قاعدة البيانات بنجاح!");
            
        } catch (Exception e) {
            System.out.println("❌ خطأ في إنشاء قاعدة البيانات: " + e.getMessage());
            e.printStackTrace();
        } finally {
            disconnect();
        }
    }
    
    /**
     * الاتصال بقاعدة البيانات
     */
    private boolean connect() {
        try {
            String url = "jdbc:oracle:thin:@" + dbConfig.getHost() + ":" + 
                        dbConfig.getPort() + ":" + dbConfig.getServiceName();
            
            connection = DriverManager.getConnection(url, 
                dbConfig.getUsername(), dbConfig.getPassword());
            
            return connection != null && !connection.isClosed();
            
        } catch (SQLException e) {
            System.out.println("خطأ في الاتصال: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * قطع الاتصال
     */
    private void disconnect() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                System.out.println("🔒 تم قطع الاتصال");
            }
        } catch (SQLException e) {
            System.out.println("خطأ في قطع الاتصال: " + e.getMessage());
        }
    }
    
    /**
     * فحص الجداول الموجودة
     */
    private void checkExistingTables() {
        try {
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet tables = metaData.getTables(null, dbConfig.getUsername().toUpperCase(), 
                                                 null, new String[]{"TABLE"});
            
            List<String> existingTables = new ArrayList<>();
            while (tables.next()) {
                existingTables.add(tables.getString("TABLE_NAME"));
            }
            
            if (existingTables.isEmpty()) {
                System.out.println("📋 لا توجد جداول في المستخدم " + dbConfig.getUsername());
            } else {
                System.out.println("📋 الجداول الموجودة (" + existingTables.size() + "):");
                for (String table : existingTables) {
                    System.out.println("   - " + table);
                }
            }
            
        } catch (SQLException e) {
            System.out.println("خطأ في فحص الجداول: " + e.getMessage());
        }
    }
    
    /**
     * إنشاء بنية قاعدة البيانات
     */
    private void createDatabaseStructure() {
        String[] createStatements = {
            // جدول وحدات القياس
            """
            CREATE TABLE UNITS_OF_MEASURE (
                UNIT_ID NUMBER(10) PRIMARY KEY,
                UNIT_CODE VARCHAR2(10) UNIQUE NOT NULL,
                UNIT_NAME VARCHAR2(50) NOT NULL,
                UNIT_DESC VARCHAR2(200),
                IS_ACTIVE NUMBER(1) DEFAULT 1,
                CREATED_DATE DATE DEFAULT SYSDATE,
                CREATED_BY VARCHAR2(50)
            )
            """,
            
            // جدول فئات الأصناف
            """
            CREATE TABLE ITEM_CATEGORIES (
                CAT_ID NUMBER(10) PRIMARY KEY,
                CAT_CODE VARCHAR2(20) UNIQUE NOT NULL,
                CAT_NAME VARCHAR2(100) NOT NULL,
                CAT_DESC VARCHAR2(500),
                PARENT_CAT_ID NUMBER(10),
                IS_ACTIVE NUMBER(1) DEFAULT 1,
                CREATED_DATE DATE DEFAULT SYSDATE,
                CREATED_BY VARCHAR2(50)
            )
            """,
            
            // جدول الأصناف الرئيسي
            """
            CREATE TABLE ITEMS (
                ITM_ID NUMBER(10) PRIMARY KEY,
                ITM_CODE VARCHAR2(30) UNIQUE NOT NULL,
                ITM_NAME VARCHAR2(100) NOT NULL,
                ITM_DESC VARCHAR2(2000),
                CAT_ID NUMBER(10),
                UNIT_ID NUMBER(10),
                IS_ACTIVE NUMBER(1) DEFAULT 1,
                CREATED_DATE DATE DEFAULT SYSDATE,
                CREATED_BY VARCHAR2(50),
                LAST_MODIFIED DATE DEFAULT SYSDATE,
                MODIFIED_BY VARCHAR2(50),
                COST_PRICE NUMBER(15,3) DEFAULT 0,
                SELL_PRICE NUMBER(15,3) DEFAULT 0,
                MIN_STOCK NUMBER(15,3) DEFAULT 0,
                MAX_STOCK NUMBER(15,3) DEFAULT 999999,
                REORDER_LEVEL NUMBER(15,3) DEFAULT 10,
                CURRENT_STOCK NUMBER(15,3) DEFAULT 0,
                LOCATION_CODE VARCHAR2(20),
                SUPPLIER_ID NUMBER(10),
                IMPORT_SOURCE VARCHAR2(20),
                IMPORT_DATE DATE,
                EXTERNAL_CODE VARCHAR2(50)
            )
            """,
            
            // المتسلسلات
            "CREATE SEQUENCE SEQ_UNITS_OF_MEASURE START WITH 1 INCREMENT BY 1",
            "CREATE SEQUENCE SEQ_ITEM_CATEGORIES START WITH 1 INCREMENT BY 1",
            "CREATE SEQUENCE SEQ_ITEMS START WITH 1 INCREMENT BY 1"
        };
        
        for (String sql : createStatements) {
            executeSQL(sql, "إنشاء جدول/متسلسل");
        }
    }
    
    /**
     * إدراج البيانات الأساسية
     */
    private void insertBasicData() {
        String[] insertStatements = {
            // وحدات القياس
            "INSERT INTO UNITS_OF_MEASURE (UNIT_ID, UNIT_CODE, UNIT_NAME, UNIT_DESC, CREATED_BY) VALUES (SEQ_UNITS_OF_MEASURE.NEXTVAL, 'PCS', 'قطعة', 'وحدة العد بالقطع', 'SYSTEM')",
            "INSERT INTO UNITS_OF_MEASURE (UNIT_ID, UNIT_CODE, UNIT_NAME, UNIT_DESC, CREATED_BY) VALUES (SEQ_UNITS_OF_MEASURE.NEXTVAL, 'KG', 'كيلوجرام', 'وحدة الوزن', 'SYSTEM')",
            "INSERT INTO UNITS_OF_MEASURE (UNIT_ID, UNIT_CODE, UNIT_NAME, UNIT_DESC, CREATED_BY) VALUES (SEQ_UNITS_OF_MEASURE.NEXTVAL, 'LTR', 'لتر', 'وحدة الحجم', 'SYSTEM')",
            
            // فئات الأصناف
            "INSERT INTO ITEM_CATEGORIES (CAT_ID, CAT_CODE, CAT_NAME, CAT_DESC, CREATED_BY) VALUES (SEQ_ITEM_CATEGORIES.NEXTVAL, '001', 'مواد غذائية', 'المواد الغذائية والمشروبات', 'SYSTEM')",
            "INSERT INTO ITEM_CATEGORIES (CAT_ID, CAT_CODE, CAT_NAME, CAT_DESC, CREATED_BY) VALUES (SEQ_ITEM_CATEGORIES.NEXTVAL, '002', 'مواد تنظيف', 'مواد التنظيف والمطهرات', 'SYSTEM')",
            "INSERT INTO ITEM_CATEGORIES (CAT_ID, CAT_CODE, CAT_NAME, CAT_DESC, CREATED_BY) VALUES (SEQ_ITEM_CATEGORIES.NEXTVAL, '003', 'أدوات مكتبية', 'الأدوات المكتبية', 'SYSTEM')",
            
            // أصناف تجريبية
            "INSERT INTO ITEMS (ITM_ID, ITM_CODE, ITM_NAME, ITM_DESC, CAT_ID, UNIT_ID, COST_PRICE, SELL_PRICE, CREATED_BY) VALUES (SEQ_ITEMS.NEXTVAL, 'TEST-001', 'صنف تجريبي 1', 'صنف تجريبي للاختبار', 1, 1, 10.50, 15.75, 'SYSTEM')",
            "INSERT INTO ITEMS (ITM_ID, ITM_CODE, ITM_NAME, ITM_DESC, CAT_ID, UNIT_ID, COST_PRICE, SELL_PRICE, CREATED_BY) VALUES (SEQ_ITEMS.NEXTVAL, 'TEST-002', 'صنف تجريبي 2', 'صنف تجريبي آخر', 2, 2, 25.00, 35.00, 'SYSTEM')"
        };
        
        for (String sql : insertStatements) {
            executeSQL(sql, "إدراج بيانات");
        }
        
        // تأكيد التغييرات
        try {
            connection.commit();
            System.out.println("✅ تم حفظ جميع البيانات");
        } catch (SQLException e) {
            System.out.println("❌ خطأ في حفظ البيانات: " + e.getMessage());
        }
    }
    
    /**
     * التحقق من إنشاء قاعدة البيانات
     */
    private void verifyDatabaseCreation() {
        try {
            // فحص عدد الجداول
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM USER_TABLES");
            rs.next();
            int tableCount = rs.getInt(1);
            System.out.println("📊 عدد الجداول المنشأة: " + tableCount);
            
            // فحص عدد الأصناف
            rs = stmt.executeQuery("SELECT COUNT(*) FROM ITEMS");
            rs.next();
            int itemCount = rs.getInt(1);
            System.out.println("📦 عدد الأصناف: " + itemCount);
            
            // فحص عدد الفئات
            rs = stmt.executeQuery("SELECT COUNT(*) FROM ITEM_CATEGORIES");
            rs.next();
            int categoryCount = rs.getInt(1);
            System.out.println("📂 عدد الفئات: " + categoryCount);
            
            // فحص عدد الوحدات
            rs = stmt.executeQuery("SELECT COUNT(*) FROM UNITS_OF_MEASURE");
            rs.next();
            int unitCount = rs.getInt(1);
            System.out.println("📏 عدد الوحدات: " + unitCount);
            
            rs.close();
            stmt.close();
            
        } catch (SQLException e) {
            System.out.println("خطأ في التحقق: " + e.getMessage());
        }
    }
    
    /**
     * تنفيذ استعلام SQL
     */
    private void executeSQL(String sql, String description) {
        try {
            Statement stmt = connection.createStatement();
            stmt.execute(sql);
            stmt.close();
            System.out.println("✅ " + description + " - تم بنجاح");
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) { // Object already exists
                System.out.println("⚠️ " + description + " - موجود مسبقاً");
            } else {
                System.out.println("❌ " + description + " - خطأ: " + e.getMessage());
            }
        }
    }
}
