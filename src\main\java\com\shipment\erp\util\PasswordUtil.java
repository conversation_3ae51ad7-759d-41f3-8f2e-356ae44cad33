package com.shipment.erp.util;

import org.mindrot.jbcrypt.BCrypt;
import java.security.SecureRandom;
import java.util.regex.Pattern;

/**
 * أداة للتعامل مع كلمات المرور
 */
public class PasswordUtil {

    private static final int BCRYPT_ROUNDS = 12;
    private static final SecureRandom RANDOM = new SecureRandom();
    
    // أنماط التحقق من كلمة المرور
    private static final Pattern UPPERCASE_PATTERN = Pattern.compile(".*[A-Z].*");
    private static final Pattern LOWERCASE_PATTERN = Pattern.compile(".*[a-z].*");
    private static final Pattern DIGIT_PATTERN = Pattern.compile(".*[0-9].*");
    private static final Pattern SPECIAL_CHAR_PATTERN = Pattern.compile(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*");
    
    // أحرف لتوليد كلمات المرور
    private static final String UPPERCASE_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String LOWERCASE_CHARS = "abcdefghijklmnopqrstuvwxyz";
    private static final String DIGIT_CHARS = "0123456789";
    private static final String SPECIAL_CHARS = "!@#$%^&*()_+-=[]{}|;:,.<>?";
    private static final String ALL_CHARS = UPPERCASE_CHARS + LOWERCASE_CHARS + DIGIT_CHARS + SPECIAL_CHARS;

    /**
     * تشفير كلمة المرور باستخدام BCrypt
     */
    public static String hashPassword(String password) {
        if (password == null || password.isEmpty()) {
            throw new IllegalArgumentException("كلمة المرور لا يمكن أن تكون فارغة");
        }
        return BCrypt.hashpw(password, BCrypt.gensalt(BCRYPT_ROUNDS));
    }

    /**
     * التحقق من تطابق كلمة المرور مع الهاش
     */
    public static boolean verifyPassword(String password, String hashedPassword) {
        if (password == null || hashedPassword == null) {
            return false;
        }
        try {
            return BCrypt.checkpw(password, hashedPassword);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * توليد كلمة مرور عشوائية
     */
    public static String generateRandomPassword(int length) {
        if (length < 8) {
            throw new IllegalArgumentException("طول كلمة المرور يجب أن يكون 8 أحرف على الأقل");
        }

        StringBuilder password = new StringBuilder();
        
        // ضمان وجود حرف واحد على الأقل من كل نوع
        password.append(getRandomChar(UPPERCASE_CHARS));
        password.append(getRandomChar(LOWERCASE_CHARS));
        password.append(getRandomChar(DIGIT_CHARS));
        password.append(getRandomChar(SPECIAL_CHARS));
        
        // إكمال باقي الأحرف عشوائياً
        for (int i = 4; i < length; i++) {
            password.append(getRandomChar(ALL_CHARS));
        }
        
        // خلط الأحرف
        return shuffleString(password.toString());
    }

    /**
     * توليد كلمة مرور عشوائية بطول افتراضي (12 حرف)
     */
    public static String generateRandomPassword() {
        return generateRandomPassword(12);
    }

    /**
     * التحقق من قوة كلمة المرور
     */
    public static PasswordStrength checkPasswordStrength(String password) {
        if (password == null || password.isEmpty()) {
            return PasswordStrength.VERY_WEAK;
        }

        int score = 0;
        
        // طول كلمة المرور
        if (password.length() >= 8) score++;
        if (password.length() >= 12) score++;
        if (password.length() >= 16) score++;
        
        // تنوع الأحرف
        if (UPPERCASE_PATTERN.matcher(password).matches()) score++;
        if (LOWERCASE_PATTERN.matcher(password).matches()) score++;
        if (DIGIT_PATTERN.matcher(password).matches()) score++;
        if (SPECIAL_CHAR_PATTERN.matcher(password).matches()) score++;
        
        // تحديد القوة بناءً على النقاط
        if (score <= 2) return PasswordStrength.VERY_WEAK;
        if (score <= 3) return PasswordStrength.WEAK;
        if (score <= 5) return PasswordStrength.MEDIUM;
        if (score <= 6) return PasswordStrength.STRONG;
        return PasswordStrength.VERY_STRONG;
    }

    /**
     * التحقق من صحة كلمة المرور
     */
    public static boolean isValidPassword(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }
        
        // يجب أن تحتوي على حرف كبير وصغير ورقم
        return UPPERCASE_PATTERN.matcher(password).matches() &&
               LOWERCASE_PATTERN.matcher(password).matches() &&
               DIGIT_PATTERN.matcher(password).matches();
    }

    /**
     * التحقق من صحة كلمة المرور مع متطلبات مخصصة
     */
    public static ValidationResult validatePassword(String password, PasswordPolicy policy) {
        ValidationResult result = new ValidationResult();
        
        if (password == null || password.isEmpty()) {
            result.addError("كلمة المرور مطلوبة");
            return result;
        }
        
        if (password.length() < policy.getMinLength()) {
            result.addError("كلمة المرور يجب أن تكون " + policy.getMinLength() + " أحرف على الأقل");
        }
        
        if (password.length() > policy.getMaxLength()) {
            result.addError("كلمة المرور يجب أن تكون " + policy.getMaxLength() + " حرف على الأكثر");
        }
        
        if (policy.isRequireUppercase() && !UPPERCASE_PATTERN.matcher(password).matches()) {
            result.addError("كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل");
        }
        
        if (policy.isRequireLowercase() && !LOWERCASE_PATTERN.matcher(password).matches()) {
            result.addError("كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل");
        }
        
        if (policy.isRequireDigit() && !DIGIT_PATTERN.matcher(password).matches()) {
            result.addError("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل");
        }
        
        if (policy.isRequireSpecialChar() && !SPECIAL_CHAR_PATTERN.matcher(password).matches()) {
            result.addError("كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل");
        }
        
        return result;
    }

    /**
     * الحصول على حرف عشوائي من مجموعة أحرف
     */
    private static char getRandomChar(String chars) {
        return chars.charAt(RANDOM.nextInt(chars.length()));
    }

    /**
     * خلط أحرف النص
     */
    private static String shuffleString(String input) {
        char[] chars = input.toCharArray();
        for (int i = chars.length - 1; i > 0; i--) {
            int j = RANDOM.nextInt(i + 1);
            char temp = chars[i];
            chars[i] = chars[j];
            chars[j] = temp;
        }
        return new String(chars);
    }

    /**
     * قوة كلمة المرور
     */
    public enum PasswordStrength {
        VERY_WEAK("ضعيف جداً", 0),
        WEAK("ضعيف", 1),
        MEDIUM("متوسط", 2),
        STRONG("قوي", 3),
        VERY_STRONG("قوي جداً", 4);

        private final String description;
        private final int level;

        PasswordStrength(String description, int level) {
            this.description = description;
            this.level = level;
        }

        public String getDescription() {
            return description;
        }

        public int getLevel() {
            return level;
        }
    }

    /**
     * سياسة كلمة المرور
     */
    public static class PasswordPolicy {
        private int minLength = 8;
        private int maxLength = 128;
        private boolean requireUppercase = true;
        private boolean requireLowercase = true;
        private boolean requireDigit = true;
        private boolean requireSpecialChar = false;

        // Getters and setters
        public int getMinLength() { return minLength; }
        public void setMinLength(int minLength) { this.minLength = minLength; }

        public int getMaxLength() { return maxLength; }
        public void setMaxLength(int maxLength) { this.maxLength = maxLength; }

        public boolean isRequireUppercase() { return requireUppercase; }
        public void setRequireUppercase(boolean requireUppercase) { this.requireUppercase = requireUppercase; }

        public boolean isRequireLowercase() { return requireLowercase; }
        public void setRequireLowercase(boolean requireLowercase) { this.requireLowercase = requireLowercase; }

        public boolean isRequireDigit() { return requireDigit; }
        public void setRequireDigit(boolean requireDigit) { this.requireDigit = requireDigit; }

        public boolean isRequireSpecialChar() { return requireSpecialChar; }
        public void setRequireSpecialChar(boolean requireSpecialChar) { this.requireSpecialChar = requireSpecialChar; }
    }

    /**
     * نتيجة التحقق من صحة كلمة المرور
     */
    public static class ValidationResult {
        private boolean valid = true;
        private java.util.List<String> errors = new java.util.ArrayList<>();

        public boolean isValid() {
            return valid && errors.isEmpty();
        }

        public void addError(String error) {
            this.valid = false;
            this.errors.add(error);
        }

        public java.util.List<String> getErrors() {
            return errors;
        }

        public String getErrorsAsString() {
            return String.join("\n", errors);
        }
    }
}
