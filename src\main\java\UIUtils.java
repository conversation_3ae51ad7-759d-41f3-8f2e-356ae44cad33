import javax.swing.*;
import javax.swing.border.Border;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

/**
 * أدوات مساعدة للواجهة
 * UI Utility Tools
 */
public class UIUtils {
    
    public static final Font ARABIC_FONT = new Font("Tahoma", Font.PLAIN, 12);
    public static final Font ARABIC_FONT_BOLD = new Font("Tahoma", Font.BOLD, 12);
    public static final Font ARABIC_FONT_LARGE = new Font("Tahoma", Font.PLAIN, 14);
    public static final Font ARABIC_FONT_TITLE = new Font("Tahoma", Font.BOLD, 16);
    
    // ألوان النظام
    public static final Color PRIMARY_COLOR = new Color(52, 152, 219);
    public static final Color SUCCESS_COLOR = new Color(40, 167, 69);
    public static final Color WARNING_COLOR = new Color(255, 193, 7);
    public static final Color DANGER_COLOR = new Color(220, 53, 69);
    public static final Color INFO_COLOR = new Color(23, 162, 184);
    public static final Color SECONDARY_COLOR = new Color(108, 117, 125);
    public static final Color LIGHT_COLOR = new Color(248, 249, 250);
    public static final Color DARK_COLOR = new Color(33, 37, 41);
    
    /**
     * إنشاء زر مُنسق
     */
    public static JButton createStyledButton(String text, Color backgroundColor, Color textColor) {
        JButton button = new JButton(text);
        button.setFont(ARABIC_FONT);
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        button.setBackground(backgroundColor);
        button.setForeground(textColor);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        
        // تأثيرات التفاعل
        addHoverEffect(button, backgroundColor);
        
        return button;
    }
    
    /**
     * إنشاء زر أساسي
     */
    public static JButton createPrimaryButton(String text) {
        return createStyledButton(text, PRIMARY_COLOR, Color.WHITE);
    }
    
    /**
     * إنشاء زر نجاح
     */
    public static JButton createSuccessButton(String text) {
        return createStyledButton(text, SUCCESS_COLOR, Color.WHITE);
    }
    
    /**
     * إنشاء زر تحذير
     */
    public static JButton createWarningButton(String text) {
        return createStyledButton(text, WARNING_COLOR, Color.BLACK);
    }
    
    /**
     * إنشاء زر خطر
     */
    public static JButton createDangerButton(String text) {
        return createStyledButton(text, DANGER_COLOR, Color.WHITE);
    }
    
    /**
     * إنشاء تسمية مُنسقة
     */
    public static JLabel createStyledLabel(String text, Font font, Color color) {
        JLabel label = new JLabel(text);
        label.setFont(font);
        label.setForeground(color);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }
    
    /**
     * إنشاء تسمية عنوان
     */
    public static JLabel createTitleLabel(String text) {
        return createStyledLabel(text, ARABIC_FONT_TITLE, DARK_COLOR);
    }
    
    /**
     * إنشاء حقل نص مُنسق
     */
    public static JTextField createStyledTextField() {
        JTextField field = new JTextField();
        field.setFont(ARABIC_FONT);
        field.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        field.setBorder(createRoundedBorder());
        field.setPreferredSize(new Dimension(200, 30));
        return field;
    }
    
    /**
     * إنشاء منطقة نص مُنسقة
     */
    public static JTextArea createStyledTextArea(int rows, int cols) {
        JTextArea area = new JTextArea(rows, cols);
        area.setFont(ARABIC_FONT);
        area.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        area.setLineWrap(true);
        area.setWrapStyleWord(true);
        area.setBorder(createRoundedBorder());
        return area;
    }
    
    /**
     * إنشاء قائمة منسدلة مُنسقة
     */
    public static JComboBox<String> createStyledComboBox(String[] items) {
        JComboBox<String> comboBox = new JComboBox<>(items);
        comboBox.setFont(ARABIC_FONT);
        comboBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        ((JLabel) comboBox.getRenderer()).setHorizontalAlignment(SwingConstants.RIGHT);
        return comboBox;
    }
    
    /**
     * إنشاء لوحة مُنسقة
     */
    public static JPanel createStyledPanel(LayoutManager layout) {
        JPanel panel = new JPanel(layout);
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(Color.WHITE);
        return panel;
    }
    
    /**
     * إنشاء لوحة بطاقة
     */
    public static JPanel createCardPanel() {
        JPanel panel = createStyledPanel(new BorderLayout());
        panel.setBorder(createCardBorder());
        return panel;
    }
    
    /**
     * إنشاء حدود مستديرة
     */
    public static Border createRoundedBorder() {
        return BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(206, 212, 218), 1),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        );
    }
    
    /**
     * إنشاء حدود بطاقة
     */
    public static Border createCardBorder() {
        return BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(222, 226, 230), 1),
            BorderFactory.createEmptyBorder(15, 15, 15, 15)
        );
    }
    
    /**
     * إضافة تأثير التمرير للزر
     */
    public static void addHoverEffect(JButton button, Color originalColor) {
        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setBackground(originalColor.darker());
            }
            
            @Override
            public void mouseExited(MouseEvent e) {
                button.setBackground(originalColor);
            }
        });
    }
    
    /**
     * إنشاء فاصل مُنسق
     */
    public static JSeparator createStyledSeparator() {
        JSeparator separator = new JSeparator();
        separator.setForeground(new Color(222, 226, 230));
        return separator;
    }
    
    /**
     * إنشاء شريط تقدم مُنسق
     */
    public static JProgressBar createStyledProgressBar() {
        JProgressBar progressBar = new JProgressBar();
        progressBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        progressBar.setStringPainted(true);
        progressBar.setFont(ARABIC_FONT);
        return progressBar;
    }
    
    /**
     * إنشاء جدول مُنسق
     */
    public static JTable createStyledTable(Object[][] data, String[] columnNames) {
        JTable table = new JTable(data, columnNames);
        table.setFont(ARABIC_FONT);
        table.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        table.setRowHeight(30);
        table.setGridColor(new Color(222, 226, 230));
        table.setSelectionBackground(PRIMARY_COLOR.brighter());
        table.setSelectionForeground(Color.BLACK);
        
        // تنسيق رأس الجدول
        table.getTableHeader().setFont(ARABIC_FONT_BOLD);
        table.getTableHeader().setBackground(LIGHT_COLOR);
        table.getTableHeader().setForeground(DARK_COLOR);
        
        return table;
    }
    
    /**
     * إنشاء شريط أدوات مُنسق
     */
    public static JToolBar createStyledToolBar() {
        JToolBar toolBar = new JToolBar();
        toolBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        toolBar.setFloatable(false);
        toolBar.setBackground(LIGHT_COLOR);
        toolBar.setBorder(BorderFactory.createMatteBorder(0, 0, 1, 0, new Color(222, 226, 230)));
        return toolBar;
    }
    
    /**
     * إنشاء لوحة رأس مُنسقة
     */
    public static JPanel createHeaderPanel(String title, String subtitle) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(PRIMARY_COLOR);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        JLabel titleLabel = new JLabel(title);
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 20));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JPanel textPanel = new JPanel(new GridLayout(2, 1, 0, 5));
        textPanel.setOpaque(false);
        textPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        textPanel.add(titleLabel);
        
        if (subtitle != null && !subtitle.isEmpty()) {
            JLabel subtitleLabel = new JLabel(subtitle);
            subtitleLabel.setFont(ARABIC_FONT_LARGE);
            subtitleLabel.setForeground(new Color(236, 240, 241));
            subtitleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            textPanel.add(subtitleLabel);
        }
        
        panel.add(textPanel, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * إنشاء نافذة تحميل
     */
    public static JDialog createLoadingDialog(JFrame parent, String message) {
        JDialog dialog = new JDialog(parent, "جاري التحميل...", true);
        dialog.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        dialog.setDefaultCloseOperation(JDialog.DO_NOTHING_ON_CLOSE);
        
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        JProgressBar progressBar = createStyledProgressBar();
        progressBar.setIndeterminate(true);
        
        JLabel messageLabel = new JLabel(message);
        messageLabel.setFont(ARABIC_FONT);
        messageLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        messageLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        panel.add(messageLabel, BorderLayout.NORTH);
        panel.add(progressBar, BorderLayout.CENTER);
        
        dialog.add(panel);
        dialog.setSize(300, 120);
        dialog.setLocationRelativeTo(parent);
        
        return dialog;
    }
    
    /**
     * عرض رسالة نجاح
     */
    public static void showSuccessMessage(Component parent, String message) {
        JOptionPane.showMessageDialog(parent, message, "نجح", JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * عرض رسالة خطأ
     */
    public static void showErrorMessage(Component parent, String message) {
        JOptionPane.showMessageDialog(parent, message, "خطأ", JOptionPane.ERROR_MESSAGE);
    }
    
    /**
     * عرض رسالة تحذير
     */
    public static void showWarningMessage(Component parent, String message) {
        JOptionPane.showMessageDialog(parent, message, "تحذير", JOptionPane.WARNING_MESSAGE);
    }
    
    /**
     * عرض رسالة تأكيد
     */
    public static boolean showConfirmDialog(Component parent, String message) {
        int result = JOptionPane.showConfirmDialog(parent, message, "تأكيد", 
            JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
        return result == JOptionPane.YES_OPTION;
    }
}
