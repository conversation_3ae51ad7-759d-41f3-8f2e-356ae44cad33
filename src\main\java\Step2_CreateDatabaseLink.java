import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;

/**
 * الخطوة 2: إنشاء Database Link وتطوير وظائف المزامنة
 */
public class Step2_CreateDatabaseLink {

    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");

            System.out.println("🔧 الخطوة 2: إنشاء Database Link");
            System.out.println("🔗 الاتصال بـ SHIP_ERP...");

            Connection conn = DriverManager.getConnection("*************************************",
                    "ship_erp", "ship_erp_password");
            System.out.println("✅ تم الاتصال");

            Statement stmt = conn.createStatement();

            // 1. إنشاء Database Link
            createDatabaseLink(stmt);

            // 2. تطوير Package ERP_ITEM_GROUPS مع وظائف الاستيراد
            enhancePackageWithImport(stmt);

            // 3. إنشاء جدولة للمزامنة التلقائية
            createSyncSchedule(stmt);

            // 4. اختبار Database Link والمزامنة
            testDatabaseLinkAndSync(conn);

            conn.close();
            System.out.println("🎉 تم إكمال الخطوة 2 بنجاح!");

        } catch (Exception e) {
            System.err.println("❌ خطأ في الخطوة 2: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * إنشاء Database Link
     */
    private static void createDatabaseLink(Statement stmt) throws SQLException {
        System.out.println("\n🔗 إنشاء Database Link...");

        // حذف Database Link القديم إن وجد
        try {
            stmt.execute("DROP DATABASE LINK IAS20251_LINK");
            System.out.println("🗑️ تم حذف Database Link القديم");
        } catch (SQLException e) {
            System.out.println("ℹ️ Database Link غير موجود مسبقاً");
        }

        // إنشاء Database Link جديد
        String createLinkSQL = """
                    CREATE DATABASE LINK IAS20251_LINK
                    CONNECT TO ias20251 IDENTIFIED BY ys123
                    USING 'localhost:1521/orcl'
                """;

        try {
            stmt.execute(createLinkSQL);
            System.out.println("✅ تم إنشاء Database Link: IAS20251_LINK");
        } catch (SQLException e) {
            System.err.println("❌ خطأ في إنشاء Database Link: " + e.getMessage());
            throw e;
        }

        // اختبار Database Link
        try {
            ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM GROUP_DETAILS@IAS20251_LINK");
            if (rs.next()) {
                int count = rs.getInt(1);
                System.out.println(
                        "✅ اختبار Database Link نجح - عدد المجموعات في IAS20251: " + count);
            }
            rs.close();
        } catch (SQLException e) {
            System.err.println("⚠️ تحذير: لا يمكن الوصول لجدول GROUP_DETAILS عبر Database Link: "
                    + e.getMessage());
        }
    }

    /**
     * تطوير Package مع وظائف الاستيراد
     */
    private static void enhancePackageWithImport(Statement stmt) throws SQLException {
        System.out.println("\n📦 تطوير Package ERP_ITEM_GROUPS مع وظائف الاستيراد...");

        // حذف Package القديم
        try {
            stmt.execute("DROP PACKAGE ERP_ITEM_GROUPS");
            System.out.println("🗑️ تم حذف Package القديم");
        } catch (SQLException e) {
            System.out.println("ℹ️ Package غير موجود مسبقاً");
        }

        // Package Specification محدث
        String packageSpec = """
                    CREATE OR REPLACE PACKAGE ERP_ITEM_GROUPS AS

                        -- الوظائف الأساسية
                        FUNCTION get_groups_count RETURN NUMBER;
                        FUNCTION get_sub_groups_count RETURN NUMBER;

                        -- إدارة المجموعات الرئيسية
                        FUNCTION add_main_group(
                            p_g_code VARCHAR2,
                            p_g_a_name VARCHAR2,
                            p_g_e_name VARCHAR2
                        ) RETURN VARCHAR2;

                        FUNCTION update_main_group(
                            p_g_code VARCHAR2,
                            p_g_a_name VARCHAR2,
                            p_g_e_name VARCHAR2
                        ) RETURN VARCHAR2;

                        FUNCTION delete_main_group(p_g_code VARCHAR2) RETURN VARCHAR2;

                        -- إدارة المجموعات الفرعية
                        FUNCTION add_sub_group(
                            p_g_code VARCHAR2,
                            p_mng_code VARCHAR2,
                            p_mng_a_name VARCHAR2,
                            p_mng_e_name VARCHAR2
                        ) RETURN VARCHAR2;

                        -- وظائف الاستيراد من IAS20251
                        FUNCTION import_main_groups_from_ias(
                            p_delete_existing VARCHAR2 DEFAULT 'N'
                        ) RETURN VARCHAR2;

                        FUNCTION import_sub_groups_from_ias(
                            p_delete_existing VARCHAR2 DEFAULT 'N'
                        ) RETURN VARCHAR2;

                        FUNCTION import_all_groups_from_ias(
                            p_delete_existing VARCHAR2 DEFAULT 'N'
                        ) RETURN VARCHAR2;

                        -- وظائف المزامنة
                        FUNCTION sync_with_ias RETURN VARCHAR2;
                        FUNCTION auto_sync_with_ias RETURN VARCHAR2;

                        -- وظائف الإحصائيات
                        FUNCTION get_sync_statistics RETURN SYS_REFCURSOR;
                        FUNCTION get_import_log RETURN SYS_REFCURSOR;

                        -- تسجيل العمليات
                        PROCEDURE log_operation(
                            p_operation VARCHAR2,
                            p_message VARCHAR2,
                            p_records_count NUMBER DEFAULT 0
                        );

                    END ERP_ITEM_GROUPS;
                """;

        stmt.execute(packageSpec);
        System.out.println("✅ تم إنشاء Package Specification");

        // Package Body محدث
        String packageBody =
                """
                                    CREATE OR REPLACE PACKAGE BODY ERP_ITEM_GROUPS AS

                                        -- متغيرات عامة
                                        g_last_sync_date DATE;

                                        -- الحصول على عدد المجموعات الرئيسية
                                        FUNCTION get_groups_count RETURN NUMBER IS
                                            l_count NUMBER;
                                        BEGIN

                        SELECT COUNT(*) INTO l_count FROM ERP_GROUP_DETAILS;
                                            RETURN l_count;
                                        EXCEPTION
                                            WHEN OTHERS THEN
                                                RETURN 0;
                                        END get_groups_count;

                                        -- الحصول على عدد المجموعات الفرعية
                                        FUNCTION get_sub_groups_count RETURN NUMBER IS
                                            l_count NUMBER;
                                        BEGIN

                        SELECT COUNT(*) INTO l_count FROM ERP_MAINSUB_GRP_DTL;
                                            RETURN l_count;
                                        EXCEPTION
                                            WHEN OTHERS THEN
                                                RETURN 0;
                                        END get_sub_groups_count;

                                        -- إضافة مجموعة رئيسية

                        FUNCTION add_main_group(
                                            p_g_code VARCHAR2,
                                            p_g_a_name VARCHAR2,
                                            p_g_e_name VARCHAR2
                                        ) RETURN VARCHAR2 IS
                                            l_count NUMBER;
                                        BEGIN

                        SELECT COUNT(*) INTO l_count
                                            FROM ERP_GROUP_DETAILS
                                            WHERE G_CODE = p_g_code;

                                            IF l_count > 0 THEN
                                                RETURN 'ERROR: كود المجموعة موجود مسبقاً';
                                            END IF;

                                            INSERT INTO ERP_GROUP_DETAILS (G_CODE, G_A_NAME, G_E_NAME)
                                            VALUES (p_g_code, p_g_a_name, p_g_e_name);

                                            log_operation('INSERT', 'تم إضافة المجموعة: ' || p_g_code, 1);
                                            COMMIT;
                                            RETURN 'SUCCESS: تم إضافة المجموعة بنجاح';

                                        EXCEPTION
                                            WHEN OTHERS THEN
                                                ROLLBACK;
                                                RETURN 'ERROR: ' || SQLERRM;
                                        END add_main_group;

                                        -- تعديل مجموعة رئيسية

                        FUNCTION update_main_group(
                                            p_g_code VARCHAR2,
                                            p_g_a_name VARCHAR2,
                                            p_g_e_name VARCHAR2
                                        ) RETURN VARCHAR2 IS
                                            l_count NUMBER;
                                        BEGIN

                        SELECT COUNT(*) INTO l_count
                                            FROM ERP_GROUP_DETAILS
                                            WHERE G_CODE = p_g_code;

                                            IF l_count = 0 THEN
                                                RETURN 'ERROR: المجموعة غير موجودة';
                                            END IF;

                                            UPDATE ERP_GROUP_DETAILS SET
                                                G_A_NAME = p_g_a_name,
                                                G_E_NAME = p_g_e_name
                                            WHERE G_CODE = p_g_code;

                                            log_operation('UPDATE', 'تم تعديل المجموعة: ' || p_g_code, 1);
                                            COMMIT;
                                            RETURN 'SUCCESS: تم تعديل المجموعة بنجاح';

                                        EXCEPTION
                                            WHEN OTHERS THEN
                                                ROLLBACK;
                                                RETURN 'ERROR: ' || SQLERRM;
                                        END update_main_group;

                                        -- حذف مجموعة رئيسية

                        FUNCTION delete_main_group(p_g_code VARCHAR2) RETURN VARCHAR2 IS
                                            l_count NUMBER;
                                        BEGIN

                        SELECT COUNT(*) INTO l_count
                                            FROM ERP_MAINSUB_GRP_DTL
                                            WHERE G_CODE = p_g_code;

                                            IF l_count > 0 THEN
                                                RETURN 'ERROR: لا يمكن حذف المجموعة لوجود مجموعات فرعية';
                                            END IF;

                                            DELETE FROM ERP_GROUP_DETAILS WHERE G_CODE = p_g_code;

                                            IF SQL%ROWCOUNT = 0 THEN
                                                RETURN 'ERROR: المجموعة غير موجودة';
                                            END IF;

                                            log_operation('DELETE', 'تم حذف المجموعة: ' || p_g_code, 1);
                                            COMMIT;
                                            RETURN 'SUCCESS: تم حذف المجموعة بنجاح';

                                        EXCEPTION
                                            WHEN OTHERS THEN
                                                ROLLBACK;
                                                RETURN 'ERROR: ' || SQLERRM;
                                        END delete_main_group;

                                        -- إضافة مجموعة فرعية

                        FUNCTION add_sub_group(
                                            p_g_code VARCHAR2,
                                            p_mng_code VARCHAR2,
                                            p_mng_a_name VARCHAR2,
                                            p_mng_e_name VARCHAR2
                                        ) RETURN VARCHAR2 IS
                                            l_count NUMBER;
                                        BEGIN

                        SELECT COUNT(*) INTO l_count
                                            FROM ERP_GROUP_DETAILS
                                            WHERE G_CODE = p_g_code;

                                            IF l_count = 0 THEN
                                                RETURN 'ERROR: المجموعة الرئيسية غير موجودة';
                                            END IF;

                        SELECT COUNT(*) INTO l_count
                                            FROM ERP_MAINSUB_GRP_DTL
                                            WHERE G_CODE = p_g_code AND MNG_CODE = p_mng_code;

                                            IF l_count > 0 THEN
                                                RETURN 'ERROR: كود المجموعة الفرعية موجود مسبقاً';
                                            END IF;

                                            INSERT INTO ERP_MAINSUB_GRP_DTL (G_CODE, MNG_CODE, MNG_A_NAME, MNG_E_NAME)
                                            VALUES (p_g_code, p_mng_code, p_mng_a_name, p_mng_e_name);

                                            log_operation('INSERT', 'تم إضافة المجموعة الفرعية: ' || p_g_code || '/' || p_mng_code, 1);
                                            COMMIT;
                                            RETURN 'SUCCESS: تم إضافة المجموعة الفرعية بنجاح';

                                        EXCEPTION
                                            WHEN OTHERS THEN
                                                ROLLBACK;
                                                RETURN 'ERROR: ' || SQLERRM;
                                        END add_sub_group;

                                        -- استيراد المجموعات الرئيسية من IAS20251

                        FUNCTION import_main_groups_from_ias(
                                            p_delete_existing VARCHAR2 DEFAULT 'N'
                                        ) RETURN VARCHAR2 IS
                                            l_count NUMBER := 0;
                                            l_imported NUMBER := 0;
                                            l_errors NUMBER := 0;
                                        BEGIN
                                            log_operation('IMPORT', 'بدء استيراد المجموعات الرئيسية من IAS20251', 0);

                                            IF p_delete_existing = 'Y' THEN
                                                DELETE FROM ERP_GROUP_DETAILS;
                                                log_operation('DELETE', 'تم حذف البيانات الموجودة', SQL%ROWCOUNT);
                                            END IF;

                                            -- استيراد من IAS20251 عبر Database Link
                                            BEGIN
                                                FOR rec

                        IN (
                                                    SELECT G_CODE, G_A_NAME, G_E_NAME, TAX_PRCNT_DFLT, ROL_LMT_QTY
                                                    FROM GROUP_DETAILS@IAS20251_LINK
                                                    WHERE G_CODE IS NOT NULL
                                                ) LOOP
                                                    BEGIN
                                                        INSERT INTO ERP_GROUP_DETAILS (
                                                            G_CODE, G_A_NAME, G_E_NAME, TAX_PRCNT_DFLT, ROL_LMT_QTY
                                                        ) VALUES (
                                                            rec.G_CODE, rec.G_A_NAME, rec.G_E_NAME,
                                                            rec.TAX_PRCNT_DFLT, rec.ROL_LMT_QTY
                                                        );
                                                        l_imported := l_imported + 1;
                                                    EXCEPTION
                                                        WHEN DUP_VAL_ON_INDEX THEN
                                                            -- تحديث البيانات الموجودة
                                                            UPDATE ERP_GROUP_DETAILS SET
                                                                G_A_NAME = rec.G_A_NAME,
                                                                G_E_NAME = rec.G_E_NAME,
                                                                TAX_PRCNT_DFLT = rec.TAX_PRCNT_DFLT,
                                                                ROL_LMT_QTY = rec.ROL_LMT_QTY
                                                            WHERE G_CODE = rec.G_CODE;
                                                            l_imported := l_imported + 1;
                                                        WHEN OTHERS THEN
                                                            l_errors := l_errors + 1;
                                                    END;
                                                END LOOP;
                                            EXCEPTION
                                                WHEN OTHERS THEN
                                                    log_operation('IMPORT', 'خطأ في الوصول لـ IAS20251: ' || SQLERRM, 0);
                                                    RETURN 'ERROR: لا يمكن الوصول لقاعدة بيانات IAS20251';
                                            END;

                                            log_operation('IMPORT', 'تم استيراد ' || l_imported || ' مجموعة رئيسية، أخطاء: ' || l_errors, l_imported);
                                            COMMIT;
                                            RETURN 'SUCCESS: تم استيراد ' || l_imported || ' مجموعة رئيسية';

                                        EXCEPTION
                                            WHEN OTHERS THEN
                                                ROLLBACK;
                                                log_operation('IMPORT', 'خطأ في الاستيراد: ' || SQLERRM, 0);
                                                RETURN 'ERROR: ' || SQLERRM;
                                        END import_main_groups_from_ias;

                                        -- استيراد المجموعات الفرعية من IAS20251

                        FUNCTION import_sub_groups_from_ias(
                                            p_delete_existing VARCHAR2 DEFAULT 'N'
                                        ) RETURN VARCHAR2 IS
                                            l_imported NUMBER := 0;
                                            l_errors NUMBER := 0;
                                        BEGIN
                                            log_operation('IMPORT', 'بدء استيراد المجموعات الفرعية من IAS20251', 0);

                                            IF p_delete_existing = 'Y' THEN
                                                DELETE FROM ERP_MAINSUB_GRP_DTL;
                                            END IF;

                                            -- استيراد من IAS20251 عبر Database Link
                                            BEGIN
                                                FOR rec

                        IN (
                                                    SELECT G_CODE, MNG_CODE, MNG_A_NAME, MNG_E_NAME
                                                    FROM IAS_MAINSUB_GRP_DTL@IAS20251_LINK
                                                    WHERE G_CODE IS NOT NULL AND MNG_CODE IS NOT NULL
                                                ) LOOP
                                                    BEGIN
                                                        INSERT INTO ERP_MAINSUB_GRP_DTL (
                                                            G_CODE, MNG_CODE, MNG_A_NAME, MNG_E_NAME
                                                        ) VALUES (
                                                            rec.G_CODE, rec.MNG_CODE, rec.MNG_A_NAME, rec.MNG_E_NAME
                                                        );
                                                        l_imported := l_imported + 1;
                                                    EXCEPTION
                                                        WHEN DUP_VAL_ON_INDEX THEN
                                                            UPDATE ERP_MAINSUB_GRP_DTL SET
                                                                MNG_A_NAME = rec.MNG_A_NAME,
                                                                MNG_E_NAME = rec.MNG_E_NAME
                                                            WHERE G_CODE = rec.G_CODE AND MNG_CODE = rec.MNG_CODE;
                                                            l_imported := l_imported + 1;
                                                        WHEN OTHERS THEN
                                                            l_errors := l_errors + 1;
                                                    END;
                                                END LOOP;
                                            EXCEPTION
                                                WHEN OTHERS THEN
                                                    log_operation('IMPORT', 'خطأ في الوصول لجدول المجموعات الفرعية: ' || SQLERRM, 0);
                                                    RETURN 'ERROR: لا يمكن الوصول لجدول المجموعات الفرعية';
                                            END;

                                            log_operation('IMPORT', 'تم استيراد ' || l_imported || ' مجموعة فرعية، أخطاء: ' || l_errors, l_imported);
                                            COMMIT;
                                            RETURN 'SUCCESS: تم استيراد ' || l_imported || ' مجموعة فرعية';

                                        EXCEPTION
                                            WHEN OTHERS THEN
                                                ROLLBACK;
                                                log_operation('IMPORT', 'خطأ في استيراد المجموعات الفرعية: ' || SQLERRM, 0);
                                                RETURN 'ERROR: ' || SQLERRM;
                                        END import_sub_groups_from_ias;

                                        -- استيراد جميع المجموعات من IAS20251
                                        FUNCTION import_all_groups_from_ias(
                                            p_delete_existing VARCHAR2 DEFAULT 'N'
                                        ) RETURN VARCHAR2 IS
                                            l_result VARCHAR2(4000);
                                            l_total_count NUMBER := 0;
                                        BEGIN
                                            log_operation('IMPORT', 'بدء استيراد جميع المجموعات من IAS20251', 0);

                                            -- استيراد المجموعات الرئيسية
                                            l_result := import_main_groups_from_ias(p_delete_existing);
                                            IF SUBSTR(l_result, 1, 5) = 'ERROR' THEN
                                                RETURN l_result;
                                            END IF;

                                            -- استيراد المجموعات الفرعية
                                            l_result := import_sub_groups_from_ias(p_delete_existing);
                                            IF SUBSTR(l_result, 1, 5) = 'ERROR' THEN
                                                RETURN l_result;
                                            END IF;

                                            SELECT COUNT(*) INTO l_total_count FROM ERP_GROUP_DETAILS;

                                            log_operation('IMPORT', 'تم استيراد جميع المجموعات بنجاح', l_total_count);
                                            RETURN 'SUCCESS: تم استيراد جميع المجموعات بنجاح - إجمالي: ' || l_total_count;

                                        EXCEPTION
                                            WHEN OTHERS THEN
                                                ROLLBACK;
                                                log_operation('IMPORT', 'خطأ في استيراد جميع المجموعات: ' || SQLERRM, 0);
                                                RETURN 'ERROR: ' || SQLERRM;
                                        END import_all_groups_from_ias;

                                        -- مزامنة مع IAS20251
                                        FUNCTION sync_with_ias RETURN VARCHAR2 IS
                                            l_result VARCHAR2(4000);
                                        BEGIN
                                            g_last_sync_date := SYSDATE;

                                            l_result := import_all_groups_from_ias('N');

                                            log_operation('SYNC', 'تم تنفيذ المزامنة مع IAS20251: ' || l_result, 0);
                                            RETURN 'SUCCESS: تم تنفيذ المزامنة بنجاح في ' || TO_CHAR(g_last_sync_date, 'DD/MM/YYYY HH24:MI:SS');

                                        EXCEPTION
                                            WHEN OTHERS THEN
                                                RETURN 'ERROR: ' || SQLERRM;
                                        END sync_with_ias;

                                        -- مزامنة تلقائية مع IAS20251
                                        FUNCTION auto_sync_with_ias RETURN VARCHAR2 IS
                                        BEGIN
                                            RETURN sync_with_ias();
                                        END auto_sync_with_ias;

                                        -- إحصائيات المزامنة
                                        FUNCTION get_sync_statistics RETURN SYS_REFCURSOR IS
                                            l_cursor SYS_REFCURSOR;
                                        BEGIN
                                            OPEN l_cursor FOR
                                                SELECT operation_type, status, COUNT(*) as operation_count,
                                                       SUM(records_count) as total_records,
                                                       MAX(operation_date) as last_operation
                                                FROM ERP_OPERATION_LOG
                                                WHERE operation_type IN ('SYNC', 'IMPORT')
                                                GROUP BY operation_type, status
                                                ORDER BY operation_type, status;

                                            RETURN l_cursor;
                                        END get_sync_statistics;

                                        -- سجل الاستيراد
                                        FUNCTION get_import_log RETURN SYS_REFCURSOR IS
                                            l_cursor SYS_REFCURSOR;
                                        BEGIN
                                            OPEN l_cursor FOR
                                                SELECT operation_type, table_name, status, message,
                                                       operation_date, records_count, username
                                                FROM ERP_OPERATION_LOG
                                                WHERE operation_type IN ('SYNC', 'IMPORT', 'DELETE')
                                                ORDER BY operation_date DESC
                                                FETCH FIRST 50 ROWS ONLY;

                                            RETURN l_cursor;
                                        END get_import_log;

                                        -- تسجيل العمليات
                                        PROCEDURE log_operation(
                                            p_operation VARCHAR2,
                                            p_message VARCHAR2,
                                            p_records_count NUMBER DEFAULT 0
                                        ) IS
                                            PRAGMA AUTONOMOUS_TRANSACTION;
                                        BEGIN
                                            INSERT INTO ERP_OPERATION_LOG (
                                                log_id, operation_type, table_name, status,
                                                message, records_count, operation_date, username
                                            ) VALUES (
                                                ERP_LOG_SEQ.NEXTVAL, p_operation, 'ERP_ITEM_GROUPS', 'SUCCESS',
                                                p_message, p_records_count, SYSDATE, USER
                                            );
                                            COMMIT;
                                        EXCEPTION
                                            WHEN OTHERS THEN
                                                ROLLBACK;
                                                NULL;
                                        END log_operation;

                                    END ERP_ITEM_GROUPS;
                                """;

        stmt.execute(packageBody);
        System.out.println("✅ تم إنشاء Package Body");
    }

    /**
     * إنشاء جدولة للمزامنة التلقائية
     */
    private static void createSyncSchedule(Statement stmt) throws SQLException {
        System.out.println("\n⏰ إنشاء جدولة للمزامنة التلقائية...");

        // إنشاء Job للمزامنة التلقائية
        try {
            // حذف Job القديم إن وجد
            stmt.execute(
                    "BEGIN DBMS_SCHEDULER.DROP_JOB('ERP_AUTO_SYNC_JOB'); EXCEPTION WHEN OTHERS THEN NULL; END;");

            String createJobSQL =
                    """
                                BEGIN
                                    DBMS_SCHEDULER.CREATE_JOB (
                                        job_name        => 'ERP_AUTO_SYNC_JOB',
                                        job_type        => 'PLSQL_BLOCK',
                                        job_action      => 'DECLARE l_result VARCHAR2(4000); BEGIN l_result := ERP_ITEM_GROUPS.auto_sync_with_ias(); END;',
                                        start_date      => SYSTIMESTAMP,
                                        repeat_interval => 'FREQ=DAILY; BYHOUR=2; BYMINUTE=0; BYSECOND=0',
                                        enabled         => TRUE,
                                        comments        => 'مزامنة تلقائية يومية مع IAS20251 في الساعة 2:00 صباحاً'
                                    );
                                END;
                            """;

            stmt.execute(createJobSQL);
            System.out.println("✅ تم إنشاء Job للمزامنة التلقائية");

        } catch (SQLException e) {
            System.err.println("⚠️ تحذير: لا يمكن إنشاء Job للمزامنة التلقائية: " + e.getMessage());
        }
    }

    /**
     * اختبار Database Link والمزامنة
     */
    private static void testDatabaseLinkAndSync(Connection conn) throws SQLException {
        System.out.println("\n🧪 اختبار Database Link والمزامنة...");

        try {
            // اختبار عدد المجموعات قبل المزامنة
            CallableStatement cs1 =
                    conn.prepareCall("{ ? = call ERP_ITEM_GROUPS.get_groups_count }");
            cs1.registerOutParameter(1, Types.NUMERIC);
            cs1.execute();
            int countBefore = cs1.getInt(1);
            System.out.println("📊 عدد المجموعات قبل المزامنة: " + countBefore);

            // اختبار المزامنة
            CallableStatement cs2 = conn.prepareCall("{ ? = call ERP_ITEM_GROUPS.sync_with_ias }");
            cs2.registerOutParameter(1, Types.VARCHAR);
            cs2.execute();
            String syncResult = cs2.getString(1);
            System.out.println("🔄 نتيجة المزامنة: " + syncResult);

            // اختبار عدد المجموعات بعد المزامنة
            cs1.execute();
            int countAfter = cs1.getInt(1);
            System.out.println("📊 عدد المجموعات بعد المزامنة: " + countAfter);

            System.out.println("✅ تم اختبار Database Link والمزامنة بنجاح!");

        } catch (SQLException e) {
            System.err.println("❌ خطأ في اختبار المزامنة: " + e.getMessage());
            throw e;
        }
    }
}
