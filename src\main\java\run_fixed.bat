@echo off
echo Oracle JDBC Fix - Running System with Libraries
echo.

echo Checking lib directory...
if not exist "lib\ojdbc11.jar" (
    echo ERROR: ojdbc11.jar not found!
    echo Running LibraryDownloader...
    java LibraryDownloader
)

echo.
echo Compiling with libraries...
javac -encoding UTF-8 -cp "lib/*;." *.java

echo.
echo Running system with Oracle JDBC...
java -Dfile.encoding=UTF-8 -Duser.language=ar -Duser.country=SA -cp "lib/*;." CompleteSystemTest

pause
