import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.Frame;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JTextField;

/**
 * نافذة إضافة مجموعة رئيسية جديدة Add Main Group Dialog
 */
public class AddMainGroupDialog extends JDialog {

    private Connection connection;
    private boolean saved = false;

    // مكونات الواجهة
    private JTextField codeField;
    private JTextField arabicNameField;
    private JTextField englishNameField;
    private JTextField taxPercentField;
    private JTextField quantityLimitField;
    private JTextField imageCodeField;
    private JTextField orderField;
    private JCheckBox webSyncCheckBox;
    private JCheckBox useSalePriceCheckBox;
    private JCheckBox allowDiscountCheckBox;
    private JCheckBox allowPIDiscountCheckBox;
    private JCheckBox activeCheckBox;

    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 13);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 13);

    public AddMainGroupDialog(Frame parent, Connection connection) {
        super(parent, "إضافة مجموعة رئيسية جديدة", true);
        this.connection = connection;

        initializeComponents();
        setupLayout();
        setDefaultValues();

        setSize(700, 750);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }

    private void initializeComponents() {
        // إنشاء الحقول
        codeField = new JTextField(20);
        codeField.setEnabled(false); // إلغاء تفعيل كود المجموعة (توليد تلقائي)
        arabicNameField = new JTextField(30);
        englishNameField = new JTextField(30);
        taxPercentField = new JTextField(15);
        quantityLimitField = new JTextField(15);
        imageCodeField = new JTextField(20);
        orderField = new JTextField(15);

        // إنشاء صناديق الاختيار
        webSyncCheckBox = new JCheckBox("مزامنة الويب");
        useSalePriceCheckBox = new JCheckBox("استخدام سعر البيع كسعر شراء");
        allowDiscountCheckBox = new JCheckBox("السماح بالخصم");
        allowPIDiscountCheckBox = new JCheckBox("السماح بخصم PI");
        activeCheckBox = new JCheckBox("نشط");

        // تطبيق الخط العربي وتحسين المظهر
        JTextField[] components = {codeField, arabicNameField, englishNameField, taxPercentField,
                quantityLimitField, imageCodeField, orderField};
        for (JTextField component : components) {
            component.setFont(arabicFont);
            component.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            component.setPreferredSize(new Dimension(250, 30));
            component.setBorder(
                    BorderFactory.createCompoundBorder(BorderFactory.createLineBorder(Color.GRAY),
                            BorderFactory.createEmptyBorder(5, 10, 5, 10)));
        }

        JCheckBox[] checkBoxes = {webSyncCheckBox, useSalePriceCheckBox, allowDiscountCheckBox,
                allowPIDiscountCheckBox, activeCheckBox};
        for (JCheckBox checkBox : checkBoxes) {
            checkBox.setFont(arabicFont);
            checkBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            checkBox.setPreferredSize(new Dimension(300, 25));
        }
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.EAST;
        gbc.fill = GridBagConstraints.HORIZONTAL;

        int row = 0;

        // كود المجموعة
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.3;
        mainPanel.add(createLabel("كود المجموعة (تلقائي):"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 0.7;
        mainPanel.add(codeField, gbc);
        row++;

        // الاسم العربي
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.3;
        mainPanel.add(createLabel("الاسم العربي *:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 0.7;
        mainPanel.add(arabicNameField, gbc);
        row++;

        // الاسم الإنجليزي
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.3;
        mainPanel.add(createLabel("الاسم الإنجليزي:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 0.7;
        mainPanel.add(englishNameField, gbc);
        row++;

        // نسبة الضريبة الافتراضية
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.3;
        mainPanel.add(createLabel("نسبة الضريبة الافتراضية:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 0.7;
        mainPanel.add(taxPercentField, gbc);
        row++;

        // حد الكمية
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.3;
        mainPanel.add(createLabel("حد الكمية:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 0.7;
        mainPanel.add(quantityLimitField, gbc);
        row++;

        // كود الصورة
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.3;
        mainPanel.add(createLabel("كود الصورة:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 0.7;
        mainPanel.add(imageCodeField, gbc);
        row++;

        // الترتيب
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.3;
        mainPanel.add(createLabel("الترتيب:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 0.7;
        mainPanel.add(orderField, gbc);
        row++;

        // صناديق الاختيار
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        mainPanel.add(webSyncCheckBox, gbc);
        row++;

        gbc.gridy = row;
        mainPanel.add(useSalePriceCheckBox, gbc);
        row++;

        gbc.gridy = row;
        mainPanel.add(allowDiscountCheckBox, gbc);
        row++;

        gbc.gridy = row;
        mainPanel.add(allowPIDiscountCheckBox, gbc);
        row++;

        gbc.gridy = row;
        mainPanel.add(activeCheckBox, gbc);

        add(mainPanel, BorderLayout.CENTER);

        // لوحة الأزرار
        JPanel buttonPanel = createButtonPanel();
        add(buttonPanel, BorderLayout.SOUTH);
    }

    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicBoldFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }

    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        JButton saveButton = new JButton("💾 حفظ");
        saveButton.setFont(arabicBoldFont);
        saveButton.setBackground(new Color(39, 174, 96));
        saveButton.setForeground(Color.WHITE);
        saveButton.setPreferredSize(new Dimension(100, 35));
        saveButton.addActionListener(e -> saveMainGroup());

        JButton cancelButton = new JButton("❌ إلغاء");
        cancelButton.setFont(arabicBoldFont);
        cancelButton.setBackground(new Color(231, 76, 60));
        cancelButton.setForeground(Color.WHITE);
        cancelButton.setPreferredSize(new Dimension(100, 35));
        cancelButton.addActionListener(e -> dispose());

        panel.add(saveButton);
        panel.add(cancelButton);

        return panel;
    }

    private void setDefaultValues() {
        // توليد كود تلقائي
        generateNextCode();

        // القيم الافتراضية
        taxPercentField.setText("0");
        quantityLimitField.setText("0");
        orderField.setText("1");
        activeCheckBox.setSelected(true);
        webSyncCheckBox.setSelected(false);
        useSalePriceCheckBox.setSelected(false);
        allowDiscountCheckBox.setSelected(true);
        allowPIDiscountCheckBox.setSelected(false);
    }

    private void generateNextCode() {
        try {
            String sql =
                    "SELECT 'G' || LPAD(NVL(MAX(TO_NUMBER(SUBSTR(G_CODE, 2))), 0) + 1, 3, '0') FROM ERP_GROUP_DETAILS WHERE G_CODE LIKE 'G%'";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            if (rs.next()) {
                String nextCode = rs.getString(1);
                codeField.setText(nextCode);
            } else {
                codeField.setText("G001");
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في توليد الكود: " + e.getMessage());
            codeField.setText("G001");
        }
    }

    private void saveMainGroup() {
        // التحقق من صحة البيانات
        if (!validateInput()) {
            return;
        }

        try {
            String sql = """
                        INSERT INTO ERP_GROUP_DETAILS
                        (G_CODE, G_A_NAME, G_E_NAME, TAX_PRCNT_DFLT, ROL_LMT_QTY, G_I_CODE,
                         SYNCHRNZ_TO_WEB_FLG, USE_SAL_PRICE_AS_PUR_PRICE, ALLOW_DISC_FLG,
                         ALLOW_DISC_PI_FLG, G_ORDR, IS_ACTIVE, CREATED_BY, CREATED_DATE)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'USER', SYSDATE)
                    """;

            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, codeField.getText().trim());
            pstmt.setString(2, arabicNameField.getText().trim());
            pstmt.setString(3, englishNameField.getText().trim());

            // نسبة الضريبة
            String taxText = taxPercentField.getText().trim();
            if (taxText.isEmpty()) {
                pstmt.setNull(4, Types.DECIMAL);
            } else {
                pstmt.setBigDecimal(4, new java.math.BigDecimal(taxText));
            }

            // حد الكمية
            String qtyText = quantityLimitField.getText().trim();
            if (qtyText.isEmpty()) {
                pstmt.setNull(5, Types.DECIMAL);
            } else {
                pstmt.setBigDecimal(5, new java.math.BigDecimal(qtyText));
            }

            pstmt.setString(6, imageCodeField.getText().trim());
            pstmt.setInt(7, webSyncCheckBox.isSelected() ? 1 : 0);
            pstmt.setInt(8, useSalePriceCheckBox.isSelected() ? 1 : 0);
            pstmt.setInt(9, allowDiscountCheckBox.isSelected() ? 1 : 0);
            pstmt.setInt(10, allowPIDiscountCheckBox.isSelected() ? 1 : 0);

            // الترتيب
            String orderText = orderField.getText().trim();
            if (orderText.isEmpty()) {
                pstmt.setNull(11, Types.INTEGER);
            } else {
                pstmt.setInt(11, Integer.parseInt(orderText));
            }

            pstmt.setInt(12, activeCheckBox.isSelected() ? 1 : 0);

            pstmt.executeUpdate();
            connection.commit();

            saved = true;
            JOptionPane.showMessageDialog(this, "تم حفظ المجموعة الرئيسية بنجاح!", "نجح الحفظ",
                    JOptionPane.INFORMATION_MESSAGE);

            dispose();

        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }

            JOptionPane.showMessageDialog(this, "خطأ في حفظ المجموعة الرئيسية:\n" + e.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    private boolean validateInput() {
        // التحقق من الحقول المطلوبة
        if (codeField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "كود المجموعة مطلوب", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            codeField.requestFocus();
            return false;
        }

        if (arabicNameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "الاسم العربي مطلوب", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            arabicNameField.requestFocus();
            return false;
        }

        // التحقق من صحة الأرقام
        try {
            String taxText = taxPercentField.getText().trim();
            if (!taxText.isEmpty()) {
                Double.parseDouble(taxText);
            }

            String qtyText = quantityLimitField.getText().trim();
            if (!qtyText.isEmpty()) {
                Double.parseDouble(qtyText);
            }

            String orderText = orderField.getText().trim();
            if (!orderText.isEmpty()) {
                Integer.parseInt(orderText);
            }

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال أرقام صحيحة", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        // التحقق من عدم تكرار الكود
        try {
            String sql = "SELECT COUNT(*) FROM ERP_GROUP_DETAILS WHERE G_CODE = ?";
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, codeField.getText().trim());
            ResultSet rs = pstmt.executeQuery();

            if (rs.next() && rs.getInt(1) > 0) {
                JOptionPane.showMessageDialog(this, "كود المجموعة موجود مسبقاً", "خطأ",
                        JOptionPane.ERROR_MESSAGE);
                codeField.requestFocus();
                return false;
            }

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في التحقق من الكود", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        return true;
    }

    public boolean isSaved() {
        return saved;
    }
}
