@echo off
chcp 65001 > nul
echo ========================================
echo    فحص متطلبات نظام إدارة الشحنات
echo    Ship ERP Requirements Check
echo ========================================
echo.

set REQUIREMENTS_MET=1

echo [INFO] فحص المتطلبات الأساسية...
echo.

REM فحص Java
echo [1/5] فحص Java JDK...
java -version > nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Java مثبت
    for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr /i "version"') do (
        set JAVA_VERSION=%%i
        set JAVA_VERSION=!JAVA_VERSION:"=!
    )
    echo     الإصدار: %JAVA_VERSION%
    
    REM التحقق من إصدار Java (يجب أن يكون 17 أو أحدث)
    for /f "tokens=1,2 delims=." %%a in ("%JAVA_VERSION%") do (
        if %%a LSS 17 (
            echo [⚠] تحذير: يُنصح باستخدام Java 17 أو أحدث
        )
    )
) else (
    echo [✗] Java غير مثبت أو غير موجود في PATH
    echo     يرجى تثبيت Java JDK 17 أو أحدث من: https://adoptium.net/
    set REQUIREMENTS_MET=0
)
echo.

REM فحص Maven
echo [2/5] فحص Apache Maven...
mvn -version > nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Maven مثبت
    for /f "tokens=3" %%i in ('mvn -version 2^>^&1 ^| findstr /i "Apache Maven"') do (
        echo     الإصدار: %%i
    )
) else (
    echo [✗] Maven غير مثبت أو غير موجود في PATH
    echo     يرجى تثبيت Apache Maven من: https://maven.apache.org/download.cgi
    set REQUIREMENTS_MET=0
)
echo.

REM فحص Oracle
echo [3/5] فحص Oracle Database...
sqlplus -v > nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Oracle SQL*Plus مثبت
    
    REM محاولة الاتصال بقاعدة البيانات المحلية
    echo exit | sqlplus -s system/manager@localhost:1521/XE > nul 2>&1
    if %errorlevel% equ 0 (
        echo [✓] قاعدة البيانات Oracle متاحة
    ) else (
        echo [⚠] تحذير: لا يمكن الاتصال بقاعدة البيانات
        echo     تأكد من تشغيل خدمة Oracle وصحة معلومات الاتصال
    )
) else (
    echo [✗] Oracle غير مثبت أو غير موجود في PATH
    echo     يرجى تثبيت Oracle Database أو Oracle Client
    set REQUIREMENTS_MET=0
)
echo.

REM فحص متغيرات البيئة
echo [4/5] فحص متغيرات البيئة...
if defined JAVA_HOME (
    echo [✓] JAVA_HOME محدد: %JAVA_HOME%
) else (
    echo [⚠] تحذير: JAVA_HOME غير محدد
    echo     يُنصح بتحديد متغير JAVA_HOME
)

if defined M2_HOME (
    echo [✓] M2_HOME محدد: %M2_HOME%
) else (
    echo [⚠] تحذير: M2_HOME غير محدد
    echo     يُنصح بتحديد متغير M2_HOME
)
echo.

REM فحص مساحة القرص
echo [5/5] فحص مساحة القرص...
for /f "tokens=3" %%i in ('dir /-c %~d0 ^| findstr /i "bytes free"') do (
    set FREE_SPACE=%%i
)
if defined FREE_SPACE (
    echo [✓] مساحة القرص متاحة: %FREE_SPACE% بايت
) else (
    echo [⚠] تحذير: لا يمكن تحديد مساحة القرص المتاحة
)
echo.

REM فحص الملفات المطلوبة
echo فحص ملفات المشروع...
if exist "pom.xml" (
    echo [✓] pom.xml موجود
) else (
    echo [✗] pom.xml غير موجود
    set REQUIREMENTS_MET=0
)

if exist "src\main\java" (
    echo [✓] مجلد المصدر موجود
) else (
    echo [✗] مجلد المصدر غير موجود
    set REQUIREMENTS_MET=0
)

if exist "src\main\resources" (
    echo [✓] مجلد الموارد موجود
) else (
    echo [✗] مجلد الموارد غير موجود
    set REQUIREMENTS_MET=0
)
echo.

REM النتيجة النهائية
echo ========================================
if %REQUIREMENTS_MET% equ 1 (
    echo [✓] جميع المتطلبات متوفرة!
    echo.
    echo الخطوات التالية:
    echo 1. تشغيل setup-database.bat لإعداد قاعدة البيانات
    echo 2. تشغيل run-ship-erp.bat لبدء التطبيق
) else (
    echo [✗] بعض المتطلبات غير متوفرة
    echo.
    echo يرجى تثبيت المتطلبات المفقودة قبل المتابعة
    echo.
    echo روابط التحميل:
    echo - Java JDK: https://adoptium.net/
    echo - Apache Maven: https://maven.apache.org/download.cgi
    echo - Oracle Database: https://www.oracle.com/database/technologies/xe-downloads.html
)
echo ========================================
echo.

pause
