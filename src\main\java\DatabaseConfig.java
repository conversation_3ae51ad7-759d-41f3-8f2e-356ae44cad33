import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Properties;

/**
 * إعداد قاعدة البيانات Oracle Oracle Database Configuration
 */
public class DatabaseConfig {

    // إعدادات الاتصال الافتراضية
    private static final String DEFAULT_HOST = "localhost";
    private static final String DEFAULT_PORT = "1521";
    private static final String DEFAULT_SERVICE_NAME = "orcl";
    private static final String DEFAULT_USERNAME = "ias20251";
    private static final String DEFAULT_PASSWORD = "ys123";

    // معلومات الاتصال الحالية
    private String host;
    private String port;
    private String serviceName;
    private String username;
    private String password;
    private Connection connection;

    public DatabaseConfig() {
        this.host = DEFAULT_HOST;
        this.port = DEFAULT_PORT;
        this.serviceName = DEFAULT_SERVICE_NAME;
        this.username = DEFAULT_USERNAME;
        this.password = DEFAULT_PASSWORD;
    }

    public DatabaseConfig(String host, String port, String serviceName, String username,
            String password) {
        this.host = host;
        this.port = port;
        this.serviceName = serviceName;
        this.username = username;
        this.password = password;
    }

    /**
     * إنشاء اتصال بقاعدة البيانات
     */
    public Connection createConnection() throws SQLException {
        try {
            // فحص وجود مكتبة Oracle JDBC
            if (!isOracleJDBCAvailable()) {
                throw new SQLException(
                        "مكتبة Oracle JDBC غير موجودة. يرجى تشغيل LibraryDownloader لتحميل المكتبات المطلوبة.");
            }

            // تحميل تعريف Oracle JDBC
            Class.forName("oracle.jdbc.driver.OracleDriver");

            // بناء URL الاتصال
            String url = String.format("**************************", host, port, serviceName);

            // إعداد خصائص الاتصال المحسنة
            Properties props = new Properties();
            props.setProperty("user", username);
            props.setProperty("password", password);

            // إعدادات الأداء والأمان
            props.setProperty("oracle.jdbc.autoCommitSpecCompliant", "false");
            props.setProperty("oracle.net.CONNECT_TIMEOUT", "15000"); // 15 ثانية
            props.setProperty("oracle.jdbc.ReadTimeout", "60000"); // 60 ثانية
            props.setProperty("oracle.net.READ_TIMEOUT", "60000");

            // إعدادات إضافية للاستقرار
            props.setProperty("oracle.jdbc.implicitStatementCacheSize", "20");
            props.setProperty("oracle.jdbc.defaultRowPrefetch", "20");

            // محاولة الاتصال مع إعادة المحاولة
            connection = connectWithRetry(url, props, 3);

            if (connection != null) {
                connection.setAutoCommit(false);

                // اختبار الاتصال
                if (!isConnectionValid(connection)) {
                    connection.close();
                    throw new SQLException("الاتصال غير صالح بعد الإنشاء");
                }
            }

            return connection;

        } catch (ClassNotFoundException e) {
            throw new SQLException(
                    "تعذر العثور على تعريف Oracle JDBC. تأكد من وجود ملف ojdbc11.jar في مجلد lib",
                    e);
        } catch (SQLException e) {
            // تحسين رسائل الخطأ
            String enhancedMessage = enhanceErrorMessage(e);
            throw new SQLException(enhancedMessage, e);
        }
    }

    /**
     * فحص وجود مكتبة Oracle JDBC
     */
    private boolean isOracleJDBCAvailable() {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * الاتصال مع إعادة المحاولة
     */
    private Connection connectWithRetry(String url, Properties props, int maxRetries)
            throws SQLException {
        SQLException lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                System.out.println("محاولة الاتصال " + attempt + "/" + maxRetries + "...");
                return DriverManager.getConnection(url, props);

            } catch (SQLException e) {
                lastException = e;
                System.err.println("فشلت المحاولة " + attempt + ": " + e.getMessage());

                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(2000); // انتظار ثانيتين قبل المحاولة التالية
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        if (lastException != null) {
            throw lastException;
        } else {
            throw new SQLException("فشل في الاتصال بعد " + maxRetries + " محاولات");
        }
    }

    /**
     * اختبار صحة الاتصال
     */
    private boolean isConnectionValid(Connection conn) {
        try {
            return conn != null && !conn.isClosed() && conn.isValid(5);
        } catch (SQLException e) {
            return false;
        }
    }

    /**
     * تحسين رسائل الخطأ
     */
    private String enhanceErrorMessage(SQLException e) {
        int errorCode = e.getErrorCode();
        String originalMessage = e.getMessage();

        switch (errorCode) {
            case 17002:
                return "فشل الاتصال بالخادم. تأكد من أن Oracle Database يعمل وأن عنوان الخادم والمنفذ صحيحان.\nالخطأ الأصلي: "
                        + originalMessage;

            case 1017:
                return "اسم المستخدم أو كلمة المرور غير صحيحة. تحقق من بيانات الاعتماد.\nالخطأ الأصلي: "
                        + originalMessage;

            case 12505:
                return "اسم الخدمة (SID) غير صحيح. تأكد من صحة اسم الخدمة.\nالخطأ الأصلي: "
                        + originalMessage;

            case 12541:
                return "لا يمكن الوصول إلى Oracle Listener. تأكد من تشغيل Listener على المنفذ المحدد.\nالخطأ الأصلي: "
                        + originalMessage;

            default:
                return "خطأ في الاتصال بقاعدة البيانات: " + originalMessage;
        }
    }

    /**
     * اختبار الاتصال بقاعدة البيانات
     */
    public boolean testConnection() {
        try (Connection testConn = createConnection()) {
            if (testConn != null && !testConn.isClosed()) {
                // تنفيذ استعلام بسيط للتأكد من الاتصال
                try (Statement stmt = testConn.createStatement();
                        ResultSet rs = stmt.executeQuery("SELECT 1 FROM DUAL")) {
                    return rs.next();
                }
            }
            return false;
        } catch (SQLException e) {
            System.err.println("خطأ في اختبار الاتصال: " + e.getMessage());
            return false;
        }
    }

    /**
     * الحصول على معلومات قاعدة البيانات
     */
    public DatabaseInfo getDatabaseInfo() throws SQLException {
        try (Connection conn = createConnection()) {
            DatabaseMetaData metaData = conn.getMetaData();

            DatabaseInfo info = new DatabaseInfo();
            info.setDatabaseProductName(metaData.getDatabaseProductName());
            info.setDatabaseProductVersion(metaData.getDatabaseProductVersion());
            info.setDriverName(metaData.getDriverName());
            info.setDriverVersion(metaData.getDriverVersion());
            info.setUrl(metaData.getURL());
            info.setUserName(metaData.getUserName());

            return info;
        }
    }

    /**
     * إغلاق الاتصال
     */
    public void closeConnection() {
        if (connection != null) {
            try {
                if (!connection.isClosed()) {
                    connection.close();
                }
            } catch (SQLException e) {
                System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
            }
        }
    }

    // Getters and Setters
    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Connection getConnection() {
        return connection;
    }

    /**
     * فئة معلومات قاعدة البيانات
     */
    public static class DatabaseInfo {
        private String databaseProductName;
        private String databaseProductVersion;
        private String driverName;
        private String driverVersion;
        private String url;
        private String userName;

        // Getters and Setters
        public String getDatabaseProductName() {
            return databaseProductName;
        }

        public void setDatabaseProductName(String databaseProductName) {
            this.databaseProductName = databaseProductName;
        }

        public String getDatabaseProductVersion() {
            return databaseProductVersion;
        }

        public void setDatabaseProductVersion(String databaseProductVersion) {
            this.databaseProductVersion = databaseProductVersion;
        }

        public String getDriverName() {
            return driverName;
        }

        public void setDriverName(String driverName) {
            this.driverName = driverName;
        }

        public String getDriverVersion() {
            return driverVersion;
        }

        public void setDriverVersion(String driverVersion) {
            this.driverVersion = driverVersion;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        @Override
        public String toString() {
            return String.format("قاعدة البيانات: %s %s\nالتعريف: %s %s\nالمستخدم: %s\nالرابط: %s",
                    databaseProductName, databaseProductVersion, driverName, driverVersion,
                    userName, url);
        }
    }
}
