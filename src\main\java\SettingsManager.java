import java.io.*;
import java.util.Properties;
import javax.swing.UIManager;
import java.awt.Color;

/**
 * مدير الإعدادات - حفظ وتحميل الإعدادات بشكل دائم
 * Settings Manager - Save and load settings permanently
 */
public class SettingsManager {
    
    private static final String SETTINGS_FILE = "ship_erp_settings.properties";
    private static final String THEME_KEY = "ui.theme";
    private static final String LANGUAGE_KEY = "ui.language";
    
    /**
     * حفظ إعدادات المظهر واللغة
     */
    public static void saveSettings(String theme, String language) {
        Properties properties = new Properties();
        
        try {
            // تحميل الإعدادات الموجودة أولاً
            File settingsFile = new File(SETTINGS_FILE);
            if (settingsFile.exists()) {
                try (FileInputStream fis = new FileInputStream(settingsFile)) {
                    properties.load(fis);
                }
            }
            
            // تحديث الإعدادات الجديدة
            properties.setProperty(THEME_KEY, theme);
            properties.setProperty(LANGUAGE_KEY, language);
            
            // حفظ الإعدادات
            try (FileOutputStream fos = new FileOutputStream(SETTINGS_FILE)) {
                properties.store(fos, "Ship ERP Settings - إعدادات نظام إدارة الشحنات");
            }
            
            System.out.println("تم حفظ الإعدادات: المظهر=" + theme + ", اللغة=" + language);
            
        } catch (IOException e) {
            System.err.println("خطأ في حفظ الإعدادات: " + e.getMessage());
        }
    }
    
    /**
     * تحميل إعدادات المظهر واللغة
     */
    public static Properties loadSettings() {
        Properties properties = new Properties();
        
        try {
            File settingsFile = new File(SETTINGS_FILE);
            if (settingsFile.exists()) {
                try (FileInputStream fis = new FileInputStream(settingsFile)) {
                    properties.load(fis);
                }
                System.out.println("تم تحميل الإعدادات من الملف");
            } else {
                // إعدادات افتراضية
                properties.setProperty(THEME_KEY, "فاتح");
                properties.setProperty(LANGUAGE_KEY, "العربية");
                System.out.println("تم تحميل الإعدادات الافتراضية");
            }
        } catch (IOException e) {
            System.err.println("خطأ في تحميل الإعدادات: " + e.getMessage());
            // إعدادات افتراضية في حالة الخطأ
            properties.setProperty(THEME_KEY, "فاتح");
            properties.setProperty(LANGUAGE_KEY, "العربية");
        }
        
        return properties;
    }
    
    /**
     * تطبيق المظهر المحفوظ عند بدء التطبيق
     */
    public static void applyStoredTheme() {
        Properties settings = loadSettings();
        String savedTheme = settings.getProperty(THEME_KEY, "فاتح");
        
        try {
            if (savedTheme.equals("فاتح")) {
                applyLightTheme();
            } else if (savedTheme.equals("مظلم")) {
                applyDarkTheme();
            } else if (savedTheme.equals("تلقائي")) {
                applySystemTheme();
            } else if (savedTheme.equals("مخصص")) {
                applyCustomTheme();
            }
            
            System.out.println("تم تطبيق المظهر المحفوظ: " + savedTheme);
            
        } catch (Exception e) {
            System.err.println("خطأ في تطبيق المظهر المحفوظ: " + e.getMessage());
        }
    }
    
    /**
     * الحصول على المظهر المحفوظ
     */
    public static String getSavedTheme() {
        Properties settings = loadSettings();
        return settings.getProperty(THEME_KEY, "فاتح");
    }
    
    /**
     * الحصول على اللغة المحفوظة
     */
    public static String getSavedLanguage() {
        Properties settings = loadSettings();
        return settings.getProperty(LANGUAGE_KEY, "العربية");
    }
    
    /**
     * تطبيق المظهر الفاتح
     */
    private static void applyLightTheme() throws Exception {
        for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
            if ("Nimbus".equals(info.getName()) || "Windows".equals(info.getName()) || "Metal".equals(info.getName())) {
                UIManager.setLookAndFeel(info.getClassName());
                break;
            }
        }
        
        UIManager.put("Panel.background", new Color(255, 255, 255));
        UIManager.put("Button.background", new Color(248, 249, 250));
        UIManager.put("TextField.background", Color.WHITE);
        UIManager.put("ComboBox.background", Color.WHITE);
        UIManager.put("TabbedPane.background", new Color(248, 249, 250));
        UIManager.put("TabbedPane.selected", Color.WHITE);
        UIManager.put("Label.foreground", new Color(33, 37, 41));
        UIManager.put("Button.foreground", new Color(33, 37, 41));
    }
    
    /**
     * تطبيق المظهر المظلم
     */
    private static void applyDarkTheme() throws Exception {
        for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
            if ("Metal".equals(info.getName())) {
                UIManager.setLookAndFeel(info.getClassName());
                break;
            }
        }
        
        UIManager.put("Panel.background", new Color(33, 37, 41));
        UIManager.put("Button.background", new Color(52, 58, 64));
        UIManager.put("TextField.background", new Color(52, 58, 64));
        UIManager.put("ComboBox.background", new Color(52, 58, 64));
        UIManager.put("TabbedPane.background", new Color(33, 37, 41));
        UIManager.put("TabbedPane.selected", new Color(52, 58, 64));
        UIManager.put("Label.foreground", new Color(248, 249, 250));
        UIManager.put("Button.foreground", new Color(248, 249, 250));
        UIManager.put("TextField.foreground", new Color(248, 249, 250));
        UIManager.put("ComboBox.foreground", new Color(248, 249, 250));
        UIManager.put("TabbedPane.foreground", new Color(248, 249, 250));
    }
    
    /**
     * تطبيق المظهر التلقائي
     */
    private static void applySystemTheme() throws Exception {
        for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
            if ("Windows".equals(info.getName()) || "Aqua".equals(info.getName()) || "GTK+".equals(info.getName())) {
                UIManager.setLookAndFeel(info.getClassName());
                return;
            }
        }
        for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
            if ("Nimbus".equals(info.getName())) {
                UIManager.setLookAndFeel(info.getClassName());
                break;
            }
        }
    }
    
    /**
     * تطبيق المظهر المخصص
     */
    private static void applyCustomTheme() throws Exception {
        for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
            if ("Nimbus".equals(info.getName())) {
                UIManager.setLookAndFeel(info.getClassName());
                break;
            }
        }
        
        UIManager.put("Panel.background", new Color(240, 248, 255));
        UIManager.put("Button.background", new Color(0, 123, 255));
        UIManager.put("TextField.background", Color.WHITE);
        UIManager.put("ComboBox.background", Color.WHITE);
        UIManager.put("TabbedPane.background", new Color(240, 248, 255));
        UIManager.put("TabbedPane.selected", Color.WHITE);
        UIManager.put("Label.foreground", new Color(0, 86, 179));
        UIManager.put("Button.foreground", Color.WHITE);
        UIManager.put("TabbedPane.foreground", new Color(0, 86, 179));
    }
}
