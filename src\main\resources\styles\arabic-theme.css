/* نمط عربي متقدم لنظام إدارة الشحنات */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* الجذر - إعدادات عامة */
.root {
    -fx-font-family: "Noto Sans Arabic", "Segoe UI", "Tahoma", sans-serif;
    -fx-font-size: 14px;
    -fx-base: #f8f9fa;
    -fx-background: #ffffff;
    -fx-control-inner-background: #ffffff;
    -fx-control-inner-background-alt: #f8f9fa;
    -fx-accent: #007bff;
    -fx-default-button: #007bff;
    -fx-focus-color: #007bff;
    -fx-faint-focus-color: rgba(0, 123, 255, 0.1);
    -fx-selection-bar: #007bff;
    -fx-selection-bar-non-focused: #6c757d;
    -fx-text-fill: #212529;
    -fx-node-orientation: right-to-left;
}

/* شريط القوائم */
.menu-bar {
    -fx-background-color: #343a40;
    -fx-border-color: #495057;
    -fx-border-width: 0 0 1 0;
}

.menu-bar .menu {
    -fx-text-fill: #ffffff;
    -fx-font-weight: 500;
}

.menu-bar .menu:hover {
    -fx-background-color: #495057;
}

.menu-bar .menu:showing {
    -fx-background-color: #007bff;
}

.context-menu {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 10, 0, 0, 2);
}

.menu-item {
    -fx-text-fill: #212529;
    -fx-padding: 8 16 8 16;
}

.menu-item:hover {
    -fx-background-color: #f8f9fa;
}

/* شريط الأدوات */
.tool-bar {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
    -fx-padding: 8;
    -fx-spacing: 8;
}

.tool-bar .button {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-text-fill: #495057;
    -fx-padding: 8 12 8 12;
    -fx-background-radius: 4;
    -fx-border-radius: 4;
}

.tool-bar .button:hover {
    -fx-background-color: #e9ecef;
    -fx-text-fill: #212529;
}

.tool-bar .button:pressed {
    -fx-background-color: #dee2e6;
}

/* الأزرار */
.button {
    -fx-background-color: #6c757d;
    -fx-text-fill: #ffffff;
    -fx-font-weight: 500;
    -fx-padding: 8 16 8 16;
    -fx-background-radius: 4;
    -fx-border-radius: 4;
    -fx-cursor: hand;
}

.button:hover {
    -fx-background-color: #5a6268;
}

.button:pressed {
    -fx-background-color: #545b62;
}

.button:disabled {
    -fx-opacity: 0.6;
    -fx-cursor: default;
}

/* الزر الأساسي */
.primary-button {
    -fx-background-color: #007bff;
    -fx-text-fill: #ffffff;
}

.primary-button:hover {
    -fx-background-color: #0056b3;
}

.primary-button:pressed {
    -fx-background-color: #004085;
}

/* الزر الثانوي */
.secondary-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: #ffffff;
}

.secondary-button:hover {
    -fx-background-color: #5a6268;
}

/* زر النجاح */
.success-button {
    -fx-background-color: #28a745;
    -fx-text-fill: #ffffff;
}

.success-button:hover {
    -fx-background-color: #218838;
}

/* زر الخطر */
.danger-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: #ffffff;
}

.danger-button:hover {
    -fx-background-color: #c82333;
}

/* زر صغير */
.small-button {
    -fx-font-size: 12px;
    -fx-padding: 4 8 4 8;
}

/* حقول النص */
.text-field, .text-area, .password-field {
    -fx-background-color: #ffffff;
    -fx-border-color: #ced4da;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
    -fx-padding: 8 12 8 12;
    -fx-text-fill: #495057;
    -fx-prompt-text-fill: #6c757d;
}

.text-field:focused, .text-area:focused, .password-field:focused {
    -fx-border-color: #007bff;
    -fx-effect: dropshadow(gaussian, rgba(0, 123, 255, 0.25), 0, 0, 0, 0);
}

.text-field:error, .text-area:error, .password-field:error {
    -fx-border-color: #dc3545;
}

/* التسميات */
.label {
    -fx-text-fill: #495057;
    -fx-font-weight: 500;
}

.page-title {
    -fx-font-size: 24px;
    -fx-font-weight: 700;
    -fx-text-fill: #212529;
    -fx-padding: 0 0 16 0;
}

.group-title {
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-text-fill: #495057;
    -fx-padding: 0 0 8 0;
}

.sidebar-title {
    -fx-font-size: 18px;
    -fx-font-weight: 600;
    -fx-text-fill: #212529;
    -fx-padding: 0 0 12 0;
}

.welcome-title {
    -fx-font-size: 32px;
    -fx-font-weight: 700;
    -fx-text-fill: #007bff;
}

/* الشريط الجانبي */
.sidebar {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 1 0 0;
}

/* القائمة الشجرية */
.tree-view {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.tree-view .tree-cell {
    -fx-background-color: transparent;
    -fx-text-fill: #495057;
    -fx-padding: 4 8 4 8;
    -fx-font-weight: 500;
}

.tree-view .tree-cell:selected {
    -fx-background-color: #007bff;
    -fx-text-fill: #ffffff;
}

.tree-view .tree-cell:hover {
    -fx-background-color: #e9ecef;
}

/* الجداول */
.table-view {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
}

.table-view .column-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
    -fx-text-fill: #495057;
    -fx-font-weight: 600;
    -fx-padding: 12 8 12 8;
}

.table-view .table-cell {
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
    -fx-padding: 8;
    -fx-text-fill: #495057;
}

.table-view .table-row-cell:selected {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
}

.table-view .table-row-cell:hover {
    -fx-background-color: #f5f5f5;
}

/* التبويبات */
.tab-pane {
    -fx-background-color: #ffffff;
}

.tab-pane .tab-header-area {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
}

.tab-pane .tab {
    -fx-background-color: transparent;
    -fx-text-fill: #495057;
    -fx-font-weight: 500;
    -fx-padding: 12 16 12 16;
}

.tab-pane .tab:selected {
    -fx-background-color: #ffffff;
    -fx-text-fill: #007bff;
    -fx-border-color: #007bff;
    -fx-border-width: 0 0 2 0;
}

.tab-pane .tab:hover {
    -fx-background-color: #e9ecef;
}

/* منطقة المحتوى */
.content-area {
    -fx-background-color: #ffffff;
    -fx-padding: 16;
}

/* مجموعات الإعدادات */
.settings-group {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-padding: 16;
    -fx-spacing: 12;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 5, 0, 0, 1);
}

/* البطاقات السريعة */
.quick-card {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 8, 0, 0, 2);
    -fx-cursor: hand;
}

.quick-card:hover {
    -fx-border-color: #007bff;
    -fx-effect: dropshadow(gaussian, rgba(0, 123, 255, 0.3), 12, 0, 0, 4);
}

/* شريط الحالة */
.status-bar {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1 0 0 0;
    -fx-text-fill: #6c757d;
    -fx-font-size: 12px;
}

/* مربعات الاختيار */
.check-box {
    -fx-text-fill: #495057;
}

.check-box .box {
    -fx-background-color: #ffffff;
    -fx-border-color: #ced4da;
    -fx-border-width: 1;
    -fx-border-radius: 3;
    -fx-background-radius: 3;
}

.check-box:selected .box {
    -fx-background-color: #007bff;
    -fx-border-color: #007bff;
}

.check-box:selected .mark {
    -fx-background-color: #ffffff;
}

/* القوائم المنسدلة */
.combo-box {
    -fx-background-color: #ffffff;
    -fx-border-color: #ced4da;
    -fx-border-width: 1;
    -fx-border-radius: 4;
    -fx-background-radius: 4;
}

.combo-box:focused {
    -fx-border-color: #007bff;
}

.combo-box .list-cell {
    -fx-text-fill: #495057;
    -fx-padding: 8 12 8 12;
}

/* أشرطة التمرير */
.scroll-bar {
    -fx-background-color: #f8f9fa;
}

.scroll-bar .thumb {
    -fx-background-color: #ced4da;
    -fx-background-radius: 4;
}

.scroll-bar .thumb:hover {
    -fx-background-color: #adb5bd;
}

/* الفواصل */
.separator {
    -fx-background-color: #dee2e6;
}

/* رسائل الخطأ */
.error-label {
    -fx-text-fill: #dc3545;
    -fx-font-size: 12px;
    -fx-font-weight: 500;
}

/* رسائل النجاح */
.success-label {
    -fx-text-fill: #28a745;
    -fx-font-size: 12px;
    -fx-font-weight: 500;
}

/* رسائل التحذير */
.warning-label {
    -fx-text-fill: #ffc107;
    -fx-font-size: 12px;
    -fx-font-weight: 500;
}

/* نوافذ الحوار */
.dialog-pane {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 20, 0, 0, 5);
}

.dialog-pane .header-panel {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
}

/* تحسينات للطباعة */
@media print {
    .root {
        -fx-background-color: white;
    }
    
    .button, .tool-bar {
        -fx-opacity: 0;
    }
}
