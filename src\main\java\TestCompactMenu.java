import javax.swing.*;
import java.awt.*;

/**
 * اختبار القائمة المضغوطة
 */
public class TestCompactMenu extends JFrame {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new TestCompactMenu().setVisible(true);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, "خطأ: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    public TestCompactMenu() {
        setTitle("اختبار القائمة المضغوطة");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1000, 700);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // إنشاء النافذة الرئيسية المحسنة
        try {
            EnhancedMainWindow mainWindow = new EnhancedMainWindow();
            mainWindow.setVisible(true);
            this.dispose(); // إغلاق نافذة الاختبار
        } catch (Exception e) {
            // في حالة فشل النافذة الرئيسية، إنشاء اختبار بسيط
            createSimpleTest();
        }
    }
    
    private void createSimpleTest() {
        setLayout(new BorderLayout());
        
        // إنشاء شجرة القائمة
        TreeMenuPanel treePanel = new TreeMenuPanel(this);
        
        // لوحة المحتوى
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.setBackground(Color.WHITE);
        contentPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        JLabel welcomeLabel = new JLabel("مرحباً بك في نظام إدارة الشحنات");
        welcomeLabel.setFont(new Font("Tahoma", Font.BOLD, 16));
        welcomeLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        welcomeLabel.setHorizontalAlignment(SwingConstants.CENTER);
        contentPanel.add(welcomeLabel, BorderLayout.CENTER);
        
        // إنشاء JSplitPane
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        splitPane.setRightComponent(treePanel);
        splitPane.setLeftComponent(contentPanel);
        splitPane.setDividerLocation(150); // العرض المضغوط الجديد
        splitPane.setResizeWeight(0.0);
        splitPane.setOneTouchExpandable(true);
        
        add(splitPane, BorderLayout.CENTER);
        
        // شريط الحالة
        JLabel statusLabel = new JLabel("تم تطبيق العرض المضغوط الجديد - عرض القائمة: 150 بكسل");
        statusLabel.setFont(new Font("Tahoma", Font.PLAIN, 12));
        statusLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        statusLabel.setBackground(new Color(240, 240, 240));
        statusLabel.setOpaque(true);
        add(statusLabel, BorderLayout.SOUTH);
    }
}
