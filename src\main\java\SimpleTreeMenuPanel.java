import javax.swing.*;
import javax.swing.tree.*;
import java.awt.*;

/**
 * لوحة القائمة الشجرية المبسطة للاختبار
 */
public class SimpleTreeMenuPanel extends JPanel {
    
    private JTree menuTree;
    private DefaultMutableTreeNode rootNode;
    private Font arabicFont;
    
    public SimpleTreeMenuPanel() {
        this.arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        createTreeStructure();
        setupTreeComponents();
        setupLayout();
    }
    
    private void createTreeStructure() {
        // إنشاء العقدة الجذر
        rootNode = new DefaultMutableTreeNode("الأنظمة الرئيسية");
        
        // إدارة الأصناف
        DefaultMutableTreeNode itemsNode = new DefaultMutableTreeNode("إدارة الأصناف");
        itemsNode.add(new DefaultMutableTreeNode("بيانات الأصناف"));
        itemsNode.add(new DefaultMutableTreeNode("مجموعات الأصناف"));
        itemsNode.add(new DefaultMutableTreeNode("وحدات القياس"));
        rootNode.add(itemsNode);
        
        // إدارة الشحنات
        DefaultMutableTreeNode shipmentsNode = new DefaultMutableTreeNode("إدارة الشحنات");
        shipmentsNode.add(new DefaultMutableTreeNode("شحنات جديدة"));
        shipmentsNode.add(new DefaultMutableTreeNode("تتبع الشحنات"));
        shipmentsNode.add(new DefaultMutableTreeNode("تقارير الشحنات"));
        rootNode.add(shipmentsNode);
        
        // إدارة العملاء
        DefaultMutableTreeNode customersNode = new DefaultMutableTreeNode("إدارة العملاء");
        customersNode.add(new DefaultMutableTreeNode("بيانات العملاء"));
        customersNode.add(new DefaultMutableTreeNode("فئات العملاء"));
        customersNode.add(new DefaultMutableTreeNode("تقارير العملاء"));
        rootNode.add(customersNode);
        
        // التقارير
        DefaultMutableTreeNode reportsNode = new DefaultMutableTreeNode("التقارير");
        reportsNode.add(new DefaultMutableTreeNode("تقارير الأصناف"));
        reportsNode.add(new DefaultMutableTreeNode("تقارير الشحنات"));
        reportsNode.add(new DefaultMutableTreeNode("تقارير العملاء"));
        reportsNode.add(new DefaultMutableTreeNode("تقارير مالية"));
        rootNode.add(reportsNode);
        
        // الإعدادات
        DefaultMutableTreeNode settingsNode = new DefaultMutableTreeNode("الإعدادات");
        settingsNode.add(new DefaultMutableTreeNode("إعدادات عامة"));
        settingsNode.add(new DefaultMutableTreeNode("إدارة المستخدمين"));
        settingsNode.add(new DefaultMutableTreeNode("النسخ الاحتياطي"));
        rootNode.add(settingsNode);
    }
    
    private void setupTreeComponents() {
        // إنشاء نموذج الشجرة
        DefaultTreeModel treeModel = new DefaultTreeModel(rootNode);
        
        // إنشاء الشجرة
        menuTree = new JTree(treeModel);
        menuTree.setFont(arabicFont);
        menuTree.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        menuTree.setRootVisible(true);
        menuTree.setShowsRootHandles(true);
        
        // تخصيص مظهر الشجرة
        menuTree.setRowHeight(25);
        menuTree.setBackground(new Color(250, 250, 250));
        
        // توسيع العقد الرئيسية
        for (int i = 0; i < menuTree.getRowCount(); i++) {
            menuTree.expandRow(i);
        }
        
        // معالج النقر
        menuTree.addTreeSelectionListener(e -> {
            DefaultMutableTreeNode selectedNode = 
                (DefaultMutableTreeNode) menuTree.getLastSelectedPathComponent();
            
            if (selectedNode != null && selectedNode.isLeaf()) {
                String nodeName = selectedNode.toString();
                JOptionPane.showMessageDialog(this, 
                    "تم اختيار: " + nodeName, 
                    "اختيار من القائمة", 
                    JOptionPane.INFORMATION_MESSAGE);
            }
        });
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // إنشاء لوحة التمرير
        JScrollPane scrollPane = new JScrollPane(menuTree);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createTitledBorder("القائمة الرئيسية"),
                BorderFactory.createEmptyBorder(5, 5, 5, 5)));
        
        // تطبيق الأبعاد المضغوطة الجديدة
        scrollPane.setPreferredSize(new Dimension(150, 400));
        scrollPane.setMinimumSize(new Dimension(140, 300));
        
        add(scrollPane, BorderLayout.CENTER);
        
        // تذييل مع معلومات
        JPanel footerPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        footerPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        footerPanel.setBackground(new Color(240, 240, 240));
        
        JLabel infoLabel = new JLabel("عرض مضغوط: 150px");
        infoLabel.setFont(new Font("Tahoma", Font.PLAIN, 10));
        infoLabel.setForeground(Color.GRAY);
        footerPanel.add(infoLabel);
        
        add(footerPanel, BorderLayout.SOUTH);
    }
    
    public void expandAllNodes() {
        for (int i = 0; i < menuTree.getRowCount(); i++) {
            menuTree.expandRow(i);
        }
    }
    
    public void collapseAllNodes() {
        for (int i = menuTree.getRowCount() - 1; i >= 1; i--) {
            menuTree.collapseRow(i);
        }
    }
}
