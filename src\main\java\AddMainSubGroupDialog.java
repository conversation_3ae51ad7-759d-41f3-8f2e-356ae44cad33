import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.Frame;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JComponent;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JTextField;

/**
 * نافذة إضافة مجموعة فرعية جديدة Add Main Sub Group Dialog
 */
public class AddMainSubGroupDialog extends JDialog {

    private Connection connection;
    private boolean saved = false;

    // مكونات الواجهة
    private JComboBox<String> mainGroupCombo;
    private JTextField subCodeField;
    private JTextField arabicNameField;
    private JTextField englishNameField;
    private JTextField imageCodeField;
    private JTextField orderField;
    private JCheckBox webSyncCheckBox;
    private JCheckBox activeCheckBox;

    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 13);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 13);

    public AddMainSubGroupDialog(Frame parent, Connection connection) {
        super(parent, "إضافة مجموعة فرعية جديدة", true);
        this.connection = connection;

        initializeComponents();
        setupLayout();
        loadMainGroups();
        setDefaultValues();

        setSize(650, 500);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }

    private void initializeComponents() {
        // إنشاء الحقول
        mainGroupCombo = new JComboBox<>();
        subCodeField = new JTextField(20);
        subCodeField.setEnabled(false); // إلغاء تفعيل كود المجموعة (توليد تلقائي)
        arabicNameField = new JTextField(30);
        englishNameField = new JTextField(30);
        imageCodeField = new JTextField(20);
        orderField = new JTextField(15);

        // إنشاء صناديق الاختيار
        webSyncCheckBox = new JCheckBox("مزامنة الويب");
        activeCheckBox = new JCheckBox("نشط");

        // تطبيق الخط العربي وتحسين المظهر
        JComponent[] textComponents = {mainGroupCombo, subCodeField, arabicNameField,
                englishNameField, imageCodeField, orderField};
        for (JComponent component : textComponents) {
            component.setFont(arabicFont);
            component.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            component.setPreferredSize(new Dimension(250, 30));
            if (component instanceof JTextField) {
                component.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(Color.GRAY),
                        BorderFactory.createEmptyBorder(5, 10, 5, 10)));
            }
        }

        JCheckBox[] checkBoxes = {webSyncCheckBox, activeCheckBox};
        for (JCheckBox checkBox : checkBoxes) {
            checkBox.setFont(arabicFont);
            checkBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            checkBox.setPreferredSize(new Dimension(300, 25));
        }

        // إضافة مستمع لتغيير المجموعة الرئيسية
        mainGroupCombo.addActionListener(e -> generateSubCode());
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.EAST;
        gbc.fill = GridBagConstraints.HORIZONTAL;

        int row = 0;

        // المجموعة الرئيسية
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.3;
        mainPanel.add(createLabel("المجموعة الرئيسية *:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 0.7;
        mainPanel.add(mainGroupCombo, gbc);
        row++;

        // كود المجموعة الفرعية
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.3;
        mainPanel.add(createLabel("كود المجموعة الفرعية (تلقائي):"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 0.7;
        mainPanel.add(subCodeField, gbc);
        row++;

        // الاسم العربي
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("الاسم العربي *:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(arabicNameField, gbc);
        row++;

        // الاسم الإنجليزي
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("الاسم الإنجليزي:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(englishNameField, gbc);
        row++;

        // كود الصورة
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("كود الصورة:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(imageCodeField, gbc);
        row++;

        // الترتيب
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("الترتيب:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(orderField, gbc);
        row++;

        // صناديق الاختيار
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        mainPanel.add(webSyncCheckBox, gbc);
        row++;

        gbc.gridy = row;
        mainPanel.add(activeCheckBox, gbc);

        add(mainPanel, BorderLayout.CENTER);

        // لوحة الأزرار
        JPanel buttonPanel = createButtonPanel();
        add(buttonPanel, BorderLayout.SOUTH);
    }

    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicBoldFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }

    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        JButton saveButton = new JButton("💾 حفظ");
        saveButton.setFont(arabicBoldFont);
        saveButton.setBackground(new Color(39, 174, 96));
        saveButton.setForeground(Color.WHITE);
        saveButton.setPreferredSize(new Dimension(100, 35));
        saveButton.addActionListener(e -> saveMainSubGroup());

        JButton cancelButton = new JButton("❌ إلغاء");
        cancelButton.setFont(arabicBoldFont);
        cancelButton.setBackground(new Color(231, 76, 60));
        cancelButton.setForeground(Color.WHITE);
        cancelButton.setPreferredSize(new Dimension(100, 35));
        cancelButton.addActionListener(e -> dispose());

        panel.add(saveButton);
        panel.add(cancelButton);

        return panel;
    }

    private void loadMainGroups() {
        try {
            String sql =
                    "SELECT G_CODE, G_A_NAME FROM ERP_GROUP_DETAILS WHERE IS_ACTIVE = 1 ORDER BY G_CODE";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            mainGroupCombo.removeAllItems();
            mainGroupCombo.addItem("-- اختر المجموعة الرئيسية --");

            while (rs.next()) {
                String code = rs.getString("G_CODE");
                String name = rs.getString("G_A_NAME");
                mainGroupCombo.addItem(code + " - " + name);
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات الرئيسية: " + e.getMessage());
        }
    }

    private void generateSubCode() {
        if (mainGroupCombo.getSelectedIndex() <= 0) {
            subCodeField.setText("");
            return;
        }

        try {
            String selectedItem = (String) mainGroupCombo.getSelectedItem();
            String mainGroupCode = selectedItem.split(" - ")[0];

            String sql =
                    """
                                SELECT ? || LPAD(NVL(MAX(TO_NUMBER(SUBSTR(MNG_CODE, LENGTH(?) + 1))), 0) + 1, 2, '0')
                                FROM ERP_MAINSUB_GRP_DTL
                                WHERE G_CODE = ? AND MNG_CODE LIKE ? || '%'
                            """;

            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, mainGroupCode);
            pstmt.setString(2, mainGroupCode);
            pstmt.setString(3, mainGroupCode);
            pstmt.setString(4, mainGroupCode);

            ResultSet rs = pstmt.executeQuery();

            if (rs.next()) {
                String nextCode = rs.getString(1);
                subCodeField.setText(nextCode);
            } else {
                subCodeField.setText(mainGroupCode + "01");
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في توليد كود المجموعة الفرعية: " + e.getMessage());
        }
    }

    private void setDefaultValues() {
        orderField.setText("1");
        activeCheckBox.setSelected(true);
        webSyncCheckBox.setSelected(false);
    }

    private void saveMainSubGroup() {
        // التحقق من صحة البيانات
        if (!validateInput()) {
            return;
        }

        try {
            String selectedItem = (String) mainGroupCombo.getSelectedItem();
            String mainGroupCode = selectedItem.split(" - ")[0];

            String sql = """
                        INSERT INTO ERP_MAINSUB_GRP_DTL
                        (G_CODE, MNG_CODE, MNG_A_NAME, MNG_E_NAME, MNG_I_CODE,
                         SYNCHRNZ_TO_WEB_FLG, MNG_ORDR, IS_ACTIVE, CREATED_BY, CREATED_DATE)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'USER', SYSDATE)
                    """;

            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, mainGroupCode);
            pstmt.setString(2, subCodeField.getText().trim());
            pstmt.setString(3, arabicNameField.getText().trim());
            pstmt.setString(4, englishNameField.getText().trim());
            pstmt.setString(5, imageCodeField.getText().trim());
            pstmt.setInt(6, webSyncCheckBox.isSelected() ? 1 : 0);

            // الترتيب
            String orderText = orderField.getText().trim();
            if (orderText.isEmpty()) {
                pstmt.setNull(7, Types.INTEGER);
            } else {
                pstmt.setInt(7, Integer.parseInt(orderText));
            }

            pstmt.setInt(8, activeCheckBox.isSelected() ? 1 : 0);

            pstmt.executeUpdate();
            connection.commit();

            saved = true;
            JOptionPane.showMessageDialog(this, "تم حفظ المجموعة الفرعية بنجاح!", "نجح الحفظ",
                    JOptionPane.INFORMATION_MESSAGE);

            dispose();

        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }

            JOptionPane.showMessageDialog(this, "خطأ في حفظ المجموعة الفرعية:\n" + e.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    private boolean validateInput() {
        // التحقق من اختيار المجموعة الرئيسية
        if (mainGroupCombo.getSelectedIndex() <= 0) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار المجموعة الرئيسية", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            mainGroupCombo.requestFocus();
            return false;
        }

        // التحقق من الحقول المطلوبة
        if (subCodeField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "كود المجموعة الفرعية مطلوب", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            subCodeField.requestFocus();
            return false;
        }

        if (arabicNameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "الاسم العربي مطلوب", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            arabicNameField.requestFocus();
            return false;
        }

        // التحقق من صحة الترتيب
        try {
            String orderText = orderField.getText().trim();
            if (!orderText.isEmpty()) {
                Integer.parseInt(orderText);
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال رقم صحيح للترتيب", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            orderField.requestFocus();
            return false;
        }

        // التحقق من عدم تكرار الكود
        try {
            String sql = "SELECT COUNT(*) FROM ERP_MAINSUB_GRP_DTL WHERE MNG_CODE = ?";
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, subCodeField.getText().trim());
            ResultSet rs = pstmt.executeQuery();

            if (rs.next() && rs.getInt(1) > 0) {
                JOptionPane.showMessageDialog(this, "كود المجموعة الفرعية موجود مسبقاً", "خطأ",
                        JOptionPane.ERROR_MESSAGE);
                subCodeField.requestFocus();
                return false;
            }

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في التحقق من الكود", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        return true;
    }

    public boolean isSaved() {
        return saved;
    }
}
