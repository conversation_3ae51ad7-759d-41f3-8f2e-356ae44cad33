import javax.swing.JOptionPane;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * النقطة الرئيسية لتشغيل نظام إدارة الشحنات Main Entry Point for Ship ERP System
 */
public class ShipERPMain {

    public static void main(String[] args) {
        // تعيين خصائص النظام للعربية
        System.setProperty("user.language", "ar");
        System.setProperty("user.country", "SA");
        System.setProperty("file.encoding", "UTF-8");

        // تعيين Look and Feel
        try {
            for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
                if ("Windows".equals(info.getName())) {
                    UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (Exception e) {
            System.err.println("تعذر تعيين مظهر النظام: " + e.getMessage());
        }

        // تشغيل النافذة الرئيسية في Event Dispatch Thread
        SwingUtilities.invokeLater(() -> {
            try {
                System.out.println("🚀 بدء تشغيل نظام إدارة الشحنات...");

                // إنشاء النافذة الرئيسية
                EnhancedMainWindow mainWindow = new EnhancedMainWindow();
                mainWindow.setVisible(true);

                System.out.println("✅ تم تشغيل النظام بنجاح");

            } catch (Exception e) {
                System.err.println("❌ خطأ في تشغيل النظام: " + e.getMessage());
                e.printStackTrace();

                // إظهار رسالة خطأ للمستخدم
                JOptionPane.showMessageDialog(null, "خطأ في تشغيل النظام:\n" + e.getMessage(),
                        "خطأ في النظام", JOptionPane.ERROR_MESSAGE);

                System.exit(1);
            }
        });
    }
}
