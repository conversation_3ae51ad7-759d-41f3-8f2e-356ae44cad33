@echo off
chcp 65001 > nul
title نظام إدارة الأصناف مع Oracle - الحل النهائي

echo ====================================
echo    نظام إدارة الأصناف مع Oracle
echo    ERP System with Oracle Support
echo    الحل النهائي للجداول المحددة
echo ====================================
echo.

echo [1/5] فحص المكتبات المطلوبة...
if not exist "lib\ojdbc11.jar" (
    echo ❌ مكتبة Oracle JDBC مفقودة!
    echo تشغيل LibraryDownloader...
    java LibraryDownloader
    if errorlevel 1 (
        echo ❌ فشل في تحميل المكتبات!
        echo.
        echo الحلول المقترحة:
        echo 1. تشغيل: java LibraryDownloader
        echo 2. التأكد من الاتصال بالإنترنت
        echo 3. فحص صلاحيات الكتابة في مجلد lib
        pause
        exit /b 1
    )
)

if not exist "lib\orai18n.jar" (
    echo ❌ مكتبة Oracle Internationalization مفقودة!
    echo هذه المكتبة مطلوبة لحل خطأ ORA-17056
    echo تشغيل LibraryDownloader...
    java LibraryDownloader
    if errorlevel 1 (
        echo ❌ فشل في تحميل المكتبات!
        echo.
        echo ملاحظة: مكتبة orai18n.jar مطلوبة لحل خطأ:
        echo ORA-17056: مجموعة أحرف غير مدعومة AR8MSWIN1256
        pause
        exit /b 1
    )
)

echo ✅ جميع المكتبات المطلوبة موجودة
echo   - ojdbc11.jar (Oracle JDBC Driver)
echo   - orai18n.jar (دعم الأحرف العربية - حل ORA-17056)
echo.

echo [2/5] تجميع النظام مع المكتبات...
javac -encoding UTF-8 -cp "lib/*;." *.java
if errorlevel 1 (
    echo ❌ فشل في التجميع!
    echo تحقق من وجود ملفات Java المطلوبة
    pause
    exit /b 1
)

echo ✅ تم التجميع بنجاح
echo.

echo [3/5] اختبار المكتبات...
echo فحص تحميل مكتبات Oracle...
java -cp "lib/*;." QuickLibraryTest
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في تحميل المكتبات
)
echo.

echo [4/5] فحص الاتصال بـ Oracle...
echo بيانات الاتصال الافتراضية:
echo   الخادم: localhost:1521:orcl
echo   المستخدم: ysdba2
echo   الجداول المستهدفة: IAS20251.IAS_ITM_MST و IAS20251.IAS_ITM_DTL
echo.

echo [5/5] تشغيل النظام...
echo.
echo ====================================
echo    بدء تشغيل النظام - الحل النهائي
echo ====================================
echo.
echo ملاحظات مهمة:
echo • النظام يعمل مع إعدادات اللغة الإنجليزية لتجنب مشاكل regex
echo • مكتبة orai18n.jar محملة لدعم الأحرف العربية وحل ORA-17056
echo • جميع مكتبات Oracle متاحة في classpath
echo • الجداول المحددة: IAS_ITM_MST (4647 صنف) و IAS_ITM_DTL (9108 تفصيل)
echo • استيراد البيانات الفعلية من المستخدم IAS20251
echo.
echo للاستيراد:
echo 1. اذهب إلى: إدارة الأصناف → ربط النظام واستيراد البيانات
echo 2. اضغط زر "📥 استيراد IAS" لاستيراد 4630 صنف نشط
echo.

java -Duser.language=en -Duser.country=US -Dfile.encoding=UTF-8 -cp "lib/*;." CompleteSystemTest

echo.
echo ====================================
echo    انتهى تشغيل النظام
echo ====================================
echo.
echo إذا واجهت مشكلة "مكتبات Oracle مفقودة":
echo 1. أعد تشغيل هذا الملف: START_ERP_WITH_ORACLE.bat
echo 2. أو شغّل: java LibraryDownloader
echo 3. أو شغّل: CHECK_ORACLE_LIBRARIES.bat للفحص
echo.
pause
