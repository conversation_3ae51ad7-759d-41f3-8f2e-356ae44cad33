-- =============================================================================
-- سكريبت إنشاء جدول وحدات القياس المحسن
-- Enhanced Measurement Units Table Creation Script
-- =============================================================================
-- التاريخ: 2025-07-15
-- الهدف: إنشاء جدول وحدات القياس المحسن مبني على جدول MEASUREMENT الأصلي
-- =============================================================================

-- حذف الجدول إذا كان موجوداً (للاختبار فقط)
-- DROP TABLE ERP_MEASUREMENT_UNITS CASCADE CONSTRAINTS;

-- إنشاء جدول وحدات القياس المحسن
CREATE TABLE ERP_MEASUREMENT_UNITS (
    -- الحقول الأساسية
    MEASURE_CODE VARCHAR2(10) NOT NULL,
    MEASURE_NAME VARCHAR2(100) NOT NULL,
    MEASURE_NAME_EN VARCHAR2(100),
    MEASURE_CODE_GB VARCHAR2(10),
    
    -- خصائص وحدة القياس
    MEASURE_TYPE NUMBER(1) DEFAULT 1,
    MEASURE_WT_TYPE NUMBER(2),
    MEASURE_WT_CONN NUMBER(1) DEFAULT 0,
    DFLT_SIZE NUMBER(22,4),
    ALLOW_UPD NUMBER(1) DEFAULT 1,
    UNT_SALE_TYP NUMBER(2) DEFAULT 3,
    
    -- الحقول المحسنة الجديدة
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    DESCRIPTION VARCHAR2(500),
    SYMBOL VARCHAR2(10),
    CONVERSION_FACTOR NUMBER(22,6) DEFAULT 1,
    
    -- حقول التدقيق
    AD_U_ID NUMBER(5),
    AD_DATE DATE DEFAULT SYSDATE,
    UP_U_ID NUMBER(5),
    UP_DATE DATE,
    UP_CNT NUMBER(10) DEFAULT 0,
    AD_TRMNL_NM VARCHAR2(50),
    UP_TRMNL_NM VARCHAR2(50),
    
    -- المفتاح الأساسي
    CONSTRAINT PK_ERP_MEASUREMENT_UNITS PRIMARY KEY (MEASURE_CODE)
);

-- إضافة التعليقات للجدول
COMMENT ON TABLE ERP_MEASUREMENT_UNITS IS 'جدول وحدات القياس المحسن - Enhanced Measurement Units Table';

-- إضافة التعليقات للأعمدة
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.MEASURE_CODE IS 'كود وحدة القياس (مفتاح أساسي)';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.MEASURE_NAME IS 'اسم وحدة القياس بالعربية';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.MEASURE_NAME_EN IS 'اسم وحدة القياس بالإنجليزية';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.MEASURE_CODE_GB IS 'الكود العالمي لوحدة القياس';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.MEASURE_TYPE IS 'نوع وحدة القياس (1=عادي، 2=وزن، 3=حجم، 4=طول، 5=مساحة)';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.MEASURE_WT_TYPE IS 'نوع الوزن';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.MEASURE_WT_CONN IS 'ربط الوزن';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.DFLT_SIZE IS 'الحجم الافتراضي';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.ALLOW_UPD IS 'السماح بالتحديث (1=نعم، 0=لا)';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.UNT_SALE_TYP IS 'نوع وحدة البيع';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.IS_ACTIVE IS 'حالة النشاط (1=نشط، 0=غير نشط)';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.DESCRIPTION IS 'وصف وحدة القياس';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.SYMBOL IS 'رمز وحدة القياس (مثل: كجم، متر، لتر)';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.CONVERSION_FACTOR IS 'معامل التحويل للوحدة الأساسية';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.AD_U_ID IS 'معرف المستخدم المضيف';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.AD_DATE IS 'تاريخ الإضافة';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.UP_U_ID IS 'معرف المستخدم المحدث';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.UP_DATE IS 'تاريخ التحديث';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.UP_CNT IS 'عدد مرات التحديث';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.AD_TRMNL_NM IS 'اسم الجهاز المضيف';
COMMENT ON COLUMN ERP_MEASUREMENT_UNITS.UP_TRMNL_NM IS 'اسم الجهاز المحدث';

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IDX_ERP_MEASURE_NAME ON ERP_MEASUREMENT_UNITS(MEASURE_NAME);
CREATE INDEX IDX_ERP_MEASURE_ACTIVE ON ERP_MEASUREMENT_UNITS(IS_ACTIVE);
CREATE INDEX IDX_ERP_MEASURE_TYPE ON ERP_MEASUREMENT_UNITS(MEASURE_TYPE);
CREATE INDEX IDX_ERP_MEASURE_SYMBOL ON ERP_MEASUREMENT_UNITS(SYMBOL);

-- إنشاء تسلسل لتوليد أكواد تلقائية (اختياري)
CREATE SEQUENCE SEQ_ERP_MEASURE_CODE
    START WITH 1000
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

COMMENT ON SEQUENCE SEQ_ERP_MEASURE_CODE IS 'تسلسل توليد أكواد وحدات القياس';

-- إدراج بيانات أساسية
INSERT INTO ERP_MEASUREMENT_UNITS (
    MEASURE_CODE, MEASURE_NAME, MEASURE_NAME_EN, SYMBOL, DESCRIPTION, 
    MEASURE_TYPE, IS_ACTIVE, CONVERSION_FACTOR, AD_U_ID, AD_DATE
) VALUES ('PIECE', 'قطعة', 'Piece', 'قطعة', 'وحدة العد الأساسية', 1, 1, 1, 1, SYSDATE);

INSERT INTO ERP_MEASUREMENT_UNITS (
    MEASURE_CODE, MEASURE_NAME, MEASURE_NAME_EN, SYMBOL, DESCRIPTION, 
    MEASURE_TYPE, IS_ACTIVE, CONVERSION_FACTOR, AD_U_ID, AD_DATE
) VALUES ('KG', 'كيلوجرام', 'Kilogram', 'كجم', 'وحدة الوزن الأساسية', 2, 1, 1, 1, SYSDATE);

INSERT INTO ERP_MEASUREMENT_UNITS (
    MEASURE_CODE, MEASURE_NAME, MEASURE_NAME_EN, SYMBOL, DESCRIPTION, 
    MEASURE_TYPE, IS_ACTIVE, CONVERSION_FACTOR, AD_U_ID, AD_DATE
) VALUES ('LITER', 'لتر', 'Liter', 'لتر', 'وحدة الحجم الأساسية', 3, 1, 1, 1, SYSDATE);

INSERT INTO ERP_MEASUREMENT_UNITS (
    MEASURE_CODE, MEASURE_NAME, MEASURE_NAME_EN, SYMBOL, DESCRIPTION, 
    MEASURE_TYPE, IS_ACTIVE, CONVERSION_FACTOR, AD_U_ID, AD_DATE
) VALUES ('METER', 'متر', 'Meter', 'متر', 'وحدة الطول الأساسية', 4, 1, 1, 1, SYSDATE);

INSERT INTO ERP_MEASUREMENT_UNITS (
    MEASURE_CODE, MEASURE_NAME, MEASURE_NAME_EN, SYMBOL, DESCRIPTION, 
    MEASURE_TYPE, IS_ACTIVE, CONVERSION_FACTOR, AD_U_ID, AD_DATE
) VALUES ('SQM', 'متر مربع', 'Square Meter', 'م²', 'وحدة المساحة الأساسية', 5, 1, 1, 1, SYSDATE);

-- تأكيد الإدراج
COMMIT;

-- عرض النتائج
SELECT 
    MEASURE_CODE,
    MEASURE_NAME,
    MEASURE_NAME_EN,
    SYMBOL,
    CASE MEASURE_TYPE
        WHEN 1 THEN 'عادي'
        WHEN 2 THEN 'وزن'
        WHEN 3 THEN 'حجم'
        WHEN 4 THEN 'طول'
        WHEN 5 THEN 'مساحة'
        ELSE 'غير محدد'
    END AS TYPE_NAME,
    CASE IS_ACTIVE
        WHEN 1 THEN 'نشط'
        ELSE 'غير نشط'
    END AS STATUS
FROM ERP_MEASUREMENT_UNITS
ORDER BY MEASURE_NAME;

-- إحصائيات الجدول
SELECT 
    COUNT(*) AS TOTAL_UNITS,
    COUNT(CASE WHEN IS_ACTIVE = 1 THEN 1 END) AS ACTIVE_UNITS,
    COUNT(CASE WHEN IS_ACTIVE = 0 THEN 1 END) AS INACTIVE_UNITS
FROM ERP_MEASUREMENT_UNITS;

-- =============================================================================
-- انتهاء السكريبت
-- =============================================================================

PROMPT 'تم إنشاء جدول وحدات القياس المحسن بنجاح!';
PROMPT 'Enhanced Measurement Units table created successfully!';
PROMPT '';
PROMPT 'الجدول: ERP_MEASUREMENT_UNITS';
PROMPT 'الفهارس: 4 فهارس للأداء';
PROMPT 'البيانات الأساسية: 5 وحدات قياس';
PROMPT '';
PROMPT 'يمكنك الآن استخدام نافذة إدارة وحدات القياس في النظام';
PROMPT 'You can now use the Measurement Units Management window in the system';
