package com.shipment.erp.service;

import com.shipment.erp.model.UnitOfMeasure;
import java.util.List;
import java.util.Optional;

/**
 * خدمة وحدات القياس
 * Unit of Measure Service Interface
 */
public interface UnitOfMeasureService extends BaseService<UnitOfMeasure, Long> {
    
    /**
     * البحث عن وحدة قياس بالكود
     * Find unit of measure by code
     */
    Optional<UnitOfMeasure> findByCode(String code);
    
    /**
     * البحث عن وحدة قياس بالاسم العربي
     * Find unit of measure by Arabic name
     */
    Optional<UnitOfMeasure> findByNameAr(String nameAr);
    
    /**
     * الحصول على جميع وحدات القياس النشطة
     * Get all active units of measure
     */
    List<UnitOfMeasure> findActiveUnits();
    
    /**
     * الحصول على وحدات القياس الأساسية
     * Get base units of measure
     */
    List<UnitOfMeasure> findBaseUnits();
    
    /**
     * الحصول على وحدات القياس المرتبطة بوحدة أساسية
     * Get units of measure related to a base unit
     */
    List<UnitOfMeasure> findRelatedUnits(UnitOfMeasure baseUnit);
    
    /**
     * البحث في وحدات القياس
     * Search units of measure
     */
    List<UnitOfMeasure> searchUnits(String searchText);
    
    /**
     * إنشاء وحدة قياس جديدة
     * Create new unit of measure
     */
    UnitOfMeasure createUnit(UnitOfMeasure unit) throws Exception;
    
    /**
     * تحديث وحدة قياس
     * Update unit of measure
     */
    UnitOfMeasure updateUnit(UnitOfMeasure unit) throws Exception;
    
    /**
     * حذف وحدة قياس
     * Delete unit of measure
     */
    void deleteUnit(Long unitId) throws Exception;
    
    /**
     * تفعيل/إلغاء تفعيل وحدة قياس
     * Activate/Deactivate unit of measure
     */
    void toggleUnitStatus(Long unitId) throws Exception;
    
    /**
     * التحقق من صحة بيانات وحدة القياس
     * Validate unit of measure data
     */
    void validateUnit(UnitOfMeasure unit) throws Exception;
    
    /**
     * التحقق من إمكانية حذف وحدة القياس
     * Check if unit can be deleted
     */
    boolean canDeleteUnit(Long unitId);
    
    /**
     * تحويل الكمية بين وحدات القياس
     * Convert quantity between units of measure
     */
    Double convertQuantity(Double quantity, UnitOfMeasure fromUnit, UnitOfMeasure toUnit) throws Exception;
    
    /**
     * الحصول على إحصائيات وحدات القياس
     * Get unit of measure statistics
     */
    UnitStatistics getUnitStatistics();
    
    /**
     * فئة إحصائيات وحدات القياس
     * Unit statistics class
     */
    class UnitStatistics {
        private long totalUnits;
        private long activeUnits;
        private long baseUnits;
        private long derivedUnits;
        
        // Constructors, getters and setters
        public UnitStatistics() {}
        
        public UnitStatistics(long totalUnits, long activeUnits, long baseUnits, long derivedUnits) {
            this.totalUnits = totalUnits;
            this.activeUnits = activeUnits;
            this.baseUnits = baseUnits;
            this.derivedUnits = derivedUnits;
        }
        
        public long getTotalUnits() { return totalUnits; }
        public void setTotalUnits(long totalUnits) { this.totalUnits = totalUnits; }
        
        public long getActiveUnits() { return activeUnits; }
        public void setActiveUnits(long activeUnits) { this.activeUnits = activeUnits; }
        
        public long getBaseUnits() { return baseUnits; }
        public void setBaseUnits(long baseUnits) { this.baseUnits = baseUnits; }
        
        public long getDerivedUnits() { return derivedUnits; }
        public void setDerivedUnits(long derivedUnits) { this.derivedUnits = derivedUnits; }
    }
}
