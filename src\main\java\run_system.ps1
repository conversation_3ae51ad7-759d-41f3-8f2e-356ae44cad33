# تشغيل نظام إدارة الشحنات المتقدم
# Advanced Shipping Management System Runner

Write-Host "=== نظام إدارة الشحنات المتقدم ===" -ForegroundColor Green
Write-Host "Advanced Shipping Management System" -ForegroundColor Green
Write-Host "Ship ERP - Complete System Test" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# تعيين الترميز
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$env:JAVA_TOOL_OPTIONS = "-Dfile.encoding=UTF-8"

# الانتقال لمجلد المشروع
Set-Location $PSScriptRoot

# فحص وجود الملف الرئيسي
if (-not (Test-Path "CompleteSystemTest.java")) {
    Write-Host "خطأ: ملف CompleteSystemTest.java غير موجود!" -ForegroundColor Red
    Write-Host "Error: CompleteSystemTest.java not found!" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "[1/3] جاري فحص النظام..." -ForegroundColor Yellow
Write-Host "[1/3] Checking system..." -ForegroundColor Yellow

# فحص Java
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "Java متوفر: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "خطأ: Java غير مثبت أو غير متوفر في PATH" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "[2/3] جاري تجميع الملفات..." -ForegroundColor Yellow
Write-Host "[2/3] Compiling files..." -ForegroundColor Yellow

# تجميع الملفات مع معالجة الأخطاء
try {
    $compileResult = javac -encoding UTF-8 -cp . *.java 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "تم التجميع بنجاح!" -ForegroundColor Green
        Write-Host "Compilation successful!" -ForegroundColor Green
    } else {
        Write-Host "فشل في التجميع!" -ForegroundColor Red
        Write-Host "Compilation failed!" -ForegroundColor Red
        Write-Host "تفاصيل الخطأ:" -ForegroundColor Yellow
        Write-Host $compileResult -ForegroundColor Red
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
} catch {
    Write-Host "خطأ في التجميع: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "[3/3] جاري تشغيل النظام..." -ForegroundColor Yellow
Write-Host "[3/3] Starting system..." -ForegroundColor Yellow

Write-Host "المعاملات المستخدمة:" -ForegroundColor Cyan
Write-Host "  -Dfile.encoding=UTF-8" -ForegroundColor Gray
Write-Host "  -Duser.language=ar" -ForegroundColor Gray
Write-Host "  -Duser.country=SA" -ForegroundColor Gray
Write-Host "  -Dawt.useSystemAAFontSettings=lcd" -ForegroundColor Gray
Write-Host "  -Dswing.aatext=true" -ForegroundColor Gray
Write-Host ""

# تشغيل النظام مع المعاملات الصحيحة
try {
    $javaArgs = @(
        "-Dfile.encoding=UTF-8"
        "-Duser.language=ar"
        "-Duser.country=SA"
        "-Dawt.useSystemAAFontSettings=lcd"
        "-Dswing.aatext=true"
        "CompleteSystemTest"
    )

    Write-Host "تشغيل الأمر: java $($javaArgs -join ' ')" -ForegroundColor Gray
    Write-Host ""

    & java $javaArgs

    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "تم تشغيل النظام بنجاح!" -ForegroundColor Green
        Write-Host "System started successfully!" -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "فشل في تشغيل النظام! رمز الخطأ: $LASTEXITCODE" -ForegroundColor Red
        Write-Host "Failed to start system! Error code: $LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host ""
    Write-Host "خطأ في تشغيل النظام: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "System error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Cyan
Write-Host "Press any key to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
