import javax.swing.JFrame;
import javax.swing.JOptionPane;
import javax.swing.SwingUtilities;

/**
 * اختبار نافذة الإعدادات العامة
 */
public class TestGeneralSettings {
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                // إنشاء نافذة رئيسية مؤقتة
                JFrame mainFrame = new JFrame("النافذة الرئيسية");
                mainFrame.setSize(400, 300);
                mainFrame.setLocationRelativeTo(null);
                mainFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);

                // إنشاء وعرض نافذة الإعدادات العامة
                GeneralSettingsWindow settingsWindow = new GeneralSettingsWindow(mainFrame);
                settingsWindow.setVisible(true);

                System.out.println("✅ تم فتح نافذة الإعدادات العامة");
                System.out.println(
                        "📋 اذهب إلى تبويب 'قاعدة البيانات' لرؤية أزرار إدارة قاعدة البيانات");
                System.out.println(
                        "🏗️ ابحث عن زر 'إنشاء بنية قاعدة البيانات' في قسم 'إدارة قاعدة البيانات'");

            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(null, "خطأ في فتح نافذة الإعدادات: " + e.getMessage(),
                        "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
