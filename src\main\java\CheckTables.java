import java.sql.*;

public class CheckTables {
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            System.out.println("🔗 محاولة الاتصال بـ SHIP_ERP...");
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            System.out.println("✅ تم الاتصال بـ SHIP_ERP");
            
            // فحص الجداول
            String[] tables = {
                "ERP_GROUP_DETAILS",
                "ERP_MAINSUB_GRP_DTL", 
                "ERP_SUB_GRP_DTL",
                "ERP_ASSISTANT_GROUP",
                "ERP_DETAIL_GROUP"
            };
            
            DatabaseMetaData metaData = conn.getMetaData();
            
            for (String tableName : tables) {
                System.out.println("🔍 فحص الجدول: " + tableName);
                
                ResultSet rs = metaData.getTables(null, "SHIP_ERP", tableName, new String[]{"TABLE"});
                if (rs.next()) {
                    System.out.println("✅ الجدول موجود: " + tableName);
                    
                    // عد الصفوف
                    Statement stmt = conn.createStatement();
                    ResultSet countRs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName);
                    if (countRs.next()) {
                        System.out.println("📊 عدد الصفوف: " + countRs.getInt(1));
                    }
                    countRs.close();
                    stmt.close();
                } else {
                    System.out.println("❌ الجدول غير موجود: " + tableName);
                }
                rs.close();
            }
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
