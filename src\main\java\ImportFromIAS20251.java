import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.swing.JFrame;
import javax.swing.JOptionPane;

/**
 * استيراد وحدات القياس من النظام الأصلي IAS20251 Import measurement units from original IAS20251
 * system
 */
public class ImportFromIAS20251 {

    // إعدادات قاعدة البيانات IAS20251
    private static final String IAS_URL = "*************************************";
    private static final String IAS_USER = "ias20251";
    private static final String IAS_PASSWORD = "ys123";

    // إعدادات قاعدة البيانات SHIP_ERP
    private static final String ERP_URL = "*************************************";
    private static final String ERP_USER = "ship_erp";
    private static final String ERP_PASSWORD = "ship_erp_password";

    /**
     * استيراد البيانات من IAS20251 إلى SHIP_ERP
     */
    public static ImportResult importMeasurementUnits(JFrame parentWindow) {
        ImportResult result = new ImportResult();

        Connection iasConnection = null;
        Connection erpConnection = null;

        try {
            // تحميل Oracle JDBC Driver
            try {
                Class.forName("oracle.jdbc.OracleDriver");
                System.out.println("✅ تم تحميل Oracle JDBC Driver بنجاح");
            } catch (ClassNotFoundException e) {
                System.err.println("❌ فشل في تحميل Oracle JDBC Driver");
                throw e;
            }

            // الاتصال بقاعدة البيانات IAS20251
            System.out.println("🔄 محاولة الاتصال بقاعدة البيانات IAS20251...");
            iasConnection = DriverManager.getConnection(IAS_URL, IAS_USER, IAS_PASSWORD);
            System.out.println("✅ متصل بقاعدة البيانات IAS20251");

            // الاتصال بقاعدة البيانات SHIP_ERP
            System.out.println("🔄 محاولة الاتصال بقاعدة البيانات SHIP_ERP...");
            erpConnection = DriverManager.getConnection(ERP_URL, ERP_USER, ERP_PASSWORD);
            erpConnection.setAutoCommit(false);
            System.out.println("✅ متصل بقاعدة البيانات SHIP_ERP");

            // قراءة البيانات من IAS20251
            List<MeasurementUnit> units = readFromIAS20251(iasConnection);
            result.totalRecords = units.size();
            System.out.println("📖 تم قراءة " + units.size() + " وحدة قياس من IAS20251");

            if (units.isEmpty()) {
                result.message = "لا توجد بيانات في جدول MEASUREMENT في IAS20251";
                return result;
            }

            // عرض نافذة تأكيد للمستخدم
            int choice = JOptionPane.showConfirmDialog(parentWindow,
                    "تم العثور على " + units.size() + " وحدة قياس في النظام الأصلي IAS20251\n"
                            + "هل تريد استيرادها إلى نظام SHIP_ERP؟\n\n"
                            + "ملاحظة: سيتم استبدال البيانات الموجودة",
                    "تأكيد الاستيراد", JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);

            if (choice != JOptionPane.YES_OPTION) {
                result.message = "تم إلغاء عملية الاستيراد بواسطة المستخدم";
                return result;
            }

            // حذف البيانات الموجودة في SHIP_ERP
            clearExistingData(erpConnection);

            // استيراد البيانات الجديدة
            importToShipERP(erpConnection, units, result);

            // تأكيد الحفظ
            erpConnection.commit();
            result.success = true;
            result.message =
                    "تم استيراد " + result.importedRecords + " وحدة قياس بنجاح من IAS20251";

            System.out.println("🎉 " + result.message);

        } catch (ClassNotFoundException e) {
            result.message = "خطأ: لم يتم العثور على Oracle JDBC driver\n" + e.getMessage();
            System.err.println("❌ " + result.message);
        } catch (SQLException e) {
            String errorCode = e.getErrorCode() > 0 ? " (ORA-" + e.getErrorCode() + ")" : "";
            result.message = "خطأ في قاعدة البيانات" + errorCode + ": " + e.getMessage();

            // رسائل خطأ مفصلة
            if (e.getMessage().contains("invalid username")
                    || e.getMessage().contains("ORA-01017")) {
                result.message +=
                        "\n\nتأكد من:\n• صحة اسم المستخدم وكلمة المرور\n• وجود المستخدمين في قاعدة البيانات";
            } else if (e.getMessage().contains("Connection refused")
                    || e.getMessage().contains("ORA-12541")) {
                result.message +=
                        "\n\nتأكد من:\n• تشغيل Oracle Database\n• صحة عنوان الخادم والمنفذ";
            } else if (e.getMessage().contains("table or view does not exist")
                    || e.getMessage().contains("ORA-00942")) {
                result.message +=
                        "\n\nتأكد من:\n• وجود جدول MEASUREMENT في IAS20251\n• وجود جدول ERP_MEASUREMENT في SHIP_ERP";
            }

            System.err.println("❌ " + result.message);

            // التراجع عن التغييرات
            try {
                if (erpConnection != null) {
                    erpConnection.rollback();
                    System.out.println("🔄 تم التراجع عن التغييرات");
                }
            } catch (SQLException rollbackEx) {
                System.err.println("❌ خطأ في التراجع: " + rollbackEx.getMessage());
            }
        } catch (Exception e) {
            result.message = "خطأ عام: " + e.getMessage();
            System.err.println("❌ " + result.message);
            e.printStackTrace();
        } finally {
            // إغلاق الاتصالات
            try {
                if (iasConnection != null)
                    iasConnection.close();
                if (erpConnection != null)
                    erpConnection.close();
            } catch (SQLException e) {
                System.err.println("❌ خطأ في إغلاق الاتصال: " + e.getMessage());
            }
        }

        return result;
    }

    /**
     * قراءة البيانات من جدول MEASUREMENT في IAS20251
     */
    private static List<MeasurementUnit> readFromIAS20251(Connection connection)
            throws SQLException {
        List<MeasurementUnit> units = new ArrayList<>();

        String query = """
                    SELECT
                        MEASURE_CODE,
                        MEASURE,
                        MEASURE_F_NM,
                        MEASURE_CODE_GB,
                        MEASURE_TYPE,
                        MEASURE_WT_TYPE,
                        MEASURE_WT_CONN,
                        DFLT_SIZE,
                        ALLOW_UPD,
                        UNT_SALE_TYP,
                        AD_U_ID,
                        AD_DATE,
                        UP_U_ID,
                        UP_DATE,
                        UP_CNT,
                        AD_TRMNL_NM,
                        UP_TRMNL_NM
                    FROM MEASUREMENT
                    ORDER BY MEASURE_CODE
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                MeasurementUnit unit = new MeasurementUnit();
                unit.measureCode = rs.getString("MEASURE_CODE");
                unit.measure = rs.getString("MEASURE");
                unit.measureFNm = rs.getString("MEASURE_F_NM");
                unit.measureCodeGb = rs.getString("MEASURE_CODE_GB");
                unit.measureType = rs.getObject("MEASURE_TYPE", Integer.class);
                unit.measureWtType = rs.getObject("MEASURE_WT_TYPE", Integer.class);
                unit.measureWtConn = rs.getObject("MEASURE_WT_CONN", Integer.class);
                unit.dfltSize = rs.getObject("DFLT_SIZE", Double.class);
                unit.allowUpd = rs.getObject("ALLOW_UPD", Integer.class);
                unit.untSaleTyp = rs.getObject("UNT_SALE_TYP", Integer.class);
                unit.adUId = rs.getObject("AD_U_ID", Integer.class);
                unit.adDate = rs.getTimestamp("AD_DATE");
                unit.upUId = rs.getObject("UP_U_ID", Integer.class);
                unit.upDate = rs.getTimestamp("UP_DATE");
                unit.upCnt = rs.getObject("UP_CNT", Integer.class);
                unit.adTrmnlNm = rs.getString("AD_TRMNL_NM");
                unit.upTrmnlNm = rs.getString("UP_TRMNL_NM");

                units.add(unit);
            }
        }

        return units;
    }

    /**
     * حذف البيانات الموجودة في SHIP_ERP
     */
    private static void clearExistingData(Connection connection) throws SQLException {
        String deleteSQL = "DELETE FROM ERP_MEASUREMENT";
        try (PreparedStatement stmt = connection.prepareStatement(deleteSQL)) {
            int deleted = stmt.executeUpdate();
            System.out.println("🗑️ تم حذف " + deleted + " سجل موجود من SHIP_ERP");
        }
    }

    /**
     * استيراد البيانات إلى SHIP_ERP
     */
    private static void importToShipERP(Connection connection, List<MeasurementUnit> units,
            ImportResult result) throws SQLException {
        String insertSQL = """
                    INSERT INTO ERP_MEASUREMENT (
                        MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB,
                        MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE,
                        ALLOW_UPD, UNT_SALE_TYP, AD_U_ID, AD_DATE,
                        UP_U_ID, UP_DATE, UP_CNT, AD_TRMNL_NM, UP_TRMNL_NM
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """;

        try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
            for (MeasurementUnit unit : units) {
                try {
                    stmt.setString(1, unit.measureCode);
                    stmt.setString(2, unit.measure);
                    stmt.setString(3, unit.measureFNm);
                    stmt.setString(4, unit.measureCodeGb);
                    stmt.setObject(5, unit.measureType);
                    stmt.setObject(6, unit.measureWtType);
                    stmt.setObject(7, unit.measureWtConn);
                    stmt.setObject(8, unit.dfltSize);
                    stmt.setObject(9, unit.allowUpd);
                    stmt.setObject(10, unit.untSaleTyp);
                    stmt.setObject(11, unit.adUId);
                    stmt.setTimestamp(12, unit.adDate);
                    stmt.setObject(13, unit.upUId);
                    stmt.setTimestamp(14, unit.upDate);
                    stmt.setObject(15, unit.upCnt);
                    stmt.setString(16, unit.adTrmnlNm);
                    stmt.setString(17, unit.upTrmnlNm);

                    stmt.executeUpdate();
                    result.importedRecords++;

                } catch (SQLException e) {
                    result.errorRecords++;
                    System.err.println(
                            "❌ خطأ في استيراد الوحدة " + unit.measureCode + ": " + e.getMessage());
                }
            }
        }
    }

    /**
     * كلاس لتمثيل وحدة القياس
     */
    static class MeasurementUnit {
        String measureCode;
        String measure;
        String measureFNm;
        String measureCodeGb;
        Integer measureType;
        Integer measureWtType;
        Integer measureWtConn;
        Double dfltSize;
        Integer allowUpd;
        Integer untSaleTyp;
        Integer adUId;
        Timestamp adDate;
        Integer upUId;
        Timestamp upDate;
        Integer upCnt;
        String adTrmnlNm;
        String upTrmnlNm;
    }

    /**
     * كلاس لنتيجة الاستيراد
     */
    public static class ImportResult {
        public boolean success = false;
        public String message = "";
        public int totalRecords = 0;
        public int importedRecords = 0;
        public int errorRecords = 0;
    }
}
