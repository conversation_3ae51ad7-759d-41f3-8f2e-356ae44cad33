package com.shipment.erp.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * كيان العملة
 * يمثل العملات المستخدمة في النظام
 */
@Entity
@Table(name = "CURRENCIES", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"CODE"})
})
@SequenceGenerator(name = "currency_seq", sequenceName = "SEQ_CURRENCY", allocationSize = 1)
public class Currency extends BaseEntity {

    @Column(name = "CODE", nullable = false, unique = true, length = 3)
    @NotBlank(message = "رمز العملة مطلوب")
    @Size(min = 3, max = 3, message = "رمز العملة يجب أن يكون 3 أحرف")
    private String code;

    @Column(name = "NAME", nullable = false, length = 100)
    @NotBlank(message = "اسم العملة مطلوب")
    @Size(max = 100, message = "اسم العملة يجب أن يكون أقل من 100 حرف")
    private String name;

    @Column(name = "NAME_EN", length = 100)
    @Size(max = 100, message = "الاسم الإنجليزي يجب أن يكون أقل من 100 حرف")
    private String nameEn;

    @Column(name = "SYMBOL", length = 10)
    @Size(max = 10, message = "رمز العملة يجب أن يكون أقل من 10 أحرف")
    private String symbol;

    @Column(name = "EXCHANGE_RATE", nullable = false, precision = 15, scale = 6)
    @NotNull(message = "سعر الصرف مطلوب")
    @DecimalMin(value = "0.000001", message = "سعر الصرف يجب أن يكون أكبر من صفر")
    private BigDecimal exchangeRate = BigDecimal.ONE;

    @Column(name = "IS_DEFAULT", nullable = false)
    private Boolean isDefault = false;

    @Column(name = "IS_ACTIVE", nullable = false)
    private Boolean isActive = true;

    /**
     * Constructor افتراضي
     */
    public Currency() {
        super();
    }

    /**
     * Constructor مع الرمز والاسم
     */
    public Currency(String code, String name) {
        this();
        this.code = code;
        this.name = name;
    }

    /**
     * Constructor مع الرمز والاسم وسعر الصرف
     */
    public Currency(String code, String name, BigDecimal exchangeRate) {
        this(code, name);
        this.exchangeRate = exchangeRate;
    }

    /**
     * Constructor مع جميع البيانات الأساسية
     */
    public Currency(String code, String name, String nameEn, String symbol, BigDecimal exchangeRate) {
        this(code, name, exchangeRate);
        this.nameEn = nameEn;
        this.symbol = symbol;
    }

    // Getters and Setters

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code != null ? code.toUpperCase() : null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    /**
     * تحويل مبلغ من هذه العملة إلى العملة الأساسية
     */
    public BigDecimal convertToBaseCurrency(BigDecimal amount) {
        if (amount == null || exchangeRate == null) {
            return BigDecimal.ZERO;
        }
        return amount.multiply(exchangeRate);
    }

    /**
     * تحويل مبلغ من العملة الأساسية إلى هذه العملة
     */
    public BigDecimal convertFromBaseCurrency(BigDecimal amount) {
        if (amount == null || exchangeRate == null || exchangeRate.equals(BigDecimal.ZERO)) {
            return BigDecimal.ZERO;
        }
        return amount.divide(exchangeRate, 6, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * تحويل مبلغ من عملة أخرى إلى هذه العملة
     */
    public BigDecimal convertFromCurrency(BigDecimal amount, Currency fromCurrency) {
        if (amount == null || fromCurrency == null) {
            return BigDecimal.ZERO;
        }
        
        // تحويل إلى العملة الأساسية أولاً
        BigDecimal baseAmount = fromCurrency.convertToBaseCurrency(amount);
        
        // ثم تحويل من العملة الأساسية إلى هذه العملة
        return convertFromBaseCurrency(baseAmount);
    }

    /**
     * تنسيق المبلغ مع رمز العملة
     */
    public String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "0";
        }
        
        String formattedAmount = String.format("%.2f", amount);
        
        if (symbol != null && !symbol.isEmpty()) {
            return formattedAmount + " " + symbol;
        } else {
            return formattedAmount + " " + code;
        }
    }

    /**
     * الحصول على الاسم المعروض (عربي أو إنجليزي)
     */
    public String getDisplayName() {
        return name != null ? name : nameEn;
    }

    /**
     * الحصول على الاسم الكامل مع الرمز
     */
    public String getFullName() {
        String displayName = getDisplayName();
        return displayName + " (" + code + ")";
    }

    @Override
    public String toString() {
        return "Currency{" +
                "id=" + getId() +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", nameEn='" + nameEn + '\'' +
                ", symbol='" + symbol + '\'' +
                ", exchangeRate=" + exchangeRate +
                ", isDefault=" + isDefault +
                ", isActive=" + isActive +
                '}';
    }
}
