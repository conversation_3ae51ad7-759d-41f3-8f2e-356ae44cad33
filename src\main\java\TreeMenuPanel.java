import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.HashMap;
import java.util.Map;
import javax.swing.BorderFactory;
import javax.swing.Icon;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;
import javax.swing.JScrollPane;
import javax.swing.JTree;
import javax.swing.SwingConstants;
import javax.swing.SwingUtilities;
import javax.swing.border.EmptyBorder;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeCellRenderer;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;

/**
 * لوحة القائمة الشجرية المتقدمة Advanced Tree Menu Panel
 */
public class TreeMenuPanel extends JPanel {

    private JTree menuTree;
    private DefaultMutableTreeNode rootNode;
    private DefaultTreeModel treeModel;
    private Font arabicFont;
    private JFrame parentFrame;
    private Map<String, Runnable> menuActions;

    public TreeMenuPanel(JFrame parentFrame) {
        this.parentFrame = parentFrame;

        // إعداد خصائص النظام للنص العربي
        setupArabicSystemProperties();

        // تهيئة الخط العربي
        this.arabicFont = createArabicFont();
        this.menuActions = new HashMap<>();

        initializeMenuActions();
        createTreeStructure();
        setupTreeComponents();
        setupLayout();
        setupEventHandlers();
    }

    /**
     * إعداد خصائص النظام للنص العربي
     */
    private void setupArabicSystemProperties() {
        try {
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("sun.jnu.encoding", "UTF-8");
            System.setProperty("user.language", "ar");
            System.setProperty("user.country", "SA");
            System.setProperty("awt.useSystemAAFontSettings", "lcd");
            System.setProperty("swing.aatext", "true");
            System.setProperty("swing.useSystemAAFontSettings", "lcd");
        } catch (Exception e) {
            System.err.println("خطأ في إعداد خصائص النظام: " + e.getMessage());
        }
    }

    /**
     * إنشاء خط عربي محسن
     */
    private Font createArabicFont() {
        String[] arabicFonts = {"Tahoma", "Arial Unicode MS", "Segoe UI", "Arial", "SansSerif"};

        for (String fontName : arabicFonts) {
            try {
                Font font = new Font(fontName, Font.PLAIN, 12);
                if (font.canDisplayUpTo("النص العربي") == -1) {
                    System.out.println("تم اختيار الخط: " + fontName);
                    return font;
                }
            } catch (Exception e) {
                continue;
            }
        }

        return new Font("Dialog", Font.PLAIN, 12);
    }

    /**
     * تهيئة إجراءات القائمة
     */
    private void initializeMenuActions() {
        // إجراءات إدارة الشحنات
        menuActions.put("قائمة الشحنات", () -> showMessage("فتح قائمة الشحنات"));
        menuActions.put("شحنة جديدة", () -> showMessage("إنشاء شحنة جديدة"));
        menuActions.put("تتبع الشحنات", () -> showMessage("فتح نظام تتبع الشحنات"));
        menuActions.put("تقارير الشحن", () -> showMessage("عرض تقارير الشحن"));

        // إجراءات إدارة العملاء
        menuActions.put("قائمة العملاء", () -> showMessage("فتح قائمة العملاء"));
        menuActions.put("عميل جديد", () -> showMessage("إضافة عميل جديد"));
        menuActions.put("تقارير العملاء", () -> showMessage("عرض تقارير العملاء"));

        // إجراءات المحاسبة
        menuActions.put("الفواتير", () -> showMessage("فتح نظام الفواتير"));
        menuActions.put("المدفوعات", () -> showMessage("إدارة المدفوعات"));
        menuActions.put("التقارير المالية", () -> showMessage("عرض التقارير المالية"));
        menuActions.put("الحسابات", () -> showMessage("إدارة الحسابات"));

        // إجراءات المخزون
        menuActions.put("قائمة المخزون", () -> showMessage("فتح قائمة المخزون"));
        menuActions.put("إضافة عنصر", () -> showMessage("إضافة عنصر جديد للمخزون"));
        menuActions.put("جرد المخزون", () -> showMessage("تنفيذ جرد المخزون"));
        menuActions.put("تقارير المخزون", () -> showMessage("عرض تقارير المخزون"));

        // إجراءات إدارة الأصناف
        menuActions.put("وحدات القياس", () -> openUnitOfMeasureWindow());
        menuActions.put("مجموعات الأصناف", () -> openItemGroupsManagementWindow());
        menuActions.put("بيانات الأصناف", () -> openAdvancedItemDataWindow());
        menuActions.put("بيانات الأصناف الشاملة", () -> openComprehensiveItemDataWindow());
        menuActions.put("ربط النظام واستيراد البيانات", () -> openSystemIntegrationWindow());
        menuActions.put("تقارير الأصناف", () -> openItemReportsWindow());

        // إجراءات الإعدادات العامة
        menuActions.put("إدارة الربط والاستيراد", () -> openSystemIntegrationManagementWindow());

        // إجراءات التقارير
        menuActions.put("تقارير المبيعات", () -> showMessage("عرض تقارير المبيعات"));
        menuActions.put("تقارير الأرباح", () -> showMessage("عرض تقارير الأرباح"));
        menuActions.put("تقارير الأداء", () -> showMessage("عرض تقارير الأداء"));
        menuActions.put("تقارير مخصصة", () -> showMessage("إنشاء تقارير مخصصة"));

        // إجراءات الإعدادات
        menuActions.put("الإعدادات العامة", () -> {
            GeneralSettingsWindow settingsWindow = new GeneralSettingsWindow(parentFrame);
            settingsWindow.setVisible(true);
        });
        menuActions.put("إدارة المستخدمين", () -> {
            UserManagementWindow usersWindow = new UserManagementWindow(parentFrame);
            usersWindow.setVisible(true);
        });
        menuActions.put("إعدادات النظام", () -> showMessage("فتح إعدادات النظام"));
        menuActions.put("النسخ الاحتياطية", () -> showMessage("إدارة النسخ الاحتياطية"));

        // إجراءات المساعدة
        menuActions.put("دليل المستخدم", () -> showMessage("فتح دليل المستخدم"));
        menuActions.put("الدعم الفني", () -> showMessage("الاتصال بالدعم الفني"));
        menuActions.put("حول البرنامج", () -> showAboutDialog());
    }

    /**
     * إنشاء هيكل الشجرة
     */
    private void createTreeStructure() {
        rootNode = new DefaultMutableTreeNode("نظام إدارة الشحنات");

        // إدارة الشحنات
        DefaultMutableTreeNode shippingNode = new DefaultMutableTreeNode("إدارة الشحنات");
        shippingNode.add(new DefaultMutableTreeNode("قائمة الشحنات"));
        shippingNode.add(new DefaultMutableTreeNode("شحنة جديدة"));
        shippingNode.add(new DefaultMutableTreeNode("تتبع الشحنات"));
        shippingNode.add(new DefaultMutableTreeNode("تقارير الشحن"));
        rootNode.add(shippingNode);

        // إدارة العملاء
        DefaultMutableTreeNode customersNode = new DefaultMutableTreeNode("إدارة العملاء");
        customersNode.add(new DefaultMutableTreeNode("قائمة العملاء"));
        customersNode.add(new DefaultMutableTreeNode("عميل جديد"));
        customersNode.add(new DefaultMutableTreeNode("تقارير العملاء"));
        rootNode.add(customersNode);

        // المحاسبة والمالية
        DefaultMutableTreeNode accountingNode = new DefaultMutableTreeNode("المحاسبة والمالية");
        accountingNode.add(new DefaultMutableTreeNode("الفواتير"));
        accountingNode.add(new DefaultMutableTreeNode("المدفوعات"));
        accountingNode.add(new DefaultMutableTreeNode("التقارير المالية"));
        accountingNode.add(new DefaultMutableTreeNode("الحسابات"));
        rootNode.add(accountingNode);

        // إدارة المخزون
        DefaultMutableTreeNode inventoryNode = new DefaultMutableTreeNode("إدارة المخزون");
        inventoryNode.add(new DefaultMutableTreeNode("قائمة المخزون"));
        inventoryNode.add(new DefaultMutableTreeNode("إضافة عنصر"));
        inventoryNode.add(new DefaultMutableTreeNode("جرد المخزون"));
        inventoryNode.add(new DefaultMutableTreeNode("تقارير المخزون"));
        rootNode.add(inventoryNode);

        // إدارة الأصناف
        DefaultMutableTreeNode itemsNode = new DefaultMutableTreeNode("إدارة الأصناف");
        itemsNode.add(new DefaultMutableTreeNode("وحدات القياس"));
        itemsNode.add(new DefaultMutableTreeNode("مجموعات الأصناف"));
        itemsNode.add(new DefaultMutableTreeNode("بيانات الأصناف"));
        itemsNode.add(new DefaultMutableTreeNode("بيانات الأصناف الشاملة"));
        itemsNode.add(new DefaultMutableTreeNode("ربط النظام واستيراد البيانات"));
        itemsNode.add(new DefaultMutableTreeNode("تقارير الأصناف"));
        rootNode.add(itemsNode);

        // التقارير والإحصائيات
        DefaultMutableTreeNode reportsNode = new DefaultMutableTreeNode("التقارير والإحصائيات");
        reportsNode.add(new DefaultMutableTreeNode("تقارير المبيعات"));
        reportsNode.add(new DefaultMutableTreeNode("تقارير الأرباح"));
        reportsNode.add(new DefaultMutableTreeNode("تقارير الأداء"));
        reportsNode.add(new DefaultMutableTreeNode("تقارير مخصصة"));
        rootNode.add(reportsNode);

        // الإعدادات والإدارة
        DefaultMutableTreeNode settingsNode = new DefaultMutableTreeNode("الإعدادات والإدارة");
        settingsNode.add(new DefaultMutableTreeNode("الإعدادات العامة"));
        settingsNode.add(new DefaultMutableTreeNode("إدارة المستخدمين"));
        settingsNode.add(new DefaultMutableTreeNode("إدارة الربط والاستيراد"));
        settingsNode.add(new DefaultMutableTreeNode("إعدادات النظام"));
        settingsNode.add(new DefaultMutableTreeNode("النسخ الاحتياطية"));
        rootNode.add(settingsNode);

        // المساعدة والدعم
        DefaultMutableTreeNode helpNode = new DefaultMutableTreeNode("المساعدة والدعم");
        helpNode.add(new DefaultMutableTreeNode("دليل المستخدم"));
        helpNode.add(new DefaultMutableTreeNode("الدعم الفني"));
        helpNode.add(new DefaultMutableTreeNode("حول البرنامج"));
        rootNode.add(helpNode);

        treeModel = new DefaultTreeModel(rootNode);
    }

    /**
     * إعداد مكونات الشجرة
     */
    private void setupTreeComponents() {
        menuTree = new JTree(treeModel);

        // إعداد الشجرة للنص العربي
        menuTree.setFont(arabicFont);
        menuTree.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        menuTree.setRootVisible(true);
        menuTree.setShowsRootHandles(true);
        menuTree.setRowHeight(25);

        // تخصيص مظهر الشجرة
        menuTree.setCellRenderer(new CustomTreeCellRenderer());

        // توسيع العقد الرئيسية
        for (int i = 0; i < menuTree.getRowCount(); i++) {
            menuTree.expandRow(i);
        }

        // تحديد العقدة الجذر
        menuTree.setSelectionRow(0);
    }

    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setBackground(new Color(248, 249, 250));
        setBorder(new EmptyBorder(10, 10, 10, 10));

        // رأس القائمة
        JPanel headerPanel = createHeaderPanel();
        add(headerPanel, BorderLayout.NORTH);

        // الشجرة مع شريط التمرير
        JScrollPane scrollPane = new JScrollPane(menuTree);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createTitledBorder("القائمة الرئيسية"),
                BorderFactory.createEmptyBorder(5, 5, 5, 5)));
        scrollPane.setPreferredSize(new Dimension(180, 400)); // تقليل العرض المفضل
        scrollPane.setMinimumSize(new Dimension(160, 300)); // تقليل الحد الأدنى

        add(scrollPane, BorderLayout.CENTER); // تغيير هنا

        // تذييل مع معلومات المستخدم
        JPanel footerPanel = createFooterPanel();
        add(footerPanel, BorderLayout.SOUTH);
    }

    /**
     * إنشاء رأس القائمة
     */
    private JPanel createHeaderPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(new Color(52, 152, 219));
        panel.setBorder(new EmptyBorder(15, 15, 15, 15));

        JLabel titleLabel = new JLabel("القائمة الرئيسية");
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 16));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);

        panel.add(titleLabel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء تذييل القائمة
     */
    private JPanel createFooterPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(new Color(248, 249, 250));
        panel.setBorder(new EmptyBorder(10, 15, 10, 15));

        JLabel userLabel = new JLabel("المستخدم: admin");
        userLabel.setFont(new Font("Tahoma", Font.PLAIN, 11));
        userLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel timeLabel = new JLabel(
                "الوقت: " + new java.text.SimpleDateFormat("HH:mm").format(new java.util.Date()));
        timeLabel.setFont(new Font("Tahoma", Font.PLAIN, 11));
        timeLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        panel.add(userLabel, BorderLayout.NORTH);
        panel.add(timeLabel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        menuTree.addTreeSelectionListener(e -> {
            DefaultMutableTreeNode selectedNode =
                    (DefaultMutableTreeNode) menuTree.getLastSelectedPathComponent();

            if (selectedNode != null && selectedNode.isLeaf() && !selectedNode.isRoot()) {
                String menuItem = selectedNode.toString();
                Runnable action = menuActions.get(menuItem);

                if (action != null) {
                    action.run();
                }
            }
        });

        // إضافة قائمة سياق (Right-click menu)
        menuTree.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                if (SwingUtilities.isRightMouseButton(e)) {
                    showContextMenu(e);
                }
            }
        });
    }

    /**
     * عرض قائمة السياق
     */
    private void showContextMenu(MouseEvent e) {
        TreePath path = menuTree.getPathForLocation(e.getX(), e.getY());
        if (path != null) {
            menuTree.setSelectionPath(path);
            DefaultMutableTreeNode node = (DefaultMutableTreeNode) path.getLastPathComponent();

            JPopupMenu contextMenu = new JPopupMenu();
            contextMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

            if (node.isLeaf() && !node.isRoot()) {
                JMenuItem openItem = new JMenuItem("فتح");
                openItem.setFont(arabicFont);
                openItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                openItem.addActionListener(ev -> {
                    String menuItem = node.toString();
                    Runnable action = menuActions.get(menuItem);
                    if (action != null) {
                        action.run();
                    }
                });
                contextMenu.add(openItem);

                contextMenu.addSeparator();

                JMenuItem infoItem = new JMenuItem("معلومات");
                infoItem.setFont(arabicFont);
                infoItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                infoItem.addActionListener(ev -> showItemInfo(node.toString()));
                contextMenu.add(infoItem);
            } else {
                JMenuItem expandItem = new JMenuItem(menuTree.isExpanded(path) ? "طي" : "توسيع");
                expandItem.setFont(arabicFont);
                expandItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                expandItem.addActionListener(ev -> {
                    if (menuTree.isExpanded(path)) {
                        menuTree.collapsePath(path);
                    } else {
                        menuTree.expandPath(path);
                    }
                });
                contextMenu.add(expandItem);
            }

            contextMenu.show(menuTree, e.getX(), e.getY());
        }
    }

    /**
     * عرض معلومات العنصر
     */
    private void showItemInfo(String itemName) {
        String info = getItemDescription(itemName);
        JOptionPane.showMessageDialog(this, info, "معلومات: " + itemName,
                JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * الحصول على وصف العنصر
     */
    private String getItemDescription(String itemName) {
        Map<String, String> descriptions = new HashMap<>();

        descriptions.put("قائمة الشحنات", "عرض وإدارة جميع الشحنات في النظام");
        descriptions.put("شحنة جديدة", "إنشاء شحنة جديدة وإدخال بياناتها");
        descriptions.put("تتبع الشحنات", "تتبع حالة الشحنات ومعرفة موقعها");
        descriptions.put("تقارير الشحن", "عرض تقارير مفصلة عن عمليات الشحن");

        descriptions.put("قائمة العملاء", "عرض وإدارة بيانات جميع العملاء");
        descriptions.put("عميل جديد", "إضافة عميل جديد إلى النظام");
        descriptions.put("تقارير العملاء", "عرض تقارير عن نشاط العملاء");

        descriptions.put("الفواتير", "إدارة الفواتير وإنشاء فواتير جديدة");
        descriptions.put("المدفوعات", "تسجيل ومتابعة المدفوعات");
        descriptions.put("التقارير المالية", "عرض التقارير المالية والمحاسبية");
        descriptions.put("الحسابات", "إدارة الحسابات المالية");

        descriptions.put("الإعدادات العامة", "تكوين الإعدادات العامة للنظام");
        descriptions.put("إدارة المستخدمين", "إدارة المستخدمين وصلاحياتهم");

        return descriptions.getOrDefault(itemName, "لا توجد معلومات متاحة لهذا العنصر");
    }

    /**
     * عرض رسالة
     */
    private void showMessage(String message) {
        JOptionPane.showMessageDialog(this, message, "إشعار", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * فتح نافذة وحدات القياس
     */
    private void openUnitOfMeasureWindow() {
        SwingUtilities.invokeLater(() -> {
            try {
                System.out.println("🔄 فتح نافذة وحدات القياس...");

                // فتح النافذة مباشرة بدون نوافذ مؤقتة
                OriginalMeasurementUnitsWindow window = new OriginalMeasurementUnitsWindow();
                window.setVisible(true);

                System.out.println("✅ تم فتح نافذة وحدات القياس بنجاح!");

            } catch (Exception e) {
                System.err.println("❌ خطأ في فتح نافذة وحدات القياس: " + e.getMessage());
                e.printStackTrace();

                // عرض رسالة خطأ واضحة للمستخدم
                String errorMessage = "فشل في فتح نافذة وحدات القياس:\n" + e.getMessage();

                if (e.getMessage() != null) {
                    if (e.getMessage().contains("oracle") || e.getMessage().contains("ORA-")) {
                        errorMessage += "\n\nتأكد من:\n" + "• تشغيل قاعدة البيانات Oracle\n"
                                + "• وجود المستخدم ship_erp\n"
                                + "• تنفيذ سكريبت create_measurement_table.sql";
                    } else if (e.getMessage().contains("ClassNotFoundException")) {
                        errorMessage += "\n\nتأكد من وجود ملف ojdbc11.jar في مجلد lib";
                    }
                }

                showMessage(errorMessage);
            }
        });
    }

    /**
     * فتح نافذة إدارة مجموعات الأصناف الجديدة
     */
    private void openItemGroupsManagementWindow() {
        try {
            ItemGroupsManagementWindow window = new ItemGroupsManagementWindow();
            window.setVisible(true);
        } catch (Exception e) {
            showMessage("خطأ في فتح نافذة مجموعات الأصناف: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * فتح نافذة بيانات الأصناف الحقيقية
     */
    private void openAdvancedItemDataWindow() {
        try {
            RealItemDataWindow window = new RealItemDataWindow();
            window.setVisible(true);
        } catch (Exception e) {
            showMessage("خطأ في فتح نافذة بيانات الأصناف: " + e.getMessage());
        }
    }

    /**
     * فتح نافذة بيانات الأصناف الشاملة - جميع الحقول مع التعليقات العربية
     */
    private void openComprehensiveItemDataWindow() {
        try {
            ComprehensiveItemDataWindow window = new ComprehensiveItemDataWindow();
            window.setVisible(true);
        } catch (Exception e) {
            showMessage("خطأ في فتح نافذة بيانات الأصناف الشاملة: " + e.getMessage());
            e.printStackTrace();
        }
    }



    /**
     * فتح نافذة ربط النظام واستيراد البيانات
     */
    private void openSystemIntegrationWindow() {
        try {
            AdvancedSystemIntegrationWindow window = new AdvancedSystemIntegrationWindow();
            window.setVisible(true);
        } catch (Exception e) {
            showMessage("خطأ في فتح نافذة ربط النظام: " + e.getMessage());
        }
    }

    /**
     * فتح نافذة إدارة الربط والاستيراد
     */
    private void openSystemIntegrationManagementWindow() {
        SwingUtilities.invokeLater(() -> {
            try {
                System.out.println("🔄 فتح نافذة إدارة الربط والاستيراد...");

                SystemIntegrationWindow window = new SystemIntegrationWindow();
                window.setVisible(true);

                System.out.println("✅ تم فتح نافذة إدارة الربط والاستيراد بنجاح!");

            } catch (Exception e) {
                System.err.println("❌ خطأ في فتح نافذة إدارة الربط والاستيراد: " + e.getMessage());
                e.printStackTrace();

                String errorMessage = "فشل في فتح نافذة إدارة الربط والاستيراد:\n" + e.getMessage();

                if (e.getMessage() != null) {
                    if (e.getMessage().contains("oracle") || e.getMessage().contains("ORA-")) {
                        errorMessage += "\n\nتأكد من:\n" + "• تشغيل قاعدة البيانات Oracle\n"
                                + "• وجود المستخدم ship_erp\n"
                                + "• تنفيذ سكريبت إنشاء جداول النظام";
                    } else if (e.getMessage().contains("ClassNotFoundException")) {
                        errorMessage += "\n\nتأكد من وجود ملف ojdbc11.jar في مجلد lib";
                    }
                }

                showMessage(errorMessage);
            }
        });
    }

    /**
     * فتح نافذة تقارير الأصناف
     */
    private void openItemReportsWindow() {
        try {
            ItemReportsWindow window = new ItemReportsWindow();
            window.setVisible(true);
        } catch (Exception e) {
            showMessage("خطأ في فتح نافذة تقارير الأصناف: " + e.getMessage());
        }
    }

    /**
     * عرض نافذة حول البرنامج
     */
    private void showAboutDialog() {
        String aboutText = "نظام إدارة الشحنات المتقدم\n" + "الإصدار: 1.0\n"
                + "تطوير: فريق التطوير\n" + "حقوق الطبع محفوظة © 2024";

        JOptionPane.showMessageDialog(this, aboutText, "حول البرنامج",
                JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * فئة مخصصة لعرض خلايا الشجرة
     */
    private class CustomTreeCellRenderer extends DefaultTreeCellRenderer {

        public CustomTreeCellRenderer() {
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        }

        @Override
        public Component getTreeCellRendererComponent(JTree tree, Object value, boolean selected,
                boolean expanded, boolean leaf, int row, boolean hasFocus) {

            super.getTreeCellRendererComponent(tree, value, selected, expanded, leaf, row,
                    hasFocus);

            // إعداد الخط العربي
            setFont(arabicFont);
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            setHorizontalAlignment(SwingConstants.RIGHT);

            // تخصيص الأيقونات
            DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;
            String nodeText = node.toString();

            if (node.isRoot()) {
                setIcon(createColoredIcon(new Color(52, 152, 219)));
            } else if (!leaf) {
                // أيقونات المجلدات الرئيسية
                if (nodeText.contains("الشحنات")) {
                    setIcon(createColoredIcon(new Color(40, 167, 69)));
                } else if (nodeText.contains("العملاء")) {
                    setIcon(createColoredIcon(new Color(255, 193, 7)));
                } else if (nodeText.contains("المحاسبة")) {
                    setIcon(createColoredIcon(new Color(220, 53, 69)));
                } else if (nodeText.contains("المخزون")) {
                    setIcon(createColoredIcon(new Color(111, 66, 193)));
                } else if (nodeText.contains("التقارير")) {
                    setIcon(createColoredIcon(new Color(23, 162, 184)));
                } else if (nodeText.contains("الإعدادات")) {
                    setIcon(createColoredIcon(new Color(108, 117, 125)));
                } else if (nodeText.contains("المساعدة")) {
                    setIcon(createColoredIcon(new Color(255, 87, 34)));
                } else {
                    setIcon(createColoredIcon(new Color(76, 175, 80)));
                }
            } else {
                // أيقونات العناصر الفرعية
                setIcon(createColoredIcon(new Color(158, 158, 158)));
            }

            // تخصيص الألوان
            if (selected) {
                setBackgroundSelectionColor(new Color(52, 152, 219, 100));
                setTextSelectionColor(Color.BLACK);
            } else {
                setBackgroundNonSelectionColor(Color.WHITE);
                setTextNonSelectionColor(Color.BLACK);
            }

            return this;
        }

        @Override
        protected void paintComponent(Graphics g) {
            // تفعيل مكافحة التشويش للنص العربي
            if (g instanceof Graphics2D) {
                Graphics2D g2d = (Graphics2D) g;
                g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,
                        RenderingHints.VALUE_TEXT_ANTIALIAS_LCD_HRGB);
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING,
                        RenderingHints.VALUE_RENDER_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS,
                        RenderingHints.VALUE_FRACTIONALMETRICS_ON);
            }

            super.paintComponent(g);
        }

        /**
         * إنشاء أيقونة ملونة
         */
        private Icon createColoredIcon(Color color) {
            return new Icon() {
                @Override
                public void paintIcon(Component c, Graphics g, int x, int y) {
                    Graphics2D g2d = (Graphics2D) g.create();
                    g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING,
                            RenderingHints.VALUE_ANTIALIAS_ON);
                    g2d.setColor(color);
                    g2d.fillOval(x + 2, y + 2, getIconWidth() - 4, getIconHeight() - 4);
                    g2d.setColor(color.darker());
                    g2d.drawOval(x + 2, y + 2, getIconWidth() - 4, getIconHeight() - 4);
                    g2d.dispose();
                }

                @Override
                public int getIconWidth() {
                    return 16;
                }

                @Override
                public int getIconHeight() {
                    return 16;
                }
            };
        }
    }

    /**
     * توسيع جميع العقد
     */
    public void expandAllNodes() {
        for (int i = 0; i < menuTree.getRowCount(); i++) {
            menuTree.expandRow(i);
        }
    }

    /**
     * طي جميع العقد
     */
    public void collapseAllNodes() {
        for (int i = menuTree.getRowCount() - 1; i >= 1; i--) {
            menuTree.collapseRow(i);
        }
    }

    /**
     * البحث في القائمة
     */
    public void searchInMenu(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            expandAllNodes();
            return;
        }

        collapseAllNodes();
        searchAndExpand(rootNode, searchText.toLowerCase());
    }

    /**
     * البحث وتوسيع العقد المطابقة
     */
    private boolean searchAndExpand(DefaultMutableTreeNode node, String searchText) {
        boolean found = false;

        if (node.toString().toLowerCase().contains(searchText)) {
            TreePath path = new TreePath(node.getPath());
            menuTree.expandPath(path);
            found = true;
        }

        for (int i = 0; i < node.getChildCount(); i++) {
            DefaultMutableTreeNode child = (DefaultMutableTreeNode) node.getChildAt(i);
            if (searchAndExpand(child, searchText)) {
                TreePath path = new TreePath(node.getPath());
                menuTree.expandPath(path);
                found = true;
            }
        }

        return found;
    }
}


