# نظام ربط النظام المتقدم - استيراد البيانات من Oracle
## Advanced System Integration - Oracle Data Import System

---

## 🎯 نظرة عامة

تم تطوير نظام ربط متقدم وشامل للاتصال بقواعد بيانات Oracle واستيراد بيانات الأصناف من الأنظمة المحاسبية الأخرى. النظام يوفر واجهة متقدمة لتوحيد البيانات بين النظامين.

---

## 🚀 الميزات الرئيسية

### **🔗 الاتصال بقاعدة البيانات**:
- **اتصال آمن** بقواعد بيانات Oracle
- **اختبار الاتصال** قبل البدء
- **معلومات تفصيلية** عن قاعدة البيانات
- **إدارة الجلسات** المتقدمة

### **🔍 استكشاف البيانات**:
- **عرض الجداول المتاحة** تلقائياً
- **معاينة هيكل الجداول** بالتفصيل
- **معاينة البيانات** قبل الاستيراد
- **إحصائيات الجداول** (عدد السجلات)

### **🔗 تطابق الحقول**:
- **ربط ذكي** بين حقول Oracle وحقول النظام
- **تطابق تلقائي** للحقول المتشابهة
- **معاينة التطابق** قبل الاستيراد
- **مرونة في التخصيص**

### **📥 استيراد متقدم**:
- **شروط WHERE** مخصصة
- **حد أقصى للسجلات** قابل للتحديد
- **خيارات متقدمة**: التحقق، تخطي المكررات، تحديث الموجود
- **سجل مفصل** لعملية الاستيراد

### **📊 تقارير وإحصائيات**:
- **تاريخ عمليات الاستيراد**
- **إحصائيات مفصلة** لكل عملية
- **تصدير السجلات** والتقارير

---

## 🏗️ الهيكل التقني

### **الملفات المطورة**:

#### **1. DatabaseConfig.java**:
```java
// إعداد وإدارة الاتصال بقاعدة البيانات
- إعدادات الاتصال (Host, Port, Service, User, Password)
- اختبار الاتصال
- معلومات قاعدة البيانات
- إدارة الجلسات
```

#### **2. OracleItemImporter.java**:
```java
// محرك استيراد البيانات من Oracle
- الاتصال بقاعدة البيانات
- استكشاف الجداول والهياكل
- معاينة البيانات
- استيراد البيانات مع التطابق
- تحويل البيانات إلى ItemData
```

#### **3. AdvancedSystemIntegrationWindow.java**:
```java
// النافذة الرئيسية للنظام
- 5 تبويبات متخصصة
- واجهة عربية كاملة مع RTL
- معالجة الأحداث المتقدمة
- تقدم العمليات في الوقت الفعلي
```

#### **4. setup_libraries.bat**:
```batch
// ملف إعداد المكتبات المطلوبة
- تحميل مكتبة Oracle JDBC
- تحميل مكتبات Apache Commons
- إنشاء ملفات التجميع والتشغيل
```

---

## 📋 التبويبات المتخصصة

### **🔗 تبويب الاتصال**:
#### **الحقول المطلوبة**:
- **عنوان الخادم**: localhost (افتراضي)
- **المنفذ**: 1521 (افتراضي)
- **اسم الخدمة**: XE (افتراضي)
- **اسم المستخدم**: hr (افتراضي)
- **كلمة المرور**: hr (افتراضي)

#### **الأزرار**:
- **🟡 اختبار الاتصال**: فحص الاتصال دون حفظ
- **🟢 الاتصال**: الاتصال الفعلي وتفعيل النظام
- **🔴 قطع الاتصال**: إنهاء الجلسة

#### **منطقة الحالة**:
- **مؤشر الحالة**: ⚪ غير متصل / 🟢 متصل / 🔴 خطأ
- **سجل الاتصال**: تفاصيل عملية الاتصال
- **معلومات قاعدة البيانات**: النسخة، التعريف، المستخدم

### **🔍 تبويب استكشاف البيانات**:
#### **الجانب الأيمن - الجداول**:
- **قائمة الجداول**: جداول تحتوي على ITEM أو PRODUCT
- **🔄 تحديث**: تحديث قائمة الجداول
- **عدد السجلات**: إحصائية فورية
- **هيكل الجدول**: تفاصيل الأعمدة والأنواع

#### **الجانب الأيسر - المعاينة**:
- **👁️ معاينة البيانات**: عرض أول 10 سجلات
- **جدول تفاعلي**: عرض البيانات بشكل منظم
- **تمرير أفقي**: لعرض جميع الأعمدة

### **🔗 تبويب تطابق الحقول**:
#### **الجانب الأيمن - جدول التطابق**:
- **حقل قاعدة البيانات**: اسم العمود في Oracle
- **نوع البيانات**: VARCHAR2, NUMBER, DATE, إلخ
- **حقل النظام**: قائمة منسدلة بحقول النظام
- **مطلوب**: هل الحقل إجباري أم لا

#### **حقول النظام المدعومة**:
```
- code: كود الصنف
- name_ar: الاسم العربي
- name_en: الاسم الإنجليزي
- description: الوصف
- category_code: كود المجموعة
- unit_code: كود وحدة القياس
- sales_price: سعر البيع
- cost_price: سعر التكلفة
- current_stock: المخزون الحالي
- min_stock_level: الحد الأدنى للمخزون
- is_active: نشط/غير نشط
- barcode: الباركود
- supplier: المورد
- manufacturer: الشركة المصنعة
```

#### **الأزرار**:
- **🟢 تطابق تلقائي**: ربط تلقائي للحقول المتشابهة
- **🟡 مسح التطابق**: إعادة تعيين جميع التطابقات

#### **الجانب الأيسر - معاينة التطابق**:
- **عرض التطابق**: ملخص الربط بين الحقول
- **تحذيرات**: حقول مطلوبة غير مربوطة
- **إحصائيات**: عدد الحقول المربوطة

### **📥 تبويب الاستيراد**:
#### **إعدادات الاستيراد**:
- **شرط WHERE**: تصفية السجلات (اختياري)
  ```sql
  مثال: ACTIVE = 'Y' AND PRICE > 0
  ```
- **الحد الأقصى للسجلات**: 1000 (افتراضي)

#### **خيارات الاستيراد**:
- **☑️ التحقق من صحة البيانات**: فحص البيانات قبل الحفظ
- **☑️ تخطي المكررات**: تجاهل الأكواد الموجودة
- **☐ تحديث الموجود**: تحديث البيانات الموجودة

#### **أزرار التحكم**:
- **🟢 بدء الاستيراد**: تنفيذ عملية الاستيراد
- **🔴 إيقاف الاستيراد**: إيقاف العملية

#### **شريط التقدم**:
- **مؤشر التقدم**: نسبة الإنجاز
- **الحالة**: "جاهز للاستيراد" / "جاري الاستيراد..." / "مكتمل"

#### **سجل الاستيراد**:
- **خلفية سوداء** مع نص أخضر (مثل Terminal)
- **سجل مفصل** لكل خطوة
- **رسائل الأخطاء** والتحذيرات
- **إحصائيات النهائية**

### **📊 تبويب التقارير**:
#### **جدول تاريخ الاستيراد**:
- **التاريخ**: وقت تنفيذ العملية
- **الجدول**: اسم الجدول المستورد منه
- **السجلات المستوردة**: عدد السجلات الناجحة
- **السجلات المرفوضة**: عدد السجلات المرفوضة
- **الحالة**: نجح / فشل / جزئي
- **الوقت المستغرق**: مدة العملية

#### **أزرار التقارير**:
- **📊 إنشاء تقرير**: تقرير مفصل عن العمليات
- **📤 تصدير السجل**: حفظ السجل في ملف

---

## 🔧 متطلبات التثبيت

### **المكتبات المطلوبة**:
1. **ojdbc11.jar** - Oracle JDBC Driver
2. **commons-dbcp2-2.9.0.jar** - Connection Pooling
3. **commons-pool2-2.11.1.jar** - Object Pooling
4. **json-20230227.jar** - JSON Processing

### **خطوات التثبيت**:
1. **تشغيل ملف الإعداد**:
   ```batch
   cd src/main/java
   setup_libraries.bat
   ```

2. **تحميل المكتبات**:
   - ضع جميع ملفات .jar في مجلد `lib`
   - تأكد من صحة أسماء الملفات

3. **التجميع**:
   ```batch
   compile_with_libs.bat
   ```

4. **التشغيل**:
   ```batch
   run_with_libs.bat
   ```

---

## 🚀 طريقة الاستخدام

### **الخطوة 1: إعداد الاتصال**
1. افتح **إدارة الأصناف** → **ربط النظام واستيراد البيانات**
2. أدخل بيانات الاتصال بقاعدة البيانات Oracle
3. اضغط **"اختبار الاتصال"** للتأكد من صحة البيانات
4. اضغط **"الاتصال"** للاتصال الفعلي

### **الخطوة 2: استكشاف البيانات**
1. انتقل لتبويب **"استكشاف البيانات"**
2. اختر جدولاً من القائمة المنسدلة
3. اضغط **"معاينة البيانات"** لرؤية عينة من البيانات
4. راجع هيكل الجدول في الجانب الأيمن

### **الخطوة 3: تطابق الحقول**
1. انتقل لتبويب **"تطابق الحقول"**
2. اضغط **"تطابق تلقائي"** للربط التلقائي
3. راجع التطابقات وعدّل حسب الحاجة
4. تأكد من ربط الحقول المطلوبة (code, name_ar)

### **الخطوة 4: تنفيذ الاستيراد**
1. انتقل لتبويب **"الاستيراد"**
2. حدد شرط WHERE إذا لزم الأمر
3. اختر الحد الأقصى للسجلات
4. فعّل الخيارات المطلوبة
5. اضغط **"بدء الاستيراد"**
6. راقب التقدم في السجل

### **الخطوة 5: مراجعة التقارير**
1. انتقل لتبويب **"التقارير"**
2. راجع تاريخ العمليات
3. أنشئ تقارير مفصلة حسب الحاجة

---

## ⚠️ ملاحظات مهمة

### **الأمان**:
- **لا تحفظ كلمات المرور** في ملفات النصوص
- **استخدم حسابات محدودة الصلاحيات** لقاعدة البيانات
- **اختبر على بيانات تجريبية** أولاً

### **الأداء**:
- **ابدأ بعدد قليل من السجلات** للاختبار
- **استخدم شروط WHERE** لتقليل البيانات
- **راقب استهلاك الذاكرة** مع البيانات الكبيرة

### **استكشاف الأخطاء**:
- **تأكد من تثبيت Oracle JDBC** بشكل صحيح
- **تحقق من بيانات الاتصال** (Host, Port, Service)
- **راجع سجل الأخطاء** في تبويب الاستيراد

---

## 🎊 النتائج المحققة

### **✅ نظام ربط متقدم**:
- **🏆 اتصال آمن** بقواعد بيانات Oracle
- **🏆 استكشاف ذكي** للجداول والبيانات
- **🏆 تطابق مرن** للحقول
- **🏆 استيراد متقدم** مع خيارات شاملة
- **🏆 تقارير مفصلة** وإحصائيات

### **✅ واجهة احترافية**:
- **🏆 5 تبويبات متخصصة** لكل مرحلة
- **🏆 واجهة عربية كاملة** مع دعم RTL
- **🏆 تصميم حديث** بألوان متناسقة
- **🏆 تفاعل ذكي** مع ردود فعل فورية

### **✅ مرونة وقابلية التوسع**:
- **🏆 دعم جداول متعددة** في نفس قاعدة البيانات
- **🏆 تطابق مخصص** للحقول
- **🏆 شروط مرنة** للاستيراد
- **🏆 قابلية التوسع** لقواعد بيانات أخرى

---

**🎉 نظام ربط النظام المتقدم جاهز للاستخدام! يمكنك الآن توحيد بيانات الأصناف بين النظامين بسهولة واحترافية!**
