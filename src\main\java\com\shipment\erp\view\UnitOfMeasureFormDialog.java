package com.shipment.erp.view;

import com.shipment.erp.model.UnitOfMeasure;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * نموذج إضافة/تعديل وحدة القياس
 * Unit of Measure Form Dialog
 */
public class UnitOfMeasureFormDialog extends JDialog {
    
    private Font arabicFont;
    private boolean isNewUnit;
    private boolean confirmed = false;
    
    // Form Components
    private JTextField codeField, nameArField, nameEnField, symbolField;
    private JTextArea descriptionArea, notesArea;
    private JCheckBox isActiveCheck, isBaseUnitCheck;
    private JComboBox<String> baseUnitCombo;
    private JSpinner conversionFactorSpinner, sortOrderSpinner;
    private JButton saveButton, cancelButton;
    
    public UnitOfMeasureFormDialog(Frame parent, String title, boolean isNewUnit) {
        super(parent, title, true);
        this.arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        this.isNewUnit = isNewUnit;
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        setSize(500, 600);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(false);
    }
    
    private void initializeComponents() {
        // حقول النص
        codeField = new JTextField();
        codeField.setFont(arabicFont);
        codeField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        codeField.setPreferredSize(new Dimension(200, 30));
        
        nameArField = new JTextField();
        nameArField.setFont(arabicFont);
        nameArField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        nameArField.setPreferredSize(new Dimension(200, 30));
        
        nameEnField = new JTextField();
        nameEnField.setFont(arabicFont);
        nameEnField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        nameEnField.setPreferredSize(new Dimension(200, 30));
        
        symbolField = new JTextField();
        symbolField.setFont(arabicFont);
        symbolField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        symbolField.setPreferredSize(new Dimension(200, 30));
        
        // مناطق النص
        descriptionArea = new JTextArea(3, 20);
        descriptionArea.setFont(arabicFont);
        descriptionArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        descriptionArea.setLineWrap(true);
        descriptionArea.setWrapStyleWord(true);
        descriptionArea.setBorder(BorderFactory.createLoweredBevelBorder());
        
        notesArea = new JTextArea(3, 20);
        notesArea.setFont(arabicFont);
        notesArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        notesArea.setLineWrap(true);
        notesArea.setWrapStyleWord(true);
        notesArea.setBorder(BorderFactory.createLoweredBevelBorder());
        
        // مربعات الاختيار
        isActiveCheck = new JCheckBox("نشط");
        isActiveCheck.setFont(arabicFont);
        isActiveCheck.setSelected(true);
        
        isBaseUnitCheck = new JCheckBox("وحدة أساسية");
        isBaseUnitCheck.setFont(arabicFont);
        
        // قائمة الوحدة الأساسية
        baseUnitCombo = new JComboBox<>();
        baseUnitCombo.setFont(arabicFont);
        baseUnitCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        baseUnitCombo.setPreferredSize(new Dimension(200, 30));
        
        // إضافة بيانات تجريبية للوحدات الأساسية
        baseUnitCombo.addItem("كيلوجرام");
        baseUnitCombo.addItem("لتر");
        baseUnitCombo.addItem("متر");
        baseUnitCombo.addItem("قطعة");
        
        // المدورات
        conversionFactorSpinner = new JSpinner(new SpinnerNumberModel(1.0, 0.001, 999999.0, 0.001));
        conversionFactorSpinner.setFont(arabicFont);
        conversionFactorSpinner.setPreferredSize(new Dimension(200, 30));
        
        sortOrderSpinner = new JSpinner(new SpinnerNumberModel(0, 0, 9999, 1));
        sortOrderSpinner.setFont(arabicFont);
        sortOrderSpinner.setPreferredSize(new Dimension(200, 30));
        
        // الأزرار
        saveButton = new JButton("حفظ");
        saveButton.setFont(arabicFont);
        saveButton.setBackground(new Color(40, 167, 69));
        saveButton.setForeground(Color.WHITE);
        saveButton.setPreferredSize(new Dimension(100, 35));
        saveButton.setFocusPainted(false);
        saveButton.setBorderPainted(false);
        saveButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        
        cancelButton = new JButton("إلغاء");
        cancelButton.setFont(arabicFont);
        cancelButton.setBackground(new Color(108, 117, 125));
        cancelButton.setForeground(Color.WHITE);
        cancelButton.setPreferredSize(new Dimension(100, 35));
        cancelButton.setFocusPainted(false);
        cancelButton.setBorderPainted(false);
        cancelButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BoxLayout(mainPanel, BoxLayout.Y_AXIS));
        mainPanel.setBorder(new EmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // إضافة الحقول
        mainPanel.add(createFieldPanel("الكود:", codeField, true));
        mainPanel.add(Box.createVerticalStrut(10));
        
        mainPanel.add(createFieldPanel("الاسم العربي:", nameArField, true));
        mainPanel.add(Box.createVerticalStrut(10));
        
        mainPanel.add(createFieldPanel("الاسم الإنجليزي:", nameEnField, false));
        mainPanel.add(Box.createVerticalStrut(10));
        
        mainPanel.add(createFieldPanel("الرمز:", symbolField, false));
        mainPanel.add(Box.createVerticalStrut(10));
        
        mainPanel.add(createTextAreaPanel("الوصف:", descriptionArea));
        mainPanel.add(Box.createVerticalStrut(10));
        
        // لوحة الخيارات
        JPanel optionsPanel = createOptionsPanel();
        mainPanel.add(optionsPanel);
        mainPanel.add(Box.createVerticalStrut(10));
        
        mainPanel.add(createFieldPanel("الوحدة الأساسية:", baseUnitCombo, false));
        mainPanel.add(Box.createVerticalStrut(10));
        
        mainPanel.add(createFieldPanel("معامل التحويل:", conversionFactorSpinner, false));
        mainPanel.add(Box.createVerticalStrut(10));
        
        mainPanel.add(createFieldPanel("ترتيب العرض:", sortOrderSpinner, false));
        mainPanel.add(Box.createVerticalStrut(10));
        
        mainPanel.add(createTextAreaPanel("ملاحظات:", notesArea));
        
        // لوحة الأزرار
        JPanel buttonPanel = createButtonPanel();
        
        add(mainPanel, BorderLayout.CENTER);
        add(buttonPanel, BorderLayout.SOUTH);
    }
    
    private JPanel createFieldPanel(String labelText, JComponent field, boolean required) {
        JPanel panel = new JPanel(new BorderLayout(10, 0));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setMaximumSize(new Dimension(Integer.MAX_VALUE, 35));
        
        JLabel label = new JLabel(labelText);
        label.setFont(arabicFont);
        label.setPreferredSize(new Dimension(120, 30));
        
        if (required) {
            label.setText(labelText + " *");
            label.setForeground(new Color(220, 53, 69));
        }
        
        panel.add(label, BorderLayout.EAST);
        panel.add(field, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createTextAreaPanel(String labelText, JTextArea textArea) {
        JPanel panel = new JPanel(new BorderLayout(10, 5));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setMaximumSize(new Dimension(Integer.MAX_VALUE, 80));
        
        JLabel label = new JLabel(labelText);
        label.setFont(arabicFont);
        
        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setPreferredSize(new Dimension(200, 60));
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        
        panel.add(label, BorderLayout.NORTH);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createOptionsPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setMaximumSize(new Dimension(Integer.MAX_VALUE, 40));
        
        panel.add(isActiveCheck);
        panel.add(Box.createHorizontalStrut(20));
        panel.add(isBaseUnitCheck);
        
        return panel;
    }
    
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 15));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createMatteBorder(1, 0, 0, 0, Color.LIGHT_GRAY));
        panel.setBackground(new Color(248, 249, 250));
        
        panel.add(saveButton);
        panel.add(cancelButton);
        
        return panel;
    }
    
    private void setupEventHandlers() {
        // تفعيل/إلغاء تفعيل الوحدة الأساسية ومعامل التحويل
        isBaseUnitCheck.addActionListener(e -> {
            boolean isBaseUnit = isBaseUnitCheck.isSelected();
            baseUnitCombo.setEnabled(!isBaseUnit);
            conversionFactorSpinner.setEnabled(!isBaseUnit);
            
            if (isBaseUnit) {
                conversionFactorSpinner.setValue(1.0);
            }
        });
        
        // أحداث الأزرار
        saveButton.addActionListener(e -> saveUnit());
        cancelButton.addActionListener(e -> dispose());
        
        // إغلاق النافذة بمفتاح Escape
        KeyStroke escapeKeyStroke = KeyStroke.getKeyStroke("ESCAPE");
        getRootPane().getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW).put(escapeKeyStroke, "ESCAPE");
        getRootPane().getActionMap().put("ESCAPE", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dispose();
            }
        });
        
        // تعيين زر الحفظ كزر افتراضي
        getRootPane().setDefaultButton(saveButton);
    }
    
    private void saveUnit() {
        try {
            // التحقق من صحة البيانات
            if (!validateForm()) {
                return;
            }
            
            // إنشاء كائن وحدة القياس
            UnitOfMeasure unit = new UnitOfMeasure();
            unit.setCode(codeField.getText().trim());
            unit.setNameAr(nameArField.getText().trim());
            unit.setNameEn(nameEnField.getText().trim());
            unit.setSymbol(symbolField.getText().trim());
            unit.setDescription(descriptionArea.getText().trim());
            unit.setIsActive(isActiveCheck.isSelected());
            unit.setIsBaseUnit(isBaseUnitCheck.isSelected());
            unit.setConversionFactor((Double) conversionFactorSpinner.getValue());
            unit.setSortOrder((Integer) sortOrderSpinner.getValue());
            unit.setNotes(notesArea.getText().trim());
            
            // حفظ الوحدة (سيتم تنفيذه لاحقاً مع الخدمة)
            confirmed = true;
            dispose();
            
            JOptionPane.showMessageDialog(getParent(),
                "تم حفظ وحدة القياس بنجاح",
                "نجح الحفظ",
                JOptionPane.INFORMATION_MESSAGE);
                
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this,
                "خطأ في حفظ وحدة القياس: " + e.getMessage(),
                "خطأ",
                JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private boolean validateForm() {
        // التحقق من الحقول المطلوبة
        if (codeField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "كود وحدة القياس مطلوب", "خطأ في البيانات", JOptionPane.ERROR_MESSAGE);
            codeField.requestFocus();
            return false;
        }
        
        if (nameArField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "الاسم العربي مطلوب", "خطأ في البيانات", JOptionPane.ERROR_MESSAGE);
            nameArField.requestFocus();
            return false;
        }
        
        // التحقق من طول الحقول
        if (codeField.getText().trim().length() > 20) {
            JOptionPane.showMessageDialog(this, "كود وحدة القياس يجب أن يكون أقل من 20 حرف", "خطأ في البيانات", JOptionPane.ERROR_MESSAGE);
            codeField.requestFocus();
            return false;
        }
        
        if (nameArField.getText().trim().length() > 100) {
            JOptionPane.showMessageDialog(this, "الاسم العربي يجب أن يكون أقل من 100 حرف", "خطأ في البيانات", JOptionPane.ERROR_MESSAGE);
            nameArField.requestFocus();
            return false;
        }
        
        return true;
    }
    
    public void loadUnitData(int rowIndex, DefaultTableModel tableModel) {
        // تحميل بيانات الوحدة للتعديل
        codeField.setText((String) tableModel.getValueAt(rowIndex, 0));
        nameArField.setText((String) tableModel.getValueAt(rowIndex, 1));
        nameEnField.setText((String) tableModel.getValueAt(rowIndex, 2));
        symbolField.setText((String) tableModel.getValueAt(rowIndex, 3));
        
        String isBaseUnit = (String) tableModel.getValueAt(rowIndex, 4);
        isBaseUnitCheck.setSelected("نعم".equals(isBaseUnit));
        
        Double conversionFactor = (Double) tableModel.getValueAt(rowIndex, 5);
        conversionFactorSpinner.setValue(conversionFactor);
        
        Boolean isActive = (Boolean) tableModel.getValueAt(rowIndex, 6);
        isActiveCheck.setSelected(isActive);
        
        Integer sortOrder = (Integer) tableModel.getValueAt(rowIndex, 7);
        sortOrderSpinner.setValue(sortOrder);
        
        // تحديث حالة الحقول
        boolean isBase = isBaseUnitCheck.isSelected();
        baseUnitCombo.setEnabled(!isBase);
        conversionFactorSpinner.setEnabled(!isBase);
    }
    
    public boolean isConfirmed() {
        return confirmed;
    }
}
