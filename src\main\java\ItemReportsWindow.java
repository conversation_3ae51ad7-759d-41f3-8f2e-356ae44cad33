import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * نافذة تقارير الأصناف
 * Item Reports Window
 */
public class ItemReportsWindow extends J<PERSON>rame {
    
    private JTabbedPane tabbedPane;
    private JTable reportsTable;
    private DefaultTableModel tableModel;
    private JComboBox<String> reportTypeCombo, categoryFilterCombo, periodFilterCombo;
    private JButton generateButton, exportButton, printButton, refreshButton;
    private JTextArea summaryArea;
    private Font arabicFont;
    private List<ItemData> itemsList;
    private List<ItemCategoryData> categoriesList;
    private List<UnitOfMeasureData> unitsList;
    
    public ItemReportsWindow() {
        this.arabicFont = new Font("Ta<PERSON>a", Font.PLAIN, 12);
        this.itemsList = new ArrayList<>();
        this.categoriesList = new ArrayList<>();
        this.unitsList = new ArrayList<>();
        
        initializeData();
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        generateSummaryReport();
        
        setTitle("تقارير الأصناف - Item Reports");
        setSize(1100, 700);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
    }
    
    /**
     * تهيئة البيانات التجريبية
     */
    private void initializeData() {
        // تهيئة المجموعات
        categoriesList.add(new ItemCategoryData("ELEC", "الإلكترونيات", "Electronics", "مجموعة الأجهزة الإلكترونية", null, 1, true, "📱"));
        categoriesList.add(new ItemCategoryData("PHONE", "الهواتف الذكية", "Smartphones", "هواتف ذكية وملحقاتها", "ELEC", 2, true, "📱"));
        categoriesList.add(new ItemCategoryData("CLOTH", "الملابس", "Clothing", "مجموعة الملابس والأزياء", null, 1, true, "👔"));
        categoriesList.add(new ItemCategoryData("FOOD", "المواد الغذائية", "Food Items", "مجموعة المواد الغذائية", null, 1, true, "🍎"));
        
        // تهيئة وحدات القياس
        unitsList.add(new UnitOfMeasureData("PCS", "قطعة", "Piece", "ق", true, null, 1.0, true, "وحدة العد الأساسية"));
        unitsList.add(new UnitOfMeasureData("KG", "كيلوجرام", "Kilogram", "كجم", true, null, 1.0, true, "وحدة قياس الوزن"));
        unitsList.add(new UnitOfMeasureData("L", "لتر", "Liter", "لتر", true, null, 1.0, true, "وحدة قياس الحجم"));
        
        // تهيئة الأصناف التجريبية
        itemsList.add(new ItemData("PHONE001", "هاتف سامسونج جالاكسي S23", "Samsung Galaxy S23", 
            "هاتف ذكي متطور", "PHONE", "PCS", 2500.0, 2000.0, 50, 10, true, true, false, true));
        itemsList.add(new ItemData("PHONE002", "هاتف آيفون 14", "iPhone 14", 
            "هاتف آيفون من آبل", "PHONE", "PCS", 3500.0, 3000.0, 30, 5, true, true, false, true));
        itemsList.add(new ItemData("PHONE003", "هاتف هواوي P50", "Huawei P50", 
            "هاتف هواوي متطور", "PHONE", "PCS", 2200.0, 1800.0, 0, 5, true, true, false, true));
        itemsList.add(new ItemData("SHIRT001", "قميص قطني رجالي", "Men's Cotton Shirt", 
            "قميص قطني عالي الجودة", "CLOTH", "PCS", 150.0, 100.0, 100, 20, true, true, false, true));
        itemsList.add(new ItemData("SHIRT002", "قميص حريري نسائي", "Women's Silk Shirt", 
            "قميص حريري أنيق", "CLOTH", "PCS", 250.0, 180.0, 8, 15, true, true, false, true));
        itemsList.add(new ItemData("APPLE001", "تفاح أحمر", "Red Apple", 
            "تفاح أحمر طازج", "FOOD", "KG", 15.0, 10.0, 200, 50, true, true, false, true));
        itemsList.add(new ItemData("ORANGE001", "برتقال طازج", "Fresh Orange", 
            "برتقال طازج عالي الجودة", "FOOD", "KG", 12.0, 8.0, 150, 30, true, true, false, true));
        itemsList.add(new ItemData("LAPTOP001", "لابتوب ديل", "Dell Laptop", 
            "لابتوب ديل عالي الأداء", "ELEC", "PCS", 4500.0, 3800.0, 25, 5, true, true, false, true));
    }
    
    /**
     * تهيئة المكونات
     */
    private void initializeComponents() {
        // التبويبات
        tabbedPane = new JTabbedPane();
        tabbedPane.setFont(arabicFont);
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // القوائم المنسدلة
        reportTypeCombo = new JComboBox<>(new String[]{
            "ملخص الأصناف", "تقرير المخزون", "تقرير القيم", "تقرير المجموعات", 
            "الأصناف منخفضة المخزون", "الأصناف المنتهية", "تقرير الأسعار"
        });
        reportTypeCombo.setFont(arabicFont);
        reportTypeCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        categoryFilterCombo = new JComboBox<>();
        categoryFilterCombo.setFont(arabicFont);
        categoryFilterCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        populateCategoryFilter();
        
        periodFilterCombo = new JComboBox<>(new String[]{
            "جميع الفترات", "الشهر الحالي", "الشهر الماضي", "الربع الحالي", "السنة الحالية"
        });
        periodFilterCombo.setFont(arabicFont);
        periodFilterCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // الأزرار
        generateButton = createButton("إنشاء التقرير", new Color(40, 167, 69));
        exportButton = createButton("تصدير Excel", new Color(0, 123, 255));
        printButton = createButton("طباعة", new Color(108, 117, 125));
        refreshButton = createButton("تحديث", new Color(255, 193, 7));
        
        // الجدول
        String[] columnNames = {"البيان", "القيمة", "النسبة", "ملاحظات"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        reportsTable = new JTable(tableModel);
        reportsTable.setFont(arabicFont);
        reportsTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        reportsTable.setRowHeight(25);
        
        // منطقة الملخص
        summaryArea = new JTextArea();
        summaryArea.setFont(arabicFont);
        summaryArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        summaryArea.setEditable(false);
        summaryArea.setBackground(new Color(248, 249, 250));
        summaryArea.setBorder(BorderFactory.createTitledBorder("ملخص التقرير"));
    }
    
    /**
     * ملء فلتر المجموعات
     */
    private void populateCategoryFilter() {
        categoryFilterCombo.removeAllItems();
        categoryFilterCombo.addItem("جميع المجموعات");
        
        for (ItemCategoryData category : categoriesList) {
            categoryFilterCombo.addItem(category.getNameAr() + " (" + category.getCode() + ")");
        }
    }
    
    /**
     * إنشاء زر مخصص
     */
    private JButton createButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setFont(arabicFont);
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(120, 35));
        return button;
    }
    
    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // اللوحة العلوية - الفلاتر والأزرار
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        topPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // لوحة الفلاتر
        JPanel filtersPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        filtersPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel reportTypeLabel = new JLabel("نوع التقرير:");
        reportTypeLabel.setFont(arabicFont);
        reportTypeCombo.setPreferredSize(new Dimension(150, 30));
        
        JLabel categoryLabel = new JLabel("المجموعة:");
        categoryLabel.setFont(arabicFont);
        categoryFilterCombo.setPreferredSize(new Dimension(150, 30));
        
        JLabel periodLabel = new JLabel("الفترة:");
        periodLabel.setFont(arabicFont);
        periodFilterCombo.setPreferredSize(new Dimension(120, 30));
        
        filtersPanel.add(reportTypeLabel);
        filtersPanel.add(reportTypeCombo);
        filtersPanel.add(Box.createHorizontalStrut(10));
        filtersPanel.add(categoryLabel);
        filtersPanel.add(categoryFilterCombo);
        filtersPanel.add(Box.createHorizontalStrut(10));
        filtersPanel.add(periodLabel);
        filtersPanel.add(periodFilterCombo);
        
        // لوحة الأزرار
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        buttonPanel.add(generateButton);
        buttonPanel.add(exportButton);
        buttonPanel.add(printButton);
        buttonPanel.add(refreshButton);
        
        topPanel.add(filtersPanel, BorderLayout.EAST);
        topPanel.add(buttonPanel, BorderLayout.WEST);
        
        // إنشاء التبويبات
        createTabs();
        
        add(topPanel, BorderLayout.NORTH);
        add(tabbedPane, BorderLayout.CENTER);
    }
    
    /**
     * إنشاء التبويبات
     */
    private void createTabs() {
        // تبويب التقرير الرئيسي
        JScrollPane reportsScrollPane = new JScrollPane(reportsTable);
        reportsScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        tabbedPane.addTab("📊 التقرير الرئيسي", reportsScrollPane);
        
        // تبويب الملخص
        JScrollPane summaryScrollPane = new JScrollPane(summaryArea);
        summaryScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        tabbedPane.addTab("📋 ملخص التقرير", summaryScrollPane);
        
        // تبويب الرسوم البيانية
        JPanel chartsPanel = createChartsPanel();
        tabbedPane.addTab("📈 الرسوم البيانية", chartsPanel);
    }
    
    /**
     * إنشاء لوحة الرسوم البيانية
     */
    private JPanel createChartsPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 2, 10, 10));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        // بطاقات الرسوم البيانية
        panel.add(createChartCard("توزيع الأصناف حسب المجموعات", "📊"));
        panel.add(createChartCard("حالة المخزون", "📦"));
        panel.add(createChartCard("توزيع القيم", "💰"));
        panel.add(createChartCard("تحليل الأسعار", "📈"));
        
        return panel;
    }
    
    /**
     * إنشاء بطاقة رسم بياني
     */
    private JPanel createChartCard(String title, String icon) {
        JPanel card = new JPanel(new BorderLayout());
        card.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        card.setBorder(BorderFactory.createTitledBorder(title));
        card.setBackground(Color.WHITE);
        
        JLabel iconLabel = new JLabel("<html><center>" + icon + "<br><br>" + title + "<br><br>قيد التطوير</center></html>");
        iconLabel.setFont(new Font("Tahoma", Font.PLAIN, 14));
        iconLabel.setHorizontalAlignment(SwingConstants.CENTER);
        iconLabel.setForeground(Color.GRAY);
        
        card.add(iconLabel, BorderLayout.CENTER);
        
        return card;
    }
    
    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        generateButton.addActionListener(e -> generateSelectedReport());
        exportButton.addActionListener(e -> exportToExcel());
        printButton.addActionListener(e -> printReport());
        refreshButton.addActionListener(e -> refreshData());
        
        reportTypeCombo.addActionListener(e -> generateSelectedReport());
        categoryFilterCombo.addActionListener(e -> generateSelectedReport());
        periodFilterCombo.addActionListener(e -> generateSelectedReport());
    }
    
    /**
     * إنشاء التقرير المحدد
     */
    private void generateSelectedReport() {
        String reportType = (String) reportTypeCombo.getSelectedItem();
        
        switch (reportType) {
            case "ملخص الأصناف":
                generateSummaryReport();
                break;
            case "تقرير المخزون":
                generateInventoryReport();
                break;
            case "تقرير القيم":
                generateValuesReport();
                break;
            case "تقرير المجموعات":
                generateCategoriesReport();
                break;
            case "الأصناف منخفضة المخزون":
                generateLowStockReport();
                break;
            case "الأصناف المنتهية":
                generateOutOfStockReport();
                break;
            case "تقرير الأسعار":
                generatePricesReport();
                break;
            default:
                generateSummaryReport();
        }
    }
    
    /**
     * إنشاء تقرير الملخص
     */
    private void generateSummaryReport() {
        tableModel.setRowCount(0);
        
        // إحصائيات عامة
        tableModel.addRow(new Object[]{"إجمالي الأصناف", itemsList.size(), "100%", "جميع الأصناف في النظام"});
        
        long activeItems = itemsList.stream().filter(ItemData::isActive).count();
        double activePercentage = (double) activeItems / itemsList.size() * 100;
        tableModel.addRow(new Object[]{"الأصناف النشطة", activeItems, String.format("%.1f%%", activePercentage), "الأصناف المفعلة حالياً"});
        
        long lowStockItems = itemsList.stream().filter(item -> item.getCurrentStock() <= item.getMinStockLevel()).count();
        double lowStockPercentage = (double) lowStockItems / itemsList.size() * 100;
        tableModel.addRow(new Object[]{"مخزون منخفض", lowStockItems, String.format("%.1f%%", lowStockPercentage), "أصناف تحتاج إعادة تموين"});
        
        long outOfStockItems = itemsList.stream().filter(item -> item.getCurrentStock() == 0).count();
        double outOfStockPercentage = (double) outOfStockItems / itemsList.size() * 100;
        tableModel.addRow(new Object[]{"نفد المخزون", outOfStockItems, String.format("%.1f%%", outOfStockPercentage), "أصناف غير متوفرة"});
        
        double totalStockValue = itemsList.stream().mapToDouble(item -> item.getCurrentStock() * item.getCostPrice()).sum();
        tableModel.addRow(new Object[]{"إجمالي قيمة المخزون", formatCurrency(totalStockValue), "-", "القيمة الإجمالية للمخزون"});
        
        // تحديث الملخص
        updateSummaryText("تقرير ملخص الأصناف", 
            String.format("يحتوي النظام على %d صنف، منها %d نشط و %d منخفض المخزون", 
                itemsList.size(), activeItems, lowStockItems));
    }
    
    /**
     * إنشاء تقرير المخزون
     */
    private void generateInventoryReport() {
        tableModel.setRowCount(0);
        
        for (ItemData item : itemsList) {
            String status = getItemStatus(item);
            String stockLevel = item.getCurrentStock() + " " + getUnitName(item.getUnitCode());
            String minLevel = item.getMinStockLevel() + " " + getUnitName(item.getUnitCode());
            
            tableModel.addRow(new Object[]{
                item.getNameAr(),
                stockLevel,
                minLevel,
                status
            });
        }
        
        updateSummaryText("تقرير المخزون", "تفاصيل مستويات المخزون لجميع الأصناف");
    }
    
    /**
     * إنشاء تقرير القيم
     */
    private void generateValuesReport() {
        tableModel.setRowCount(0);
        
        for (ItemData item : itemsList) {
            double stockValue = item.getCurrentStock() * item.getCostPrice();
            double potentialRevenue = item.getCurrentStock() * item.getSalesPrice();
            double potentialProfit = potentialRevenue - stockValue;
            
            tableModel.addRow(new Object[]{
                item.getNameAr(),
                formatCurrency(stockValue),
                formatCurrency(potentialRevenue),
                formatCurrency(potentialProfit)
            });
        }
        
        updateSummaryText("تقرير القيم", "قيم الأصناف والإيرادات المحتملة");
    }
    
    /**
     * إنشاء تقرير المجموعات
     */
    private void generateCategoriesReport() {
        tableModel.setRowCount(0);
        
        Map<String, Long> categoryCount = itemsList.stream()
            .collect(Collectors.groupingBy(ItemData::getCategoryCode, Collectors.counting()));
        
        for (Map.Entry<String, Long> entry : categoryCount.entrySet()) {
            String categoryName = getCategoryName(entry.getKey());
            long count = entry.getValue();
            double percentage = (double) count / itemsList.size() * 100;
            
            tableModel.addRow(new Object[]{
                categoryName,
                count,
                String.format("%.1f%%", percentage),
                "عدد الأصناف في المجموعة"
            });
        }
        
        updateSummaryText("تقرير المجموعات", "توزيع الأصناف حسب المجموعات");
    }
    
    /**
     * إنشاء تقرير الأصناف منخفضة المخزون
     */
    private void generateLowStockReport() {
        tableModel.setRowCount(0);
        
        itemsList.stream()
            .filter(item -> item.getCurrentStock() <= item.getMinStockLevel())
            .forEach(item -> {
                double shortage = item.getMinStockLevel() - item.getCurrentStock();
                tableModel.addRow(new Object[]{
                    item.getNameAr(),
                    item.getCurrentStock(),
                    item.getMinStockLevel(),
                    shortage > 0 ? shortage : "نفد المخزون"
                });
            });
        
        updateSummaryText("الأصناف منخفضة المخزون", "الأصناف التي تحتاج إعادة تموين");
    }
    
    /**
     * إنشاء تقرير الأصناف المنتهية
     */
    private void generateOutOfStockReport() {
        tableModel.setRowCount(0);
        
        itemsList.stream()
            .filter(item -> item.getCurrentStock() == 0)
            .forEach(item -> {
                tableModel.addRow(new Object[]{
                    item.getNameAr(),
                    getCategoryName(item.getCategoryCode()),
                    item.getMinStockLevel(),
                    "مطلوب إعادة تموين فوري"
                });
            });
        
        updateSummaryText("الأصناف المنتهية", "الأصناف غير المتوفرة في المخزون");
    }
    
    /**
     * إنشاء تقرير الأسعار
     */
    private void generatePricesReport() {
        tableModel.setRowCount(0);
        
        for (ItemData item : itemsList) {
            double profitMargin = ((item.getSalesPrice() - item.getCostPrice()) / item.getSalesPrice()) * 100;
            
            tableModel.addRow(new Object[]{
                item.getNameAr(),
                formatCurrency(item.getCostPrice()),
                formatCurrency(item.getSalesPrice()),
                String.format("%.1f%%", profitMargin)
            });
        }
        
        updateSummaryText("تقرير الأسعار", "أسعار التكلفة والبيع وهوامش الربح");
    }
    
    /**
     * تحديث نص الملخص
     */
    private void updateSummaryText(String reportTitle, String description) {
        StringBuilder summary = new StringBuilder();
        summary.append("=== ").append(reportTitle).append(" ===\n\n");
        summary.append("الوصف: ").append(description).append("\n\n");
        summary.append("تاريخ الإنشاء: ").append(java.time.LocalDateTime.now().toString()).append("\n");
        summary.append("عدد السجلات: ").append(tableModel.getRowCount()).append("\n\n");
        
        // إضافة إحصائيات إضافية
        summary.append("=== إحصائيات عامة ===\n");
        summary.append("إجمالي الأصناف: ").append(itemsList.size()).append("\n");
        summary.append("الأصناف النشطة: ").append(itemsList.stream().filter(ItemData::isActive).count()).append("\n");
        summary.append("مخزون منخفض: ").append(itemsList.stream().filter(item -> item.getCurrentStock() <= item.getMinStockLevel()).count()).append("\n");
        summary.append("نفد المخزون: ").append(itemsList.stream().filter(item -> item.getCurrentStock() == 0).count()).append("\n");
        
        double totalValue = itemsList.stream().mapToDouble(item -> item.getCurrentStock() * item.getCostPrice()).sum();
        summary.append("إجمالي قيمة المخزون: ").append(formatCurrency(totalValue)).append("\n");
        
        summaryArea.setText(summary.toString());
    }
    
    // دوال مساعدة
    private String getCategoryName(String categoryCode) {
        return categoriesList.stream()
            .filter(cat -> cat.getCode().equals(categoryCode))
            .map(ItemCategoryData::getNameAr)
            .findFirst().orElse(categoryCode);
    }
    
    private String getUnitName(String unitCode) {
        return unitsList.stream()
            .filter(unit -> unit.getCode().equals(unitCode))
            .map(UnitOfMeasureData::getNameAr)
            .findFirst().orElse(unitCode);
    }
    
    private String getItemStatus(ItemData item) {
        if (!item.isActive()) return "غير نشط";
        if (item.getCurrentStock() == 0) return "نفد المخزون";
        if (item.getCurrentStock() <= item.getMinStockLevel()) return "مخزون منخفض";
        return "طبيعي";
    }
    
    private String formatCurrency(double amount) {
        return String.format("%.2f ريال", amount);
    }
    
    // معالجات الأحداث
    private void exportToExcel() {
        JOptionPane.showMessageDialog(this, "تصدير Excel - قيد التطوير", "قريباً", JOptionPane.INFORMATION_MESSAGE);
    }
    
    private void printReport() {
        JOptionPane.showMessageDialog(this, "طباعة التقرير - قيد التطوير", "قريباً", JOptionPane.INFORMATION_MESSAGE);
    }
    
    private void refreshData() {
        generateSelectedReport();
        JOptionPane.showMessageDialog(this, "تم تحديث البيانات بنجاح", "تحديث", JOptionPane.INFORMATION_MESSAGE);
    }
}
