# نظام إدارة الشحنات المتقدم - الإصدار 2.0

## 🚀 الميزات الرئيسية

### 📋 القائمة الشجرية المتقدمة
- **هيكل منظم**: 7 أقسام رئيسية مع 25+ وظيفة فرعية
- **أيقونات ملونة**: أيقونات مخصصة لكل قسم ووظيفة
- **بحث فوري**: بحث سريع مع توسيع تلقائي للنتائج
- **قائمة سياق**: خيارات متقدمة بالنقر الأيمن
- **حجم قابل للتعديل**: إمكانية تغيير حجم القائمة وإخفائها

### 🏠 الواجهة الرئيسية المحسنة
- **تخطيط متوازن**: القائمة تأخذ 20% والمحتوى 80% من المساحة
- **وضع ملء الشاشة**: يفتح في وضع ملء الشاشة افتراضياً
- **تبديل الأوضاع**: إمكانية التبديل بين ملء الشاشة والوضع العادي
- **أزرار سريعة**: توسيع/طي/تحديث/إخفاء القائمة/تبديل الوضع
- **شريط حالة**: معلومات الحالة والوقت المباشر
- **حفظ الإعدادات**: تذكر وضع العرض المفضل

### 👥 إدارة المستخدمين الشاملة
- **40+ صلاحية**: مقسمة على 8 فئات مختلفة
- **بحث وتصفية**: بحث متقدم مع 4 معايير تصفية
- **نماذج شاملة**: إضافة/تعديل مع 3 تبويبات
- **إدارة الصلاحيات**: واجهة مخصصة لكل مستخدم

### ⚙️ الإعدادات العامة المتقدمة
- **8 تبويبات**: تغطي جميع جوانب النظام
- **50+ إعداد**: قابل للتخصيص والحفظ
- **تغيير المظهر**: 4 مظاهر مختلفة مع تطبيق فوري
- **استيراد/تصدير**: حفظ واستعادة الإعدادات

## 🛠️ المكتبات والأدوات

### UIUtils - مكتبة الواجهة
- أزرار مُنسقة بألوان احترافية
- حقول نص مع حدود مستديرة
- لوحات بطاقات Material Design
- نوافذ تحميل ورسائل منبثقة

### IconFactory - مصنع الأيقونات
- أيقونات متجهة عالية الجودة
- رموز العمليات والنظام
- ألوان قابلة للتخصيص
- رسم مع Anti-aliasing

### SettingsManager - مدير الإعدادات
- حفظ وتحميل الإعدادات
- تطبيق المظهر المحفوظ
- ملف إعدادات منظم

## 📁 هيكل الملفات

```
src/main/java/
├── ArabicDemo.java              # التطبيق الأصلي
├── EnhancedMainWindow.java      # النافذة الرئيسية المحسنة
├── TreeMenuPanel.java           # القائمة الشجرية
├── UserManagementWindow.java    # إدارة المستخدمين
├── UserFormDialog.java          # نموذج المستخدم
├── UserPermissionsDialog.java   # إدارة الصلاحيات
├── User.java                    # فئة المستخدم
├── GeneralSettingsWindow.java   # الإعدادات العامة
├── SettingsManager.java         # مدير الإعدادات
├── UIUtils.java                 # مكتبة الواجهة
├── IconFactory.java             # مصنع الأيقونات
├── TreeMenuTest.java            # اختبار القائمة الشجرية
├── UserManagementTest.java      # اختبار إدارة المستخدمين
└── CompleteSystemTest.java      # اختبار النظام الكامل
```

## 🚀 كيفية التشغيل

### التجميع
```bash
javac -encoding UTF-8 *.java
```

### التشغيل
```bash
# النظام الكامل مع شاشة البداية
java CompleteSystemTest

# النافذة الرئيسية فقط
java TreeMenuTest

# إدارة المستخدمين فقط
java UserManagementTest
```

## ⌨️ اختصارات لوحة المفاتيح

- **Ctrl+F**: التركيز على حقل البحث
- **Escape**: مسح البحث وتوسيع جميع القوائم
- **F5**: تحديث المحتوى
- **F9**: إخفاء/إظهار القائمة الجانبية
- **F11**: التبديل بين ملء الشاشة والوضع العادي
- **Ctrl+N**: جديد
- **Ctrl+O**: فتح

## 🎨 المظاهر المتاحة

1. **فاتح**: مظهر نظيف بخلفية بيضاء
2. **مظلم**: مظهر داكن مريح للعين
3. **تلقائي**: يتبع مظهر نظام التشغيل
4. **مخصص**: ألوان أزرق وذهبي مميزة

## 📊 الأقسام الرئيسية

### 🚚 إدارة الشحنات
- قائمة الشحنات
- شحنة جديدة
- تتبع الشحنات
- تقارير الشحن

### 👥 إدارة العملاء
- قائمة العملاء
- عميل جديد
- تقارير العملاء

### 💰 المحاسبة والمالية
- الفواتير
- المدفوعات
- التقارير المالية
- الحسابات

### 📦 إدارة المخزون
- قائمة المخزون
- إضافة عنصر
- جرد المخزون
- تقارير المخزون

### 📊 التقارير والإحصائيات
- تقارير المبيعات
- تقارير الأرباح
- تقارير الأداء
- تقارير مخصصة

### ⚙️ الإعدادات والإدارة
- الإعدادات العامة
- إدارة المستخدمين
- إعدادات النظام
- النسخ الاحتياطية

### ❓ المساعدة والدعم
- دليل المستخدم
- الدعم الفني
- حول البرنامج

## 🔧 الميزات التقنية

- **دعم كامل للعربية**: ComponentOrientation RTL
- **خطوط محسنة**: Tahoma للنصوص العربية
- **حفظ الإعدادات**: ملف properties منظم
- **إدارة الموارد**: تنظيف تلقائي عند الإغلاق
- **معالجة الأخطاء**: شاملة مع رسائل واضحة

## 📝 ملاحظات التطوير

- تم تطوير النظام باستخدام Java Swing
- دعم كامل للغة العربية مع تخطيط RTL
- تصميم Material Design مع ألوان احترافية
- كود منظم ومُعلق باللغة العربية
- اختبارات شاملة لجميع المكونات

## 🎯 التحسينات في الإصدار 2.0

- **حجم القائمة المحسن**: تأخذ 20% بدلاً من 50% من المساحة
- **إخفاء/إظهار القائمة**: زر وقائمة لإخفاء القائمة الجانبية
- **حفظ موضع القائمة**: تذكر عرض القائمة المفضل
- **وضع ملء الشاشة**: يفتح في وضع ملء الشاشة افتراضياً
- **تبديل الأوضاع**: إمكانية التبديل بين ملء الشاشة والوضع العادي (F11)
- **حفظ وضع العرض**: تذكر الوضع المفضل (ملء الشاشة أم عادي)
- **أزرار توسيع سريع**: في JSplitPane للتحكم السريع
- **زر تبديل الوضع**: في الشريط العلوي للتحكم السريع

---

**حقوق الطبع محفوظة © 2024 - نظام إدارة الشحنات المتقدم**
