package com.shipment.erp.repository;

import com.shipment.erp.model.SystemSettings;
import java.util.List;
import java.util.Optional;

/**
 * Repository لإعدادات النظام
 */
public interface SystemSettingsRepository extends BaseRepository<SystemSettings> {

    /**
     * البحث عن إعداد بالمفتاح
     */
    Optional<SystemSettings> findBySettingKey(String settingKey);

    /**
     * البحث عن الإعدادات بالنوع
     */
    List<SystemSettings> findBySettingType(String settingType);

    /**
     * البحث عن إعدادات النظام
     */
    List<SystemSettings> findSystemSettings();

    /**
     * البحث عن إعدادات المستخدم
     */
    List<SystemSettings> findUserSettings();

    /**
     * البحث عن الإعدادات بالمفتاح (جزئي)
     */
    List<SystemSettings> findBySettingKeyContaining(String keyPart);

    /**
     * التحقق من وجود إعداد بالمفتاح
     */
    boolean existsBySettingKey(String settingKey);

    /**
     * الحصول على قيمة إعداد
     */
    Optional<String> getSettingValue(String settingKey);

    /**
     * تحديث قيمة إعداد
     */
    void updateSettingValue(String settingKey, String settingValue);

    /**
     * إنشاء أو تحديث إعداد
     */
    SystemSettings createOrUpdateSetting(String settingKey, String settingValue, 
                                        String settingType, String description);

    /**
     * حذف إعداد بالمفتاح
     */
    void deleteBySettingKey(String settingKey);

    /**
     * الحصول على الإعدادات كـ Map
     */
    java.util.Map<String, String> getAllSettingsAsMap();

    /**
     * الحصول على إعدادات النظام كـ Map
     */
    java.util.Map<String, String> getSystemSettingsAsMap();

    /**
     * الحصول على إعدادات المستخدم كـ Map
     */
    java.util.Map<String, String> getUserSettingsAsMap();

    /**
     * البحث المتقدم في الإعدادات
     */
    List<SystemSettings> advancedSearch(String settingKey, String settingType, 
                                       Boolean isSystem, String description);

    /**
     * الحصول على الإعدادات المطلوبة للتطبيق
     */
    List<SystemSettings> getRequiredSettings();

    /**
     * التحقق من اكتمال الإعدادات المطلوبة
     */
    boolean areRequiredSettingsComplete();

    /**
     * إنشاء الإعدادات الافتراضية
     */
    void createDefaultSettings();
}
