package com.shipment.erp.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Properties;

/**
 * أداة للتعامل مع قاعدة البيانات
 */
public class DatabaseUtil {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseUtil.class);
    
    private static final String ORACLE_DRIVER = "oracle.jdbc.OracleDriver";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * إنشاء اتصال بقاعدة البيانات
     */
    public static Connection createConnection(String url, String username, String password) throws SQLException {
        try {
            Class.forName(ORACLE_DRIVER);
            Properties props = new Properties();
            props.setProperty("user", username);
            props.setProperty("password", password);
            props.setProperty("oracle.jdbc.timezoneAsRegion", "false");
            
            Connection connection = DriverManager.getConnection(url, props);
            connection.setAutoCommit(false);
            
            logger.debug("تم إنشاء اتصال بقاعدة البيانات بنجاح");
            return connection;
        } catch (ClassNotFoundException e) {
            logger.error("لم يتم العثور على Oracle JDBC Driver", e);
            throw new SQLException("Oracle JDBC Driver غير موجود", e);
        }
    }

    /**
     * اختبار الاتصال بقاعدة البيانات
     */
    public static boolean testConnection(String url, String username, String password) {
        try (Connection connection = createConnection(url, username, password)) {
            return connection != null && !connection.isClosed();
        } catch (SQLException e) {
            logger.error("فشل في اختبار الاتصال بقاعدة البيانات", e);
            return false;
        }
    }

    /**
     * تنفيذ استعلام SELECT
     */
    public static ResultSet executeQuery(Connection connection, String sql, Object... parameters) throws SQLException {
        PreparedStatement statement = connection.prepareStatement(sql);
        setParameters(statement, parameters);
        
        logger.debug("تنفيذ استعلام: {}", sql);
        return statement.executeQuery();
    }

    /**
     * تنفيذ استعلام UPDATE/INSERT/DELETE
     */
    public static int executeUpdate(Connection connection, String sql, Object... parameters) throws SQLException {
        try (PreparedStatement statement = connection.prepareStatement(sql)) {
            setParameters(statement, parameters);
            
            logger.debug("تنفيذ تحديث: {}", sql);
            int result = statement.executeUpdate();
            logger.debug("تم تحديث {} صف", result);
            
            return result;
        }
    }

    /**
     * تنفيذ استعلام مع إرجاع المفاتيح المولدة
     */
    public static ResultSet executeUpdateWithGeneratedKeys(Connection connection, String sql, Object... parameters) throws SQLException {
        PreparedStatement statement = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
        setParameters(statement, parameters);
        
        logger.debug("تنفيذ تحديث مع إرجاع المفاتيح: {}", sql);
        statement.executeUpdate();
        
        return statement.getGeneratedKeys();
    }

    /**
     * تعيين المعاملات في PreparedStatement
     */
    private static void setParameters(PreparedStatement statement, Object... parameters) throws SQLException {
        for (int i = 0; i < parameters.length; i++) {
            Object param = parameters[i];
            if (param == null) {
                statement.setNull(i + 1, Types.NULL);
            } else if (param instanceof String) {
                statement.setString(i + 1, (String) param);
            } else if (param instanceof Integer) {
                statement.setInt(i + 1, (Integer) param);
            } else if (param instanceof Long) {
                statement.setLong(i + 1, (Long) param);
            } else if (param instanceof Double) {
                statement.setDouble(i + 1, (Double) param);
            } else if (param instanceof Boolean) {
                statement.setBoolean(i + 1, (Boolean) param);
            } else if (param instanceof LocalDateTime) {
                statement.setTimestamp(i + 1, Timestamp.valueOf((LocalDateTime) param));
            } else if (param instanceof java.util.Date) {
                statement.setTimestamp(i + 1, new Timestamp(((java.util.Date) param).getTime()));
            } else if (param instanceof byte[]) {
                statement.setBytes(i + 1, (byte[]) param);
            } else {
                statement.setObject(i + 1, param);
            }
        }
    }

    /**
     * إغلاق الموارد بأمان
     */
    public static void closeQuietly(AutoCloseable... resources) {
        for (AutoCloseable resource : resources) {
            if (resource != null) {
                try {
                    resource.close();
                } catch (Exception e) {
                    logger.warn("خطأ في إغلاق المورد", e);
                }
            }
        }
    }

    /**
     * تنفيذ معاملة
     */
    public static <T> T executeInTransaction(Connection connection, TransactionCallback<T> callback) throws SQLException {
        boolean originalAutoCommit = connection.getAutoCommit();
        try {
            connection.setAutoCommit(false);
            T result = callback.execute(connection);
            connection.commit();
            logger.debug("تم تنفيذ المعاملة بنجاح");
            return result;
        } catch (Exception e) {
            connection.rollback();
            logger.error("فشل في تنفيذ المعاملة، تم التراجع", e);
            throw e;
        } finally {
            connection.setAutoCommit(originalAutoCommit);
        }
    }

    /**
     * إنشاء نسخة احتياطية من الجدول
     */
    public static void backupTable(Connection connection, String tableName, String backupTableName) throws SQLException {
        String sql = "CREATE TABLE " + backupTableName + " AS SELECT * FROM " + tableName;
        executeUpdate(connection, sql);
        logger.info("تم إنشاء نسخة احتياطية من الجدول {} إلى {}", tableName, backupTableName);
    }

    /**
     * حذف الجدول إذا كان موجوداً
     */
    public static void dropTableIfExists(Connection connection, String tableName) throws SQLException {
        String checkSql = "SELECT COUNT(*) FROM user_tables WHERE table_name = ?";
        try (PreparedStatement statement = connection.prepareStatement(checkSql)) {
            statement.setString(1, tableName.toUpperCase());
            try (ResultSet rs = statement.executeQuery()) {
                if (rs.next() && rs.getInt(1) > 0) {
                    String dropSql = "DROP TABLE " + tableName;
                    executeUpdate(connection, dropSql);
                    logger.info("تم حذف الجدول {}", tableName);
                }
            }
        }
    }

    /**
     * التحقق من وجود الجدول
     */
    public static boolean tableExists(Connection connection, String tableName) throws SQLException {
        String sql = "SELECT COUNT(*) FROM user_tables WHERE table_name = ?";
        try (PreparedStatement statement = connection.prepareStatement(sql)) {
            statement.setString(1, tableName.toUpperCase());
            try (ResultSet rs = statement.executeQuery()) {
                return rs.next() && rs.getInt(1) > 0;
            }
        }
    }

    /**
     * الحصول على عدد الصفوف في الجدول
     */
    public static long getTableRowCount(Connection connection, String tableName) throws SQLException {
        String sql = "SELECT COUNT(*) FROM " + tableName;
        try (PreparedStatement statement = connection.prepareStatement(sql);
             ResultSet rs = statement.executeQuery()) {
            return rs.next() ? rs.getLong(1) : 0;
        }
    }

    /**
     * تحسين الجدول
     */
    public static void optimizeTable(Connection connection, String tableName) throws SQLException {
        String sql = "ANALYZE TABLE " + tableName + " COMPUTE STATISTICS";
        executeUpdate(connection, sql);
        logger.info("تم تحسين الجدول {}", tableName);
    }

    /**
     * الحصول على معلومات قاعدة البيانات
     */
    public static DatabaseInfo getDatabaseInfo(Connection connection) throws SQLException {
        DatabaseMetaData metaData = connection.getMetaData();
        return new DatabaseInfo(
            metaData.getDatabaseProductName(),
            metaData.getDatabaseProductVersion(),
            metaData.getDriverName(),
            metaData.getDriverVersion(),
            metaData.getURL(),
            metaData.getUserName()
        );
    }

    /**
     * تحويل ResultSet إلى String للعرض
     */
    public static String resultSetToString(ResultSet rs) throws SQLException {
        StringBuilder sb = new StringBuilder();
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();
        
        // عناوين الأعمدة
        for (int i = 1; i <= columnCount; i++) {
            sb.append(metaData.getColumnName(i));
            if (i < columnCount) sb.append("\t");
        }
        sb.append("\n");
        
        // البيانات
        while (rs.next()) {
            for (int i = 1; i <= columnCount; i++) {
                Object value = rs.getObject(i);
                sb.append(value != null ? value.toString() : "NULL");
                if (i < columnCount) sb.append("\t");
            }
            sb.append("\n");
        }
        
        return sb.toString();
    }

    /**
     * واجهة للمعاملات
     */
    @FunctionalInterface
    public interface TransactionCallback<T> {
        T execute(Connection connection) throws SQLException;
    }

    /**
     * معلومات قاعدة البيانات
     */
    public static class DatabaseInfo {
        private final String productName;
        private final String productVersion;
        private final String driverName;
        private final String driverVersion;
        private final String url;
        private final String username;

        public DatabaseInfo(String productName, String productVersion, String driverName,
                          String driverVersion, String url, String username) {
            this.productName = productName;
            this.productVersion = productVersion;
            this.driverName = driverName;
            this.driverVersion = driverVersion;
            this.url = url;
            this.username = username;
        }

        // Getters
        public String getProductName() { return productName; }
        public String getProductVersion() { return productVersion; }
        public String getDriverName() { return driverName; }
        public String getDriverVersion() { return driverVersion; }
        public String getUrl() { return url; }
        public String getUsername() { return username; }

        @Override
        public String toString() {
            return String.format("Database: %s %s, Driver: %s %s, URL: %s, User: %s",
                productName, productVersion, driverName, driverVersion, url, username);
        }
    }
}
