# إعدادات قاعدة البيانات Oracle
database.url=***********************************
database.username=ship_erp
database.password=ship_erp_password
database.driver=oracle.jdbc.OracleDriver

# إعدادات Connection Pool
database.pool.minimumIdle=5
database.pool.maximumPoolSize=20
database.pool.connectionTimeout=30000
database.pool.idleTimeout=600000
database.pool.maxLifetime=1800000

# إعدادات Hibernate
hibernate.dialect=org.hibernate.dialect.OracleDialect
hibernate.hbm2ddl.auto=update
hibernate.show_sql=false
hibernate.format_sql=true
hibernate.use_sql_comments=true
hibernate.jdbc.batch_size=20
hibernate.order_inserts=true
hibernate.order_updates=true
hibernate.jdbc.batch_versioned_data=true

# إعدادات التطبيق
app.name=نظام إدارة الشحنات
app.version=1.0.0
app.locale=ar_SA
app.direction=rtl
app.charset=UTF-8

# إعدادات الواجهة
ui.theme=default
ui.font.family=Noto Sans Arabic
ui.font.size=14
ui.window.width=1200
ui.window.height=800
ui.window.maximized=true

# إعدادات الأمان
security.password.min.length=8
security.session.timeout=3600
security.max.login.attempts=3
security.lockout.duration=300

# إعدادات التقارير
reports.output.directory=reports
reports.temp.directory=temp
reports.default.format=PDF

# إعدادات التسجيل
logging.level.root=INFO
logging.level.com.shipment.erp=DEBUG
logging.file.name=logs/ship-erp.log
logging.file.max-size=10MB
logging.file.max-history=30

# إعدادات النسخ الاحتياطي
backup.directory=backup
backup.auto.enabled=true
backup.auto.interval=24
backup.retention.days=30
