import java.sql.*;

/**
 * اختبار الاتصال وإنشاء Package بسيط
 */
public class TestConnection {
    
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            System.out.println("🔗 اختبار الاتصال بـ SHIP_ERP...");
            
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            System.out.println("✅ تم الاتصال بنجاح");
            
            Statement stmt = conn.createStatement();
            
            // اختبار استعلام بسيط
            System.out.println("\n📊 اختبار الجداول...");
            
            try {
                ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM ERP_GROUP_DETAILS");
                if (rs.next()) {
                    System.out.println("📋 عدد المجموعات الرئيسية: " + rs.getInt(1));
                }
                rs.close();
            } catch (SQLException e) {
                System.out.println("⚠️ جدول ERP_GROUP_DETAILS: " + e.getMessage());
            }
            
            try {
                ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM ERP_MAINSUB_GRP_DTL");
                if (rs.next()) {
                    System.out.println("📋 عدد المجموعات الفرعية: " + rs.getInt(1));
                }
                rs.close();
            } catch (SQLException e) {
                System.out.println("⚠️ جدول ERP_MAINSUB_GRP_DTL: " + e.getMessage());
            }
            
            // إنشاء Package بسيط جداً
            System.out.println("\n📦 إنشاء Package بسيط...");
            
            try {
                stmt.execute("DROP PACKAGE ERP_ITEM_GROUPS");
                System.out.println("🗑️ تم حذف Package القديم");
            } catch (SQLException e) {
                System.out.println("ℹ️ Package غير موجود مسبقاً");
            }
            
            // Package Specification بسيط
            String packageSpec = """
                CREATE OR REPLACE PACKAGE ERP_ITEM_GROUPS AS
                    FUNCTION get_groups_count RETURN NUMBER;
                    FUNCTION test_function RETURN VARCHAR2;
                END ERP_ITEM_GROUPS;
            """;
            
            stmt.execute(packageSpec);
            System.out.println("✅ تم إنشاء Package Specification");
            
            // Package Body بسيط
            String packageBody = """
                CREATE OR REPLACE PACKAGE BODY ERP_ITEM_GROUPS AS
                    
                    FUNCTION get_groups_count RETURN NUMBER IS
                        l_count NUMBER;
                    BEGIN
                        SELECT COUNT(*) INTO l_count FROM ERP_GROUP_DETAILS;
                        RETURN l_count;
                    EXCEPTION
                        WHEN OTHERS THEN
                            RETURN -1;
                    END get_groups_count;
                    
                    FUNCTION test_function RETURN VARCHAR2 IS
                    BEGIN
                        RETURN 'Package يعمل بنجاح!';
                    END test_function;
                    
                END ERP_ITEM_GROUPS;
            """;
            
            stmt.execute(packageBody);
            System.out.println("✅ تم إنشاء Package Body");
            
            // اختبار Package
            System.out.println("\n🧪 اختبار Package...");
            
            try {
                CallableStatement cs1 = conn.prepareCall("{ ? = call ERP_ITEM_GROUPS.test_function }");
                cs1.registerOutParameter(1, Types.VARCHAR);
                cs1.execute();
                String testResult = cs1.getString(1);
                System.out.println("📋 نتيجة الاختبار: " + testResult);
                
                CallableStatement cs2 = conn.prepareCall("{ ? = call ERP_ITEM_GROUPS.get_groups_count }");
                cs2.registerOutParameter(1, Types.NUMERIC);
                cs2.execute();
                int count = cs2.getInt(1);
                System.out.println("📊 عدد المجموعات: " + count);
                
                System.out.println("🎉 Package يعمل بنجاح!");
                
            } catch (SQLException e) {
                System.err.println("❌ خطأ في اختبار Package: " + e.getMessage());
                
                // فحص أخطاء Package
                System.out.println("\n🔍 فحص أخطاء Package...");
                ResultSet errors = stmt.executeQuery(
                    "SELECT line, position, text FROM USER_ERRORS " +
                    "WHERE name = 'ERP_ITEM_GROUPS' AND type = 'PACKAGE BODY' " +
                    "ORDER BY line, position"
                );
                
                while (errors.next()) {
                    System.out.println("❌ خطأ في السطر " + errors.getInt("line") + 
                        " الموضع " + errors.getInt("position") + ": " + errors.getString("text"));
                }
                errors.close();
            }
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ عام: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
