import java.sql.*;
import java.util.HashMap;
import java.util.Map;

/**
 * مستورد بيانات الأصناف - تطبيق للمعايير الموحدة للاستيراد
 * يستورد البيانات من IAS_ITM_MST و IAS_ITM_DTL في ias20251 إلى ship_erp
 */
public class ItemsDataImporter extends BaseDataImporter {
    
    // جداول التفاصيل
    private String sourceDetailTableName = "IAS_ITM_DTL";
    private String targetDetailTableName = "IAS_ITM_DTL";
    
    public ItemsDataImporter() {
        super("IAS_ITM_MST", "IAS_ITM_MST");
    }
    
    /**
     * إنشاء خريطة ربط الحقول
     */
    @Override
    protected void createFieldMapping() {
        System.out.println("🔗 إنشاء خريطة ربط الحقول...");
        
        // ربط حقول الجدول الرئيسي IAS_ITM_MST
        fieldMapping.put("I_CODE", "I_CODE");                    // رقم الصنف
        fieldMapping.put("I_NAME", "I_NAME");                    // اسم الصنف
        fieldMapping.put("I_E_NAME", "I_E_NAME");                // الاسم الأجنبي
        fieldMapping.put("I_DESC", "I_DESC");                    // مواصفات الصنف
        fieldMapping.put("G_CODE", "G_CODE");                    // رقم المجموعة
        fieldMapping.put("MNG_CODE", "MNG_CODE");                // المجموعات الفرعية
        fieldMapping.put("SUBG_CODE", "SUBG_CODE");              // رقم م. تحت الفرعية
        fieldMapping.put("ITEM_SIZE", "ITEM_SIZE");              // عبوة الصنف
        fieldMapping.put("ITEM_TYPE", "ITEM_TYPE");              // نوع الصنف
        fieldMapping.put("PRIMARY_COST", "PRIMARY_COST");        // التكلفة الأولية
        fieldMapping.put("INIT_PRIMARY_COST", "INIT_PRIMARY_COST"); // تكلفة بداية التعامل
        fieldMapping.put("ALTER_CODE", "ALTER_CODE");            // الصنف البديل
        fieldMapping.put("MANF_CODE", "MANF_CODE");              // رقم التصنيع
        fieldMapping.put("V_CODE", "V_CODE");                    // رقم المورد
        fieldMapping.put("INACTIVE", "INACTIVE");                // توقيف
        fieldMapping.put("SERVICE_ITM", "SERVICE_ITM");          // صنف خدمي
        fieldMapping.put("CASH_SALE", "CASH_SALE");              // يباع نقداً
        fieldMapping.put("NO_RETURN_SALE", "NO_RETURN_SALE");    // غير قابل للإرتجاع
        fieldMapping.put("KIT_ITM", "KIT_ITM");                  // صنف مركب
        fieldMapping.put("USE_EXP_DATE", "USE_EXP_DATE");        // إستخدام تاريخ الإنتهاء
        fieldMapping.put("USE_BATCH_NO", "USE_BATCH_NO");        // إستخدام رقم الدفعة
        fieldMapping.put("USE_SERIALNO", "USE_SERIALNO");        // إستخدام الأرقام التسلسلية
        fieldMapping.put("AD_U_ID", "AD_U_ID");                  // مدخل السجل
        fieldMapping.put("AD_DATE", "AD_DATE");                  // تاريخ الإدخال
        
        System.out.println("✅ تم إنشاء خريطة ربط " + fieldMapping.size() + " حقل");
    }
    
    /**
     * تنفيذ عملية الاستيراد
     */
    @Override
    protected void performImport() throws SQLException {
        System.out.println("📥 بدء استيراد بيانات الأصناف...");
        
        // استيراد الجدول الرئيسي
        importMainTable();
        
        // استيراد جدول التفاصيل
        importDetailTable();
        
        System.out.println("✅ تم الانتهاء من استيراد بيانات الأصناف");
    }
    
    /**
     * استيراد الجدول الرئيسي IAS_ITM_MST
     */
    private void importMainTable() throws SQLException {
        System.out.println("📋 استيراد الجدول الرئيسي: " + sourceTableName);
        
        // مسح البيانات الموجودة (اختياري)
        clearTargetTable();
        
        // استعلام البيانات من المصدر
        String sourceQuery = buildSourceQuery();
        Statement sourceStmt = sourceConnection.createStatement();
        ResultSet sourceData = sourceStmt.executeQuery(sourceQuery);
        
        // إعداد استعلام الإدراج
        String insertQuery = buildInsertQuery();
        PreparedStatement insertStmt = targetConnection.prepareStatement(insertQuery);
        
        // معالجة البيانات
        int batchCount = 0;
        while (sourceData.next()) {
            try {
                if (validateData(sourceData)) {
                    setInsertParameters(insertStmt, sourceData);
                    insertStmt.addBatch();
                    batchCount++;
                    
                    // تنفيذ الدفعة كل 1000 سجل
                    if (batchCount % 1000 == 0) {
                        insertStmt.executeBatch();
                        targetConnection.commit();
                        System.out.println("✅ تم استيراد " + batchCount + " سجل");
                    }
                }
            } catch (SQLException e) {
                handleImportError("خطأ في استيراد سجل: " + sourceData.getString("I_CODE"), e);
            }
        }
        
        // تنفيذ الدفعة الأخيرة
        if (batchCount % 1000 != 0) {
            insertStmt.executeBatch();
            targetConnection.commit();
        }
        
        importedRecords = batchCount - errorRecords;
        
        sourceData.close();
        sourceStmt.close();
        insertStmt.close();
        
        System.out.println("📊 تم استيراد " + importedRecords + " سجل من الجدول الرئيسي");
    }
    
    /**
     * استيراد جدول التفاصيل IAS_ITM_DTL
     */
    private void importDetailTable() throws SQLException {
        System.out.println("📋 استيراد جدول التفاصيل: " + sourceDetailTableName);
        
        // تحليل تعليقات جدول التفاصيل
        CommentsManager.analyzeTableComments(sourceDetailTableName, "ias20251", "ys123");
        
        // مسح البيانات الموجودة
        Statement clearStmt = targetConnection.createStatement();
        clearStmt.executeUpdate("DELETE FROM " + targetDetailTableName);
        clearStmt.close();
        
        // استعلام البيانات من المصدر
        String sourceQuery = "SELECT * FROM " + sourceDetailTableName + " ORDER BY I_CODE, ITM_UNT";
        Statement sourceStmt = sourceConnection.createStatement();
        ResultSet sourceData = sourceStmt.executeQuery(sourceQuery);
        
        // إعداد استعلام الإدراج للتفاصيل
        String insertQuery = """
            INSERT INTO IAS_ITM_DTL (
                I_CODE, ITM_UNT, P_SIZE, ITM_UNT_L_DSC, ITM_UNT_F_DSC, BARCODE,
                MAIN_UNIT, SALE_UNIT, PUR_UNIT, STOCK_UNIT, PRICE_UNIT,
                INACTIVE, AD_U_ID, AD_DATE
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, SYSDATE)
        """;
        
        PreparedStatement insertStmt = targetConnection.prepareStatement(insertQuery);
        
        int detailCount = 0;
        while (sourceData.next()) {
            try {
                insertStmt.setString(1, sourceData.getString("I_CODE"));
                insertStmt.setString(2, sourceData.getString("ITM_UNT"));
                insertStmt.setDouble(3, sourceData.getDouble("P_SIZE"));
                insertStmt.setString(4, sourceData.getString("ITM_UNT_L_DSC"));
                insertStmt.setString(5, sourceData.getString("ITM_UNT_F_DSC"));
                insertStmt.setString(6, sourceData.getString("BARCODE"));
                insertStmt.setInt(7, sourceData.getInt("MAIN_UNIT"));
                insertStmt.setInt(8, sourceData.getInt("SALE_UNIT"));
                insertStmt.setInt(9, sourceData.getInt("PUR_UNIT"));
                insertStmt.setInt(10, sourceData.getInt("STOCK_UNIT"));
                insertStmt.setInt(11, sourceData.getInt("PRICE_UNIT"));
                insertStmt.setInt(12, sourceData.getInt("INACTIVE"));
                insertStmt.setInt(13, 1); // مستخدم الاستيراد
                
                insertStmt.addBatch();
                detailCount++;
                
                if (detailCount % 1000 == 0) {
                    insertStmt.executeBatch();
                    targetConnection.commit();
                    System.out.println("✅ تم استيراد " + detailCount + " سجل تفاصيل");
                }
                
            } catch (SQLException e) {
                handleImportError("خطأ في استيراد تفاصيل: " + sourceData.getString("I_CODE"), e);
            }
        }
        
        // تنفيذ الدفعة الأخيرة
        if (detailCount % 1000 != 0) {
            insertStmt.executeBatch();
            targetConnection.commit();
        }
        
        sourceData.close();
        sourceStmt.close();
        insertStmt.close();
        
        System.out.println("📊 تم استيراد " + detailCount + " سجل من جدول التفاصيل");
    }
    
    /**
     * مسح الجدول الهدف
     */
    private void clearTargetTable() throws SQLException {
        System.out.println("🗑️ مسح البيانات الموجودة...");
        
        Statement stmt = targetConnection.createStatement();
        
        // مسح التفاصيل أولاً (المفتاح الخارجي)
        stmt.executeUpdate("DELETE FROM " + targetDetailTableName);
        
        // مسح الجدول الرئيسي
        stmt.executeUpdate("DELETE FROM " + targetTableName);
        
        stmt.close();
        targetConnection.commit();
        
        System.out.println("✅ تم مسح البيانات الموجودة");
    }
    
    /**
     * بناء استعلام المصدر
     */
    private String buildSourceQuery() {
        StringBuilder query = new StringBuilder("SELECT ");
        
        // إضافة الحقول المطلوبة
        boolean first = true;
        for (String sourceField : fieldMapping.keySet()) {
            if (!first) query.append(", ");
            query.append(sourceField);
            first = false;
        }
        
        query.append(" FROM ").append(sourceTableName);
        query.append(" ORDER BY I_CODE");
        
        return query.toString();
    }
    
    /**
     * بناء استعلام الإدراج
     */
    private String buildInsertQuery() {
        StringBuilder query = new StringBuilder("INSERT INTO ").append(targetTableName).append(" (");
        StringBuilder values = new StringBuilder(" VALUES (");
        
        boolean first = true;
        for (String targetField : fieldMapping.values()) {
            if (!first) {
                query.append(", ");
                values.append(", ");
            }
            query.append(targetField);
            values.append("?");
            first = false;
        }
        
        query.append(")").append(values).append(")");
        return query.toString();
    }
    
    /**
     * تعيين معاملات الإدراج
     */
    private void setInsertParameters(PreparedStatement stmt, ResultSet sourceData) throws SQLException {
        int paramIndex = 1;
        
        for (String sourceField : fieldMapping.keySet()) {
            Object value = sourceData.getObject(sourceField);
            stmt.setObject(paramIndex++, value);
        }
    }
    
    /**
     * التحقق من صحة البيانات
     */
    @Override
    protected boolean validateData(ResultSet sourceData) throws SQLException {
        // التحقق من وجود كود الصنف
        String itemCode = sourceData.getString("I_CODE");
        if (itemCode == null || itemCode.trim().isEmpty()) {
            return false;
        }
        
        // التحقق من وجود اسم الصنف
        String itemName = sourceData.getString("I_NAME");
        if (itemName == null || itemName.trim().isEmpty()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * تشغيل مستورد بيانات الأصناف
     */
    public static void main(String[] args) {
        System.out.println("🚀 بدء استيراد بيانات الأصناف باستخدام المعايير الموحدة");
        
        ItemsDataImporter importer = new ItemsDataImporter();
        importer.executeImport();
        
        System.out.println("🎉 تم الانتهاء من عملية الاستيراد");
    }
}
