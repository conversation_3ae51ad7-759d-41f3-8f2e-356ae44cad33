import java.sql.*;

/**
 * فحص سريع لقاعدة البيانات الحقيقية
 * Server: localhost
 * Database: orcl  
 * User: ship_erp
 * Password: ship_erp_password
 */
public class QuickRealDBCheck {
    
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            System.out.println("🔍 فحص سريع لقاعدة البيانات الحقيقية");
            System.out.println("==========================================");
            System.out.println("📍 Server: localhost");
            System.out.println("📍 Database: orcl");
            System.out.println("📍 User: ship_erp");
            System.out.println();
            
            // الاتصال
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            
            System.out.println("✅ تم الاتصال بنجاح!");
            
            // معلومات قاعدة البيانات
            DatabaseMetaData metaData = conn.getMetaData();
            System.out.println("📋 اسم قاعدة البيانات: " + metaData.getDatabaseProductName());
            System.out.println("📋 إصدار قاعدة البيانات: " + metaData.getDatabaseProductVersion());
            System.out.println("📋 اسم المستخدم: " + metaData.getUserName());
            System.out.println();
            
            // استكشاف الجداول
            System.out.println("🔍 استكشاف الجداول...");
            
            ResultSet tables = metaData.getTables(null, "SHIP_ERP", "%", new String[]{"TABLE"});
            
            int tableCount = 0;
            System.out.println("📋 الجداول الموجودة:");
            
            while (tables.next()) {
                String tableName = tables.getString("TABLE_NAME");
                
                // الحصول على عدد الصفوف
                int rowCount = 0;
                try {
                    Statement stmt = conn.createStatement();
                    ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName);
                    if (rs.next()) {
                        rowCount = rs.getInt(1);
                    }
                    rs.close();
                    stmt.close();
                } catch (SQLException e) {
                    // تجاهل الأخطاء
                }
                
                System.out.printf("  %-30s %8d صف\n", tableName, rowCount);
                tableCount++;
            }
            tables.close();
            
            System.out.println("\n📊 إجمالي الجداول: " + tableCount);
            
            // البحث عن جداول مهمة
            System.out.println("\n🔍 البحث عن جداول المجموعات والأصناف...");
            
            String[] searchTerms = {"GROUP", "GRP", "ITEM", "ITM", "CATEGORY", "CAT", "PRODUCT", "PROD"};
            boolean foundImportant = false;
            
            ResultSet searchTables = metaData.getTables(null, "SHIP_ERP", "%", new String[]{"TABLE"});
            
            while (searchTables.next()) {
                String tableName = searchTables.getString("TABLE_NAME");
                
                for (String term : searchTerms) {
                    if (tableName.toUpperCase().contains(term)) {
                        foundImportant = true;
                        
                        // الحصول على عدد الصفوف
                        int rowCount = 0;
                        try {
                            Statement stmt = conn.createStatement();
                            ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName);
                            if (rs.next()) {
                                rowCount = rs.getInt(1);
                            }
                            rs.close();
                            stmt.close();
                        } catch (SQLException e) {
                            // تجاهل الأخطاء
                        }
                        
                        System.out.println("📋 جدول مهم: " + tableName + " (" + rowCount + " صف)");
                        
                        // فحص أعمدة الجدول
                        analyzeTableColumns(conn, tableName);
                        
                        break;
                    }
                }
            }
            searchTables.close();
            
            if (!foundImportant) {
                System.out.println("⚠️ لم يتم العثور على جداول مجموعات أو أصناف واضحة");
                System.out.println("💡 قد تحتاج لفحص الجداول يدوياً");
            }
            
            // فحص أكبر الجداول (قد تحتوي على بيانات مهمة)
            System.out.println("\n📊 أكبر الجداول (قد تحتوي على بيانات مهمة):");
            
            ResultSet bigTables = metaData.getTables(null, "SHIP_ERP", "%", new String[]{"TABLE"});
            
            while (bigTables.next()) {
                String tableName = bigTables.getString("TABLE_NAME");
                
                try {
                    Statement stmt = conn.createStatement();
                    ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName);
                    if (rs.next()) {
                        int rowCount = rs.getInt(1);
                        if (rowCount > 0) {
                            System.out.println("  " + tableName + ": " + rowCount + " صف");
                            
                            // إذا كان الجدول يحتوي على بيانات، اعرض عينة
                            if (rowCount > 0 && rowCount < 1000) {
                                showSampleData(conn, tableName);
                            }
                        }
                    }
                    rs.close();
                    stmt.close();
                } catch (SQLException e) {
                    // تجاهل الأخطاء
                }
            }
            bigTables.close();
            
            conn.close();
            System.out.println("\n🎉 تم الانتهاء من فحص قاعدة البيانات!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * تحليل أعمدة الجدول
     */
    private static void analyzeTableColumns(Connection conn, String tableName) {
        try {
            DatabaseMetaData metaData = conn.getMetaData();
            ResultSet columns = metaData.getColumns(null, "SHIP_ERP", tableName, null);
            
            System.out.println("    📋 الأعمدة:");
            while (columns.next()) {
                String columnName = columns.getString("COLUMN_NAME");
                String dataType = columns.getString("TYPE_NAME");
                int columnSize = columns.getInt("COLUMN_SIZE");
                
                System.out.printf("      %-20s %s\n", columnName, dataType + 
                    (columnSize > 0 ? "(" + columnSize + ")" : ""));
            }
            columns.close();
            
        } catch (SQLException e) {
            System.out.println("    ❌ خطأ في فحص الأعمدة: " + e.getMessage());
        }
    }
    
    /**
     * عرض عينة من البيانات
     */
    private static void showSampleData(Connection conn, String tableName) {
        try {
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT * FROM " + tableName + " WHERE ROWNUM <= 2");
            
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            System.out.println("    📄 عينة من البيانات:");
            
            while (rs.next()) {
                System.out.print("      ");
                for (int i = 1; i <= columnCount; i++) {
                    String value = rs.getString(i);
                    if (value != null && value.length() > 10) {
                        value = value.substring(0, 10) + "...";
                    }
                    System.out.print(metaData.getColumnName(i) + "=" + 
                        (value != null ? value : "NULL") + " | ");
                }
                System.out.println();
            }
            
            rs.close();
            stmt.close();
            
        } catch (SQLException e) {
            System.out.println("    ⚠️ لا يمكن قراءة البيانات: " + e.getMessage());
        }
    }
}
