import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Properties;

/**
 * اختبار دعم الأحرف العربية في Oracle Arabic Character Set Support Test for Oracle
 */
public class ArabicCharsetTest {

    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("   اختبار دعم الأحرف العربية في Oracle");
        System.out.println("   Arabic Character Set Support Test");
        System.out.println("====================================");
        System.out.println();

        ArabicCharsetTest test = new ArabicCharsetTest();
        test.runTest();
    }

    /**
     * تشغيل اختبار الأحرف العربية
     */
    public void runTest() {
        // فحص المكتبات المطلوبة
        if (!checkRequiredLibraries()) {
            return;
        }

        // اختبار الاتصال مع الأحرف العربية
        testArabicConnection();
    }

    /**
     * فحص المكتبات المطلوبة
     */
    private boolean checkRequiredLibraries() {
        System.out.println("🔍 فحص المكتبات المطلوبة...");

        // فحص ojdbc11.jar
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            System.out.println("✅ ojdbc11.jar محمل بنجاح");
        } catch (ClassNotFoundException e) {
            System.err.println("❌ ojdbc11.jar مفقود!");
            System.err.println("يرجى تشغيل: java LibraryDownloader");
            return false;
        }

        // فحص orai18n.jar
        String classpath = System.getProperty("java.class.path");
        if (classpath.contains("orai18n.jar")) {
            System.out.println("✅ orai18n.jar موجود في classpath");
        } else {
            System.err.println("❌ orai18n.jar مفقود من classpath!");
            System.err.println("هذه المكتبة مطلوبة لدعم الأحرف العربية");
            System.err.println("يرجى تشغيل: java LibraryDownloader");
            System.err.println("ثم: java -cp \"lib/*;.\" ArabicCharsetTest");
            return false;
        }

        System.out.println("✅ جميع المكتبات المطلوبة متوفرة");
        System.out.println();
        return true;
    }

    /**
     * اختبار الاتصال مع الأحرف العربية
     */
    private void testArabicConnection() {
        System.out.println("🔄 اختبار الاتصال مع دعم الأحرف العربية...");

        // بيانات الاتصال
        String url = "*************************************";
        String username = "ysdba2";
        String password = "ys123";

        System.out.println("رابط الاتصال: " + url);
        System.out.println("المستخدم: " + username);
        System.out.println();

        Connection connection = null;

        try {
            // إعداد خصائص الاتصال مع دعم الأحرف العربية
            Properties props = new Properties();
            props.setProperty("user", username);
            props.setProperty("password", password);

            // إعدادات خاصة بالأحرف العربية
            props.setProperty("oracle.jdbc.defaultNChar", "true");
            props.setProperty("oracle.jdbc.convertNcharLiterals", "true");

            // إعدادات إضافية لتجنب المشاكل
            props.setProperty("oracle.jdbc.autoCommitSpecCompliant", "false");
            props.setProperty("oracle.net.disableOob", "true");

            System.out.println("🔄 محاولة الاتصال مع دعم الأحرف العربية...");

            // إنشاء الاتصال
            connection = DriverManager.getConnection(url, props);

            System.out.println("✅ نجح الاتصال!");
            System.out.println();

            // اختبار الأحرف العربية
            testArabicCharacters(connection);

            // اختبار إنشاء جدول بأسماء عربية
            testArabicTableCreation(connection);

        } catch (SQLException e) {
            System.err.println("❌ فشل الاتصال!");
            System.err.println("رمز الخطأ: " + e.getErrorCode());
            System.err.println("رسالة الخطأ: " + e.getMessage());

            // تحليل نوع الخطأ
            analyzeError(e);

        } finally {
            // إغلاق الاتصال
            if (connection != null) {
                try {
                    connection.close();
                    System.out.println("🔒 تم إغلاق الاتصال");
                } catch (SQLException e) {
                    System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
                }
            }
        }
    }

    /**
     * اختبار الأحرف العربية
     */
    private void testArabicCharacters(Connection connection) throws SQLException {
        System.out.println("🔤 اختبار الأحرف العربية...");

        String arabicText = "مرحبا بك في نظام إدارة الأصناف";

        try (PreparedStatement stmt = connection
                .prepareStatement("SELECT ? AS arabic_text, LENGTH(?) AS text_length FROM DUAL")) {

            stmt.setString(1, arabicText);
            stmt.setString(2, arabicText);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    String result = rs.getString("arabic_text");
                    int length = rs.getInt("text_length");

                    System.out.println("النص الأصلي: " + arabicText);
                    System.out.println("النص المسترجع: " + result);
                    System.out.println("طول النص: " + length);

                    if (arabicText.equals(result)) {
                        System.out.println("✅ الأحرف العربية تعمل بشكل صحيح!");
                    } else {
                        System.out.println("⚠️ هناك مشكلة في عرض الأحرف العربية");
                    }
                }
            }
        }
        System.out.println();
    }

    /**
     * اختبار إنشاء جدول بأسماء عربية
     */
    private void testArabicTableCreation(Connection connection) throws SQLException {
        System.out.println("📋 اختبار إنشاء جدول بأسماء عربية...");

        try {
            // حذف الجدول إذا كان موجوداً
            try (Statement stmt = connection.createStatement()) {
                stmt.execute("DROP TABLE test_arabic_items");
                System.out.println("تم حذف الجدول السابق");
            } catch (SQLException e) {
                // الجدول غير موجود - لا مشكلة
            }

            // إنشاء جدول جديد
            String createTableSQL = """
                        CREATE TABLE test_arabic_items (
                            item_id NUMBER PRIMARY KEY,
                            item_name NVARCHAR2(100),
                            item_description NVARCHAR2(500),
                            created_date DATE DEFAULT SYSDATE
                        )
                    """;

            try (Statement stmt = connection.createStatement()) {
                stmt.execute(createTableSQL);
                System.out.println("✅ تم إنشاء الجدول بنجاح");
            }

            // إدراج بيانات عربية
            String insertSQL =
                    "INSERT INTO test_arabic_items (item_id, item_name, item_description) VALUES (?, ?, ?)";

            try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
                stmt.setInt(1, 1);
                stmt.setString(2, "صنف تجريبي");
                stmt.setString(3, "هذا صنف تجريبي لاختبار الأحرف العربية في قاعدة البيانات");

                int rows = stmt.executeUpdate();
                System.out.println("✅ تم إدراج " + rows + " سجل بنجاح");
            }

            // استرجاع البيانات
            try (Statement stmt = connection.createStatement();
                    ResultSet rs = stmt.executeQuery("SELECT * FROM test_arabic_items")) {

                while (rs.next()) {
                    System.out.println("معرف الصنف: " + rs.getInt("item_id"));
                    System.out.println("اسم الصنف: " + rs.getString("item_name"));
                    System.out.println("وصف الصنف: " + rs.getString("item_description"));
                    System.out.println("تاريخ الإنشاء: " + rs.getDate("created_date"));
                }
            }

            // تنظيف - حذف الجدول
            try (Statement stmt = connection.createStatement()) {
                stmt.execute("DROP TABLE test_arabic_items");
                System.out.println("✅ تم حذف الجدول التجريبي");
            }

            connection.commit();

        } catch (SQLException e) {
            connection.rollback();
            throw e;
        }

        System.out.println();
    }

    /**
     * تحليل نوع الخطأ
     */
    private void analyzeError(SQLException e) {
        System.out.println();
        System.out.println("🔍 تحليل الخطأ:");

        int errorCode = e.getErrorCode();
        String message = e.getMessage();

        if (errorCode == 17056 || message.contains("ORA-17056")) {
            System.out.println("❌ خطأ في مجموعة الأحرف (Character Set)");
            System.out.println("السبب: مكتبة orai18n.jar مفقودة أو غير محملة");
            System.out.println("الحل: تأكد من وجود orai18n.jar في classpath");
            System.out.println("تشغيل: java -cp \"lib/*;.\" ArabicCharsetTest");

        } else if (errorCode == 17002) {
            System.out.println("❌ مشكلة في الاتصال بالخادم");
            System.out.println("تأكد من تشغيل Oracle Database");

        } else if (errorCode == 1017) {
            System.out.println("❌ اسم المستخدم أو كلمة المرور خاطئة");

        } else {
            System.out.println("❌ خطأ غير محدد: " + message);
        }

        System.out.println();
        System.out.println("💡 للحصول على المساعدة:");
        System.out.println("1. تأكد من تشغيل: java LibraryDownloader");
        System.out.println("2. استخدم: java -cp \"lib/*;.\" ArabicCharsetTest");
        System.out.println("3. تأكد من وجود ملفي ojdbc11.jar و orai18n.jar");
    }
}
