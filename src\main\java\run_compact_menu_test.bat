@echo off
chcp 65001 > nul
echo.
echo ========================================
echo    تشغيل النافذة الرئيسية مع القائمة المضغوطة
echo ========================================
echo.

cd /d "e:\ship_erp\java\src\main\java"

echo ✅ التعديلات المطبقة بنجاح:
echo ==============================
echo.
echo 📏 TreeMenuPanel.java:
echo    • العرض المفضل: 150 بكسل (بدلاً من 180)
echo    • الحد الأدنى: 140 بكسل (بدلاً من 160)
echo.
echo 📏 EnhancedMainWindow.java:
echo    • العرض الافتراضي: 150 بكسل (بدلاً من 200)
echo    • الحد الأدنى المحفوظ: 140 بكسل (بدلاً من 180)
echo    • عرض التبديل: 150 بكسل (بدلاً من 200)
echo.
echo 🎯 النتيجة: توفير 25-30%% من عرض القائمة
echo.

echo 🚀 تشغيل النافذة الرئيسية...
java -cp "lib/*;." EnhancedMainWindow

echo.
echo ========================================
echo    تم الانتهاء
echo ========================================
pause
