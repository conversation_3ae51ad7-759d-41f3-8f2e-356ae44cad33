package com.shipment.erp.repository;

import com.shipment.erp.model.Item;
import com.shipment.erp.model.ItemCategory;
import com.shipment.erp.model.UnitOfMeasure;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * مستودع الأصناف
 * Item Repository Interface
 */
public interface ItemRepository extends BaseRepository<Item, Long> {
    
    /**
     * البحث عن صنف بالكود
     * Find item by code
     */
    Optional<Item> findByCode(String code);
    
    /**
     * البحث عن صنف بالباركود
     * Find item by barcode
     */
    Optional<Item> findByBarcode(String barcode);
    
    /**
     * البحث عن صنف بالاسم العربي
     * Find item by Arabic name
     */
    Optional<Item> findByNameAr(String nameAr);
    
    /**
     * الحصول على الأصناف حسب المجموعة
     * Get items by category
     */
    List<Item> findByCategory(ItemCategory category);
    
    /**
     * الحصول على الأصناف حسب وحدة القياس
     * Get items by unit of measure
     */
    List<Item> findByUnitOfMeasure(UnitOfMeasure unitOfMeasure);
    
    /**
     * الحصول على جميع الأصناف النشطة
     * Get all active items
     */
    List<Item> findByIsActiveTrue();
    
    /**
     * الحصول على الأصناف القابلة للبيع
     * Get sellable items
     */
    List<Item> findByIsSellableTrue();
    
    /**
     * الحصول على الأصناف القابلة للشراء
     * Get purchasable items
     */
    List<Item> findByIsPurchasableTrue();
    
    /**
     * الحصول على الأصناف التي يتم تتبع مخزونها
     * Get items with inventory tracking
     */
    List<Item> findByTrackInventoryTrue();
    
    /**
     * الحصول على الأصناف منخفضة المخزون
     * Get low stock items
     */
    List<Item> findLowStockItems();
    
    /**
     * الحصول على الأصناف المنتهية من المخزون
     * Get out of stock items
     */
    List<Item> findOutOfStockItems();
    
    /**
     * الحصول على الأصناف التي تحتاج إعادة طلب
     * Get items that need reorder
     */
    List<Item> findItemsNeedingReorder();
    
    /**
     * البحث في الأصناف بالنص
     * Search items by text
     */
    List<Item> searchByText(String searchText);
    
    /**
     * البحث في الأصناف بالنص والمجموعة
     * Search items by text and category
     */
    List<Item> searchByTextAndCategory(String searchText, ItemCategory category);
    
    /**
     * الحصول على الأصناف في نطاق سعري
     * Get items in price range
     */
    List<Item> findByPriceRange(BigDecimal minPrice, BigDecimal maxPrice);
    
    /**
     * الحصول على الأصناف حسب النوع
     * Get items by type
     */
    List<Item> findByItemType(Item.ItemType itemType);
    
    /**
     * الحصول على الأصناف حسب الشركة المصنعة
     * Get items by manufacturer
     */
    List<Item> findByManufacturer(String manufacturer);
    
    /**
     * الحصول على الأصناف حسب العلامة التجارية
     * Get items by brand
     */
    List<Item> findByBrand(String brand);
    
    /**
     * التحقق من وجود صنف بالكود
     * Check if item exists by code
     */
    boolean existsByCode(String code);
    
    /**
     * التحقق من وجود صنف بالباركود
     * Check if item exists by barcode
     */
    boolean existsByBarcode(String barcode);
    
    /**
     * عد الأصناف النشطة
     * Count active items
     */
    long countByIsActiveTrue();
    
    /**
     * عد الأصناف حسب المجموعة
     * Count items by category
     */
    long countByCategory(ItemCategory category);
    
    /**
     * حساب إجمالي قيمة المخزون
     * Calculate total inventory value
     */
    BigDecimal calculateTotalInventoryValue();
    
    /**
     * حساب قيمة المخزون حسب المجموعة
     * Calculate inventory value by category
     */
    BigDecimal calculateInventoryValueByCategory(ItemCategory category);
}
