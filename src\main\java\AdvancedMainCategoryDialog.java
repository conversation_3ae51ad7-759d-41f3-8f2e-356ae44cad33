import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Cursor;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.GridLayout;
import java.awt.Insets;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JColorChooser;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTextArea;
import javax.swing.JTextField;

/**
 * نافذة إضافة مجموعة رئيسية متقدمة Advanced Main Category Add Dialog
 */
public class AdvancedMainCategoryDialog extends JDialog {

    // الخطوط والألوان
    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 12);
    private Font titleFont = new Font("Tahoma", Font.BOLD, 16);

    private Color primaryColor = new Color(0, 123, 255);
    private Color successColor = new Color(40, 167, 69);
    private Color warningColor = new Color(255, 193, 7);
    private Color dangerColor = new Color(220, 53, 69);
    private Color lightGray = new Color(248, 249, 250);
    private Color borderColor = new Color(206, 212, 218);

    // المكونات
    private JTabbedPane tabbedPane;
    private JTextField codeField, nameArField, nameEnField, descriptionField;
    private JTextField sortOrderField, colorField;
    private JComboBox<String> iconCombo, statusCombo, priorityCombo;
    private JCheckBox isActiveCheck, isVisibleCheck, allowSubCategoriesCheck;
    private JCheckBox isSystemCategoryCheck, isDefaultCheck;
    private JTextArea notesArea, rulesArea;
    private JButton saveButton, cancelButton, resetButton, previewButton;
    private JButton colorPickerButton, iconPreviewButton;
    private JLabel previewLabel, iconPreviewLabel;
    private JProgressBar validationProgress;

    // البيانات
    private boolean confirmed = false;
    private ItemCategoryData categoryData;
    private List<ItemCategoryData> existingCategories;

    public AdvancedMainCategoryDialog(JFrame parent, String title,
            List<ItemCategoryData> existingCategories) {
        super(parent, title, true);
        this.existingCategories = existingCategories;

        initializeComponents();
        setupLayout();
        setupEventHandlers();
        setDefaultValues();

        setSize(800, 650);
        setLocationRelativeTo(parent);
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        setResizable(true);
    }

    /**
     * تهيئة المكونات
     */
    private void initializeComponents() {
        // التبويبات
        tabbedPane = new JTabbedPane();
        tabbedPane.setFont(arabicFont);
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // الحقول الأساسية
        codeField = createStyledTextField();
        nameArField = createStyledTextField();
        nameEnField = createStyledTextField();
        descriptionField = createStyledTextField();
        sortOrderField = createStyledTextField();
        sortOrderField.setText("1");
        colorField = createStyledTextField();
        colorField.setText("#007bff");

        // القوائم المنسدلة
        iconCombo = createStyledComboBox();
        statusCombo = createStyledComboBox();
        priorityCombo = createStyledComboBox();

        populateComboBoxes();

        // مربعات الاختيار
        isActiveCheck = createStyledCheckBox("نشط");
        isVisibleCheck = createStyledCheckBox("مرئي في القوائم");
        allowSubCategoriesCheck = createStyledCheckBox("السماح بالمجموعات الفرعية");
        isSystemCategoryCheck = createStyledCheckBox("مجموعة نظام");
        isDefaultCheck = createStyledCheckBox("مجموعة افتراضية");

        // مناطق النص
        notesArea = createStyledTextArea();
        rulesArea = createStyledTextArea();

        // الأزرار
        saveButton = createStyledButton("حفظ", successColor);
        cancelButton = createStyledButton("إلغاء", dangerColor);
        resetButton = createStyledButton("إعادة تعيين", warningColor);
        previewButton = createStyledButton("معاينة", primaryColor);
        colorPickerButton = createStyledButton("اختيار لون", primaryColor);
        iconPreviewButton = createStyledButton("معاينة الأيقونة", primaryColor);

        // التسميات
        previewLabel = createStyledLabel("معاينة المجموعة ستظهر هنا");
        iconPreviewLabel = createStyledLabel("📁");
        iconPreviewLabel.setFont(new Font("Segoe UI Emoji", Font.PLAIN, 24));

        // شريط التقدم
        validationProgress = new JProgressBar();
        validationProgress.setStringPainted(true);
        validationProgress.setString("التحقق من صحة البيانات...");
        validationProgress.setFont(arabicFont);
        validationProgress.setVisible(false);

        // تعيين القيم الافتراضية
        isActiveCheck.setSelected(true);
        isVisibleCheck.setSelected(true);
        allowSubCategoriesCheck.setSelected(true);
    }

    /**
     * إنشاء حقل نص مُنسق
     */
    private JTextField createStyledTextField() {
        JTextField field = new JTextField();
        field.setFont(arabicFont);
        field.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        field.setPreferredSize(new Dimension(300, 35));
        field.setBorder(
                BorderFactory.createCompoundBorder(BorderFactory.createLineBorder(borderColor, 1),
                        BorderFactory.createEmptyBorder(8, 12, 8, 12)));
        return field;
    }

    /**
     * إنشاء قائمة منسدلة مُنسقة
     */
    private JComboBox<String> createStyledComboBox() {
        JComboBox<String> combo = new JComboBox<>();
        combo.setFont(arabicFont);
        combo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        combo.setPreferredSize(new Dimension(300, 35));
        combo.setBorder(BorderFactory.createLineBorder(borderColor, 1));
        return combo;
    }

    /**
     * إنشاء مربع اختيار مُنسق
     */
    private JCheckBox createStyledCheckBox(String text) {
        JCheckBox checkBox = new JCheckBox(text);
        checkBox.setFont(arabicFont);
        checkBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        checkBox.setBackground(Color.WHITE);
        return checkBox;
    }

    /**
     * إنشاء زر مُنسق
     */
    private JButton createStyledButton(String text, Color bgColor) {
        JButton button = new JButton(text);
        button.setFont(arabicBoldFont);
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        button.setPreferredSize(new Dimension(120, 40));
        button.setBackground(bgColor);
        button.setForeground(Color.WHITE);
        button.setBorder(BorderFactory.createEmptyBorder(8, 16, 8, 16));
        button.setFocusPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));

        // تأثير hover
        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setBackground(bgColor.darker());
            }

            @Override
            public void mouseExited(MouseEvent e) {
                button.setBackground(bgColor);
            }
        });

        return button;
    }

    /**
     * إنشاء منطقة نص مُنسقة
     */
    private JTextArea createStyledTextArea() {
        JTextArea area = new JTextArea(4, 30);
        area.setFont(arabicFont);
        area.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        area.setLineWrap(true);
        area.setWrapStyleWord(true);
        area.setBorder(
                BorderFactory.createCompoundBorder(BorderFactory.createLineBorder(borderColor, 1),
                        BorderFactory.createEmptyBorder(8, 12, 8, 12)));
        return area;
    }

    /**
     * إنشاء تسمية مُنسقة
     */
    private JLabel createStyledLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }

    /**
     * ملء القوائم المنسدلة
     */
    private void populateComboBoxes() {
        // الأيقونات
        iconCombo.addItem("📁 مجلد عام");
        iconCombo.addItem("📱 إلكترونيات");
        iconCombo.addItem("👔 ملابس");
        iconCombo.addItem("🍎 مواد غذائية");
        iconCombo.addItem("🏠 أجهزة منزلية");
        iconCombo.addItem("🚗 سيارات");
        iconCombo.addItem("📚 كتب");
        iconCombo.addItem("⚽ رياضة");
        iconCombo.addItem("🎮 ألعاب");
        iconCombo.addItem("💊 صحة وجمال");
        iconCombo.addItem("🔧 أدوات");
        iconCombo.addItem("🎨 فنون وحرف");

        // الحالة
        statusCombo.addItem("نشط");
        statusCombo.addItem("غير نشط");
        statusCombo.addItem("قيد المراجعة");
        statusCombo.addItem("مؤقت");

        // الأولوية
        priorityCombo.addItem("عادية");
        priorityCombo.addItem("عالية");
        priorityCombo.addItem("متوسطة");
        priorityCombo.addItem("منخفضة");
    }

    /**
     * تعيين القيم الافتراضية
     */
    private void setDefaultValues() {
        codeField.setText("");
        nameArField.setText("");
        nameEnField.setText("");
        descriptionField.setText("");
        sortOrderField.setText("1");
        colorField.setText("#007bff");

        iconCombo.setSelectedIndex(0);
        statusCombo.setSelectedIndex(0);
        priorityCombo.setSelectedIndex(0);

        isActiveCheck.setSelected(true);
        isVisibleCheck.setSelected(true);
        allowSubCategoriesCheck.setSelected(true);
        isSystemCategoryCheck.setSelected(false);
        isDefaultCheck.setSelected(false);

        updatePreview();
    }

    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إنشاء التبويبات
        createBasicInfoTab();
        createAdvancedSettingsTab();
        createAppearanceTab();
        createRulesTab();

        // إضافة التبويبات
        add(tabbedPane, BorderLayout.CENTER);

        // شريط الأزرار السفلي
        JPanel buttonPanel = createButtonPanel();
        add(buttonPanel, BorderLayout.SOUTH);

        // شريط الحالة العلوي
        JPanel statusPanel = createStatusPanel();
        add(statusPanel, BorderLayout.NORTH);
    }

    /**
     * إنشاء تبويب المعلومات الأساسية
     */
    private void createBasicInfoTab() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        panel.setBackground(Color.WHITE);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // عنوان القسم
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel titleLabel = new JLabel("المعلومات الأساسية للمجموعة الرئيسية");
        titleLabel.setFont(titleFont);
        titleLabel.setForeground(primaryColor);
        panel.add(titleLabel, gbc);
        row++;

        gbc.gridwidth = 1;

        // الكود
        gbc.gridx = 0;
        gbc.gridy = row;
        panel.add(createFieldLabel("كود المجموعة:", true), gbc);
        gbc.gridx = 1;
        panel.add(codeField, gbc);
        row++;

        // الاسم العربي
        gbc.gridx = 0;
        gbc.gridy = row;
        panel.add(createFieldLabel("الاسم العربي:", true), gbc);
        gbc.gridx = 1;
        panel.add(nameArField, gbc);
        row++;

        // الاسم الإنجليزي
        gbc.gridx = 0;
        gbc.gridy = row;
        panel.add(createFieldLabel("الاسم الإنجليزي:", false), gbc);
        gbc.gridx = 1;
        panel.add(nameEnField, gbc);
        row++;

        // الوصف
        gbc.gridx = 0;
        gbc.gridy = row;
        panel.add(createFieldLabel("الوصف:", false), gbc);
        gbc.gridx = 1;
        panel.add(descriptionField, gbc);
        row++;

        // ترتيب العرض
        gbc.gridx = 0;
        gbc.gridy = row;
        panel.add(createFieldLabel("ترتيب العرض:", false), gbc);
        gbc.gridx = 1;
        panel.add(sortOrderField, gbc);
        row++;

        // مربعات الاختيار الأساسية
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JPanel checkPanel = new JPanel(new GridLayout(2, 2, 10, 5));
        checkPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        checkPanel.setBackground(Color.WHITE);
        checkPanel.add(isActiveCheck);
        checkPanel.add(isVisibleCheck);
        checkPanel.add(allowSubCategoriesCheck);
        checkPanel.add(isDefaultCheck);
        panel.add(checkPanel, gbc);

        tabbedPane.addTab("المعلومات الأساسية", panel);
    }

    /**
     * إنشاء تسمية حقل مع إشارة الإجبارية
     */
    private JLabel createFieldLabel(String text, boolean required) {
        JLabel label = new JLabel(text);
        label.setFont(arabicBoldFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        label.setPreferredSize(new Dimension(150, 35));

        if (required) {
            label.setText(text + " *");
            label.setForeground(dangerColor);
        }

        return label;
    }

    /**
     * إنشاء تبويب الإعدادات المتقدمة
     */
    private void createAdvancedSettingsTab() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        panel.setBackground(Color.WHITE);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // عنوان القسم
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel titleLabel = new JLabel("الإعدادات المتقدمة");
        titleLabel.setFont(titleFont);
        titleLabel.setForeground(primaryColor);
        panel.add(titleLabel, gbc);
        row++;

        gbc.gridwidth = 1;

        // الحالة
        gbc.gridx = 0;
        gbc.gridy = row;
        panel.add(createFieldLabel("الحالة:", false), gbc);
        gbc.gridx = 1;
        panel.add(statusCombo, gbc);
        row++;

        // الأولوية
        gbc.gridx = 0;
        gbc.gridy = row;
        panel.add(createFieldLabel("الأولوية:", false), gbc);
        gbc.gridx = 1;
        panel.add(priorityCombo, gbc);
        row++;

        // مربع اختيار النظام
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        panel.add(isSystemCategoryCheck, gbc);
        row++;

        // الملاحظات
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        panel.add(createFieldLabel("ملاحظات:", false), gbc);
        row++;

        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 1.0;
        JScrollPane notesScroll = new JScrollPane(notesArea);
        notesScroll.setPreferredSize(new Dimension(400, 120));
        panel.add(notesScroll, gbc);

        tabbedPane.addTab("الإعدادات المتقدمة", panel);
    }

    /**
     * إنشاء تبويب المظهر
     */
    private void createAppearanceTab() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        panel.setBackground(Color.WHITE);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // عنوان القسم
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel titleLabel = new JLabel("إعدادات المظهر والعرض");
        titleLabel.setFont(titleFont);
        titleLabel.setForeground(primaryColor);
        panel.add(titleLabel, gbc);
        row++;

        gbc.gridwidth = 1;

        // الأيقونة
        gbc.gridx = 0;
        gbc.gridy = row;
        panel.add(createFieldLabel("الأيقونة:", false), gbc);
        gbc.gridx = 1;
        JPanel iconPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        iconPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        iconPanel.setBackground(Color.WHITE);
        iconPanel.add(iconCombo);
        iconPanel.add(iconPreviewButton);
        iconPanel.add(iconPreviewLabel);
        panel.add(iconPanel, gbc);
        row++;

        // اللون
        gbc.gridx = 0;
        gbc.gridy = row;
        panel.add(createFieldLabel("اللون:", false), gbc);
        gbc.gridx = 1;
        JPanel colorPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        colorPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        colorPanel.setBackground(Color.WHITE);
        colorPanel.add(colorField);
        colorPanel.add(colorPickerButton);
        panel.add(colorPanel, gbc);
        row++;

        // معاينة المجموعة
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        panel.add(createFieldLabel("معاينة المجموعة:", false), gbc);
        row++;

        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        JPanel previewPanel = new JPanel(new BorderLayout());
        previewPanel.setBorder(
                BorderFactory.createCompoundBorder(BorderFactory.createLineBorder(borderColor, 1),
                        BorderFactory.createEmptyBorder(15, 15, 15, 15)));
        previewPanel.setBackground(lightGray);
        previewPanel.add(previewLabel, BorderLayout.CENTER);
        previewPanel.add(previewButton, BorderLayout.EAST);
        panel.add(previewPanel, gbc);

        tabbedPane.addTab("المظهر والعرض", panel);
    }

    /**
     * إنشاء تبويب القواعد
     */
    private void createRulesTab() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        panel.setBackground(Color.WHITE);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;

        // عنوان القسم
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.gridwidth = 2;
        JLabel titleLabel = new JLabel("قواعد وضوابط المجموعة");
        titleLabel.setFont(titleFont);
        titleLabel.setForeground(primaryColor);
        panel.add(titleLabel, gbc);

        // قواعد المجموعة
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.gridwidth = 2;
        panel.add(createFieldLabel("قواعد وضوابط المجموعة:", false), gbc);

        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 1.0;
        JScrollPane rulesScroll = new JScrollPane(rulesArea);
        rulesScroll.setPreferredSize(new Dimension(400, 200));
        panel.add(rulesScroll, gbc);

        // نص توضيحي
        gbc.gridx = 0;
        gbc.gridy = 3;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        gbc.weighty = 0;
        JLabel helpLabel =
                new JLabel("<html><i>يمكنك تحديد القواعد والضوابط الخاصة بهذه المجموعة مثل:<br>"
                        + "- شروط إضافة الأصناف<br>" + "- قواعد التسعير<br>"
                        + "- متطلبات المخزون<br>" + "- أي قيود أخرى</i></html>");
        helpLabel.setFont(arabicFont);
        helpLabel.setForeground(Color.GRAY);
        panel.add(helpLabel, gbc);

        tabbedPane.addTab("القواعد والضوابط", panel);
    }

    /**
     * إنشاء شريط الأزرار
     */
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(10, 20, 10, 20));
        panel.setBackground(lightGray);

        panel.add(saveButton);
        panel.add(resetButton);
        panel.add(cancelButton);

        return panel;
    }

    /**
     * إنشاء شريط الحالة
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(10, 20, 5, 20));
        panel.setBackground(lightGray);

        JLabel statusLabel = createStyledLabel("جاهز لإدخال بيانات المجموعة الجديدة");
        statusLabel.setFont(arabicFont);
        statusLabel.setForeground(primaryColor);
        panel.add(statusLabel, BorderLayout.EAST);

        validationProgress.setVisible(false);
        panel.add(validationProgress, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        // أزرار الحفظ والإلغاء
        saveButton.addActionListener(e -> saveCategory());
        cancelButton.addActionListener(e -> dispose());
        resetButton.addActionListener(e -> resetFields());
        previewButton.addActionListener(e -> updatePreview());

        // أزرار المظهر
        colorPickerButton.addActionListener(e -> chooseColor());
        iconPreviewButton.addActionListener(e -> previewIcon());

        // تحديث المعاينة عند تغيير البيانات
        codeField.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyReleased(java.awt.event.KeyEvent e) {
                updatePreview();
            }
        });

        nameArField.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyReleased(java.awt.event.KeyEvent e) {
                updatePreview();
            }
        });

        iconCombo.addActionListener(e -> updatePreview());
        colorField.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyReleased(java.awt.event.KeyEvent e) {
                updatePreview();
            }
        });
    }

    /**
     * تحديث المعاينة
     */
    private void updatePreview() {
        String code = codeField.getText().trim();
        String nameAr = nameArField.getText().trim();
        String icon = getSelectedIcon();
        String color = colorField.getText().trim();

        if (!code.isEmpty() && !nameAr.isEmpty()) {
            String preview = String.format("%s %s (%s)", icon, nameAr, code);
            previewLabel.setText(preview);
            previewLabel.setForeground(parseColor(color));
        } else {
            previewLabel.setText("أدخل الكود والاسم العربي لرؤية المعاينة");
            previewLabel.setForeground(Color.GRAY);
        }
    }

    /**
     * حفظ المجموعة
     */
    private void saveCategory() {
        if (validateInput()) {
            categoryData = createCategoryData();
            confirmed = true;
            dispose();
        }
    }

    /**
     * إعادة تعيين الحقول
     */
    private void resetFields() {
        int result = JOptionPane.showConfirmDialog(this, "هل تريد إعادة تعيين جميع الحقول؟",
                "تأكيد إعادة التعيين", JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            setDefaultValues();
        }
    }

    /**
     * اختيار لون
     */
    private void chooseColor() {
        Color currentColor = parseColor(colorField.getText());
        Color newColor = JColorChooser.showDialog(this, "اختيار لون المجموعة", currentColor);
        if (newColor != null) {
            colorField.setText(String.format("#%06X", newColor.getRGB() & 0xFFFFFF));
            updatePreview();
        }
    }

    /**
     * معاينة الأيقونة
     */
    private void previewIcon() {
        String icon = getSelectedIcon();
        iconPreviewLabel.setText(icon);
        updatePreview();
    }

    /**
     * الحصول على الأيقونة المحددة
     */
    private String getSelectedIcon() {
        String selected = (String) iconCombo.getSelectedItem();
        if (selected != null && selected.length() > 0) {
            return selected.substring(0, 2); // أول رمزين (الإيموجي)
        }
        return "📁";
    }

    /**
     * تحليل اللون من النص
     */
    private Color parseColor(String colorText) {
        try {
            if (colorText.startsWith("#")) {
                return Color.decode(colorText);
            }
        } catch (NumberFormatException e) {
            // تجاهل الخطأ واستخدام اللون الافتراضي
        }
        return primaryColor;
    }

    /**
     * التحقق من صحة الإدخال
     */
    private boolean validateInput() {
        // التحقق من الحقول الإجبارية
        if (codeField.getText().trim().isEmpty()) {
            showValidationError("يجب إدخال كود المجموعة");
            codeField.requestFocus();
            return false;
        }

        if (nameArField.getText().trim().isEmpty()) {
            showValidationError("يجب إدخال الاسم العربي");
            nameArField.requestFocus();
            return false;
        }

        // التحقق من تكرار الكود
        String code = codeField.getText().trim().toUpperCase();
        for (ItemCategoryData existing : existingCategories) {
            if (existing.getCode().equalsIgnoreCase(code)) {
                showValidationError("كود المجموعة موجود مسبقاً، يرجى اختيار كود آخر");
                codeField.requestFocus();
                return false;
            }
        }

        // التحقق من ترتيب العرض
        try {
            int sortOrder = Integer.parseInt(sortOrderField.getText().trim());
            if (sortOrder < 1) {
                showValidationError("ترتيب العرض يجب أن يكون أكبر من صفر");
                sortOrderField.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            showValidationError("ترتيب العرض يجب أن يكون رقماً صحيحاً");
            sortOrderField.requestFocus();
            return false;
        }

        return true;
    }

    /**
     * عرض رسالة خطأ في التحقق
     */
    private void showValidationError(String message) {
        JOptionPane.showMessageDialog(this, message, "خطأ في البيانات", JOptionPane.ERROR_MESSAGE);
    }

    /**
     * إنشاء بيانات المجموعة
     */
    private ItemCategoryData createCategoryData() {
        ItemCategoryData data = new ItemCategoryData();
        data.setCode(codeField.getText().trim().toUpperCase());
        data.setNameAr(nameArField.getText().trim());
        data.setNameEn(nameEnField.getText().trim());
        data.setDescription(descriptionField.getText().trim());
        data.setParentCode(null); // مجموعة رئيسية
        data.setLevel(1);
        data.setActive(isActiveCheck.isSelected());
        data.setIcon(getSelectedIcon());
        data.setColor(colorField.getText().trim());
        data.setSortOrder(Integer.parseInt(sortOrderField.getText().trim()));
        data.setNotes(notesArea.getText().trim());

        return data;
    }

    /**
     * الحصول على بيانات المجموعة
     */
    public ItemCategoryData getCategoryData() {
        return categoryData;
    }

    /**
     * التحقق من تأكيد الحفظ
     */
    public boolean isConfirmed() {
        return confirmed;
    }
}
