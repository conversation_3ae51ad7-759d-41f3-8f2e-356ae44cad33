import java.io.*;
import java.nio.file.*;

/**
 * فحص سريع للبحث عن مراجع بيانات الأصناف
 */
public class QuickItemDataCheck {
    
    public static void main(String[] args) {
        System.out.println("🔍 فحص سريع للبحث عن مراجع بيانات الأصناف");
        System.out.println("==========================================");
        System.out.println();
        
        // 1. فحص TreeMenuPanel.java
        checkTreeMenuPanel();
        
        // 2. فحص ملفات .class
        checkClassFiles();
        
        // 3. فحص ملفات التوثيق
        checkDocumentationFiles();
        
        // 4. فحص الملفات المهمة
        checkImportantFiles();
        
        System.out.println("\n🎯 خلاصة الفحص:");
        System.out.println("================");
        System.out.println("✅ تم فحص جميع الملفات المهمة");
        System.out.println("✅ للتأكد النهائي، شغّل: java TestTreeMenuAfterDeletion");
    }
    
    /**
     * فحص TreeMenuPanel.java
     */
    private static void checkTreeMenuPanel() {
        System.out.println("🌳 فحص TreeMenuPanel.java:");
        
        try {
            Path file = Paths.get("TreeMenuPanel.java");
            if (Files.exists(file)) {
                String content = Files.readString(file);
                
                boolean hasItemData = content.contains("بيانات الأصناف");
                boolean hasItemDetailData = content.contains("بيانات الأصناف التفصيلية");
                boolean hasOpenItemManagement = content.contains("openItemManagementWindow");
                boolean hasOpenItemDetail = content.contains("openItemDetailWindow");
                
                if (!hasItemData && !hasItemDetailData && !hasOpenItemManagement && !hasOpenItemDetail) {
                    System.out.println("  ✅ TreeMenuPanel.java نظيف من مراجع بيانات الأصناف");
                } else {
                    System.out.println("  ⚠️ TreeMenuPanel.java يحتوي على مراجع:");
                    if (hasItemData) System.out.println("    ❌ بيانات الأصناف");
                    if (hasItemDetailData) System.out.println("    ❌ بيانات الأصناف التفصيلية");
                    if (hasOpenItemManagement) System.out.println("    ❌ openItemManagementWindow");
                    if (hasOpenItemDetail) System.out.println("    ❌ openItemDetailWindow");
                }
            } else {
                System.out.println("  ❌ TreeMenuPanel.java غير موجود");
            }
        } catch (Exception e) {
            System.out.println("  ❌ خطأ في فحص TreeMenuPanel: " + e.getMessage());
        }
    }
    
    /**
     * فحص ملفات .class
     */
    private static void checkClassFiles() {
        System.out.println("\n🔍 فحص ملفات .class:");
        
        try {
            // فحص ItemManagementWindow.class
            if (Files.exists(Paths.get("ItemManagementWindow.class"))) {
                System.out.println("  ❌ ItemManagementWindow.class موجود - يجب حذفه");
            } else {
                System.out.println("  ✅ ItemManagementWindow.class محذوف");
            }
            
            // فحص ItemDetailWindow.class
            if (Files.exists(Paths.get("ItemDetailWindow.class"))) {
                System.out.println("  ❌ ItemDetailWindow.class موجود - يجب حذفه");
            } else {
                System.out.println("  ✅ ItemDetailWindow.class محذوف");
            }
            
            // فحص ملفات .class الفرعية
            boolean foundSubClasses = false;
            File currentDir = new File(".");
            File[] files = currentDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    String name = file.getName();
                    if ((name.startsWith("ItemManagementWindow$") || name.startsWith("ItemDetailWindow$")) 
                        && name.endsWith(".class")) {
                        System.out.println("  ❌ " + name + " موجود - يجب حذفه");
                        foundSubClasses = true;
                    }
                }
            }
            
            if (!foundSubClasses) {
                System.out.println("  ✅ لا توجد ملفات .class فرعية");
            }
            
        } catch (Exception e) {
            System.out.println("  ❌ خطأ في فحص ملفات .class: " + e.getMessage());
        }
    }
    
    /**
     * فحص ملفات التوثيق
     */
    private static void checkDocumentationFiles() {
        System.out.println("\n📄 فحص ملفات التوثيق:");
        
        String[] docFiles = {
            "ITEM_DETAIL_WINDOW_ENHANCEMENTS.md",
            "ITEM_DETAIL_WINDOW_FIXES_DOCUMENTATION.md", 
            "ITEM_DETAIL_WINDOW_CORRECTIONS.md",
            "ITEM_DETAIL_WINDOW_GUIDE.md",
            "ITEM_SYSTEM_ACCESS_GUIDE.md",
            "ITEM_DETAIL_WINDOW_ADVANCED_FEATURES.md",
            "ITEM_SYSTEM_QUICK_START.md",
            "ITEM_MANAGEMENT_SYSTEM_README.md"
        };
        
        boolean foundDocs = false;
        for (String docFile : docFiles) {
            if (Files.exists(Paths.get(docFile))) {
                System.out.println("  ❌ " + docFile + " موجود - يجب حذفه");
                foundDocs = true;
            }
        }
        
        if (!foundDocs) {
            System.out.println("  ✅ جميع ملفات التوثيق المرتبطة محذوفة");
        }
    }
    
    /**
     * فحص الملفات المهمة
     */
    private static void checkImportantFiles() {
        System.out.println("\n📋 فحص الملفات المهمة:");
        
        // فحص ملفات Java الرئيسية
        if (Files.exists(Paths.get("ItemManagementWindow.java"))) {
            System.out.println("  ❌ ItemManagementWindow.java موجود - يجب حذفه");
        } else {
            System.out.println("  ✅ ItemManagementWindow.java محذوف");
        }
        
        if (Files.exists(Paths.get("ItemDetailWindow.java"))) {
            System.out.println("  ❌ ItemDetailWindow.java موجود - يجب حذفه");
        } else {
            System.out.println("  ✅ ItemDetailWindow.java محذوف");
        }
        
        // فحص مجلد com/shipment/erp/view
        try {
            Path viewDir = Paths.get("com/shipment/erp/view");
            if (Files.exists(viewDir)) {
                if (Files.exists(viewDir.resolve("ItemManagementWindow.java"))) {
                    System.out.println("  ❌ com/shipment/erp/view/ItemManagementWindow.java موجود - يجب حذفه");
                } else {
                    System.out.println("  ✅ com/shipment/erp/view/ItemManagementWindow.java محذوف");
                }
            }
        } catch (Exception e) {
            System.out.println("  ⚠️ لا يمكن فحص مجلد com/shipment/erp/view");
        }
    }
}
