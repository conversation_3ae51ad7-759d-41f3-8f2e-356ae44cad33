package com.shipment.erp.util;

import com.shipment.erp.model.Item;
import com.shipment.erp.model.ItemCategory;
import com.shipment.erp.model.UnitOfMeasure;

import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * أداة استيراد البيانات من Excel
 * Excel Import Utility
 */
public class ExcelImportUtil {
    
    /**
     * نتيجة الاستيراد
     * Import Result
     */
    public static class ImportResult {
        private int totalRows;
        private int successfulImports;
        private int failedImports;
        private List<String> errors;
        private List<Item> importedItems;
        
        public ImportResult() {
            this.errors = new ArrayList<>();
            this.importedItems = new ArrayList<>();
        }
        
        // Getters and setters
        public int getTotalRows() { return totalRows; }
        public void setTotalRows(int totalRows) { this.totalRows = totalRows; }
        
        public int getSuccessfulImports() { return successfulImports; }
        public void setSuccessfulImports(int successfulImports) { this.successfulImports = successfulImports; }
        
        public int getFailedImports() { return failedImports; }
        public void setFailedImports(int failedImports) { this.failedImports = failedImports; }
        
        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }
        
        public List<Item> getImportedItems() { return importedItems; }
        public void setImportedItems(List<Item> importedItems) { this.importedItems = importedItems; }
        
        public void addError(String error) {
            this.errors.add(error);
        }
        
        public void addImportedItem(Item item) {
            this.importedItems.add(item);
        }
    }
    
    /**
     * استيراد الأصناف من ملف Excel
     * Import items from Excel file
     */
    public static ImportResult importItemsFromExcel(String filePath) {
        ImportResult result = new ImportResult();
        
        try {
            // مؤقتاً سنقوم بمحاكاة عملية الاستيراد
            // في التطبيق الحقيقي، سنستخدم مكتبة Apache POI لقراءة ملفات Excel
            
            result.setTotalRows(10); // محاكاة 10 صفوف
            
            // محاكاة استيراد البيانات
            for (int i = 1; i <= 10; i++) {
                try {
                    Item item = createSampleItem(i);
                    
                    // التحقق من صحة البيانات
                    if (validateItem(item, result, i)) {
                        result.addImportedItem(item);
                        result.setSuccessfulImports(result.getSuccessfulImports() + 1);
                    } else {
                        result.setFailedImports(result.getFailedImports() + 1);
                    }
                    
                } catch (Exception e) {
                    result.addError("خطأ في الصف " + i + ": " + e.getMessage());
                    result.setFailedImports(result.getFailedImports() + 1);
                }
            }
            
        } catch (Exception e) {
            result.addError("خطأ في قراءة الملف: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * إنشاء صنف تجريبي
     * Create sample item
     */
    private static Item createSampleItem(int index) {
        Item item = new Item();
        item.setCode("IMP" + String.format("%03d", index));
        item.setNameAr("صنف مستورد " + index);
        item.setNameEn("Imported Item " + index);
        item.setDescription("وصف الصنف المستورد رقم " + index);
        
        // إنشاء مجموعة تجريبية
        ItemCategory category = new ItemCategory();
        category.setCode("CAT001");
        category.setNameAr("مجموعة عامة");
        item.setCategory(category);
        
        // إنشاء وحدة قياس تجريبية
        UnitOfMeasure unit = new UnitOfMeasure();
        unit.setCode("PCS");
        unit.setNameAr("قطعة");
        item.setUnitOfMeasure(unit);
        
        item.setSalesPrice(new BigDecimal(100 + (index * 10)));
        item.setCostPrice(new BigDecimal(80 + (index * 8)));
        item.setCurrentStock(new BigDecimal(50 + index));
        item.setMinStockLevel(new BigDecimal(10));
        item.setIsActive(true);
        item.setIsSellable(true);
        item.setIsPurchasable(true);
        item.setTrackInventory(true);
        
        return item;
    }
    
    /**
     * التحقق من صحة بيانات الصنف
     * Validate item data
     */
    private static boolean validateItem(Item item, ImportResult result, int rowNumber) {
        boolean isValid = true;
        
        // التحقق من الكود
        if (item.getCode() == null || item.getCode().trim().isEmpty()) {
            result.addError("الصف " + rowNumber + ": كود الصنف مطلوب");
            isValid = false;
        }
        
        // التحقق من الاسم العربي
        if (item.getNameAr() == null || item.getNameAr().trim().isEmpty()) {
            result.addError("الصف " + rowNumber + ": اسم الصنف باللغة العربية مطلوب");
            isValid = false;
        }
        
        // التحقق من المجموعة
        if (item.getCategory() == null) {
            result.addError("الصف " + rowNumber + ": مجموعة الصنف مطلوبة");
            isValid = false;
        }
        
        // التحقق من وحدة القياس
        if (item.getUnitOfMeasure() == null) {
            result.addError("الصف " + rowNumber + ": وحدة القياس مطلوبة");
            isValid = false;
        }
        
        // التحقق من الأسعار
        if (item.getSalesPrice() != null && item.getSalesPrice().compareTo(BigDecimal.ZERO) < 0) {
            result.addError("الصف " + rowNumber + ": سعر البيع لا يمكن أن يكون سالباً");
            isValid = false;
        }
        
        if (item.getCostPrice() != null && item.getCostPrice().compareTo(BigDecimal.ZERO) < 0) {
            result.addError("الصف " + rowNumber + ": سعر التكلفة لا يمكن أن يكون سالباً");
            isValid = false;
        }
        
        // التحقق من المخزون
        if (item.getCurrentStock() != null && item.getCurrentStock().compareTo(BigDecimal.ZERO) < 0) {
            result.addError("الصف " + rowNumber + ": المخزون الحالي لا يمكن أن يكون سالباً");
            isValid = false;
        }
        
        return isValid;
    }
    
    /**
     * إنشاء قالب Excel للاستيراد
     * Create Excel template for import
     */
    public static void createImportTemplate(String filePath) throws IOException {
        // في التطبيق الحقيقي، سنستخدم Apache POI لإنشاء ملف Excel
        // مؤقتاً سنقوم بمحاكاة العملية
        
        System.out.println("تم إنشاء قالب الاستيراد في: " + filePath);
        
        // القالب يجب أن يحتوي على الأعمدة التالية:
        // - كود الصنف (مطلوب)
        // - الاسم العربي (مطلوب)
        // - الاسم الإنجليزي
        // - الوصف
        // - كود المجموعة (مطلوب)
        // - كود وحدة القياس (مطلوب)
        // - سعر البيع
        // - سعر التكلفة
        // - المخزون الحالي
        // - الحد الأدنى للمخزون
        // - نشط (نعم/لا)
        // - قابل للبيع (نعم/لا)
        // - قابل للشراء (نعم/لا)
        // - تتبع المخزون (نعم/لا)
    }
    
    /**
     * تصدير الأصناف إلى Excel
     * Export items to Excel
     */
    public static void exportItemsToExcel(List<Item> items, String filePath) throws IOException {
        // في التطبيق الحقيقي، سنستخدم Apache POI لإنشاء ملف Excel
        // مؤقتاً سنقوم بمحاكاة العملية
        
        System.out.println("تم تصدير " + items.size() + " صنف إلى: " + filePath);
    }
    
    /**
     * قراءة ملف Excel وإرجاع البيانات كقائمة من الصفوف
     * Read Excel file and return data as list of rows
     */
    private static List<List<String>> readExcelFile(String filePath) throws IOException {
        List<List<String>> rows = new ArrayList<>();
        
        // في التطبيق الحقيقي، سنستخدم Apache POI لقراءة الملف
        // مؤقتاً سنقوم بإرجاع بيانات تجريبية
        
        return rows;
    }
    
    /**
     * تحويل صف Excel إلى كائن Item
     * Convert Excel row to Item object
     */
    private static Item convertRowToItem(List<String> row) {
        Item item = new Item();
        
        try {
            // تحويل البيانات من الصف إلى خصائص الصنف
            if (row.size() > 0) item.setCode(row.get(0));
            if (row.size() > 1) item.setNameAr(row.get(1));
            if (row.size() > 2) item.setNameEn(row.get(2));
            if (row.size() > 3) item.setDescription(row.get(3));
            
            // المزيد من التحويلات...
            
        } catch (Exception e) {
            throw new RuntimeException("خطأ في تحويل البيانات: " + e.getMessage());
        }
        
        return item;
    }
    
    /**
     * التحقق من تنسيق ملف Excel
     * Validate Excel file format
     */
    public static boolean validateExcelFormat(String filePath) {
        try {
            // التحقق من امتداد الملف
            if (!filePath.toLowerCase().endsWith(".xlsx") && !filePath.toLowerCase().endsWith(".xls")) {
                return false;
            }
            
            // التحقق من وجود الملف
            java.io.File file = new java.io.File(filePath);
            if (!file.exists() || !file.canRead()) {
                return false;
            }
            
            // المزيد من التحققات...
            
            return true;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * الحصول على معلومات ملف Excel
     * Get Excel file information
     */
    public static ExcelFileInfo getExcelFileInfo(String filePath) throws IOException {
        ExcelFileInfo info = new ExcelFileInfo();
        
        // في التطبيق الحقيقي، سنقرأ معلومات الملف الفعلية
        info.setFileName(new java.io.File(filePath).getName());
        info.setFileSize(new java.io.File(filePath).length());
        info.setSheetCount(1);
        info.setRowCount(100); // مثال
        info.setColumnCount(14); // مثال
        
        return info;
    }
    
    /**
     * فئة معلومات ملف Excel
     * Excel file information class
     */
    public static class ExcelFileInfo {
        private String fileName;
        private long fileSize;
        private int sheetCount;
        private int rowCount;
        private int columnCount;
        
        // Getters and setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        
        public int getSheetCount() { return sheetCount; }
        public void setSheetCount(int sheetCount) { this.sheetCount = sheetCount; }
        
        public int getRowCount() { return rowCount; }
        public void setRowCount(int rowCount) { this.rowCount = rowCount; }
        
        public int getColumnCount() { return columnCount; }
        public void setColumnCount(int columnCount) { this.columnCount = columnCount; }
    }
}
