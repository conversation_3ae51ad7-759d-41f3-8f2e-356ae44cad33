<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="
           http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context.xsd
           http://www.springframework.org/schema/tx
           http://www.springframework.org/schema/tx/spring-tx.xsd">

    <!-- تفعيل Component Scanning -->
    <context:component-scan base-package="com.shipment.erp"/>
    
    <!-- تحميل ملف الإعدادات -->
    <context:property-placeholder location="classpath:application.properties"/>
    
    <!-- إعداد DataSource -->
    <bean id="dataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <property name="driverClassName" value="${database.driver}"/>
        <property name="jdbcUrl" value="${database.url}"/>
        <property name="username" value="${database.username}"/>
        <property name="password" value="${database.password}"/>
        <property name="minimumIdle" value="${database.pool.minimumIdle}"/>
        <property name="maximumPoolSize" value="${database.pool.maximumPoolSize}"/>
        <property name="connectionTimeout" value="${database.pool.connectionTimeout}"/>
        <property name="idleTimeout" value="${database.pool.idleTimeout}"/>
        <property name="maxLifetime" value="${database.pool.maxLifetime}"/>
        <property name="poolName" value="ShipERPPool"/>
    </bean>
    
    <!-- إعداد SessionFactory -->
    <bean id="sessionFactory" class="org.springframework.orm.hibernate5.LocalSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="configLocation" value="classpath:hibernate.cfg.xml"/>
        <property name="hibernateProperties">
            <props>
                <prop key="hibernate.dialect">${hibernate.dialect}</prop>
                <prop key="hibernate.hbm2ddl.auto">${hibernate.hbm2ddl.auto}</prop>
                <prop key="hibernate.show_sql">${hibernate.show_sql}</prop>
                <prop key="hibernate.format_sql">${hibernate.format_sql}</prop>
                <prop key="hibernate.use_sql_comments">${hibernate.use_sql_comments}</prop>
                <prop key="hibernate.jdbc.batch_size">${hibernate.jdbc.batch_size}</prop>
                <prop key="hibernate.order_inserts">${hibernate.order_inserts}</prop>
                <prop key="hibernate.order_updates">${hibernate.order_updates}</prop>
                <prop key="hibernate.jdbc.batch_versioned_data">${hibernate.jdbc.batch_versioned_data}</prop>
                <prop key="hibernate.connection.characterEncoding">UTF-8</prop>
                <prop key="hibernate.connection.useUnicode">true</prop>
            </props>
        </property>
    </bean>
    
    <!-- إعداد Transaction Manager -->
    <bean id="transactionManager" class="org.springframework.orm.hibernate5.HibernateTransactionManager">
        <property name="sessionFactory" ref="sessionFactory"/>
    </bean>
    
    <!-- تفعيل Transaction Annotations -->
    <tx:annotation-driven transaction-manager="transactionManager"/>
    
    <!-- إعداد Validator -->
    <bean id="validator" class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean"/>
    
    <!-- إعداد MessageSource للرسائل المترجمة -->
    <bean id="messageSource" class="org.springframework.context.support.ResourceBundleMessageSource">
        <property name="basenames">
            <list>
                <value>messages</value>
                <value>validation</value>
                <value>errors</value>
            </list>
        </property>
        <property name="defaultEncoding" value="UTF-8"/>
        <property name="useCodeAsDefaultMessage" value="true"/>
    </bean>
    
    <!-- إعداد LocaleResolver -->
    <bean id="localeResolver" class="org.springframework.web.servlet.i18n.SessionLocaleResolver">
        <property name="defaultLocale" value="ar_SA"/>
    </bean>
    
    <!-- إعداد ApplicationEventPublisher -->
    <bean id="applicationEventPublisher" class="org.springframework.context.event.SimpleApplicationEventMulticaster"/>
    
    <!-- إعداد TaskExecutor للمهام غير المتزامنة -->
    <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="5"/>
        <property name="maxPoolSize" value="20"/>
        <property name="queueCapacity" value="100"/>
        <property name="threadNamePrefix" value="ShipERP-"/>
        <property name="waitForTasksToCompleteOnShutdown" value="true"/>
        <property name="awaitTerminationSeconds" value="60"/>
    </bean>
    
    <!-- إعداد PasswordEncoder -->
    <bean id="passwordEncoder" class="com.shipment.erp.security.BCryptPasswordEncoder"/>
    
    <!-- إعداد AuditService -->
    <bean id="auditService" class="com.shipment.erp.service.AuditService"/>
    
    <!-- إعداد ConfigurationService -->
    <bean id="configurationService" class="com.shipment.erp.service.ConfigurationService"/>
    
    <!-- إعداد SecurityService -->
    <bean id="securityService" class="com.shipment.erp.service.SecurityService"/>
    
    <!-- إعداد ReportService -->
    <bean id="reportService" class="com.shipment.erp.service.ReportService"/>
    
    <!-- إعداد BackupService -->
    <bean id="backupService" class="com.shipment.erp.service.BackupService"/>
    
</beans>
