@echo off
echo ====================================
echo    Final Arabic Character Set Test
echo ====================================
echo.

echo Checking libraries...
if not exist "lib\ojdbc11.jar" (
    echo ERROR: ojdbc11.jar missing!
    pause
    exit /b 1
)

if not exist "lib\orai18n.jar" (
    echo ERROR: orai18n.jar missing!
    pause
    exit /b 1
)

echo SUCCESS: Both libraries found
echo.

echo Running Arabic charset test with English locale...
java -Duser.language=en -Duser.country=US -Dfile.encoding=UTF-8 -cp "lib/*;." ArabicCharsetTest

pause
