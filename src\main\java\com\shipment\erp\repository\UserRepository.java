package com.shipment.erp.repository;

import com.shipment.erp.model.User;
import com.shipment.erp.model.Role;
import com.shipment.erp.model.Branch;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository للمستخدمين
 */
public interface UserRepository extends BaseRepository<User> {

    /**
     * البحث عن مستخدم باسم المستخدم
     */
    Optional<User> findByUsername(String username);

    /**
     * البحث عن مستخدم بالبريد الإلكتروني
     */
    Optional<User> findByEmail(String email);

    /**
     * البحث عن المستخدمين بالدور
     */
    List<User> findByRole(Role role);

    /**
     * البحث عن المستخدمين بالفرع
     */
    List<User> findByBranch(Branch branch);

    /**
     * البحث عن المستخدمين بالاسم الكامل
     */
    List<User> findByFullNameContaining(String fullName);

    /**
     * البحث عن المستخدمين النشطين
     */
    List<User> findActiveUsers();

    /**
     * البحث عن المستخدمين غير النشطين
     */
    List<User> findInactiveUsers();

    /**
     * البحث عن المستخدمين المقفلين
     */
    List<User> findLockedUsers();

    /**
     * البحث عن المستخدمين الذين سجلوا دخول مؤخراً
     */
    List<User> findRecentlyLoggedInUsers(LocalDateTime since);

    /**
     * البحث عن المستخدمين الذين لم يسجلوا دخول منذ فترة
     */
    List<User> findUsersNotLoggedInSince(LocalDateTime since);

    /**
     * التحقق من وجود مستخدم باسم المستخدم
     */
    boolean existsByUsername(String username);

    /**
     * التحقق من وجود مستخدم بالبريد الإلكتروني
     */
    boolean existsByEmail(String email);

    /**
     * الحصول على عدد المستخدمين النشطين
     */
    long countActiveUsers();

    /**
     * الحصول على عدد المستخدمين غير النشطين
     */
    long countInactiveUsers();

    /**
     * الحصول على عدد المستخدمين المقفلين
     */
    long countLockedUsers();

    /**
     * الحصول على عدد المستخدمين بدور معين
     */
    long countUsersByRole(Role role);

    /**
     * الحصول على عدد المستخدمين بفرع معين
     */
    long countUsersByBranch(Branch branch);

    /**
     * تحديث آخر تسجيل دخول
     */
    void updateLastLogin(Long userId, LocalDateTime lastLogin);

    /**
     * تحديث محاولات تسجيل الدخول
     */
    void updateLoginAttempts(Long userId, int attempts);

    /**
     * تحديث حالة القفل
     */
    void updateLockStatus(Long userId, LocalDateTime lockedUntil);

    /**
     * إلغاء قفل جميع المستخدمين المنتهية صلاحية قفلهم
     */
    int unlockExpiredUsers();

    /**
     * البحث المتقدم في المستخدمين
     */
    List<User> advancedSearch(String username, String fullName, String email, 
                             Role role, Branch branch, Boolean isActive);

    /**
     * الحصول على المستخدمين مع الأدوار
     */
    List<User> findUsersWithRoles();

    /**
     * الحصول على المستخدمين بدون أدوار
     */
    List<User> findUsersWithoutRoles();

    /**
     * الحصول على المستخدمين الذين يحتاجون لتغيير كلمة المرور
     */
    List<User> findUsersNeedingPasswordChange(int daysOld);
}
