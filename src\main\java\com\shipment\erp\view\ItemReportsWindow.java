package com.shipment.erp.view;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * نافذة تقارير الأصناف
 * Item Reports Window
 */
public class ItemReportsWindow extends JFrame {
    
    private Font arabicFont;
    
    // UI Components
    private JTabbedPane tabbedPane;
    private JPanel summaryPanel, stockPanel, valuePanel, movementPanel;
    
    // Summary components
    private JLabel totalItemsLabel, activeItemsLabel, lowStockItemsLabel, outOfStockItemsLabel;
    private JLabel totalValueLabel, avgPriceLabel;
    
    // Filter components
    private JComboBox<String> categoryFilter, periodFilter;
    private JDateChooser fromDateChooser, toDateChooser;
    private JButton generateReportButton, exportButton, printButton;
    
    public ItemReportsWindow() {
        this.arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        loadReportData();
        
        setTitle("تقارير الأصناف - Item Reports");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(1000, 700);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }
    
    private void initializeComponents() {
        // إعداد التبويبات
        tabbedPane = new JTabbedPane();
        tabbedPane.setFont(arabicFont);
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // إنشاء التبويبات
        summaryPanel = createSummaryPanel();
        stockPanel = createStockPanel();
        valuePanel = createValuePanel();
        movementPanel = createMovementPanel();
        
        tabbedPane.addTab("ملخص الأصناف", summaryPanel);
        tabbedPane.addTab("تقرير المخزون", stockPanel);
        tabbedPane.addTab("تقرير القيم", valuePanel);
        tabbedPane.addTab("حركة الأصناف", movementPanel);
        
        // مرشحات
        categoryFilter = new JComboBox<>();
        categoryFilter.setFont(arabicFont);
        categoryFilter.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        categoryFilter.addItem("جميع المجموعات");
        categoryFilter.addItem("الإلكترونيات");
        categoryFilter.addItem("الملابس");
        categoryFilter.addItem("المواد الغذائية");
        
        periodFilter = new JComboBox<>();
        periodFilter.setFont(arabicFont);
        periodFilter.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        periodFilter.addItem("اليوم");
        periodFilter.addItem("هذا الأسبوع");
        periodFilter.addItem("هذا الشهر");
        periodFilter.addItem("هذا العام");
        periodFilter.addItem("فترة مخصصة");
        
        // أزرار
        generateReportButton = createButton("إنشاء التقرير", new Color(40, 167, 69));
        exportButton = createButton("تصدير Excel", new Color(23, 162, 184));
        printButton = createButton("طباعة", new Color(108, 117, 125));
    }
    
    private JButton createButton(String text, Color backgroundColor) {
        JButton button = new JButton(text);
        button.setFont(arabicFont);
        button.setBackground(backgroundColor);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(120, 35));
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        return button;
    }
    
    private JPanel createSummaryPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(new EmptyBorder(20, 20, 20, 20));
        
        // لوحة الإحصائيات الرئيسية
        JPanel statsPanel = new JPanel(new GridLayout(2, 3, 20, 20));
        statsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // بطاقات الإحصائيات
        statsPanel.add(createStatCard("إجمالي الأصناف", "156", "صنف", new Color(40, 167, 69)));
        statsPanel.add(createStatCard("الأصناف النشطة", "142", "صنف", new Color(0, 123, 255)));
        statsPanel.add(createStatCard("مخزون منخفض", "8", "صنف", new Color(255, 193, 7)));
        statsPanel.add(createStatCard("نفد المخزون", "3", "صنف", new Color(220, 53, 69)));
        statsPanel.add(createStatCard("إجمالي القيمة", "2,450,000", "ريال", new Color(23, 162, 184)));
        statsPanel.add(createStatCard("متوسط السعر", "157.69", "ريال", new Color(108, 117, 125)));
        
        panel.add(statsPanel, BorderLayout.CENTER);
        
        // لوحة الرسم البياني
        JPanel chartPanel = createChartPanel();
        panel.add(chartPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private JPanel createStatCard(String title, String value, String unit, Color color) {
        JPanel card = new JPanel();
        card.setLayout(new BoxLayout(card, BoxLayout.Y_AXIS));
        card.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(color, 2),
            new EmptyBorder(15, 15, 15, 15)
        ));
        card.setBackground(Color.WHITE);
        card.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel titleLabel = new JLabel(title);
        titleLabel.setFont(new Font(arabicFont.getName(), Font.BOLD, 14));
        titleLabel.setForeground(color);
        titleLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        JLabel valueLabel = new JLabel(value);
        valueLabel.setFont(new Font(arabicFont.getName(), Font.BOLD, 24));
        valueLabel.setForeground(Color.BLACK);
        valueLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        JLabel unitLabel = new JLabel(unit);
        unitLabel.setFont(arabicFont);
        unitLabel.setForeground(Color.GRAY);
        unitLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        card.add(titleLabel);
        card.add(Box.createVerticalStrut(10));
        card.add(valueLabel);
        card.add(Box.createVerticalStrut(5));
        card.add(unitLabel);
        
        return card;
    }
    
    private JPanel createChartPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createTitledBorder("توزيع الأصناف حسب المجموعات"));
        panel.setPreferredSize(new Dimension(0, 200));
        
        // رسم بياني مبسط (يمكن استخدام مكتبة رسم بياني متقدمة)
        JLabel chartLabel = new JLabel("<html><div style='text-align: center;'>" +
            "<h3>الرسم البياني</h3>" +
            "<p>الإلكترونيات: 45%</p>" +
            "<p>الملابس: 30%</p>" +
            "<p>المواد الغذائية: 25%</p>" +
            "</div></html>");
        chartLabel.setFont(arabicFont);
        chartLabel.setHorizontalAlignment(SwingConstants.CENTER);
        chartLabel.setOpaque(true);
        chartLabel.setBackground(new Color(248, 249, 250));
        
        panel.add(chartLabel, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createStockPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(new EmptyBorder(20, 20, 20, 20));
        
        // جدول تقرير المخزون
        String[] columnNames = {"الكود", "اسم الصنف", "المجموعة", "المخزون الحالي", "الحد الأدنى", "الحالة"};
        Object[][] data = {
            {"ITEM001", "هاتف ذكي سامسونج", "الإلكترونيات", "50", "10", "طبيعي"},
            {"ITEM002", "قميص قطني أزرق", "الملابس", "4", "5", "منخفض"},
            {"ITEM003", "أرز بسمتي", "المواد الغذائية", "100", "20", "طبيعي"},
            {"ITEM004", "لابتوب ديل", "الإلكترونيات", "0", "3", "نفد"},
            {"ITEM005", "فستان صيفي", "الملابس", "15", "5", "طبيعي"}
        };
        
        JTable stockTable = new JTable(data, columnNames);
        stockTable.setFont(arabicFont);
        stockTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        stockTable.setRowHeight(25);
        
        JScrollPane scrollPane = new JScrollPane(stockTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createValuePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(new EmptyBorder(20, 20, 20, 20));
        
        // جدول تقرير القيم
        String[] columnNames = {"الكود", "اسم الصنف", "سعر التكلفة", "سعر البيع", "المخزون", "قيمة المخزون"};
        Object[][] data = {
            {"ITEM001", "هاتف ذكي سامسونج", "2000.00", "2500.00", "50", "125,000.00"},
            {"ITEM002", "قميص قطني أزرق", "80.00", "120.00", "25", "3,000.00"},
            {"ITEM003", "أرز بسمتي", "12.00", "15.50", "100", "1,550.00"},
            {"ITEM004", "لابتوب ديل", "3500.00", "4500.00", "8", "36,000.00"},
            {"ITEM005", "فستان صيفي", "150.00", "200.00", "15", "3,000.00"}
        };
        
        JTable valueTable = new JTable(data, columnNames);
        valueTable.setFont(arabicFont);
        valueTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        valueTable.setRowHeight(25);
        
        JScrollPane scrollPane = new JScrollPane(valueTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // لوحة الإجماليات
        JPanel totalsPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        totalsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        totalsPanel.setBorder(new EmptyBorder(10, 0, 0, 0));
        
        JLabel totalLabel = new JLabel("إجمالي قيمة المخزون: 168,550.00 ريال");
        totalLabel.setFont(new Font(arabicFont.getName(), Font.BOLD, 14));
        totalLabel.setForeground(new Color(40, 167, 69));
        
        totalsPanel.add(totalLabel);
        panel.add(totalsPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private JPanel createMovementPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(new EmptyBorder(20, 20, 20, 20));
        
        // جدول حركة الأصناف
        String[] columnNames = {"التاريخ", "الكود", "اسم الصنف", "نوع الحركة", "الكمية", "السعر"};
        Object[][] data = {
            {"2024-01-15", "ITEM001", "هاتف ذكي سامسونج", "مبيعات", "-2", "2500.00"},
            {"2024-01-14", "ITEM002", "قميص قطني أزرق", "مشتريات", "+20", "80.00"},
            {"2024-01-13", "ITEM003", "أرز بسمتي", "مبيعات", "-5", "15.50"},
            {"2024-01-12", "ITEM004", "لابتوب ديل", "مبيعات", "-1", "4500.00"},
            {"2024-01-11", "ITEM005", "فستان صيفي", "مشتريات", "+10", "150.00"}
        };
        
        JTable movementTable = new JTable(data, columnNames);
        movementTable.setFont(arabicFont);
        movementTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        movementTable.setRowHeight(25);
        
        JScrollPane scrollPane = new JScrollPane(movementTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // اللوحة العلوية - المرشحات والأزرار
        JPanel topPanel = createTopPanel();
        add(topPanel, BorderLayout.NORTH);
        
        // التبويبات
        add(tabbedPane, BorderLayout.CENTER);
    }
    
    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(new EmptyBorder(10, 10, 10, 10));
        panel.setBackground(new Color(248, 249, 250));
        
        // لوحة المرشحات
        JPanel filtersPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        filtersPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        filtersPanel.setOpaque(false);
        
        JLabel categoryLabel = new JLabel("المجموعة:");
        categoryLabel.setFont(arabicFont);
        
        JLabel periodLabel = new JLabel("الفترة:");
        periodLabel.setFont(arabicFont);
        
        filtersPanel.add(categoryLabel);
        filtersPanel.add(categoryFilter);
        filtersPanel.add(Box.createHorizontalStrut(10));
        filtersPanel.add(periodLabel);
        filtersPanel.add(periodFilter);
        
        // لوحة الأزرار
        JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        buttonsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        buttonsPanel.setOpaque(false);
        
        buttonsPanel.add(generateReportButton);
        buttonsPanel.add(exportButton);
        buttonsPanel.add(printButton);
        
        panel.add(filtersPanel, BorderLayout.EAST);
        panel.add(buttonsPanel, BorderLayout.WEST);
        
        return panel;
    }
    
    private void setupEventHandlers() {
        generateReportButton.addActionListener(e -> generateReport());
        exportButton.addActionListener(e -> exportReport());
        printButton.addActionListener(e -> printReport());
        
        periodFilter.addActionListener(e -> {
            String selected = (String) periodFilter.getSelectedItem();
            // تحديث المرشحات حسب الفترة المختارة
        });
    }
    
    private void loadReportData() {
        // تحميل بيانات التقارير
        // سيتم تنفيذها لاحقاً مع الخدمات
    }
    
    private void generateReport() {
        String category = (String) categoryFilter.getSelectedItem();
        String period = (String) periodFilter.getSelectedItem();
        
        JOptionPane.showMessageDialog(this,
            "سيتم إنشاء التقرير للمجموعة: " + category + "\n" +
            "للفترة: " + period,
            "إنشاء التقرير",
            JOptionPane.INFORMATION_MESSAGE);
    }
    
    private void exportReport() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("Excel Files", "xlsx"));
        fileChooser.setDialogTitle("حفظ التقرير");
        
        if (fileChooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
            String filePath = fileChooser.getSelectedFile().getAbsolutePath();
            if (!filePath.endsWith(".xlsx")) {
                filePath += ".xlsx";
            }
            
            JOptionPane.showMessageDialog(this,
                "سيتم تصدير التقرير إلى: " + filePath,
                "تصدير التقرير",
                JOptionPane.INFORMATION_MESSAGE);
        }
    }
    
    private void printReport() {
        JOptionPane.showMessageDialog(this,
            "سيتم طباعة التقرير",
            "طباعة التقرير",
            JOptionPane.INFORMATION_MESSAGE);
    }
    
    // فئة مخصصة لاختيار التاريخ (مبسطة)
    private class JDateChooser extends JTextField {
        public JDateChooser() {
            super(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            setFont(arabicFont);
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            setPreferredSize(new Dimension(120, 30));
        }
    }
}
