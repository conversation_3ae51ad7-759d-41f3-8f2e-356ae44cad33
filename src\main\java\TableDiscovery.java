import java.sql.*;

/**
 * أداة اكتشاف جداول وحدات الصنف في قاعدة البيانات
 */
public class TableDiscovery {
    
    public static void main(String[] args) {
        findItemUnitsTable();
    }
    
    /**
     * البحث عن جداول وحدات الصنف في قاعدة البيانات
     */
    public static void findItemUnitsTable() {
        try (Connection conn = DriverManager.getConnection(
                "*************************************", "ship_erp", "ship_erp_password")) {

            System.out.println("=== البحث عن جداول وحدات الصنف ===");
            
            // البحث في جداول النظام عن الجداول المتعلقة بالوحدات
            String[] possibleTables = {
                "IAS_ITM_UNITS", "ITM_UNITS", "ITEM_UNITS", "IAS_UNITS", 
                "IAS_ITM_UNT", "ITM_UNT", "UNITS", "IAS_UNT",
                "IAS_ITM_MEASURE", "ITM_MEASURE", "MEASUREMENT_UNITS",
                "IAS_ITM_DTL", "ITM_DTL", "MEASUREMENT"
            };
            
            for (String tableName : possibleTables) {
                try {
                    String sql = "SELECT COUNT(*) FROM " + tableName;
                    try (PreparedStatement stmt = conn.prepareStatement(sql);
                         ResultSet rs = stmt.executeQuery()) {
                        if (rs.next()) {
                            int count = rs.getInt(1);
                            System.out.println("✅ جدول " + tableName + " موجود ويحتوي على " + count + " سجل");
                            
                            // عرض هيكل الجدول
                            showTableStructure(conn, tableName);
                        }
                    }
                } catch (Exception e) {
                    System.out.println("❌ جدول " + tableName + " غير موجود");
                }
            }
            
            System.out.println("================================");

        } catch (Exception e) {
            System.err.println("خطأ في البحث عن جداول وحدات الصنف: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * عرض هيكل جدول معين
     */
    public static void showTableStructure(Connection conn, String tableName) {
        try {
            String sql = "SELECT * FROM " + tableName + " WHERE ROWNUM = 1";
            try (PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                ResultSetMetaData metaData = rs.getMetaData();
                System.out.println("--- هيكل جدول " + tableName + " ---");
                for (int i = 1; i <= metaData.getColumnCount(); i++) {
                    System.out.println("  " + i + ": " + metaData.getColumnName(i) + 
                                     " (" + metaData.getColumnTypeName(i) + ")");
                }
                System.out.println("----------------------------");
            }
        } catch (Exception e) {
            System.err.println("خطأ في عرض هيكل جدول " + tableName + ": " + e.getMessage());
        }
    }
}
