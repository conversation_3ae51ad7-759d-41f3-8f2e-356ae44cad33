import java.sql.*;

public class TestSHIPConnection {
    public static void main(String[] args) {
        String[] passwords = {"ship123", "oracle", "123", "ship_erp", "ship"};
        
        for (String password : passwords) {
            try {
                System.out.println("🔗 محاولة الاتصال بـ SHIP_ERP بكلمة المرور: " + password);
                Class.forName("oracle.jdbc.driver.OracleDriver");
                Connection conn = DriverManager.getConnection(
                    "*************************************", 
                    "ship_erp", 
                    password
                );
                
                System.out.println("✅ نجح الاتصال بكلمة المرور: " + password);
                
                // اختبار استعلام بسيط
                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM USER_TABLES");
                if (rs.next()) {
                    System.out.println("📊 عدد الجداول: " + rs.getInt(1));
                }
                
                conn.close();
                break;
                
            } catch (Exception e) {
                System.out.println("❌ فشل الاتصال بكلمة المرور: " + password + " - " + e.getMessage());
            }
        }
    }
}
