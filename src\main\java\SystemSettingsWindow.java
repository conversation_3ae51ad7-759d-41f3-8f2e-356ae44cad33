import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Cursor;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.SQLException;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JComponent;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPasswordField;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.SwingWorker;
import javax.swing.border.TitledBorder;

/**
 * نافذة الإعدادات العامة للنظام System Settings Window
 */
public class SystemSettingsWindow extends JFrame {

    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 12);

    // ألوان النظام
    private Color primaryColor = new Color(41, 128, 185);
    private Color successColor = new Color(39, 174, 96);
    private Color warningColor = new Color(243, 156, 18);
    private Color dangerColor = new Color(231, 76, 60);

    private DatabaseConfig dbConfig;
    private JTextArea logArea;
    private JProgressBar progressBar;

    public SystemSettingsWindow() {
        initializeComponents();
    }

    private void initializeComponents() {
        setTitle("الإعدادات العامة للنظام - System Settings");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(800, 600);
        setLocationRelativeTo(null);

        // تطبيق الخط العربي
        setFont(arabicFont);

        // إنشاء التبويبات
        JTabbedPane tabbedPane = new JTabbedPane();
        tabbedPane.setFont(arabicFont);
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // تبويب إعدادات قاعدة البيانات
        tabbedPane.addTab("🗄️ قاعدة البيانات", createDatabaseTab());

        // تبويب الإعدادات العامة
        tabbedPane.addTab("⚙️ إعدادات عامة", createGeneralTab());

        // تبويب النسخ الاحتياطي
        tabbedPane.addTab("💾 النسخ الاحتياطي", createBackupTab());

        add(tabbedPane, BorderLayout.CENTER);

        // شريط الحالة
        add(createStatusBar(), BorderLayout.SOUTH);
    }

    /**
     * إنشاء تبويب إعدادات قاعدة البيانات
     */
    private JPanel createDatabaseTab() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // قسم إعدادات الاتصال
        JPanel connectionPanel = createConnectionPanel();

        // قسم إدارة قاعدة البيانات
        JPanel managementPanel = createDatabaseManagementPanel();

        // قسم السجلات
        JPanel logPanel = createLogPanel();

        // تخطيط الصفحة
        JSplitPane topSplit =
                new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, connectionPanel, managementPanel);
        topSplit.setDividerLocation(400);

        JSplitPane mainSplit = new JSplitPane(JSplitPane.VERTICAL_SPLIT, topSplit, logPanel);
        mainSplit.setDividerLocation(300);

        panel.add(mainSplit, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء قسم إعدادات الاتصال
     */
    private JPanel createConnectionPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder(BorderFactory.createEtchedBorder(),
                "إعدادات الاتصال بقاعدة البيانات", TitledBorder.RIGHT, TitledBorder.TOP,
                arabicBoldFont));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        // حقول إعدادات الاتصال
        JTextField hostField = new JTextField("localhost", 15);
        JTextField portField = new JTextField("1521", 10);
        JTextField serviceField = new JTextField("orcl", 15);
        JTextField usernameField = new JTextField("ship_erp", 15);
        JPasswordField passwordField = new JPasswordField("ship_erp_password", 15);

        // تطبيق الخط العربي
        hostField.setFont(arabicFont);
        portField.setFont(arabicFont);
        serviceField.setFont(arabicFont);
        usernameField.setFont(arabicFont);
        passwordField.setFont(arabicFont);

        // إضافة الحقول
        addLabelAndField(panel, gbc, "الخادم:", hostField, 0);
        addLabelAndField(panel, gbc, "المنفذ:", portField, 1);
        addLabelAndField(panel, gbc, "اسم الخدمة:", serviceField, 2);
        addLabelAndField(panel, gbc, "اسم المستخدم:", usernameField, 3);
        addLabelAndField(panel, gbc, "كلمة المرور:", passwordField, 4);

        // زر اختبار الاتصال
        gbc.gridx = 0;
        gbc.gridy = 5;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;

        JButton testButton = createStyledButton("🔗 اختبار الاتصال", primaryColor);
        testButton.addActionListener(e -> testConnection(hostField, portField, serviceField,
                usernameField, passwordField));
        panel.add(testButton, gbc);

        return panel;
    }

    /**
     * إنشاء قسم إدارة قاعدة البيانات
     */
    private JPanel createDatabaseManagementPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder(BorderFactory.createEtchedBorder(),
                "إدارة قاعدة البيانات", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.gridx = 0;

        // زر إنشاء قاعدة البيانات
        JButton createDBButton = createStyledButton("🏗️ إنشاء بنية قاعدة البيانات", successColor);
        createDBButton.addActionListener(e -> createDatabaseStructure());
        gbc.gridy = 0;
        panel.add(createDBButton, gbc);

        // زر فحص قاعدة البيانات
        JButton checkDBButton = createStyledButton("🔍 فحص بنية قاعدة البيانات", primaryColor);
        checkDBButton.addActionListener(e -> checkDatabaseStructure());
        gbc.gridy = 1;
        panel.add(checkDBButton, gbc);

        // زر إعادة تعيين قاعدة البيانات
        JButton resetDBButton = createStyledButton("🔄 إعادة تعيين قاعدة البيانات", warningColor);
        resetDBButton.addActionListener(e -> resetDatabase());
        gbc.gridy = 2;
        panel.add(resetDBButton, gbc);

        // زر حذف قاعدة البيانات
        JButton deleteDBButton = createStyledButton("🗑️ حذف قاعدة البيانات", dangerColor);
        deleteDBButton.addActionListener(e -> deleteDatabase());
        gbc.gridy = 3;
        panel.add(deleteDBButton, gbc);

        return panel;
    }

    /**
     * إنشاء قسم السجلات
     */
    private JPanel createLogPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder(BorderFactory.createEtchedBorder(),
                "سجل العمليات", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        logArea = new JTextArea(10, 50);
        logArea.setFont(arabicFont);
        logArea.setEditable(false);
        logArea.setBackground(Color.BLACK);
        logArea.setForeground(Color.GREEN);

        JScrollPane scrollPane = new JScrollPane(logArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);

        panel.add(scrollPane, BorderLayout.CENTER);

        // شريط التقدم
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setFont(arabicFont);
        panel.add(progressBar, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * إنشاء تبويب الإعدادات العامة
     */
    private JPanel createGeneralTab() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.EAST;

        // إعدادات عامة
        JTextField appNameField = new JTextField("نظام إدارة الأصناف", 20);
        JTextField versionField = new JTextField("1.0.0", 10);
        JComboBox<String> languageCombo = new JComboBox<>(new String[] {"العربية", "English"});
        JCheckBox autoBackupCheck = new JCheckBox("تفعيل النسخ الاحتياطي التلقائي");

        // تطبيق الخط العربي
        appNameField.setFont(arabicFont);
        versionField.setFont(arabicFont);
        languageCombo.setFont(arabicFont);
        autoBackupCheck.setFont(arabicFont);

        addLabelAndField(panel, gbc, "اسم التطبيق:", appNameField, 0);
        addLabelAndField(panel, gbc, "الإصدار:", versionField, 1);
        addLabelAndField(panel, gbc, "اللغة:", languageCombo, 2);

        gbc.gridx = 0;
        gbc.gridy = 3;
        gbc.gridwidth = 2;
        panel.add(autoBackupCheck, gbc);

        // زر حفظ الإعدادات
        gbc.gridy = 4;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        JButton saveButton = createStyledButton("💾 حفظ الإعدادات", successColor);
        panel.add(saveButton, gbc);

        return panel;
    }

    /**
     * إنشاء تبويب النسخ الاحتياطي
     */
    private JPanel createBackupTab() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.gridx = 0;

        // أزرار النسخ الاحتياطي
        JButton createBackupButton = createStyledButton("📦 إنشاء نسخة احتياطية", primaryColor);
        gbc.gridy = 0;
        panel.add(createBackupButton, gbc);

        JButton restoreBackupButton = createStyledButton("📥 استعادة نسخة احتياطية", warningColor);
        gbc.gridy = 1;
        panel.add(restoreBackupButton, gbc);

        JButton scheduleBackupButton = createStyledButton("⏰ جدولة النسخ الاحتياطي", successColor);
        gbc.gridy = 2;
        panel.add(scheduleBackupButton, gbc);

        return panel;
    }

    /**
     * إنشاء شريط الحالة
     */
    private JPanel createStatusBar() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        panel.setBorder(BorderFactory.createEtchedBorder());

        JLabel statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);
        panel.add(statusLabel);

        return panel;
    }

    /**
     * إضافة تسمية وحقل
     */
    private void addLabelAndField(JPanel panel, GridBagConstraints gbc, String labelText,
            JComponent field, int row) {
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;

        JLabel label = new JLabel(labelText);
        label.setFont(arabicBoldFont);
        panel.add(label, gbc);

        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(field, gbc);
    }

    /**
     * إنشاء زر منسق
     */
    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setFont(arabicBoldFont);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(250, 35));
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));

        // تأثير hover
        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setBackground(color.darker());
            }

            @Override
            public void mouseExited(MouseEvent e) {
                button.setBackground(color);
            }
        });

        return button;
    }

    /**
     * اختبار الاتصال بقاعدة البيانات
     */
    private void testConnection(JTextField hostField, JTextField portField, JTextField serviceField,
            JTextField usernameField, JPasswordField passwordField) {

        SwingWorker<Boolean, String> worker = new SwingWorker<Boolean, String>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                publish("🔄 اختبار الاتصال بقاعدة البيانات...");

                try {
                    String url = "jdbc:oracle:thin:@" + hostField.getText() + ":"
                            + portField.getText() + ":" + serviceField.getText();

                    Connection conn = DriverManager.getConnection(url, usernameField.getText(),
                            new String(passwordField.getPassword()));

                    if (conn != null && !conn.isClosed()) {
                        publish("✅ تم الاتصال بنجاح!");

                        // فحص معلومات قاعدة البيانات
                        DatabaseMetaData metaData = conn.getMetaData();
                        publish("📊 معلومات قاعدة البيانات:");
                        publish("   - اسم قاعدة البيانات: " + metaData.getDatabaseProductName());
                        publish("   - إصدار قاعدة البيانات: "
                                + metaData.getDatabaseProductVersion());
                        publish("   - اسم المستخدم: " + metaData.getUserName());

                        conn.close();
                        return true;
                    }

                } catch (SQLException e) {
                    publish("❌ فشل في الاتصال: " + e.getMessage());
                    return false;
                }

                return false;
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String message : chunks) {
                    logArea.append(message + "\n");
                    logArea.setCaretPosition(logArea.getDocument().getLength());
                }
            }

            @Override
            protected void done() {
                try {
                    boolean success = get();
                    if (success) {
                        JOptionPane.showMessageDialog(SystemSettingsWindow.this,
                                "تم الاتصال بقاعدة البيانات بنجاح!", "نجح الاتصال",
                                JOptionPane.INFORMATION_MESSAGE);
                    } else {
                        JOptionPane.showMessageDialog(SystemSettingsWindow.this,
                                "فشل في الاتصال بقاعدة البيانات!", "خطأ في الاتصال",
                                JOptionPane.ERROR_MESSAGE);
                    }
                } catch (Exception e) {
                    logArea.append("❌ خطأ: " + e.getMessage() + "\n");
                }
            }
        };

        worker.execute();
    }

    /**
     * إنشاء بنية قاعدة البيانات
     */
    private void createDatabaseStructure() {
        JOptionPane.showMessageDialog(this, "سيتم إنشاء بنية قاعدة البيانات",
                "إنشاء قاعدة البيانات", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * فحص بنية قاعدة البيانات
     */
    private void checkDatabaseStructure() {
        JOptionPane.showMessageDialog(this, "سيتم فحص بنية قاعدة البيانات", "فحص قاعدة البيانات",
                JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * إعادة تعيين قاعدة البيانات
     */
    private void resetDatabase() {
        JOptionPane.showMessageDialog(this, "سيتم إعادة تعيين قاعدة البيانات", "إعادة تعيين",
                JOptionPane.WARNING_MESSAGE);
    }

    /**
     * حذف قاعدة البيانات
     */
    private void deleteDatabase() {
        int result = JOptionPane.showConfirmDialog(this, "هل أنت متأكد من حذف قاعدة البيانات؟",
                "تأكيد الحذف", JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);
        if (result == JOptionPane.YES_OPTION) {
            JOptionPane.showMessageDialog(this, "سيتم حذف قاعدة البيانات", "حذف قاعدة البيانات",
                    JOptionPane.ERROR_MESSAGE);
        }
    }
}
