package com.shipment.erp.repository;

import com.shipment.erp.model.UnitOfMeasure;
import java.util.List;
import java.util.Optional;

/**
 * مستودع وحدات القياس
 * Unit of Measure Repository Interface
 */
public interface UnitOfMeasureRepository extends BaseRepository<UnitOfMeasure, Long> {
    
    /**
     * البحث عن وحدة قياس بالكود
     * Find unit of measure by code
     */
    Optional<UnitOfMeasure> findByCode(String code);
    
    /**
     * البحث عن وحدة قياس بالاسم العربي
     * Find unit of measure by Arabic name
     */
    Optional<UnitOfMeasure> findByNameAr(String nameAr);
    
    /**
     * البحث عن وحدة قياس بالرمز
     * Find unit of measure by symbol
     */
    Optional<UnitOfMeasure> findBySymbol(String symbol);
    
    /**
     * الحصول على جميع وحدات القياس النشطة
     * Get all active units of measure
     */
    List<UnitOfMeasure> findByIsActiveTrue();
    
    /**
     * الحصول على وحدات القياس الأساسية
     * Get base units of measure
     */
    List<UnitOfMeasure> findByIsBaseUnitTrue();
    
    /**
     * الحصول على وحدات القياس المرتبطة بوحدة أساسية
     * Get units of measure related to a base unit
     */
    List<UnitOfMeasure> findByBaseUnit(UnitOfMeasure baseUnit);
    
    /**
     * البحث في وحدات القياس بالنص
     * Search units of measure by text
     */
    List<UnitOfMeasure> searchByText(String searchText);
    
    /**
     * الحصول على وحدات القياس مرتبة حسب ترتيب العرض
     * Get units of measure ordered by sort order
     */
    List<UnitOfMeasure> findAllOrderBySortOrder();
    
    /**
     * التحقق من وجود وحدة قياس بالكود
     * Check if unit of measure exists by code
     */
    boolean existsByCode(String code);
    
    /**
     * التحقق من وجود وحدة قياس بالاسم العربي
     * Check if unit of measure exists by Arabic name
     */
    boolean existsByNameAr(String nameAr);
    
    /**
     * عد وحدات القياس النشطة
     * Count active units of measure
     */
    long countByIsActiveTrue();
}
