package com.shipment.erp.repository;

import com.shipment.erp.model.Item;
import com.shipment.erp.model.ItemCategory;
import com.shipment.erp.model.UnitOfMeasure;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * تنفيذ مستودع الأصناف
 * Item Repository Implementation
 */
@Repository
public class ItemRepositoryImpl extends BaseRepositoryImpl<Item, Long> implements ItemRepository {
    
    @Autowired
    private SessionFactory sessionFactory;
    
    public ItemRepositoryImpl() {
        super(Item.class);
    }
    
    @Override
    public Optional<Item> findByCode(String code) {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.code = :code", Item.class);
        query.setParameter("code", code);
        return query.uniqueResultOptional();
    }
    
    @Override
    public Optional<Item> findByBarcode(String barcode) {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.barcode = :barcode", Item.class);
        query.setParameter("barcode", barcode);
        return query.uniqueResultOptional();
    }
    
    @Override
    public Optional<Item> findByNameAr(String nameAr) {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.nameAr = :nameAr", Item.class);
        query.setParameter("nameAr", nameAr);
        return query.uniqueResultOptional();
    }
    
    @Override
    public List<Item> findByCategory(ItemCategory category) {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.category = :category AND i.isActive = true " +
            "ORDER BY i.sortOrder, i.nameAr", Item.class);
        query.setParameter("category", category);
        return query.getResultList();
    }
    
    @Override
    public List<Item> findByUnitOfMeasure(UnitOfMeasure unitOfMeasure) {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.unitOfMeasure = :unitOfMeasure AND i.isActive = true " +
            "ORDER BY i.nameAr", Item.class);
        query.setParameter("unitOfMeasure", unitOfMeasure);
        return query.getResultList();
    }
    
    @Override
    public List<Item> findByIsActiveTrue() {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.isActive = true ORDER BY i.sortOrder, i.nameAr", Item.class);
        return query.getResultList();
    }
    
    @Override
    public List<Item> findByIsSellableTrue() {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.isSellable = true AND i.isActive = true " +
            "ORDER BY i.nameAr", Item.class);
        return query.getResultList();
    }
    
    @Override
    public List<Item> findByIsPurchasableTrue() {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.isPurchasable = true AND i.isActive = true " +
            "ORDER BY i.nameAr", Item.class);
        return query.getResultList();
    }
    
    @Override
    public List<Item> findByTrackInventoryTrue() {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.trackInventory = true AND i.isActive = true " +
            "ORDER BY i.nameAr", Item.class);
        return query.getResultList();
    }
    
    @Override
    public List<Item> findLowStockItems() {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.trackInventory = true AND i.isActive = true " +
            "AND i.currentStock <= i.minStockLevel " +
            "ORDER BY i.currentStock ASC", Item.class);
        return query.getResultList();
    }
    
    @Override
    public List<Item> findOutOfStockItems() {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.trackInventory = true AND i.isActive = true " +
            "AND (i.currentStock IS NULL OR i.currentStock <= 0) " +
            "ORDER BY i.nameAr", Item.class);
        return query.getResultList();
    }
    
    @Override
    public List<Item> findItemsNeedingReorder() {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.trackInventory = true AND i.isActive = true " +
            "AND i.reorderPoint IS NOT NULL " +
            "AND i.currentStock <= i.reorderPoint " +
            "ORDER BY i.currentStock ASC", Item.class);
        return query.getResultList();
    }
    
    @Override
    public List<Item> searchByText(String searchText) {
        Session session = sessionFactory.getCurrentSession();
        String searchPattern = "%" + searchText.toLowerCase() + "%";
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE " +
            "(LOWER(i.nameAr) LIKE :searchText OR " +
            "LOWER(i.nameEn) LIKE :searchText OR " +
            "LOWER(i.code) LIKE :searchText OR " +
            "LOWER(i.barcode) LIKE :searchText OR " +
            "LOWER(i.description) LIKE :searchText) " +
            "AND i.isActive = true " +
            "ORDER BY i.sortOrder, i.nameAr", Item.class);
        query.setParameter("searchText", searchPattern);
        return query.getResultList();
    }
    
    @Override
    public List<Item> searchByTextAndCategory(String searchText, ItemCategory category) {
        Session session = sessionFactory.getCurrentSession();
        String searchPattern = "%" + searchText.toLowerCase() + "%";
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE " +
            "(LOWER(i.nameAr) LIKE :searchText OR " +
            "LOWER(i.nameEn) LIKE :searchText OR " +
            "LOWER(i.code) LIKE :searchText OR " +
            "LOWER(i.barcode) LIKE :searchText OR " +
            "LOWER(i.description) LIKE :searchText) " +
            "AND i.category = :category " +
            "AND i.isActive = true " +
            "ORDER BY i.sortOrder, i.nameAr", Item.class);
        query.setParameter("searchText", searchPattern);
        query.setParameter("category", category);
        return query.getResultList();
    }
    
    @Override
    public List<Item> findByPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.salesPrice BETWEEN :minPrice AND :maxPrice " +
            "AND i.isActive = true ORDER BY i.salesPrice", Item.class);
        query.setParameter("minPrice", minPrice);
        query.setParameter("maxPrice", maxPrice);
        return query.getResultList();
    }
    
    @Override
    public List<Item> findByItemType(Item.ItemType itemType) {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.itemType = :itemType AND i.isActive = true " +
            "ORDER BY i.nameAr", Item.class);
        query.setParameter("itemType", itemType);
        return query.getResultList();
    }
    
    @Override
    public List<Item> findByManufacturer(String manufacturer) {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.manufacturer = :manufacturer AND i.isActive = true " +
            "ORDER BY i.nameAr", Item.class);
        query.setParameter("manufacturer", manufacturer);
        return query.getResultList();
    }
    
    @Override
    public List<Item> findByBrand(String brand) {
        Session session = sessionFactory.getCurrentSession();
        Query<Item> query = session.createQuery(
            "FROM Item i WHERE i.brand = :brand AND i.isActive = true " +
            "ORDER BY i.nameAr", Item.class);
        query.setParameter("brand", brand);
        return query.getResultList();
    }
    
    @Override
    public boolean existsByCode(String code) {
        Session session = sessionFactory.getCurrentSession();
        Query<Long> query = session.createQuery(
            "SELECT COUNT(i) FROM Item i WHERE i.code = :code", Long.class);
        query.setParameter("code", code);
        return query.uniqueResult() > 0;
    }
    
    @Override
    public boolean existsByBarcode(String barcode) {
        Session session = sessionFactory.getCurrentSession();
        Query<Long> query = session.createQuery(
            "SELECT COUNT(i) FROM Item i WHERE i.barcode = :barcode", Long.class);
        query.setParameter("barcode", barcode);
        return query.uniqueResult() > 0;
    }
    
    @Override
    public long countByIsActiveTrue() {
        Session session = sessionFactory.getCurrentSession();
        Query<Long> query = session.createQuery(
            "SELECT COUNT(i) FROM Item i WHERE i.isActive = true", Long.class);
        return query.uniqueResult();
    }
    
    @Override
    public long countByCategory(ItemCategory category) {
        Session session = sessionFactory.getCurrentSession();
        Query<Long> query = session.createQuery(
            "SELECT COUNT(i) FROM Item i WHERE i.category = :category", Long.class);
        query.setParameter("category", category);
        return query.uniqueResult();
    }
    
    @Override
    public BigDecimal calculateTotalInventoryValue() {
        Session session = sessionFactory.getCurrentSession();
        Query<BigDecimal> query = session.createQuery(
            "SELECT COALESCE(SUM(i.currentStock * i.averageCost), 0) " +
            "FROM Item i WHERE i.trackInventory = true AND i.isActive = true", 
            BigDecimal.class);
        return query.uniqueResult();
    }
    
    @Override
    public BigDecimal calculateInventoryValueByCategory(ItemCategory category) {
        Session session = sessionFactory.getCurrentSession();
        Query<BigDecimal> query = session.createQuery(
            "SELECT COALESCE(SUM(i.currentStock * i.averageCost), 0) " +
            "FROM Item i WHERE i.category = :category " +
            "AND i.trackInventory = true AND i.isActive = true", 
            BigDecimal.class);
        query.setParameter("category", category);
        return query.uniqueResult();
    }
}
