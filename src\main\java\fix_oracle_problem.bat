@echo off
chcp 65001 > nul
title حل مشكلة Oracle JDBC نهائياً

echo ====================================
echo    حل مشكلة Oracle JDBC نهائياً
echo    Oracle JDBC Problem Final Fix
echo ====================================
echo.

echo [1/5] فحص المجلد الحالي...
echo المجلد الحالي: %CD%
echo.

echo [2/5] فحص وجود مجلد lib...
if not exist "lib" (
    echo ❌ مجلد lib غير موجود!
    echo إنشاء مجلد lib...
    mkdir lib
)

if not exist "lib\ojdbc11.jar" (
    echo ❌ ملف ojdbc11.jar مفقود!
    echo [3/5] تشغيل LibraryDownloader...
    java LibraryDownloader
    
    if errorlevel 1 (
        echo ❌ فشل في تحميل المكتبات!
        pause
        exit /b 1
    )
) else (
    echo ✅ ملف ojdbc11.jar موجود
)

echo.
echo [4/5] تجميع النظام مع المكتبات...
javac -encoding UTF-8 -cp "lib/*;." *.java

if errorlevel 1 (
    echo ❌ فشل في التجميع!
    pause
    exit /b 1
)

echo ✅ تم التجميع بنجاح!
echo.

echo [5/5] تشغيل النظام مع المكتبات...
echo.
echo ====================================
echo    تشغيل النظام مع Oracle JDBC
echo ====================================
echo.

java -Dfile.encoding=UTF-8 -Duser.language=ar -Duser.country=SA -cp "lib/*;." CompleteSystemTest

echo.
echo ====================================
echo    انتهى التشغيل
echo ====================================
pause
