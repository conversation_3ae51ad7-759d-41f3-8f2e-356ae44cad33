@echo off
title Updated Oracle Connection Settings

echo ====================================
echo    UPDATED Oracle Connection Settings
echo    إعدادات الاتصال المحدثة بـ Oracle
echo ====================================
echo.

echo CONNECTION SETTINGS UPDATED TO:
echo ===============================
echo.

echo ✅ Host: localhost
echo ✅ Port: 1521
echo ✅ Service Name: orcl
echo ✅ Username: ship_erp
echo ✅ Password: ship_erp_password
echo.

echo FILES UPDATED:
echo ==============
echo.

echo 1. DatabaseConfig.java:
echo   - DEFAULT_USERNAME = "ship_erp"
echo   - DEFAULT_PASSWORD = "ship_erp_password"
echo   - DEFAULT_SERVICE_NAME = "orcl"
echo.

echo 2. CompleteSystemTest.java:
echo   - dbConfig.setUsername("ship_erp")
echo   - dbConfig.setPassword("ship_erp_password")
echo.

echo 3. AutoDatabaseConnectionTest.java:
echo   - dbConfig.setUsername("ship_erp")
echo   - dbConfig.setPassword("ship_erp_password")
echo.

echo 4. AdvancedSystemIntegrationWindow.java:
echo   - usernameField default: "ship_erp"
echo   - passwordField default: "ship_erp_password"
echo   - serviceNameField default: "orcl"
echo.

echo CURRENT STATUS:
echo ==============
echo.

echo ✅ All configuration files updated
echo ✅ System compiled successfully
echo ✅ Application running with new settings (Terminal 160)
echo ⚠️ Regex issue prevents command-line testing
echo ✅ GUI interface will use correct credentials
echo.

echo TESTING INSTRUCTIONS:
echo =====================
echo.

echo The application is now configured to use ship_erp credentials.
echo.

echo To test the connection:
echo 1. Go to the running application (Terminal 160)
echo 2. Navigate to: Item Management - System Integration
echo 3. You should see the form pre-filled with:
echo    - Host: localhost
echo    - Port: 1521
echo    - Service: orcl
echo    - Username: ship_erp
echo    - Password: ship_erp_password
echo 4. Click "Test Connection" or "Connect"
echo 5. If successful, click "Import IAS" button
echo.

echo EXPECTED BEHAVIOR:
echo =================
echo.

echo ✅ Automatic connection test on startup (if ship_erp user exists)
echo ✅ GUI shows ship_erp credentials by default
echo ✅ Import functionality works with ship_erp user
echo ✅ Access to IAS tables through ship_erp user
echo.

echo IMPORTANT NOTES:
echo ===============
echo.

echo 1. Make sure ship_erp user exists in Oracle database
echo 2. Ensure ship_erp has access to IAS20251 schema
echo 3. Grant necessary permissions to ship_erp user:
echo    - CONNECT privilege
echo    - SELECT on IAS20251.IAS_ITM_MST
echo    - SELECT on IAS20251.IAS_ITM_DTL
echo.

echo SQL to create user (if needed):
echo CREATE USER ship_erp IDENTIFIED BY ship_erp_password;
echo GRANT CONNECT TO ship_erp;
echo GRANT SELECT ON IAS20251.IAS_ITM_MST TO ship_erp;
echo GRANT SELECT ON IAS20251.IAS_ITM_DTL TO ship_erp;
echo.

echo ====================================
echo    Configuration Update Complete!
echo ====================================
pause
