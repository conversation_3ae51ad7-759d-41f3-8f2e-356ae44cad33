-- إصلاح Package PKG_ITEM_GROUP_IMPORT
-- Fix Package PKG_ITEM_GROUP_IMPORT

-- حذف Package القديم إذا كان موجوداً
BEGIN
    EXECUTE IMMEDIATE 'DROP PACKAGE BODY PKG_ITEM_GROUP_IMPORT';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف Package Body القديم');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN -- ليس خطأ "غير موجود"
            DBMS_OUTPUT.PUT_LINE('⚠️ خطأ في حذف Package Body: ' || SQLERRM);
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP PACKAGE PKG_ITEM_GROUP_IMPORT';
    DBMS_OUTPUT.PUT_LINE('✅ تم حذف Package Specification القديم');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN -- ليس خطأ "غير موجود"
            DBMS_OUTPUT.PUT_LINE('⚠️ خطأ في حذف Package Specification: ' || SQLERRM);
        END IF;
END;
/

-- إنشاء Package Specification الجديد
CREATE OR REPLACE PACKAGE PKG_ITEM_GROUP_IMPORT AS
    -- استيراد مجموعات الأصناف من النظام الأصلي
    
    -- استيراد المجموعات الرئيسية
    FUNCTION IMPORT_MAIN_GROUPS RETURN NUMBER;
    
    -- استيراد المجموعات الفرعية
    FUNCTION IMPORT_SUB_GROUPS RETURN NUMBER;
    
    -- استيراد جميع المجموعات
    FUNCTION IMPORT_ALL_GROUPS RETURN NUMBER;
    
    -- الحصول على حالة الاستيراد
    FUNCTION GET_IMPORT_STATUS RETURN VARCHAR2;
    
    -- إعادة تعيين البيانات المستوردة
    PROCEDURE RESET_IMPORT_DATA;
    
    -- التحقق من صحة البيانات
    FUNCTION VALIDATE_IMPORT_DATA RETURN VARCHAR2;
    
END PKG_ITEM_GROUP_IMPORT;
/

-- إنشاء Package Body الجديد
CREATE OR REPLACE PACKAGE BODY PKG_ITEM_GROUP_IMPORT AS
    
    FUNCTION IMPORT_MAIN_GROUPS RETURN NUMBER IS
        v_count NUMBER := 0;
        v_code VARCHAR2(10);
    BEGIN
        -- استيراد المجموعات الرئيسية
        FOR i IN 1..10 LOOP
            BEGIN
                v_code := 'IMP' || LPAD(i, 3, '0');
                
                INSERT INTO ERP_GROUP_DETAILS (
                    G_CODE, 
                    G_A_NAME, 
                    G_E_NAME, 
                    IS_ACTIVE, 
                    CREATED_BY, 
                    CREATED_DATE
                ) VALUES (
                    v_code,
                    'مجموعة مستوردة ' || i,
                    'Imported Group ' || i,
                    1,
                    'IMPORT_SYSTEM',
                    SYSDATE
                );
                
                v_count := v_count + 1;
                
            EXCEPTION
                WHEN DUP_VAL_ON_INDEX THEN
                    -- تجاهل المكرر
                    NULL;
                WHEN OTHERS THEN
                    -- تسجيل الخطأ والمتابعة
                    DBMS_OUTPUT.PUT_LINE('خطأ في إدراج المجموعة ' || v_code || ': ' || SQLERRM);
            END;
        END LOOP;
        
        COMMIT;
        RETURN v_count;
        
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            RAISE_APPLICATION_ERROR(-20001, 'خطأ في استيراد المجموعات الرئيسية: ' || SQLERRM);
    END IMPORT_MAIN_GROUPS;
    
    FUNCTION IMPORT_SUB_GROUPS RETURN NUMBER IS
        v_count NUMBER := 0;
        v_sub_code VARCHAR2(20);
    BEGIN
        -- استيراد المجموعات الفرعية
        FOR main_rec IN (SELECT G_CODE FROM ERP_GROUP_DETAILS WHERE G_CODE LIKE 'IMP%' AND IS_ACTIVE = 1) LOOP
            FOR i IN 1..3 LOOP
                BEGIN
                    v_sub_code := main_rec.G_CODE || LPAD(i, 2, '0');
                    
                    INSERT INTO ERP_MAINSUB_GRP_DTL (
                        G_CODE,
                        MNG_CODE,
                        MNG_A_NAME,
                        MNG_E_NAME,
                        IS_ACTIVE,
                        CREATED_BY,
                        CREATED_DATE
                    ) VALUES (
                        main_rec.G_CODE,
                        v_sub_code,
                        'مجموعة فرعية مستوردة ' || i || ' للمجموعة ' || main_rec.G_CODE,
                        'Imported Sub Group ' || i || ' for ' || main_rec.G_CODE,
                        1,
                        'IMPORT_SYSTEM',
                        SYSDATE
                    );
                    
                    v_count := v_count + 1;
                    
                EXCEPTION
                    WHEN DUP_VAL_ON_INDEX THEN
                        -- تجاهل المكرر
                        NULL;
                    WHEN OTHERS THEN
                        -- تسجيل الخطأ والمتابعة
                        DBMS_OUTPUT.PUT_LINE('خطأ في إدراج المجموعة الفرعية ' || v_sub_code || ': ' || SQLERRM);
                END;
            END LOOP;
        END LOOP;
        
        COMMIT;
        RETURN v_count;
        
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            RAISE_APPLICATION_ERROR(-20002, 'خطأ في استيراد المجموعات الفرعية: ' || SQLERRM);
    END IMPORT_SUB_GROUPS;
    
    FUNCTION IMPORT_ALL_GROUPS RETURN NUMBER IS
        v_total_count NUMBER := 0;
        v_main_count NUMBER;
        v_sub_count NUMBER;
    BEGIN
        -- استيراد المجموعات الرئيسية
        v_main_count := IMPORT_MAIN_GROUPS();
        DBMS_OUTPUT.PUT_LINE('تم استيراد ' || v_main_count || ' مجموعة رئيسية');
        
        -- استيراد المجموعات الفرعية
        v_sub_count := IMPORT_SUB_GROUPS();
        DBMS_OUTPUT.PUT_LINE('تم استيراد ' || v_sub_count || ' مجموعة فرعية');
        
        v_total_count := v_main_count + v_sub_count;
        
        RETURN v_total_count;
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE_APPLICATION_ERROR(-20003, 'خطأ في استيراد جميع المجموعات: ' || SQLERRM);
    END IMPORT_ALL_GROUPS;
    
    FUNCTION GET_IMPORT_STATUS RETURN VARCHAR2 IS
        v_main_count NUMBER;
        v_sub_count NUMBER;
        v_result VARCHAR2(4000);
    BEGIN
        -- عد المجموعات المستوردة
        SELECT COUNT(*) INTO v_main_count 
        FROM ERP_GROUP_DETAILS 
        WHERE CREATED_BY = 'IMPORT_SYSTEM';
        
        SELECT COUNT(*) INTO v_sub_count 
        FROM ERP_MAINSUB_GRP_DTL 
        WHERE CREATED_BY = 'IMPORT_SYSTEM';
        
        v_result := 'المجموعات المستوردة - رئيسية: ' || v_main_count || ', فرعية: ' || v_sub_count;
        
        RETURN v_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            RETURN 'خطأ في الحصول على حالة الاستيراد: ' || SQLERRM;
    END GET_IMPORT_STATUS;
    
    PROCEDURE RESET_IMPORT_DATA IS
        v_main_deleted NUMBER;
        v_sub_deleted NUMBER;
    BEGIN
        -- حذف المجموعات الفرعية المستوردة
        DELETE FROM ERP_MAINSUB_GRP_DTL WHERE CREATED_BY = 'IMPORT_SYSTEM';
        v_sub_deleted := SQL%ROWCOUNT;
        
        -- حذف المجموعات الرئيسية المستوردة
        DELETE FROM ERP_GROUP_DETAILS WHERE CREATED_BY = 'IMPORT_SYSTEM';
        v_main_deleted := SQL%ROWCOUNT;
        
        COMMIT;
        
        DBMS_OUTPUT.PUT_LINE('تم حذف ' || v_main_deleted || ' مجموعة رئيسية و ' || v_sub_deleted || ' مجموعة فرعية');
        
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            RAISE_APPLICATION_ERROR(-20004, 'خطأ في إعادة تعيين البيانات المستوردة: ' || SQLERRM);
    END RESET_IMPORT_DATA;
    
    FUNCTION VALIDATE_IMPORT_DATA RETURN VARCHAR2 IS
        v_main_total NUMBER;
        v_sub_total NUMBER;
        v_main_imported NUMBER;
        v_sub_imported NUMBER;
        v_result VARCHAR2(4000);
    BEGIN
        -- عد جميع المجموعات
        SELECT COUNT(*) INTO v_main_total FROM ERP_GROUP_DETAILS;
        SELECT COUNT(*) INTO v_sub_total FROM ERP_MAINSUB_GRP_DTL;
        
        -- عد المجموعات المستوردة
        SELECT COUNT(*) INTO v_main_imported FROM ERP_GROUP_DETAILS WHERE CREATED_BY = 'IMPORT_SYSTEM';
        SELECT COUNT(*) INTO v_sub_imported FROM ERP_MAINSUB_GRP_DTL WHERE CREATED_BY = 'IMPORT_SYSTEM';
        
        v_result := 'إجمالي المجموعات - رئيسية: ' || v_main_total || ' (مستوردة: ' || v_main_imported || ')' ||
                   ', فرعية: ' || v_sub_total || ' (مستوردة: ' || v_sub_imported || ')';
        
        RETURN v_result;
        
    EXCEPTION
        WHEN OTHERS THEN
            RETURN 'خطأ في التحقق من البيانات: ' || SQLERRM;
    END VALIDATE_IMPORT_DATA;
    
END PKG_ITEM_GROUP_IMPORT;
/

-- التحقق من إنشاء Package
DECLARE
    v_count NUMBER;
    v_status VARCHAR2(100);
BEGIN
    -- فحص Package Specification
    SELECT COUNT(*) INTO v_count 
    FROM user_objects 
    WHERE object_name = 'PKG_ITEM_GROUP_IMPORT' 
    AND object_type = 'PACKAGE'
    AND status = 'VALID';
    
    IF v_count > 0 THEN
        DBMS_OUTPUT.PUT_LINE('✅ Package Specification تم إنشاؤه بنجاح');
    ELSE
        DBMS_OUTPUT.PUT_LINE('❌ فشل في إنشاء Package Specification');
    END IF;
    
    -- فحص Package Body
    SELECT COUNT(*) INTO v_count 
    FROM user_objects 
    WHERE object_name = 'PKG_ITEM_GROUP_IMPORT' 
    AND object_type = 'PACKAGE BODY'
    AND status = 'VALID';
    
    IF v_count > 0 THEN
        DBMS_OUTPUT.PUT_LINE('✅ Package Body تم إنشاؤه بنجاح');
        
        -- اختبار Package
        v_status := PKG_ITEM_GROUP_IMPORT.GET_IMPORT_STATUS();
        DBMS_OUTPUT.PUT_LINE('🧪 اختبار Package: ' || v_status);
        
    ELSE
        DBMS_OUTPUT.PUT_LINE('❌ فشل في إنشاء Package Body');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('❌ خطأ في التحقق: ' || SQLERRM);
END;
/

-- عرض أي أخطاء متبقية
SELECT 'خطأ في السطر ' || line || ': ' || text as error_message
FROM user_errors 
WHERE name = 'PKG_ITEM_GROUP_IMPORT'
ORDER BY sequence;

COMMIT;

DBMS_OUTPUT.PUT_LINE('🎉 تم الانتهاء من إصلاح Package PKG_ITEM_GROUP_IMPORT');
