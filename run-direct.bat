@echo off
echo ========================================
echo    Ship ERP System - Direct Run
echo ========================================
echo.

echo [INFO] Starting system with direct Java path...
echo.

set JAVA_PATH="C:\Program Files\Java\jdk-17\bin\java.exe"
set JAVAC_PATH="C:\Program Files\Java\jdk-17\bin\javac.exe"

echo [1/3] Checking Java installation...
%JAVA_PATH% -version > nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java not found at expected location
    echo [INFO] Please check Java installation
    pause
    exit /b 1
)
echo [OK] Java found and working

echo [2/3] Compiling application...
cd src\main\java

%JAVAC_PATH% -encoding UTF-8 ArabicDemo.java GeneralSettingsWindow.java
if %errorlevel% neq 0 (
    echo [ERROR] Compilation failed
    cd ..\..\..
    pause
    exit /b 1
)
echo [OK] Compilation successful

echo [3/3] Starting application...
echo.
echo ========================================
echo    Application Started Successfully!
echo ========================================
echo.

echo [INFO] The application window should open now...
echo [INFO] Login credentials:
echo       Username: admin
echo       Password: admin123

%JAVA_PATH% -Dfile.encoding=UTF-8 -Duser.language=ar -Duser.country=SA ArabicDemo

echo.
echo [INFO] Application closed
cd ..\..\..

pause
