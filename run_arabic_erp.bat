@echo off
echo ========================================
echo تشغيل نظام إدارة الشحنات مع دعم النص العربي
echo Running ERP System with Arabic Text Support
echo ========================================

REM إعداد متغيرات البيئة للنص العربي
set JAVA_OPTS=-Dfile.encoding=UTF-8
set JAVA_OPTS=%JAVA_OPTS% -Dsun.jnu.encoding=UTF-8
set JAVA_OPTS=%JAVA_OPTS% -Duser.language=ar
set JAVA_OPTS=%JAVA_OPTS% -Duser.country=SA
set JAVA_OPTS=%JAVA_OPTS% -Duser.region=SA
set JAVA_OPTS=%JAVA_OPTS% -Dawt.useSystemAAFontSettings=lcd
set JAVA_OPTS=%JAVA_OPTS% -Dswing.aatext=true
set JAVA_OPTS=%JAVA_OPTS% -Dswing.useSystemAAFontSettings=lcd
set JAVA_OPTS=%JAVA_OPTS% -Dsun.java2d.xrender=true
set JAVA_OPTS=%JAVA_OPTS% -Dsun.java2d.pmoffscreen=false

REM الانتقال إلى مجلد الكود المصدري
cd /d "%~dp0src\main\java"

echo تجميع الملفات...
echo Compiling files...

REM تجميع مدير النصوص العربية أولاً
javac %JAVA_OPTS% ArabicTextManager.java
if errorlevel 1 (
    echo خطأ في تجميع مدير النصوص العربية
    echo Error compiling ArabicTextManager
    pause
    exit /b 1
)

REM تجميع TreeMenuPanel
javac %JAVA_OPTS% TreeMenuPanel.java
if errorlevel 1 (
    echo خطأ في تجميع TreeMenuPanel
    echo Error compiling TreeMenuPanel
    pause
    exit /b 1
)

REM تجميع النظام الرئيسي
javac %JAVA_OPTS% EnhancedShipERP.java
if errorlevel 1 (
    echo خطأ في تجميع النظام الرئيسي
    echo Error compiling main system
    pause
    exit /b 1
)

echo تم التجميع بنجاح!
echo Compilation successful!
echo.

echo تشغيل النظام...
echo Starting system...
echo.

REM تشغيل النظام مع الخيارات المحسنة للنص العربي
java %JAVA_OPTS% EnhancedShipERP

echo.
echo تم إغلاق النظام
echo System closed
pause
