import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Cursor;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.swing.BorderFactory;
import javax.swing.DefaultCellEditor;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPasswordField;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.SwingUtilities;
import javax.swing.SwingWorker;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة ربط النظام المتقدمة - استيراد البيانات من الأنظمة الأخرى Advanced System Integration Window
 * - Import Data from External Systems
 */
public class AdvancedSystemIntegrationWindow extends JFrame {

    // الخطوط والألوان
    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 12);
    private Font titleFont = new Font("Tahoma", Font.BOLD, 16);

    private Color primaryColor = new Color(0, 123, 255);
    private Color successColor = new Color(40, 167, 69);
    private Color warningColor = new Color(255, 193, 7);
    private Color dangerColor = new Color(220, 53, 69);
    private Color lightGray = new Color(248, 249, 250);
    private Color borderColor = new Color(222, 226, 230);

    // التبويبات الرئيسية
    private JTabbedPane mainTabbedPane;

    // تبويب الاتصال بقاعدة البيانات
    private JTextField hostField, portField, serviceNameField, usernameField;
    private JPasswordField passwordField;
    private JButton testConnectionButton, connectButton, disconnectButton, importIASButton;
    private JTextArea connectionStatusArea;
    private JLabel connectionStatusLabel;

    // تبويب استكشاف البيانات
    private JComboBox<String> tablesCombo;
    private JTable previewTable;
    private DefaultTableModel previewTableModel;
    private JButton refreshTablesButton, previewDataButton;
    private JLabel recordCountLabel;
    private JTextArea tableStructureArea;

    // تبويب تطابق الحقول
    private JTable mappingTable;
    private DefaultTableModel mappingTableModel;
    private JButton autoMapButton, clearMappingButton;
    private JTextArea mappingPreviewArea;

    // تبويب الاستيراد
    private JTextField whereClauseField, maxRecordsField;
    private JCheckBox validateDataCheck, skipDuplicatesCheck, updateExistingCheck;
    private JButton startImportButton, stopImportButton;
    private JProgressBar importProgressBar;
    private JTextArea importLogArea;

    // تبويب التقارير
    private JTable importHistoryTable;
    private DefaultTableModel importHistoryTableModel;
    private JButton generateReportButton, exportLogButton;

    // البيانات والاتصال
    private DatabaseConfig dbConfig;
    private OracleItemImporter importer;
    private boolean isConnected = false;
    private List<ItemData> importedItems;

    public AdvancedSystemIntegrationWindow() {
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        loadDefaultSettings();

        setTitle("ربط النظام المتقدم - استيراد البيانات من الأنظمة الأخرى");
        setSize(1200, 800);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        // تطبيق الاتجاه العربي
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }

    /**
     * تهيئة المكونات
     */
    private void initializeComponents() {
        // التبويبات الرئيسية
        mainTabbedPane = new JTabbedPane();
        mainTabbedPane.setFont(arabicBoldFont);
        mainTabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // تبويب الاتصال
        createConnectionTab();

        // تبويب استكشاف البيانات
        createDataExplorationTab();

        // تبويب تطابق الحقول
        createFieldMappingTab();

        // تبويب الاستيراد
        createImportTab();

        // تبويب التقارير
        createReportsTab();

        // تهيئة البيانات
        dbConfig = new DatabaseConfig();
        importedItems = new ArrayList<>();

        // فحص المكتبات المطلوبة
        checkRequiredLibraries();
    }

    /**
     * إنشاء تبويب الاتصال بقاعدة البيانات
     */
    private void createConnectionTab() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // العنوان
        JLabel titleLabel = new JLabel("🔗 إعداد الاتصال بقاعدة البيانات Oracle");
        titleLabel.setFont(titleFont);
        titleLabel.setForeground(primaryColor);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 20, 0));
        panel.add(titleLabel, BorderLayout.NORTH);

        // نموذج الاتصال
        JPanel formPanel = createConnectionForm();
        panel.add(formPanel, BorderLayout.CENTER);

        // منطقة الحالة
        JPanel statusPanel = createConnectionStatusPanel();
        panel.add(statusPanel, BorderLayout.SOUTH);

        mainTabbedPane.addTab("🔗 الاتصال", panel);
    }

    /**
     * إنشاء نموذج الاتصال
     */
    private JPanel createConnectionForm() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(
                BorderFactory.createTitledBorder(BorderFactory.createLineBorder(borderColor),
                        "بيانات الاتصال", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // الخادم
        gbc.gridx = 0;
        gbc.gridy = row;
        panel.add(createLabel("عنوان الخادم:", true), gbc);
        gbc.gridx = 1;
        hostField = createTextField("localhost");
        panel.add(hostField, gbc);

        // المنفذ
        gbc.gridx = 2;
        gbc.gridy = row;
        panel.add(createLabel("المنفذ:", true), gbc);
        gbc.gridx = 3;
        portField = createTextField("1521");
        portField.setPreferredSize(new Dimension(100, 30));
        panel.add(portField, gbc);
        row++;

        // اسم الخدمة
        gbc.gridx = 0;
        gbc.gridy = row;
        panel.add(createLabel("اسم الخدمة:", true), gbc);
        gbc.gridx = 1;
        serviceNameField = createTextField("orcl");
        panel.add(serviceNameField, gbc);
        row++;

        // اسم المستخدم
        gbc.gridx = 0;
        gbc.gridy = row;
        panel.add(createLabel("اسم المستخدم:", true), gbc);
        gbc.gridx = 1;
        usernameField = createTextField("ias20251");
        panel.add(usernameField, gbc);

        // كلمة المرور
        gbc.gridx = 2;
        gbc.gridy = row;
        panel.add(createLabel("كلمة المرور:", true), gbc);
        gbc.gridx = 3;
        passwordField = new JPasswordField();
        passwordField.setFont(arabicFont);
        passwordField.setPreferredSize(new Dimension(150, 30));
        passwordField.setText("ys123");
        panel.add(passwordField, gbc);
        row++;

        // الأزرار
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.CENTER;

        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        testConnectionButton = createStyledButton("اختبار الاتصال", warningColor);
        connectButton = createStyledButton("الاتصال", successColor);
        disconnectButton = createStyledButton("قطع الاتصال", dangerColor);
        disconnectButton.setEnabled(false);

        // زر استيراد من جداول IAS
        JButton importIASButton = createStyledButton("📥 استيراد IAS", primaryColor);
        importIASButton.setEnabled(false); // يتم تفعيله بعد الاتصال
        importIASButton.addActionListener(e -> importFromIASTables());

        // زر تحليل قاعدة البيانات
        JButton analyzeDBButton =
                createStyledButton("🔍 تحليل قاعدة البيانات", new Color(138, 43, 226));
        analyzeDBButton.addActionListener(e -> openDatabaseAnalysis());

        buttonPanel.add(testConnectionButton);
        buttonPanel.add(connectButton);
        buttonPanel.add(disconnectButton);
        buttonPanel.add(analyzeDBButton);
        buttonPanel.add(importIASButton);

        // حفظ مرجع للزر لتفعيله لاحقاً
        this.importIASButton = importIASButton;

        panel.add(buttonPanel, gbc);

        return panel;
    }

    /**
     * إنشاء منطقة حالة الاتصال
     */
    private JPanel createConnectionStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(
                BorderFactory.createTitledBorder(BorderFactory.createLineBorder(borderColor),
                        "حالة الاتصال", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        connectionStatusLabel = new JLabel("⚪ غير متصل");
        connectionStatusLabel.setFont(arabicBoldFont);
        connectionStatusLabel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        panel.add(connectionStatusLabel, BorderLayout.NORTH);

        connectionStatusArea = new JTextArea(5, 50);
        connectionStatusArea.setFont(arabicFont);
        connectionStatusArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        connectionStatusArea.setEditable(false);
        connectionStatusArea.setBackground(lightGray);
        connectionStatusArea.setText("في انتظار الاتصال بقاعدة البيانات...");

        JScrollPane scrollPane = new JScrollPane(connectionStatusArea);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء تبويب استكشاف البيانات
     */
    private void createDataExplorationTab() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // العنوان
        JLabel titleLabel = new JLabel("🔍 استكشاف البيانات والجداول");
        titleLabel.setFont(titleFont);
        titleLabel.setForeground(primaryColor);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 20, 0));
        panel.add(titleLabel, BorderLayout.NORTH);

        // المحتوى الرئيسي
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // الجانب الأيمن - قائمة الجداول
        JPanel tablesPanel = createTablesPanel();
        splitPane.setRightComponent(tablesPanel);

        // الجانب الأيسر - معاينة البيانات
        JPanel previewPanel = createDataPreviewPanel();
        splitPane.setLeftComponent(previewPanel);

        splitPane.setDividerLocation(400);
        panel.add(splitPane, BorderLayout.CENTER);

        mainTabbedPane.addTab("🔍 استكشاف البيانات", panel);
    }

    /**
     * إنشاء لوحة الجداول
     */
    private JPanel createTablesPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(
                BorderFactory.createTitledBorder(BorderFactory.createLineBorder(borderColor),
                        "الجداول المتاحة", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        // شريط الأدوات
        JPanel toolbarPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        toolbarPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        refreshTablesButton = createStyledButton("تحديث", primaryColor);
        toolbarPanel.add(refreshTablesButton);

        panel.add(toolbarPanel, BorderLayout.NORTH);

        // قائمة الجداول
        tablesCombo = new JComboBox<>();
        tablesCombo.setFont(arabicFont);
        tablesCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        tablesCombo.setPreferredSize(new Dimension(300, 30));
        panel.add(tablesCombo, BorderLayout.CENTER);

        // معلومات الجدول
        JPanel infoPanel = new JPanel(new BorderLayout());
        infoPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        recordCountLabel = new JLabel("عدد السجلات: غير محدد");
        recordCountLabel.setFont(arabicFont);
        recordCountLabel.setBorder(BorderFactory.createEmptyBorder(10, 0, 10, 0));
        infoPanel.add(recordCountLabel, BorderLayout.NORTH);

        tableStructureArea = new JTextArea(10, 30);
        tableStructureArea.setFont(new Font("Courier New", Font.PLAIN, 11));
        tableStructureArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        tableStructureArea.setEditable(false);
        tableStructureArea.setBackground(lightGray);

        JScrollPane structureScrollPane = new JScrollPane(tableStructureArea);
        structureScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        structureScrollPane.setBorder(BorderFactory.createTitledBorder("هيكل الجدول"));
        infoPanel.add(structureScrollPane, BorderLayout.CENTER);

        panel.add(infoPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * إنشاء لوحة معاينة البيانات
     */
    private JPanel createDataPreviewPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(
                BorderFactory.createTitledBorder(BorderFactory.createLineBorder(borderColor),
                        "معاينة البيانات", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        // شريط الأدوات
        JPanel toolbarPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        toolbarPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        previewDataButton = createStyledButton("معاينة البيانات", primaryColor);
        toolbarPanel.add(previewDataButton);

        panel.add(toolbarPanel, BorderLayout.NORTH);

        // جدول المعاينة
        previewTableModel = new DefaultTableModel();
        previewTable = new JTable(previewTableModel);
        previewTable.setFont(arabicFont);
        previewTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        previewTable.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);

        JScrollPane scrollPane = new JScrollPane(previewTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء مساعدات الواجهة
     */
    private JLabel createLabel(String text, boolean required) {
        JLabel label = new JLabel(text);
        label.setFont(arabicBoldFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        if (required) {
            label.setText(text + " *");
            label.setForeground(dangerColor);
        }

        return label;
    }

    private JTextField createTextField(String defaultValue) {
        JTextField field = new JTextField(defaultValue);
        field.setFont(arabicFont);
        field.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        field.setPreferredSize(new Dimension(200, 30));
        field.setBorder(
                BorderFactory.createCompoundBorder(BorderFactory.createLineBorder(borderColor),
                        BorderFactory.createEmptyBorder(5, 10, 5, 10)));
        return field;
    }

    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setFont(arabicBoldFont);
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(120, 35));
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));

        // تأثير hover
        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setBackground(color.darker());
            }

            @Override
            public void mouseExited(MouseEvent e) {
                button.setBackground(color);
            }
        });

        return button;
    }

    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        add(mainTabbedPane, BorderLayout.CENTER);
    }

    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        // أزرار الاتصال
        testConnectionButton.addActionListener(e -> testConnection());
        connectButton.addActionListener(e -> connectToDatabase());
        disconnectButton.addActionListener(e -> disconnectFromDatabase());

        // أزرار استكشاف البيانات
        refreshTablesButton.addActionListener(e -> refreshTables());
        previewDataButton.addActionListener(e -> previewTableData());

        // تغيير الجدول المحدد
        tablesCombo.addActionListener(e -> onTableSelectionChanged());
    }

    /**
     * تحميل الإعدادات الافتراضية
     */
    private void loadDefaultSettings() {
        // تعطيل التبويبات حتى يتم الاتصال
        for (int i = 1; i < mainTabbedPane.getTabCount(); i++) {
            mainTabbedPane.setEnabledAt(i, false);
        }
    }

    /**
     * إنشاء تبويب تطابق الحقول
     */
    private void createFieldMappingTab() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // العنوان
        JLabel titleLabel = new JLabel("🔗 تطابق الحقول - ربط حقول قاعدة البيانات مع حقول النظام");
        titleLabel.setFont(titleFont);
        titleLabel.setForeground(primaryColor);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 20, 0));
        panel.add(titleLabel, BorderLayout.NORTH);

        // المحتوى الرئيسي
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // الجانب الأيمن - جدول التطابق
        JPanel mappingPanel = createMappingPanel();
        splitPane.setRightComponent(mappingPanel);

        // الجانب الأيسر - معاينة التطابق
        JPanel previewPanel = createMappingPreviewPanel();
        splitPane.setLeftComponent(previewPanel);

        splitPane.setDividerLocation(600);
        panel.add(splitPane, BorderLayout.CENTER);

        mainTabbedPane.addTab("🔗 تطابق الحقول", panel);
    }

    /**
     * إنشاء لوحة التطابق
     */
    private JPanel createMappingPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(
                BorderFactory.createTitledBorder(BorderFactory.createLineBorder(borderColor),
                        "تطابق الحقول", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        // شريط الأدوات
        JPanel toolbarPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        toolbarPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        autoMapButton = createStyledButton("تطابق تلقائي", successColor);
        clearMappingButton = createStyledButton("مسح التطابق", warningColor);

        toolbarPanel.add(autoMapButton);
        toolbarPanel.add(clearMappingButton);
        panel.add(toolbarPanel, BorderLayout.NORTH);

        // جدول التطابق
        String[] columnNames = {"حقل قاعدة البيانات", "نوع البيانات", "حقل النظام", "مطلوب"};
        mappingTableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return column == 2; // فقط عمود حقل النظام قابل للتعديل
            }
        };

        mappingTable = new JTable(mappingTableModel);
        mappingTable.setFont(arabicFont);
        mappingTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mappingTable.setRowHeight(25);

        // إعداد عمود حقل النظام كقائمة منسدلة
        JComboBox<String> systemFieldsCombo =
                new JComboBox<>(new String[] {"", "code", "name_ar", "name_en", "description",
                        "category_code", "unit_code", "sales_price", "cost_price", "current_stock",
                        "min_stock_level", "is_active", "barcode", "supplier", "manufacturer"});
        systemFieldsCombo.setFont(arabicFont);
        mappingTable.getColumnModel().getColumn(2)
                .setCellEditor(new DefaultCellEditor(systemFieldsCombo));

        JScrollPane scrollPane = new JScrollPane(mappingTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء لوحة معاينة التطابق
     */
    private JPanel createMappingPreviewPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(
                BorderFactory.createTitledBorder(BorderFactory.createLineBorder(borderColor),
                        "معاينة التطابق", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        mappingPreviewArea = new JTextArea();
        mappingPreviewArea.setFont(new Font("Courier New", Font.PLAIN, 12));
        mappingPreviewArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mappingPreviewArea.setEditable(false);
        mappingPreviewArea.setBackground(lightGray);
        mappingPreviewArea.setText("اختر جدولاً وقم بتطابق الحقول لرؤية المعاينة...");

        JScrollPane scrollPane = new JScrollPane(mappingPreviewArea);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء تبويب الاستيراد
     */
    private void createImportTab() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // العنوان
        JLabel titleLabel = new JLabel("📥 استيراد البيانات - تنفيذ عملية الاستيراد");
        titleLabel.setFont(titleFont);
        titleLabel.setForeground(primaryColor);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 20, 0));
        panel.add(titleLabel, BorderLayout.NORTH);

        // المحتوى الرئيسي
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        splitPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // الجزء العلوي - إعدادات الاستيراد
        JPanel settingsPanel = createImportSettingsPanel();
        splitPane.setTopComponent(settingsPanel);

        // الجزء السفلي - سجل الاستيراد
        JPanel logPanel = createImportLogPanel();
        splitPane.setBottomComponent(logPanel);

        splitPane.setDividerLocation(300);
        panel.add(splitPane, BorderLayout.CENTER);

        mainTabbedPane.addTab("📥 الاستيراد", panel);
    }

    /**
     * إنشاء لوحة إعدادات الاستيراد
     */
    private JPanel createImportSettingsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(
                BorderFactory.createTitledBorder(BorderFactory.createLineBorder(borderColor),
                        "إعدادات الاستيراد", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        // نموذج الإعدادات
        JPanel formPanel = new JPanel(new GridBagLayout());
        formPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // شرط WHERE
        gbc.gridx = 0;
        gbc.gridy = row;
        formPanel.add(createLabel("شرط WHERE:", false), gbc);
        gbc.gridx = 1;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        whereClauseField = createTextField("");
        whereClauseField.setPreferredSize(new Dimension(400, 30));
        formPanel.add(whereClauseField, gbc);
        row++;

        // الحد الأقصى للسجلات
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;
        formPanel.add(createLabel("الحد الأقصى للسجلات:", false), gbc);
        gbc.gridx = 1;
        maxRecordsField = createTextField("1000");
        maxRecordsField.setPreferredSize(new Dimension(100, 30));
        formPanel.add(maxRecordsField, gbc);
        row++;

        // خيارات الاستيراد
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 3;
        JPanel optionsPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        optionsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        validateDataCheck = new JCheckBox("التحقق من صحة البيانات");
        validateDataCheck.setFont(arabicFont);
        validateDataCheck.setSelected(true);

        skipDuplicatesCheck = new JCheckBox("تخطي المكررات");
        skipDuplicatesCheck.setFont(arabicFont);
        skipDuplicatesCheck.setSelected(true);

        updateExistingCheck = new JCheckBox("تحديث الموجود");
        updateExistingCheck.setFont(arabicFont);

        optionsPanel.add(validateDataCheck);
        optionsPanel.add(skipDuplicatesCheck);
        optionsPanel.add(updateExistingCheck);

        formPanel.add(optionsPanel, gbc);
        row++;

        // أزرار التحكم
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 3;
        gbc.anchor = GridBagConstraints.CENTER;

        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        startImportButton = createStyledButton("بدء الاستيراد", successColor);
        stopImportButton = createStyledButton("إيقاف الاستيراد", dangerColor);
        stopImportButton.setEnabled(false);

        buttonPanel.add(startImportButton);
        buttonPanel.add(stopImportButton);

        formPanel.add(buttonPanel, gbc);

        panel.add(formPanel, BorderLayout.CENTER);

        // شريط التقدم
        JPanel progressPanel = new JPanel(new BorderLayout());
        progressPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        progressPanel.setBorder(BorderFactory.createEmptyBorder(10, 0, 0, 0));

        importProgressBar = new JProgressBar();
        importProgressBar.setStringPainted(true);
        importProgressBar.setString("جاهز للاستيراد");
        importProgressBar.setFont(arabicFont);

        progressPanel.add(importProgressBar, BorderLayout.CENTER);
        panel.add(progressPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * إنشاء لوحة سجل الاستيراد
     */
    private JPanel createImportLogPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(
                BorderFactory.createTitledBorder(BorderFactory.createLineBorder(borderColor),
                        "سجل الاستيراد", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        importLogArea = new JTextArea();
        importLogArea.setFont(new Font("Courier New", Font.PLAIN, 11));
        importLogArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        importLogArea.setEditable(false);
        importLogArea.setBackground(Color.BLACK);
        importLogArea.setForeground(Color.GREEN);
        importLogArea.setText("في انتظار بدء عملية الاستيراد...\n");

        JScrollPane scrollPane = new JScrollPane(importLogArea);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء تبويب التقارير
     */
    private void createReportsTab() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // العنوان
        JLabel titleLabel = new JLabel("📊 التقارير والإحصائيات - تتبع عمليات الاستيراد");
        titleLabel.setFont(titleFont);
        titleLabel.setForeground(primaryColor);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 20, 0));
        panel.add(titleLabel, BorderLayout.NORTH);

        // جدول تاريخ الاستيراد
        String[] columnNames = {"التاريخ", "الجدول", "السجلات المستوردة", "السجلات المرفوضة",
                "الحالة", "الوقت المستغرق"};
        importHistoryTableModel = new DefaultTableModel(columnNames, 0);
        importHistoryTable = new JTable(importHistoryTableModel);
        importHistoryTable.setFont(arabicFont);
        importHistoryTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        importHistoryTable.setRowHeight(25);

        JScrollPane scrollPane = new JScrollPane(importHistoryTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(scrollPane, BorderLayout.CENTER);

        // أزرار التقارير
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        generateReportButton = createStyledButton("إنشاء تقرير", primaryColor);
        exportLogButton = createStyledButton("تصدير السجل", warningColor);

        buttonPanel.add(generateReportButton);
        buttonPanel.add(exportLogButton);

        panel.add(buttonPanel, BorderLayout.SOUTH);

        mainTabbedPane.addTab("📊 التقارير", panel);
    }

    /**
     * اختبار الاتصال بقاعدة البيانات
     */
    private void testConnection() {
        updateConnectionConfig();

        SwingWorker<Boolean, String> worker = new SwingWorker<Boolean, String>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                publish("جاري اختبار الاتصال...");

                // إصلاح إعدادات اللغة لتجنب مشاكل regex
                fixLocaleSettings();

                try {
                    boolean result = dbConfig.testConnection();
                    if (result) {
                        DatabaseConfig.DatabaseInfo info = dbConfig.getDatabaseInfo();
                        publish("✅ نجح الاتصال!");
                        publish(info.toString());
                        publish("🔄 اختبار استعلام بسيط...");

                        // اختبار استعلام بسيط
                        testSimpleQuery();
                        publish("✅ الاستعلام نجح!");

                    } else {
                        publish("❌ فشل الاتصال!");
                    }
                    return result;
                } catch (Exception e) {
                    publish("❌ خطأ في الاتصال: " + e.getMessage());
                    return false;
                }
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String message : chunks) {
                    connectionStatusArea.append(message + "\n");
                }
                connectionStatusArea
                        .setCaretPosition(connectionStatusArea.getDocument().getLength());
            }

            @Override
            protected void done() {
                try {
                    boolean success = get();
                    if (success) {
                        connectionStatusLabel.setText("🟢 الاتصال ناجح");
                        connectionStatusLabel.setForeground(successColor);
                    } else {
                        connectionStatusLabel.setText("🔴 فشل الاتصال");
                        connectionStatusLabel.setForeground(dangerColor);
                    }
                } catch (Exception e) {
                    connectionStatusLabel.setText("🔴 خطأ في الاتصال");
                    connectionStatusLabel.setForeground(dangerColor);
                }
            }
        };

        worker.execute();
    }

    /**
     * الاتصال بقاعدة البيانات
     */
    private void connectToDatabase() {
        updateConnectionConfig();

        SwingWorker<Boolean, String> worker = new SwingWorker<Boolean, String>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                publish("جاري الاتصال بقاعدة البيانات...");
                publish("📋 إعدادات الاتصال:");
                publish("   الخادم: " + dbConfig.getHost());
                publish("   المنفذ: " + dbConfig.getPort());
                publish("   الخدمة: " + dbConfig.getServiceName());
                publish("   المستخدم: " + dbConfig.getUsername());
                publish("   كلمة المرور: "
                        + (dbConfig.getPassword().isEmpty() ? "فارغة" : "محددة"));

                try {
                    // إصلاح إعدادات اللغة لتجنب مشاكل regex
                    fixLocaleSettings();

                    importer = new OracleItemImporter(dbConfig);
                    boolean result = importer.connect();

                    if (result) {
                        publish("✅ تم الاتصال بنجاح!");
                        DatabaseConfig.DatabaseInfo info = dbConfig.getDatabaseInfo();
                        publish(info.toString());
                    } else {
                        publish("❌ فشل في الاتصال!");
                    }

                    return result;
                } catch (Exception e) {
                    publish("❌ خطأ في الاتصال: " + e.getMessage());
                    e.printStackTrace(); // للتشخيص
                    return false;
                }
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String message : chunks) {
                    connectionStatusArea.append(message + "\n");
                }
                connectionStatusArea
                        .setCaretPosition(connectionStatusArea.getDocument().getLength());
            }

            @Override
            protected void done() {
                try {
                    boolean success = get();
                    isConnected = success;

                    if (success) {
                        connectionStatusLabel.setText("🟢 متصل");
                        connectionStatusLabel.setForeground(successColor);

                        // تفعيل التبويبات الأخرى
                        for (int i = 1; i < mainTabbedPane.getTabCount(); i++) {
                            mainTabbedPane.setEnabledAt(i, true);
                        }

                        connectButton.setEnabled(false);
                        disconnectButton.setEnabled(true);
                        importIASButton.setEnabled(true); // تفعيل زر استيراد IAS

                        // تحديث قائمة الجداول
                        refreshTables();

                    } else {
                        connectionStatusLabel.setText("🔴 غير متصل");
                        connectionStatusLabel.setForeground(dangerColor);
                    }
                } catch (Exception e) {
                    connectionStatusLabel.setText("🔴 خطأ في الاتصال");
                    connectionStatusLabel.setForeground(dangerColor);
                }
            }
        };

        worker.execute();
    }

    /**
     * قطع الاتصال بقاعدة البيانات
     */
    private void disconnectFromDatabase() {
        if (importer != null) {
            importer.disconnect();
        }

        isConnected = false;
        connectionStatusLabel.setText("⚪ غير متصل");
        connectionStatusLabel.setForeground(Color.GRAY);
        connectionStatusArea.append("تم قطع الاتصال.\n");

        // تعطيل التبويبات الأخرى
        for (int i = 1; i < mainTabbedPane.getTabCount(); i++) {
            mainTabbedPane.setEnabledAt(i, false);
        }

        connectButton.setEnabled(true);
        disconnectButton.setEnabled(false);
        importIASButton.setEnabled(false); // إلغاء تفعيل زر استيراد IAS

        // مسح البيانات
        tablesCombo.removeAllItems();
        previewTableModel.setRowCount(0);
        mappingTableModel.setRowCount(0);
    }

    /**
     * تحديث إعدادات الاتصال
     */
    private void updateConnectionConfig() {
        dbConfig.setHost(hostField.getText().trim());
        dbConfig.setPort(portField.getText().trim());
        dbConfig.setServiceName(serviceNameField.getText().trim());
        dbConfig.setUsername(usernameField.getText().trim());
        dbConfig.setPassword(new String(passwordField.getPassword()));
    }

    /**
     * تحديث قائمة الجداول
     */
    private void refreshTables() {
        if (!isConnected || importer == null) {
            return;
        }

        SwingWorker<java.util.List<String>, Void> worker =
                new SwingWorker<java.util.List<String>, Void>() {
                    @Override
                    protected java.util.List<String> doInBackground() throws Exception {
                        return importer.getAvailableTables();
                    }

                    @Override
                    protected void done() {
                        try {
                            java.util.List<String> tables = get();

                            tablesCombo.removeAllItems();
                            tablesCombo.addItem("-- اختر جدولاً --");

                            for (String table : tables) {
                                tablesCombo.addItem(table);
                            }

                            connectionStatusArea.append(
                                    "تم تحديث قائمة الجداول: " + tables.size() + " جدول.\n");

                        } catch (Exception e) {
                            connectionStatusArea
                                    .append("خطأ في تحديث الجداول: " + e.getMessage() + "\n");
                        }
                    }
                };

        worker.execute();
    }

    /**
     * معاينة بيانات الجدول
     */
    private void previewTableData() {
        String selectedTable = (String) tablesCombo.getSelectedItem();
        if (selectedTable == null || selectedTable.startsWith("--")) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار جدول أولاً", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        SwingWorker<java.util.List<Map<String, Object>>, String> worker =
                new SwingWorker<java.util.List<Map<String, Object>>, String>() {
                    @Override
                    protected java.util.List<Map<String, Object>> doInBackground()
                            throws Exception {
                        publish("جاري تحميل معاينة البيانات...");
                        return importer.previewTableData(selectedTable, 10);
                    }

                    @Override
                    protected void process(java.util.List<String> chunks) {
                        for (String message : chunks) {
                            connectionStatusArea.append(message + "\n");
                        }
                    }

                    @Override
                    protected void done() {
                        try {
                            java.util.List<Map<String, Object>> preview = get();

                            // مسح البيانات السابقة
                            previewTableModel.setRowCount(0);
                            previewTableModel.setColumnCount(0);

                            if (!preview.isEmpty()) {
                                // إعداد الأعمدة
                                Map<String, Object> firstRow = preview.get(0);
                                for (String columnName : firstRow.keySet()) {
                                    previewTableModel.addColumn(columnName);
                                }

                                // إضافة البيانات
                                for (Map<String, Object> row : preview) {
                                    Object[] rowData = new Object[firstRow.size()];
                                    int i = 0;
                                    for (Object value : row.values()) {
                                        rowData[i++] = value;
                                    }
                                    previewTableModel.addRow(rowData);
                                }

                                connectionStatusArea
                                        .append("تم تحميل " + preview.size() + " سجل للمعاينة.\n");
                            } else {
                                connectionStatusArea
                                        .append("الجدول فارغ أو لا يحتوي على بيانات.\n");
                            }

                        } catch (Exception e) {
                            connectionStatusArea
                                    .append("خطأ في معاينة البيانات: " + e.getMessage() + "\n");
                        }
                    }
                };

        worker.execute();
    }

    /**
     * معالج تغيير الجدول المحدد
     */
    private void onTableSelectionChanged() {
        String selectedTable = (String) tablesCombo.getSelectedItem();
        if (selectedTable == null || selectedTable.startsWith("--")) {
            recordCountLabel.setText("عدد السجلات: غير محدد");
            tableStructureArea.setText("");
            mappingTableModel.setRowCount(0);
            return;
        }

        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                // الحصول على عدد السجلات
                int recordCount = importer.getRecordCount(selectedTable, null);
                publish("عدد السجلات: " + recordCount);

                // الحصول على هيكل الجدول
                java.util.List<OracleItemImporter.ColumnInfo> columns =
                        importer.getTableStructure(selectedTable);
                StringBuilder structure = new StringBuilder();
                structure.append("هيكل الجدول ").append(selectedTable).append(":\n");
                structure.append("=".repeat(50)).append("\n");

                for (OracleItemImporter.ColumnInfo column : columns) {
                    structure.append(column.toString()).append("\n");
                }

                publish("STRUCTURE:" + structure.toString());

                // تحديث جدول التطابق
                publish("MAPPING_UPDATE");
                mappingTableModel.setRowCount(0);
                for (OracleItemImporter.ColumnInfo column : columns) {
                    Object[] row = {column.getName(), column.getDataType()
                            + (column.getLength() > 0 ? "(" + column.getLength() + ")" : ""), "", // حقل
                                                                                                  // النظام
                                                                                                  // -
                                                                                                  // فارغ
                                                                                                  // في
                                                                                                  // البداية
                            column.isNullable() ? "لا" : "نعم"};
                    mappingTableModel.addRow(row);
                }

                return null;
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String message : chunks) {
                    if (message.startsWith("عدد السجلات:")) {
                        recordCountLabel.setText(message);
                    } else if (message.startsWith("STRUCTURE:")) {
                        tableStructureArea.setText(message.substring(10));
                    } else if (!message.equals("MAPPING_UPDATE")) {
                        connectionStatusArea.append(message + "\n");
                    }
                }
            }
        };

        worker.execute();
    }

    /**
     * فحص المكتبات المطلوبة
     */
    private void checkRequiredLibraries() {
        SwingUtilities.invokeLater(() -> {
            // فحص مسار classpath أولاً
            String classpath = System.getProperty("java.class.path");
            connectionStatusArea.append("🔍 فحص مسار المكتبات...\n");

            // فحص وجود مجلد lib
            java.io.File libDir = new java.io.File("lib");
            if (!libDir.exists()) {
                connectionStatusArea.append("❌ مجلد lib غير موجود!\n");
                connectionStatusArea
                        .append("المسار الحالي: " + System.getProperty("user.dir") + "\n");
                connectionStatusArea.append("يرجى تشغيل LibraryDownloader أولاً\n\n");
                showLibraryMissingDialog();
                return;
            }

            // فحص وجود ملف ojdbc11.jar
            java.io.File oracleJar = new java.io.File("lib/ojdbc11.jar");
            if (!oracleJar.exists()) {
                connectionStatusArea.append("❌ ملف ojdbc11.jar غير موجود في مجلد lib!\n");
                connectionStatusArea.append("يرجى تشغيل LibraryDownloader أولاً\n\n");
                showLibraryMissingDialog();
                return;
            }

            connectionStatusArea.append("✅ ملف ojdbc11.jar موجود ("
                    + String.format("%.1f MB", oracleJar.length() / (1024.0 * 1024.0)) + ")\n");

            // فحص وجود ملف orai18n.jar (مطلوب للأحرف العربية)
            java.io.File orai18nJar = new java.io.File("lib/orai18n.jar");
            if (!orai18nJar.exists()) {
                connectionStatusArea
                        .append("❌ ملف orai18n.jar غير موجود (مطلوب للأحرف العربية)!\n");
                connectionStatusArea.append("يرجى تشغيل LibraryDownloader أولاً\n\n");
                showLibraryMissingDialog();
                return;
            }

            connectionStatusArea.append("✅ ملف orai18n.jar موجود ("
                    + String.format("%.1f MB", orai18nJar.length() / (1024.0 * 1024.0)) + ")\n");

            // فحص تحميل المكتبات بدلاً من classpath
            try {
                // محاولة تحميل مكتبة Oracle JDBC
                Class.forName("oracle.jdbc.driver.OracleDriver");
                connectionStatusArea.append("✅ مكتبة Oracle JDBC محملة بنجاح\n");

                // فحص وجود orai18n.jar في classpath
                if (classpath.contains("orai18n.jar")) {
                    connectionStatusArea
                            .append("✅ مكتبة Oracle Internationalization محملة بنجاح\n");
                } else {
                    connectionStatusArea
                            .append("⚠️ تحذير: مكتبة orai18n.jar غير موجودة في classpath\n");
                    connectionStatusArea.append("قد تواجه مشاكل مع الأحرف العربية (ORA-17056)\n");
                }

                // اختبار إنشاء اتصال وهمي للتأكد
                testOracleDriverLoading();

            } catch (ClassNotFoundException e) {
                connectionStatusArea.append("❌ فشل في تحميل مكتبة Oracle JDBC!\n");
                connectionStatusArea.append("السبب: " + e.getMessage() + "\n");
                connectionStatusArea.append("الحل: إعادة تشغيل النظام مع المكتبات\n");
                connectionStatusArea.append("استخدم: java -cp \"lib/*;.\" CompleteSystemTest\n\n");

                showLibraryMissingDialog();
                return;
            }

            // فحص المكتبات الأخرى
            checkOtherLibraries();
        });
    }

    /**
     * فحص المكتبات الأخرى
     */
    private void checkOtherLibraries() {
        String[] libraries = {"org.apache.commons.dbcp2.BasicDataSource",
                "org.apache.commons.pool2.ObjectPool", "org.json.JSONObject"};

        String[] libraryNames = {"Apache Commons DBCP2", "Apache Commons Pool2", "JSON Library"};

        for (int i = 0; i < libraries.length; i++) {
            try {
                Class.forName(libraries[i]);
                connectionStatusArea.append("✅ " + libraryNames[i] + " موجودة\n");
            } catch (ClassNotFoundException e) {
                connectionStatusArea.append("⚠️ " + libraryNames[i] + " مفقودة (اختيارية)\n");
            }
        }

        connectionStatusArea.append("\n");
    }

    /**
     * إعادة فحص المكتبات
     */
    public void recheckLibraries() {
        connectionStatusArea.setText("");
        testConnectionButton.setEnabled(true);
        connectButton.setEnabled(true);
        checkRequiredLibraries();
    }

    /**
     * إصلاح إعدادات اللغة والمنطقة لتجنب مشاكل regex
     */
    private void fixLocaleSettings() {
        // تعيين اللغة الإنجليزية للنظام لتجنب مشاكل regex
        System.setProperty("user.language", "en");
        System.setProperty("user.country", "US");
        System.setProperty("user.region", "US");

        // تعيين إعدادات Oracle
        System.setProperty("oracle.jdbc.autoCommitSpecCompliant", "false");
        System.setProperty("oracle.net.disableOob", "true");

        // تعيين Locale الافتراضي
        java.util.Locale.setDefault(java.util.Locale.US);
    }

    /**
     * اختبار استعلام بسيط
     */
    private void testSimpleQuery() throws Exception {
        try (java.sql.Connection conn = dbConfig.createConnection();
                java.sql.Statement stmt = conn.createStatement();
                java.sql.ResultSet rs = stmt.executeQuery("SELECT SYSDATE FROM DUAL")) {

            if (rs.next()) {
                // الاستعلام نجح
            }
        }
    }

    /**
     * اختبار تحميل تعريف Oracle
     */
    private void testOracleDriverLoading() {
        try {
            // محاولة إنشاء instance من OracleDriver
            oracle.jdbc.driver.OracleDriver driver = new oracle.jdbc.driver.OracleDriver();
            connectionStatusArea.append("✅ تم إنشاء instance من OracleDriver بنجاح\n");
            connectionStatusArea.append("إصدار التعريف: " + driver.getMajorVersion() + "."
                    + driver.getMinorVersion() + "\n");
        } catch (Exception e) {
            connectionStatusArea.append("⚠️ تحذير: " + e.getMessage() + "\n");
        }
    }

    /**
     * عرض رسالة المكتبات المفقودة
     */
    private void showLibraryMissingDialog() {
        // تعطيل أزرار الاتصال
        testConnectionButton.setEnabled(false);
        connectButton.setEnabled(false);

        // عرض رسالة تحذيرية مفصلة
        String message = "❌ مكتبات Oracle مفقودة أو غير محملة!\n\n" + "🔧 الحلول المقترحة:\n\n"
                + "1️⃣ تشغيل LibraryDownloader (يحمل جميع المكتبات المطلوبة):\n"
                + "   java LibraryDownloader\n\n" + "2️⃣ إعادة تشغيل النظام مع المكتبات:\n"
                + "   java -cp \"lib/*;.\" CompleteSystemTest\n\n"
                + "3️⃣ استخدام ملف التشغيل المحسن:\n" + "   .\\run_oracle_fixed.bat\n\n"
                + "4️⃣ التأكد من وجود الملفات المطلوبة:\n"
                + "   - lib/ojdbc11.jar (Oracle JDBC Driver)\n"
                + "   - lib/orai18n.jar (دعم الأحرف العربية)\n\n"
                + "💡 ملاحظة: مكتبة orai18n.jar مطلوبة لحل خطأ:\n"
                + "   ORA-17056: مجموعة أحرف غير مدعومة AR8MSWIN1256\n\n"
                + "🔄 يجب إعادة تشغيل النظام بالكامل بعد تحميل المكتبات";

        JOptionPane.showMessageDialog(this, message, "مكتبات Oracle JDBC مفقودة",
                JOptionPane.ERROR_MESSAGE);
    }



    /**
     * استيراد البيانات من جداول IAS_ITM_MST و IAS_ITM_DTL
     */
    private void importFromIASTables() {
        if (!isConnected || importer == null) {
            JOptionPane.showMessageDialog(this, "يجب الاتصال بقاعدة البيانات أولاً!", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return;
        }

        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                publish("🔍 فحص وجود الجداول المطلوبة...");

                try {
                    // فحص وجود الجداول
                    if (!importer.checkIASTablesExist()) {
                        publish("❌ الجداول المطلوبة غير موجودة!");
                        publish("يجب أن تكون الجداول التالية موجودة:");
                        publish("- IAS_ITM_MST (جدول الأصناف الرئيسي)");
                        publish("- IAS_ITM_DTL (جدول تفاصيل الأصناف)");
                        return null;
                    }

                    publish("✅ تم العثور على الجداول المطلوبة");

                    // عرض إحصائيات الجداول
                    publish("📊 إحصائيات الجداول:");
                    String stats = importer.getIASTablesStatistics();
                    String[] lines = stats.split("\n");
                    for (String line : lines) {
                        if (!line.trim().isEmpty()) {
                            publish(line);
                        }
                    }

                    publish("📥 جاري استيراد البيانات من IAS_ITM_MST و IAS_ITM_DTL...");

                    // استيراد البيانات
                    List<OracleItemImporter.ImportedItem> items =
                            importer.importFromIASItemTables();

                    publish("✅ تم استيراد " + items.size() + " صنف بنجاح!");

                    // تحديث الجدول في واجهة المستخدم
                    SwingUtilities.invokeLater(() -> {
                        updateItemTableWithIASData(items);
                    });

                    publish("🎉 تم الانتهاء من عملية الاستيراد بنجاح!");

                } catch (Exception e) {
                    publish("❌ خطأ في الاستيراد: " + e.getMessage());
                    e.printStackTrace();
                }

                return null;
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String message : chunks) {
                    connectionStatusArea.append(message + "\n");
                    connectionStatusArea
                            .setCaretPosition(connectionStatusArea.getDocument().getLength());
                }
            }

            @Override
            protected void done() {
                connectionStatusArea.append("انتهت عملية الاستيراد.\n");
            }
        };

        worker.execute();
    }

    /**
     * تحديث جدول الأصناف ببيانات IAS
     */
    private void updateItemTableWithIASData(List<OracleItemImporter.ImportedItem> items) {
        // تحديث جدول المعاينة
        previewTableModel.setRowCount(0);

        // إعداد أعمدة الجدول
        String[] columnNames = {"كود الصنف", "اسم الصنف", "الوصف", "الفئة", "الوحدة", "سعر التكلفة",
                "سعر البيع", "الكمية", "الحد الأدنى", "نشط", "تاريخ الإنشاء"};
        previewTableModel.setColumnIdentifiers(columnNames);

        // إضافة البيانات الجديدة
        for (OracleItemImporter.ImportedItem item : items) {
            Object[] row = {item.getField("ITM_CODE"), item.getField("ITM_NAME"),
                    item.getField("ITM_DESC"), item.getField("CAT_ID"), item.getField("UNIT_ID"),
                    item.getField("COST_PRICE"), item.getField("SELL_PRICE"),
                    item.getField("STOCK_QTY"), item.getField("MIN_STOCK"),
                    item.getField("IS_ACTIVE"), item.getField("CREATED_DATE")};
            previewTableModel.addRow(row);
        }

        // تحديث عدد السجلات
        recordCountLabel.setText("عدد السجلات: " + items.size());
        connectionStatusArea.append("تم عرض " + items.size() + " صنف في الجدول.\n");
    }

    /**
     * فتح نافذة تحليل قاعدة البيانات
     */
    private void openDatabaseAnalysis() {
        if (dbConfig == null || !isConnected) {
            JOptionPane.showMessageDialog(this, "يجب الاتصال بقاعدة البيانات أولاً!", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return;
        }

        try {
            // فتح نافذة تحليل قاعدة البيانات
            DatabaseAnalysisWindow analysisWindow = new DatabaseAnalysisWindow();
            analysisWindow.setVisible(true);

            connectionStatusArea.append("🔍 تم فتح نافذة تحليل قاعدة البيانات\n");
            connectionStatusArea.append("📋 استخدم الأزرار لتحليل الجداول والعلاقات\n");

        } catch (Exception e) {
            connectionStatusArea.append("❌ خطأ في فتح نافذة التحليل: " + e.getMessage() + "\n");
            JOptionPane.showMessageDialog(this, "خطأ في فتح نافذة التحليل:\n" + e.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }
}
