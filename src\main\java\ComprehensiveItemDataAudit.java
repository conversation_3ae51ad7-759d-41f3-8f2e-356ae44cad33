import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.regex.Pattern;

/**
 * فحص شامل ودقيق للتطبيق للبحث عن أي بيانات تتعلق ببيانات الأصناف
 * Comprehensive audit for item data references
 */
public class ComprehensiveItemDataAudit {
    
    private static final String[] SEARCH_TERMS = {
        "بيانات الأصناف",
        "بيانات الأصناف التفصيلية", 
        "ItemManagementWindow",
        "ItemDetailWindow",
        "openItemManagementWindow",
        "openItemDetailWindow"
    };
    
    private static final String[] FILE_EXTENSIONS = {
        ".java", ".class", ".md", ".txt", ".bat", ".sql", ".properties"
    };
    
    private static List<String> foundReferences = new ArrayList<>();
    private static List<String> suspiciousFiles = new ArrayList<>();
    
    public static void main(String[] args) {
        System.out.println("🔍 فحص شامل للتطبيق - البحث عن بيانات الأصناف");
        System.out.println("=================================================");
        System.out.println();
        
        try {
            // فحص المجلد الرئيسي
            Path rootPath = Paths.get(".");
            scanDirectory(rootPath);
            
            // فحص مجلد src
            Path srcPath = Paths.get("src");
            if (Files.exists(srcPath)) {
                scanDirectory(srcPath);
            }
            
            // تقرير النتائج
            generateReport();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ في الفحص: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * فحص مجلد
     */
    private static void scanDirectory(Path directory) throws IOException {
        if (!Files.exists(directory) || !Files.isDirectory(directory)) {
            return;
        }
        
        System.out.println("📁 فحص المجلد: " + directory.toAbsolutePath());
        
        Files.walk(directory)
            .filter(Files::isRegularFile)
            .filter(ComprehensiveItemDataAudit::isRelevantFile)
            .forEach(ComprehensiveItemDataAudit::scanFile);
    }
    
    /**
     * التحقق من أن الملف ذو صلة
     */
    private static boolean isRelevantFile(Path file) {
        String fileName = file.getFileName().toString().toLowerCase();
        
        // تجاهل ملفات معينة
        if (fileName.contains("audit") || fileName.contains("test") || 
            fileName.contains("temp") || fileName.contains("backup")) {
            return false;
        }
        
        // فحص الامتدادات المهمة
        for (String ext : FILE_EXTENSIONS) {
            if (fileName.endsWith(ext.toLowerCase())) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * فحص ملف
     */
    private static void scanFile(Path file) {
        try {
            String content = Files.readString(file);
            String fileName = file.toString();
            
            boolean foundInFile = false;
            
            for (String term : SEARCH_TERMS) {
                if (content.contains(term)) {
                    foundReferences.add("📄 " + fileName + " → " + term);
                    foundInFile = true;
                }
            }
            
            if (foundInFile) {
                suspiciousFiles.add(fileName);
            }
            
        } catch (Exception e) {
            // تجاهل أخطاء القراءة للملفات الثنائية
        }
    }
    
    /**
     * إنشاء تقرير النتائج
     */
    private static void generateReport() {
        System.out.println("\n📊 تقرير الفحص الشامل");
        System.out.println("====================");
        
        if (foundReferences.isEmpty()) {
            System.out.println("✅ ممتاز! لم يتم العثور على أي مراجع لبيانات الأصناف");
            System.out.println("✅ التطبيق نظيف تماماً من نوافذ بيانات الأصناف");
        } else {
            System.out.println("⚠️ تم العثور على " + foundReferences.size() + " مرجع:");
            System.out.println();
            
            for (String ref : foundReferences) {
                System.out.println(ref);
            }
            
            System.out.println("\n🗂️ الملفات المشبوهة (" + suspiciousFiles.size() + "):");
            for (String file : suspiciousFiles) {
                System.out.println("  - " + file);
            }
        }
        
        System.out.println("\n📋 ملخص الفحص:");
        System.out.println("  - المصطلحات المبحوث عنها: " + SEARCH_TERMS.length);
        System.out.println("  - المراجع الموجودة: " + foundReferences.size());
        System.out.println("  - الملفات المشبوهة: " + suspiciousFiles.size());
        
        // فحص ملفات .class
        checkClassFiles();
        
        // فحص TreeMenuPanel
        checkTreeMenuPanel();
        
        // توصيات
        generateRecommendations();
    }
    
    /**
     * فحص ملفات .class
     */
    private static void checkClassFiles() {
        System.out.println("\n🔍 فحص ملفات .class:");
        
        try {
            Path javaDir = Paths.get("src/main/java");
            if (Files.exists(javaDir)) {
                boolean foundClassFiles = Files.walk(javaDir)
                    .filter(Files::isRegularFile)
                    .filter(p -> p.toString().toLowerCase().contains("itemmanagement") || 
                                p.toString().toLowerCase().contains("itemdetail"))
                    .filter(p -> p.toString().endsWith(".class"))
                    .peek(p -> System.out.println("  ⚠️ ملف .class مشبوه: " + p))
                    .count() > 0;
                
                if (!foundClassFiles) {
                    System.out.println("  ✅ لا توجد ملفات .class مشبوهة");
                }
            }
        } catch (Exception e) {
            System.out.println("  ❌ خطأ في فحص ملفات .class: " + e.getMessage());
        }
    }
    
    /**
     * فحص TreeMenuPanel
     */
    private static void checkTreeMenuPanel() {
        System.out.println("\n🌳 فحص TreeMenuPanel.java:");
        
        try {
            Path treeMenuFile = Paths.get("src/main/java/TreeMenuPanel.java");
            if (Files.exists(treeMenuFile)) {
                String content = Files.readString(treeMenuFile);
                
                boolean hasItemManagement = content.contains("بيانات الأصناف");
                boolean hasItemDetail = content.contains("بيانات الأصناف التفصيلية");
                boolean hasOpenItemManagement = content.contains("openItemManagementWindow");
                boolean hasOpenItemDetail = content.contains("openItemDetailWindow");
                
                if (!hasItemManagement && !hasItemDetail && !hasOpenItemManagement && !hasOpenItemDetail) {
                    System.out.println("  ✅ TreeMenuPanel.java نظيف من مراجع بيانات الأصناف");
                } else {
                    System.out.println("  ⚠️ TreeMenuPanel.java يحتوي على مراجع:");
                    if (hasItemManagement) System.out.println("    - بيانات الأصناف");
                    if (hasItemDetail) System.out.println("    - بيانات الأصناف التفصيلية");
                    if (hasOpenItemManagement) System.out.println("    - openItemManagementWindow");
                    if (hasOpenItemDetail) System.out.println("    - openItemDetailWindow");
                }
            } else {
                System.out.println("  ❌ TreeMenuPanel.java غير موجود");
            }
        } catch (Exception e) {
            System.out.println("  ❌ خطأ في فحص TreeMenuPanel: " + e.getMessage());
        }
    }
    
    /**
     * إنشاء التوصيات
     */
    private static void generateRecommendations() {
        System.out.println("\n💡 التوصيات:");
        
        if (foundReferences.isEmpty()) {
            System.out.println("  ✅ التطبيق نظيف! لا حاجة لإجراءات إضافية");
            System.out.println("  ✅ يمكن تشغيل التطبيق بأمان");
        } else {
            System.out.println("  🔧 يُنصح بالإجراءات التالية:");
            System.out.println("    1. حذف الملفات المشبوهة المذكورة أعلاه");
            System.out.println("    2. إعادة تجميع TreeMenuPanel.java");
            System.out.println("    3. حذف جميع ملفات .class القديمة");
            System.out.println("    4. إعادة تشغيل التطبيق للتأكد");
        }
        
        System.out.println("\n🎯 للتأكد النهائي:");
        System.out.println("  java TestTreeMenuAfterDeletion");
    }
}
