# 📊 تطبيق بنية جداول IAS_ITM_MST و IAS_ITM_DTL
## IAS Tables Structure Implementation in Comprehensive Item Data Window

---

## 🎯 **الهدف المحقق:**

تم فهم وتطبيق بنية الجداول المستخدمة في بيانات الأصناف الشاملة بشكل كامل:

### **📋 الجداول المطبقة:**

#### **1. IAS_ITM_MST (الجدول الرئيسي):**
- **الوصف:** الجدول الرئيسي لبيانات الأصناف
- **عدد الأعمدة:** 229 عمود
- **المفتاح الأساسي:** `I_CODE` (كود الصنف)
- **البيانات:** 4647 صنف (4630 نشط)

#### **2. IAS_ITM_DTL (الجدول التفصيلي):**
- **الوصف:** الجدول التفصيلي لوحدات القياس والتفاصيل
- **عدد الأعمدة:** 32 عمود
- **المفتاح المركب:** `(I_CODE, ITM_UNT)`
- **البيانات:** 9108 تفصيل وحدة

#### **3. العلاقة بين الجدولين:**
- **نوع العلاقة:** One-to-Many (صنف واحد ← عدة وحدات)
- **مفتاح الربط:** `I_CODE`
- **الوحدة الرئيسية:** `MAIN_UNIT = 1` في IAS_ITM_DTL

---

## 🔗 **الاستعلامات المدمجة المطبقة:**

### **1. استعلام قائمة الأصناف مع الوحدة الرئيسية:**
```sql
SELECT 
    m.I_CODE,
    m.I_NAME,
    m.I_E_NAME,
    m.G_CODE,
    m.PRIMARY_COST,
    m.ITEM_TYPE,
    m.INACTIVE,
    m.SERVICE_ITM,
    m.CASH_SALE,
    d.ITM_UNT as MAIN_UNIT,
    d.P_SIZE as PACKAGE_SIZE,
    d.BARCODE
FROM IAS_ITM_MST m
LEFT JOIN IAS_ITM_DTL d ON m.I_CODE = d.I_CODE AND d.MAIN_UNIT = 1
WHERE m.INACTIVE = 0
ORDER BY m.I_CODE
```

### **2. استعلام تفاصيل الوحدات لصنف محدد:**
```sql
SELECT 
    I_CODE, ITM_UNT, P_SIZE, ITM_UNT_L_DSC, ITM_UNT_F_DSC,
    MAIN_UNIT, SALE_UNIT, PUR_UNIT, STOCK_UNIT, PRICE_UNIT,
    BARCODE, WEIGHT_UNIT, LVL_UNIT, INACTIVE
FROM IAS_ITM_DTL 
WHERE I_CODE = ?
ORDER BY ITM_UNT
```

### **3. استعلام جميع تفاصيل الوحدات:**
```sql
SELECT 
    I_CODE, ITM_UNT, P_SIZE, ITM_UNT_L_DSC, ITM_UNT_F_DSC,
    MAIN_UNIT, SALE_UNIT, PUR_UNIT, STOCK_UNIT, PRICE_UNIT,
    BARCODE, WEIGHT_UNIT, LVL_UNIT, INACTIVE
FROM IAS_ITM_DTL 
ORDER BY I_CODE, ITM_UNT
```

---

## 🏗️ **التطبيق في النافذة:**

### **1. تبويب قائمة الأصناف (محدث):**

#### **الأعمدة المعروضة (12 عمود):**
1. كود الصنف (من IAS_ITM_MST)
2. اسم الصنف (من IAS_ITM_MST)
3. الاسم الإنجليزي (من IAS_ITM_MST)
4. كود المجموعة (من IAS_ITM_MST)
5. تسعير الأصناف (من IAS_ITM_MST)
6. نوع الصنف (من IAS_ITM_MST)
7. الحالة (من IAS_ITM_MST)
8. صنف خدمي (من IAS_ITM_MST)
9. يباع نقداً (من IAS_ITM_MST)
10. **الوحدة الرئيسية (من IAS_ITM_DTL)** ← جديد
11. **حجم العبوة (من IAS_ITM_DTL)** ← جديد
12. **الباركود (من IAS_ITM_DTL)** ← جديد

### **2. تبويب تفاصيل الوحدات (جديد):**

#### **الأعمدة المعروضة (14 عمود):**
1. كود الصنف
2. وحدة القياس
3. حجم العبوة
4. وصف الوحدة المحلي
5. وصف الوحدة الأجنبي
6. الوحدة الرئيسية
7. وحدة البيع
8. وحدة الشراء
9. وحدة المخزون
10. وحدة التسعير
11. الباركود
12. وزن الوحدة
13. مستوى الوحدة
14. الحالة

#### **الوظائف المتاحة:**
- **البحث بكود الصنف:** لعرض وحدات صنف محدد
- **تحميل جميع الوحدات:** لعرض جميع الوحدات من الجدول
- **عرض العلاقات:** بين الأصناف ووحداتها

---

## 🎯 **المزايا المحققة:**

### **1. فهم شامل للبنية:**
- ✅ **تحليل كامل** لهيكل الجدولين
- ✅ **فهم العلاقات والروابط** بينهما
- ✅ **تطبيق الاستعلامات المدمجة** بشكل صحيح

### **2. عرض البيانات المترابطة:**
- ✅ **دمج البيانات** من الجدولين في عرض واحد
- ✅ **إظهار الوحدة الرئيسية** لكل صنف
- ✅ **عرض تفاصيل الوحدات** في تبويب منفصل

### **3. وظائف متقدمة:**
- ✅ **البحث في تفاصيل الوحدات** بكود الصنف
- ✅ **تحميل جميع الوحدات** من الجدول التفصيلي
- ✅ **عرض العلاقات** بين الأصناف ووحداتها

### **4. تطبيق معايير قاعدة البيانات:**
- ✅ **استخدام المفاتيح الصحيحة** للربط
- ✅ **تطبيق قيود العلاقات** بشكل صحيح
- ✅ **معالجة البيانات المفقودة** باستخدام LEFT JOIN

---

## 📊 **الإحصائيات:**

### **البيانات المعروضة:**
- **4630 صنف نشط** في قائمة الأصناف
- **9108 تفصيل وحدة** في تبويب الوحدات
- **متوسط 1.96 وحدة** لكل صنف
- **4640 وحدة رئيسية** مربوطة بالأصناف

### **الأداء:**
- **استعلامات محسنة** باستخدام الفهارس
- **تحميل تدريجي** للبيانات حسب الحاجة
- **ذاكرة محسنة** بعدم تحميل جميع البيانات مرة واحدة

---

## 🚀 **النتيجة النهائية:**

تم تطبيق **فهم شامل** لبنية جداول بيانات الأصناف مع:

1. **عرض البيانات المترابطة** من IAS_ITM_MST و IAS_ITM_DTL
2. **استخدام العلاقات الصحيحة** بين الجدولين
3. **تطبيق الاستعلامات المدمجة** بشكل احترافي
4. **عرض تفاصيل الوحدات** في تبويب منفصل
5. **فهم شامل** لهيكل قاعدة البيانات

النافذة الآن تعكس **الفهم الكامل** لبنية جداول بيانات الأصناف وتستغل **العلاقات والروابط** بينها بشكل مثالي! 🎉
