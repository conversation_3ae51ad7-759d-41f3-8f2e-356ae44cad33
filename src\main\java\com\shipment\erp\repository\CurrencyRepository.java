package com.shipment.erp.repository;

import com.shipment.erp.model.Currency;
import java.util.List;
import java.util.Optional;

/**
 * Repository للعملات
 */
public interface CurrencyRepository extends BaseRepository<Currency> {

    /**
     * البحث عن عملة بالرمز
     */
    Optional<Currency> findByCode(String code);

    /**
     * البحث عن عملة بالاسم
     */
    Optional<Currency> findByName(String name);

    /**
     * البحث عن العملة الافتراضية
     */
    Optional<Currency> findDefaultCurrency();

    /**
     * البحث عن العملات النشطة
     */
    List<Currency> findActiveCurrencies();

    /**
     * البحث عن العملات غير النشطة
     */
    List<Currency> findInactiveCurrencies();

    /**
     * البحث عن العملات بالاسم (جزئي)
     */
    List<Currency> findByNameContaining(String name);

    /**
     * التحقق من وجود عملة بالرمز
     */
    boolean existsByCode(String code);

    /**
     * التحقق من وجود عملة بالاسم
     */
    boolean existsByName(String name);

    /**
     * التحقق من وجود عملة افتراضية
     */
    boolean hasDefaultCurrency();

    /**
     * الحصول على عدد العملات النشطة
     */
    long countActiveCurrencies();

    /**
     * الحصول على عدد العملات غير النشطة
     */
    long countInactiveCurrencies();

    /**
     * تعيين عملة كافتراضية
     */
    void setAsDefault(Long currencyId);

    /**
     * إلغاء تعيين العملة الافتراضية
     */
    void unsetDefault();

    /**
     * الحصول على العملات مرتبة بالاستخدام
     */
    List<Currency> findMostUsedCurrencies(int limit);

    /**
     * البحث المتقدم في العملات
     */
    List<Currency> advancedSearch(String code, String name, Boolean isActive, Boolean isDefault);
}
