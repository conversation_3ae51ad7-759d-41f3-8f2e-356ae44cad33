import java.sql.*;

/**
 * فحص سريع للأعمدة الموجودة في الجداول
 */
public class QuickColumnCheck {
    
    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("   Quick Column Check");
        System.out.println("====================================");
        
        try {
            // الاتصال بقاعدة البيانات
            String url = "*************************************";
            String username = "ysdba2";
            String password = "ys123";
            
            Connection conn = DriverManager.getConnection(url, username, password);
            
            // فحص أعمدة IAS_ITM_MST
            System.out.println("\nColumns in IAS20251.IAS_ITM_MST:");
            System.out.println("=================================");
            checkTableColumns(conn, "IAS20251", "IAS_ITM_MST");
            
            // فحص أعمدة IAS_ITM_DTL
            System.out.println("\nColumns in IAS20251.IAS_ITM_DTL:");
            System.out.println("=================================");
            checkTableColumns(conn, "IAS20251", "IAS_ITM_DTL");
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void checkTableColumns(Connection conn, String owner, String tableName) {
        try {
            String query = """
                SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE
                FROM ALL_TAB_COLUMNS 
                WHERE OWNER = ? AND TABLE_NAME = ?
                ORDER BY COLUMN_ID
            """;
            
            PreparedStatement stmt = conn.prepareStatement(query);
            stmt.setString(1, owner);
            stmt.setString(2, tableName);
            
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                String columnName = rs.getString("COLUMN_NAME");
                String dataType = rs.getString("DATA_TYPE");
                int dataLength = rs.getInt("DATA_LENGTH");
                String nullable = rs.getString("NULLABLE");
                
                System.out.printf("%-25s %-15s (%d) %s%n", 
                    columnName, dataType, dataLength, nullable.equals("Y") ? "NULL" : "NOT NULL");
            }
            
            rs.close();
            stmt.close();
            
        } catch (SQLException e) {
            System.err.println("Error checking columns: " + e.getMessage());
        }
    }
}
