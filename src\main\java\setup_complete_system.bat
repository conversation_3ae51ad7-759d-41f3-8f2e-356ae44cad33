@echo off
chcp 65001 > nul
echo ========================================
echo    تثبيت وإعداد نظام إدارة الشحنات
echo ========================================

echo.
echo 📋 فحص المكتبات المطلوبة...

REM فحص وجود مجلد المكتبات
if not exist "lib" (
    echo ❌ مجلد المكتبات غير موجود
    echo 📁 إنشاء مجلد المكتبات...
    mkdir lib
)

REM فحص المكتبات الأساسية
set MISSING_LIBS=0

if not exist "lib\ojdbc11.jar" (
    echo ❌ مكتبة Oracle JDBC غير موجودة: ojdbc11.jar
    set MISSING_LIBS=1
)

if not exist "lib\orai18n.jar" (
    echo ❌ مكتبة Oracle i18n غير موجودة: orai18n.jar
    set MISSING_LIBS=1
)

if not exist "lib\h2-2.2.224.jar" (
    echo ❌ مكتبة H2 Database غير موجودة: h2-2.2.224.jar
    set MISSING_LIBS=1
)

if %MISSING_LIBS%==1 (
    echo.
    echo ⚠️  بعض المكتبات مفقودة. يرجى تحميلها من:
    echo    - Oracle JDBC: https://www.oracle.com/database/technologies/appdev/jdbc-downloads.html
    echo    - H2 Database: https://www.h2database.com/html/download.html
    echo.
    echo 📥 نسخ المكتبات الموجودة...
) else (
    echo ✅ جميع المكتبات الأساسية موجودة
)

echo.
echo 🔧 تجميع النظام...

REM تجميع الملفات الأساسية أولاً
echo 📝 تجميع الملفات الأساسية...
javac -encoding UTF-8 -cp "lib/*;." CommentsManager.java
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ في تجميع CommentsManager
    pause
    exit /b 1
)

javac -encoding UTF-8 -cp "lib/*;." BaseDataImporter.java
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ في تجميع BaseDataImporter
    pause
    exit /b 1
)

javac -encoding UTF-8 -cp "lib/*;." ItemsDataImporter.java
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ في تجميع ItemsDataImporter
    pause
    exit /b 1
)

javac -encoding UTF-8 -cp "lib/*;." ImportManager.java
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ في تجميع ImportManager
    pause
    exit /b 1
)

echo 📝 تجميع النوافذ...
javac -encoding UTF-8 -cp "lib/*;." ComprehensiveItemDataWindow.java
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ في تجميع ComprehensiveItemDataWindow
    pause
    exit /b 1
)

javac -encoding UTF-8 -cp "lib/*;." TreeMenuPanel.java
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ في تجميع TreeMenuPanel
    pause
    exit /b 1
)

javac -encoding UTF-8 -cp "lib/*;." EnhancedMainWindow.java
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ في تجميع EnhancedMainWindow
    pause
    exit /b 1
)

javac -encoding UTF-8 -cp "lib/*;." ShipERPMain.java
if %ERRORLEVEL% neq 0 (
    echo ❌ خطأ في تجميع ShipERPMain
    pause
    exit /b 1
)

echo ✅ تم تجميع جميع الملفات بنجاح

echo.
echo 🔍 فحص الاتصالات...

REM فحص اتصال قاعدة البيانات
echo 📡 فحص اتصال قاعدة البيانات...
java -Duser.language=en -Duser.country=US -Dfile.encoding=UTF-8 -cp "lib/*;." AutoDatabaseConnectionTest > connection_test.log 2>&1

if %ERRORLEVEL%==0 (
    echo ✅ اتصال قاعدة البيانات يعمل
) else (
    echo ⚠️  مشكلة في اتصال قاعدة البيانات - راجع connection_test.log
)

echo.
echo 🎯 إنشاء سكريبت التشغيل...

REM إنشاء سكريبت تشغيل محسن
echo @echo off > run_ship_erp.bat
echo chcp 65001 ^> nul >> run_ship_erp.bat
echo echo ========================================== >> run_ship_erp.bat
echo echo     نظام إدارة الشحنات المتقدم >> run_ship_erp.bat
echo echo ========================================== >> run_ship_erp.bat
echo echo. >> run_ship_erp.bat
echo echo 🚀 بدء تشغيل النظام... >> run_ship_erp.bat
echo java -Duser.language=ar -Duser.country=SA -Dfile.encoding=UTF-8 -cp "lib/*;." ShipERPMain >> run_ship_erp.bat
echo if %%ERRORLEVEL%% neq 0 ^( >> run_ship_erp.bat
echo     echo ❌ خطأ في تشغيل النظام >> run_ship_erp.bat
echo     pause >> run_ship_erp.bat
echo ^) >> run_ship_erp.bat

echo ✅ تم إنشاء سكريبت التشغيل: run_ship_erp.bat

echo.
echo 📊 إنشاء سكريبت التشخيص...

REM إنشاء سكريبت تشخيص شامل
echo @echo off > diagnose_ship_erp.bat
echo chcp 65001 ^> nul >> diagnose_ship_erp.bat
echo echo ========================================== >> diagnose_ship_erp.bat
echo echo     تشخيص نظام إدارة الشحنات >> diagnose_ship_erp.bat
echo echo ========================================== >> diagnose_ship_erp.bat
echo echo. >> diagnose_ship_erp.bat
echo echo 🔍 فحص المكتبات... >> diagnose_ship_erp.bat
echo dir lib\*.jar >> diagnose_ship_erp.bat
echo echo. >> diagnose_ship_erp.bat
echo echo 🔍 فحص الملفات المجمعة... >> diagnose_ship_erp.bat
echo dir *.class >> diagnose_ship_erp.bat
echo echo. >> diagnose_ship_erp.bat
echo echo 🔍 فحص اتصال قاعدة البيانات... >> diagnose_ship_erp.bat
echo java -cp "lib/*;." AutoDatabaseConnectionTest >> diagnose_ship_erp.bat
echo echo. >> diagnose_ship_erp.bat
echo echo 🔍 فحص التعليقات... >> diagnose_ship_erp.bat
echo java -cp "lib/*;." IAS20251CommentsAnalyzer2 >> diagnose_ship_erp.bat
echo pause >> diagnose_ship_erp.bat

echo ✅ تم إنشاء سكريپت التشخيص: diagnose_ship_erp.bat

echo.
echo 🎉 تم إعداد النظام بنجاح!
echo.
echo 📋 الملفات المتاحة:
echo    - run_ship_erp.bat      : تشغيل النظام الرئيسي
echo    - diagnose_ship_erp.bat : تشخيص مشاكل النظام
echo    - connection_test.log   : سجل فحص الاتصالات
echo.
echo 💡 لتشغيل النظام: run_ship_erp.bat
echo 💡 لتشخيص المشاكل: diagnose_ship_erp.bat
echo.
pause
