# Java
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IDE
## IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

## Eclipse
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders
.project
.classpath
.factorypath
.buildpath
.target

## NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/

## VS Code
.vscode/settings.json
.vscode/tasks.json
.vscode/launch.json
.vscode/extensions.json
*.code-workspace

# Operating System
## Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

## macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

## Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Application Specific
## Logs
logs/
*.log
*.log.*

## Database
*.db
*.sqlite
*.sqlite3

## Backup
backup/
*.backup
*.bak

## Reports
reports/
*.pdf
*.xlsx
*.docx

## Temporary files
temp/
tmp/
*.tmp
*.temp

## Configuration (sensitive)
application-local.properties
application-prod.properties
database-local.properties

## Security
*.key
*.pem
*.p12
*.jks
*.keystore
*.truststore

# JavaFX
*.fxbuild

# Hibernate
hibernate.properties

# Spring
spring.log

# Test
test-output/
*.tlog

# Package Files
*.7z
*.dmg
*.gz
*.iso
*.rar
*.tar
*.zip

# Virtual machine crash logs
hs_err_pid*

# Oracle
*.dmp
*.log
*.trc
*.aud

# Custom
config/local/
data/
uploads/
downloads/
