-- ====================================
-- إنشاء بنية قاعدة البيانات الكاملة لتطبيق ERP
-- Complete Database Structure for ERP Application
-- User: ship_erp
-- ====================================

-- 1. إنشاء جداول الأصناف (Items)
-- ====================================

-- جدول فئات الأصناف
CREATE TABLE ITEM_CATEGORIES (
    CAT_ID NUMBER(10) PRIMARY KEY,
    CAT_CODE VARCHAR2(20) UNIQUE NOT NULL,
    CAT_NAME VARCHAR2(100) NOT NULL,
    CAT_DESC VARCHAR2(500),
    PARENT_CAT_ID NUMBER(10),
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50),
    LAST_MODIFIED DATE DEFAULT SYSDATE,
    MODIFIED_BY VARCHAR2(50),
    CONSTRAINT FK_PARENT_CATEGORY FOREIGN KEY (PARENT_CAT_ID) REFERENCES ITEM_CATEGORIES(CAT_ID)
);

-- جدول وحدات القياس
CREATE TABLE UNITS_OF_MEASURE (
    UNIT_ID NUMBER(10) PRIMARY KEY,
    UNIT_CODE VARCHAR2(10) UNIQUE NOT NULL,
    UNIT_NAME VARCHAR2(50) NOT NULL,
    UNIT_DESC VARCHAR2(200),
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50),
    LAST_MODIFIED DATE DEFAULT SYSDATE,
    MODIFIED_BY VARCHAR2(50)
);

-- جدول الأصناف الرئيسي
CREATE TABLE ITEMS (
    ITM_ID NUMBER(10) PRIMARY KEY,
    ITM_CODE VARCHAR2(30) UNIQUE NOT NULL,
    ITM_NAME VARCHAR2(100) NOT NULL,
    ITM_DESC VARCHAR2(2000),
    CAT_ID NUMBER(10),
    UNIT_ID NUMBER(10),
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50),
    LAST_MODIFIED DATE DEFAULT SYSDATE,
    MODIFIED_BY VARCHAR2(50),
    
    -- الحقول المالية
    COST_PRICE NUMBER(15,3) DEFAULT 0,
    SELL_PRICE NUMBER(15,3) DEFAULT 0,
    MIN_STOCK NUMBER(15,3) DEFAULT 0,
    MAX_STOCK NUMBER(15,3) DEFAULT 999999,
    REORDER_LEVEL NUMBER(15,3) DEFAULT 10,
    CURRENT_STOCK NUMBER(15,3) DEFAULT 0,
    
    -- حقول إضافية
    LOCATION_CODE VARCHAR2(20),
    SUPPLIER_ID NUMBER(10),
    LAST_PURCHASE_DATE DATE,
    LAST_SALE_DATE DATE,
    
    -- حقول الاستيراد
    IMPORT_SOURCE VARCHAR2(20),
    IMPORT_DATE DATE,
    EXTERNAL_CODE VARCHAR2(50),
    
    CONSTRAINT FK_ITEM_CATEGORY FOREIGN KEY (CAT_ID) REFERENCES ITEM_CATEGORIES(CAT_ID),
    CONSTRAINT FK_ITEM_UNIT FOREIGN KEY (UNIT_ID) REFERENCES UNITS_OF_MEASURE(UNIT_ID)
);

-- 2. إنشاء جداول المستخدمين والصلاحيات
-- ====================================

-- جدول المستخدمين
CREATE TABLE USERS (
    USER_ID NUMBER(10) PRIMARY KEY,
    USERNAME VARCHAR2(50) UNIQUE NOT NULL,
    PASSWORD_HASH VARCHAR2(255) NOT NULL,
    FULL_NAME VARCHAR2(100) NOT NULL,
    EMAIL VARCHAR2(100),
    PHONE VARCHAR2(20),
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50),
    LAST_MODIFIED DATE DEFAULT SYSDATE,
    MODIFIED_BY VARCHAR2(50),
    LAST_LOGIN DATE
);

-- جدول الأدوار
CREATE TABLE ROLES (
    ROLE_ID NUMBER(10) PRIMARY KEY,
    ROLE_NAME VARCHAR2(50) UNIQUE NOT NULL,
    ROLE_DESC VARCHAR2(200),
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50)
);

-- جدول ربط المستخدمين بالأدوار
CREATE TABLE USER_ROLES (
    USER_ID NUMBER(10),
    ROLE_ID NUMBER(10),
    ASSIGNED_DATE DATE DEFAULT SYSDATE,
    ASSIGNED_BY VARCHAR2(50),
    PRIMARY KEY (USER_ID, ROLE_ID),
    CONSTRAINT FK_USER_ROLES_USER FOREIGN KEY (USER_ID) REFERENCES USERS(USER_ID),
    CONSTRAINT FK_USER_ROLES_ROLE FOREIGN KEY (ROLE_ID) REFERENCES ROLES(ROLE_ID)
);

-- 3. إنشاء جداول الموردين والعملاء
-- ====================================

-- جدول الموردين
CREATE TABLE SUPPLIERS (
    SUPPLIER_ID NUMBER(10) PRIMARY KEY,
    SUPPLIER_CODE VARCHAR2(20) UNIQUE NOT NULL,
    SUPPLIER_NAME VARCHAR2(100) NOT NULL,
    CONTACT_PERSON VARCHAR2(100),
    PHONE VARCHAR2(20),
    EMAIL VARCHAR2(100),
    ADDRESS VARCHAR2(500),
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50),
    LAST_MODIFIED DATE DEFAULT SYSDATE,
    MODIFIED_BY VARCHAR2(50)
);

-- 4. إنشاء جداول المواقع والمخازن
-- ====================================

-- جدول المواقع
CREATE TABLE LOCATIONS (
    LOCATION_ID NUMBER(10) PRIMARY KEY,
    LOCATION_CODE VARCHAR2(20) UNIQUE NOT NULL,
    LOCATION_NAME VARCHAR2(100) NOT NULL,
    LOCATION_DESC VARCHAR2(200),
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CREATED_BY VARCHAR2(50)
);

-- 5. إنشاء جداول الإعدادات
-- ====================================

-- جدول الإعدادات العامة
CREATE TABLE SYSTEM_SETTINGS (
    SETTING_ID NUMBER(10) PRIMARY KEY,
    SETTING_KEY VARCHAR2(100) UNIQUE NOT NULL,
    SETTING_VALUE VARCHAR2(1000),
    SETTING_DESC VARCHAR2(200),
    SETTING_TYPE VARCHAR2(20) DEFAULT 'STRING',
    IS_ACTIVE NUMBER(1) DEFAULT 1,
    CREATED_DATE DATE DEFAULT SYSDATE,
    LAST_MODIFIED DATE DEFAULT SYSDATE,
    MODIFIED_BY VARCHAR2(50)
);

-- 6. إنشاء جداول سجلات الاستيراد
-- ====================================

-- جدول سجلات الاستيراد
CREATE TABLE IMPORT_LOGS (
    LOG_ID NUMBER(10) PRIMARY KEY,
    IMPORT_SOURCE VARCHAR2(50) NOT NULL,
    IMPORT_TYPE VARCHAR2(50) NOT NULL,
    TOTAL_RECORDS NUMBER(10) DEFAULT 0,
    SUCCESS_RECORDS NUMBER(10) DEFAULT 0,
    FAILED_RECORDS NUMBER(10) DEFAULT 0,
    IMPORT_STATUS VARCHAR2(20) DEFAULT 'IN_PROGRESS',
    START_TIME DATE DEFAULT SYSDATE,
    END_TIME DATE,
    ERROR_MESSAGE CLOB,
    IMPORTED_BY VARCHAR2(50),
    CREATED_DATE DATE DEFAULT SYSDATE
);

-- جدول تفاصيل أخطاء الاستيراد
CREATE TABLE IMPORT_ERRORS (
    ERROR_ID NUMBER(10) PRIMARY KEY,
    LOG_ID NUMBER(10),
    RECORD_NUMBER NUMBER(10),
    ERROR_TYPE VARCHAR2(50),
    ERROR_MESSAGE VARCHAR2(1000),
    RECORD_DATA CLOB,
    CREATED_DATE DATE DEFAULT SYSDATE,
    CONSTRAINT FK_IMPORT_ERROR_LOG FOREIGN KEY (LOG_ID) REFERENCES IMPORT_LOGS(LOG_ID)
);

-- 7. إنشاء المتسلسلات (Sequences)
-- ====================================

CREATE SEQUENCE SEQ_ITEM_CATEGORIES START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_UNITS_OF_MEASURE START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_ITEMS START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_USERS START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_ROLES START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_SUPPLIERS START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_LOCATIONS START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_SYSTEM_SETTINGS START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_IMPORT_LOGS START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE SEQ_IMPORT_ERRORS START WITH 1 INCREMENT BY 1;

-- 8. إنشاء الفهارس (Indexes)
-- ====================================

CREATE INDEX IDX_ITEMS_CODE ON ITEMS(ITM_CODE);
CREATE INDEX IDX_ITEMS_NAME ON ITEMS(ITM_NAME);
CREATE INDEX IDX_ITEMS_CATEGORY ON ITEMS(CAT_ID);
CREATE INDEX IDX_ITEMS_ACTIVE ON ITEMS(IS_ACTIVE);
CREATE INDEX IDX_ITEMS_IMPORT_SOURCE ON ITEMS(IMPORT_SOURCE);
CREATE INDEX IDX_USERS_USERNAME ON USERS(USERNAME);
CREATE INDEX IDX_SUPPLIERS_CODE ON SUPPLIERS(SUPPLIER_CODE);
CREATE INDEX IDX_IMPORT_LOGS_SOURCE ON IMPORT_LOGS(IMPORT_SOURCE);
CREATE INDEX IDX_IMPORT_LOGS_STATUS ON IMPORT_LOGS(IMPORT_STATUS);

COMMIT;

-- إنشاء البيانات الأساسية سيتم في ملف منفصل
-- Basic data will be created in a separate file
