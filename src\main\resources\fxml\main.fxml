<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.shipment.erp.controller.MainController">
   
   <!-- شريط القوائم العلوي -->
   <top>
      <VBox>
         <!-- شريط القوائم -->
         <MenuBar>
            <!-- قائمة الملف -->
            <Menu text="%menu.file">
               <MenuItem onAction="#handleNewFile" text="%menu.file.new" />
               <MenuItem onAction="#handleOpenFile" text="%menu.file.open" />
               <SeparatorMenuItem />
               <MenuItem onAction="#handleSaveFile" text="%menu.file.save" />
               <MenuItem onAction="#handleSaveAsFile" text="%menu.file.save.as" />
               <SeparatorMenuItem />
               <MenuItem onAction="#handlePrint" text="%menu.file.print" />
               <SeparatorMenuItem />
               <MenuItem onAction="#handleExit" text="%menu.file.exit" />
            </Menu>
            
            <!-- قائمة التحرير -->
            <Menu text="%menu.edit">
               <MenuItem onAction="#handleUndo" text="%menu.edit.undo" />
               <MenuItem onAction="#handleRedo" text="%menu.edit.redo" />
               <SeparatorMenuItem />
               <MenuItem onAction="#handleCut" text="%menu.edit.cut" />
               <MenuItem onAction="#handleCopy" text="%menu.edit.copy" />
               <MenuItem onAction="#handlePaste" text="%menu.edit.paste" />
            </Menu>
            
            <!-- قائمة الإعدادات -->
            <Menu text="%menu.settings">
               <MenuItem onAction="#handleGeneralSettings" text="%settings.general" />
               <MenuItem onAction="#handleFiscalYear" text="%settings.fiscal.year" />
               <MenuItem onAction="#handleCurrencies" text="%settings.currencies" />
               <MenuItem onAction="#handleCompanyData" text="%settings.company" />
               <MenuItem onAction="#handleUsers" text="%settings.users" />
               <MenuItem onAction="#handlePermissions" text="%settings.permissions" />
            </Menu>
            
            <!-- قائمة المساعدة -->
            <Menu text="%menu.help">
               <MenuItem onAction="#handleUserGuide" text="%menu.help.user.guide" />
               <MenuItem onAction="#handleAbout" text="%menu.help.about" />
            </Menu>
         </MenuBar>
         
         <!-- شريط الأدوات -->
         <ToolBar>
            <Button onAction="#handleNewFile" text="%button.new">
               <graphic>
                  <ImageView fitHeight="16.0" fitWidth="16.0">
                     <image>
                        <Image url="@../images/new.png" />
                     </image>
                  </ImageView>
               </graphic>
            </Button>
            <Button onAction="#handleSaveFile" text="%button.save">
               <graphic>
                  <ImageView fitHeight="16.0" fitWidth="16.0">
                     <image>
                        <Image url="@../images/save.png" />
                     </image>
                  </ImageView>
               </graphic>
            </Button>
            <Separator orientation="VERTICAL" />
            <Button onAction="#handlePrint" text="%button.print">
               <graphic>
                  <ImageView fitHeight="16.0" fitWidth="16.0">
                     <image>
                        <Image url="@../images/print.png" />
                     </image>
                  </ImageView>
               </graphic>
            </Button>
            <Separator orientation="VERTICAL" />
            <Button onAction="#handleRefresh" text="%button.refresh">
               <graphic>
                  <ImageView fitHeight="16.0" fitWidth="16.0">
                     <image>
                        <Image url="@../images/refresh.png" />
                     </image>
                  </ImageView>
               </graphic>
            </Button>
         </ToolBar>
      </VBox>
   </top>
   
   <!-- المحتوى الرئيسي -->
   <center>
      <SplitPane dividerPositions="0.25">
         
         <!-- الشريط الجانبي - القائمة الشجرية -->
         <VBox styleClass="sidebar">
            <padding>
               <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
            </padding>
            
            <!-- عنوان القائمة -->
            <Label styleClass="sidebar-title" text="%menu.main.title">
               <font>
                  <Font size="16.0" />
               </font>
            </Label>
            
            <!-- القائمة الشجرية -->
            <TreeView fx:id="navigationTree" VBox.vgrow="ALWAYS">
               <root>
                  <TreeItem expanded="true" value="%menu.main.title">
                     <children>
                        <!-- نظام الإعدادات -->
                        <TreeItem expanded="true" value="%menu.settings">
                           <children>
                              <TreeItem value="%settings.general" />
                              <TreeItem value="%settings.fiscal.year" />
                              <TreeItem value="%settings.currencies" />
                              <TreeItem value="%settings.company" />
                              <TreeItem value="%settings.users" />
                              <TreeItem value="%settings.permissions" />
                              <TreeItem value="%settings.new.year" />
                           </children>
                        </TreeItem>
                        
                        <!-- إدارة الأصناف -->
                        <TreeItem value="%menu.items">
                           <children>
                              <TreeItem value="%items.categories" />
                              <TreeItem value="%items.list" />
                              <TreeItem value="%items.reports" />
                           </children>
                        </TreeItem>
                        
                        <!-- إدارة الموردين -->
                        <TreeItem value="%menu.suppliers">
                           <children>
                              <TreeItem value="%suppliers.list" />
                              <TreeItem value="%suppliers.reports" />
                           </children>
                        </TreeItem>
                        
                        <!-- متابعة الشحنات -->
                        <TreeItem value="%menu.shipments">
                           <children>
                              <TreeItem value="%shipments.tracking" />
                              <TreeItem value="%shipments.reports" />
                           </children>
                        </TreeItem>
                        
                        <!-- الإدخالات الجمركية -->
                        <TreeItem value="%menu.customs">
                           <children>
                              <TreeItem value="%customs.entries" />
                              <TreeItem value="%customs.reports" />
                           </children>
                        </TreeItem>
                        
                        <!-- إدارة التكاليف -->
                        <TreeItem value="%menu.costs">
                           <children>
                              <TreeItem value="%costs.management" />
                              <TreeItem value="%costs.reports" />
                           </children>
                        </TreeItem>
                        
                        <!-- التقارير -->
                        <TreeItem value="%menu.reports">
                           <children>
                              <TreeItem value="%reports.general" />
                              <TreeItem value="%reports.financial" />
                              <TreeItem value="%reports.operational" />
                           </children>
                        </TreeItem>
                     </children>
                  </TreeItem>
               </root>
            </TreeView>
         </VBox>
         
         <!-- منطقة المحتوى -->
         <VBox styleClass="content-area">
            <!-- شريط التبويبات -->
            <TabPane fx:id="contentTabPane" VBox.vgrow="ALWAYS">
               <!-- تبويب الترحيب -->
               <Tab closable="false" text="%tab.welcome">
                  <VBox alignment="CENTER" spacing="20.0">
                     <padding>
                        <Insets bottom="50.0" left="50.0" right="50.0" top="50.0" />
                     </padding>
                     
                     <ImageView fitHeight="100.0" fitWidth="100.0">
                        <image>
                           <Image url="@../images/welcome.png" />
                        </image>
                     </ImageView>
                     
                     <Label styleClass="welcome-title" text="%welcome.title">
                        <font>
                           <Font size="28.0" />
                        </font>
                     </Label>
                     
                     <Label text="%welcome.message" wrapText="true" />
                     
                     <!-- بطاقات سريعة -->
                     <HBox alignment="CENTER" spacing="20.0">
                        <VBox alignment="CENTER" spacing="10.0" styleClass="quick-card">
                           <padding>
                              <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                           </padding>
                           <ImageView fitHeight="32.0" fitWidth="32.0">
                              <image>
                                 <Image url="@../images/settings.png" />
                              </image>
                           </ImageView>
                           <Label text="%quick.settings" />
                           <Button onAction="#handleQuickSettings" text="%button.open" />
                        </VBox>
                        
                        <VBox alignment="CENTER" spacing="10.0" styleClass="quick-card">
                           <padding>
                              <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                           </padding>
                           <ImageView fitHeight="32.0" fitWidth="32.0">
                              <image>
                                 <Image url="@../images/users.png" />
                              </image>
                           </ImageView>
                           <Label text="%quick.users" />
                           <Button onAction="#handleQuickUsers" text="%button.open" />
                        </VBox>
                        
                        <VBox alignment="CENTER" spacing="10.0" styleClass="quick-card">
                           <padding>
                              <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                           </padding>
                           <ImageView fitHeight="32.0" fitWidth="32.0">
                              <image>
                                 <Image url="@../images/reports.png" />
                              </image>
                           </ImageView>
                           <Label text="%quick.reports" />
                           <Button onAction="#handleQuickReports" text="%button.open" />
                        </VBox>
                     </HBox>
                  </VBox>
               </Tab>
            </TabPane>
         </VBox>
      </SplitPane>
   </center>
   
   <!-- شريط الحالة -->
   <bottom>
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="status-bar">
         <padding>
            <Insets bottom="5.0" left="10.0" right="10.0" top="5.0" />
         </padding>
         
         <Label fx:id="statusLabel" text="%status.ready" />
         <Separator orientation="VERTICAL" />
         <Label fx:id="userLabel" text="" />
         <Region HBox.hgrow="ALWAYS" />
         <Label fx:id="dateTimeLabel" />
      </HBox>
   </bottom>
</BorderPane>
