import java.sql.*;

/**
 * إنشاء Package تنفيذ الاستيراد والإعدادات
 * Create Import Execution and Configuration Packages
 */
public class CreateImportExecutionPackage {
    
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.OracleDriver");
            System.out.println("✅ تم تحميل Oracle JDBC driver بنجاح");
            
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            conn.setAutoCommit(false);
            
            System.out.println("✅ متصل بقاعدة البيانات SHIP_ERP");
            
            // إنشاء الـ Packages
            createImportExecutionPackage(conn);
            createConfigurationPackage(conn);
            
            conn.commit();
            System.out.println("🎉 تم إنشاء Packages الاستيراد والإعدادات بنجاح!");
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * إنشاء Package تنفيذ الاستيراد
     */
    private static void createImportExecutionPackage(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء Package تنفيذ الاستيراد...");
        
        // Package Specification
        String packageSpec = """
            CREATE OR REPLACE PACKAGE PKG_IMPORT_EXECUTION AS
                -- تنفيذ عمليات الاستيراد
                
                -- تنفيذ استيراد لربط جدول محدد
                FUNCTION EXECUTE_IMPORT(
                    p_mapping_id NUMBER,
                    p_import_type VARCHAR2 DEFAULT 'MANUAL',
                    p_executed_by VARCHAR2 DEFAULT 'SYSTEM'
                ) RETURN NUMBER;
                
                -- التحقق من صحة البيانات قبل الاستيراد
                FUNCTION VALIDATE_IMPORT_DATA(
                    p_mapping_id NUMBER
                ) RETURN VARCHAR2;
                
                -- إنشاء نسخة احتياطية قبل الاستيراد
                PROCEDURE CREATE_BACKUP(
                    p_table_name VARCHAR2,
                    p_backup_name VARCHAR2
                );
                
                -- تسجيل بداية عملية الاستيراد
                FUNCTION LOG_IMPORT_START(
                    p_mapping_id NUMBER,
                    p_import_type VARCHAR2,
                    p_executed_by VARCHAR2
                ) RETURN NUMBER;
                
                -- تسجيل انتهاء عملية الاستيراد
                PROCEDURE LOG_IMPORT_END(
                    p_import_id NUMBER,
                    p_total_records NUMBER,
                    p_imported_records NUMBER,
                    p_failed_records NUMBER,
                    p_status VARCHAR2,
                    p_error_message CLOB DEFAULT NULL
                );
                
                -- الحصول على إحصائيات الاستيراد
                FUNCTION GET_IMPORT_STATISTICS(
                    p_mapping_id NUMBER,
                    p_days_back NUMBER DEFAULT 30
                ) RETURN SYS_REFCURSOR;
                
                -- إلغاء عملية استيراد جارية
                PROCEDURE CANCEL_IMPORT(p_import_id NUMBER);
                
            END PKG_IMPORT_EXECUTION;
        """;
        
        executeSQL(conn, packageSpec, "Package Specification - تنفيذ الاستيراد");
        
        // Package Body
        String packageBody = """
            CREATE OR REPLACE PACKAGE BODY PKG_IMPORT_EXECUTION AS
                
                FUNCTION EXECUTE_IMPORT(
                    p_mapping_id NUMBER,
                    p_import_type VARCHAR2 DEFAULT 'MANUAL',
                    p_executed_by VARCHAR2 DEFAULT 'SYSTEM'
                ) RETURN NUMBER IS
                    v_import_id NUMBER;
                    v_mapping_info SYS_REFCURSOR;
                    v_source_table VARCHAR2(100);
                    v_target_table VARCHAR2(100);
                    v_connection_id NUMBER;
                    v_total_records NUMBER := 0;
                    v_imported_records NUMBER := 0;
                    v_failed_records NUMBER := 0;
                    v_start_time TIMESTAMP;
                    v_end_time TIMESTAMP;
                    v_duration NUMBER;
                BEGIN
                    v_start_time := SYSTIMESTAMP;
                    
                    -- تسجيل بداية الاستيراد
                    v_import_id := LOG_IMPORT_START(p_mapping_id, p_import_type, p_executed_by);
                    
                    -- الحصول على معلومات الربط
                    SELECT SOURCE_TABLE_NAME, TARGET_TABLE_NAME, CONNECTION_ID
                    INTO v_source_table, v_target_table, v_connection_id
                    FROM ERP_TABLE_MAPPING
                    WHERE MAPPING_ID = p_mapping_id AND IS_ACTIVE = 1;
                    
                    -- محاكاة عملية الاستيراد
                    -- في التطبيق الحقيقي، سيتم هنا الاتصال بالنظام المصدر وتنفيذ الاستيراد
                    v_total_records := 100; -- مثال
                    v_imported_records := 95; -- مثال
                    v_failed_records := 5; -- مثال
                    
                    -- تحديث معلومات آخر استيراد
                    PKG_TABLE_MAPPING.UPDATE_LAST_IMPORT_INFO(p_mapping_id, v_imported_records);
                    
                    v_end_time := SYSTIMESTAMP;
                    v_duration := EXTRACT(SECOND FROM (v_end_time - v_start_time));
                    
                    -- تسجيل انتهاء الاستيراد
                    LOG_IMPORT_END(v_import_id, v_total_records, v_imported_records, 
                                  v_failed_records, 'SUCCESS', NULL);
                    
                    RETURN v_import_id;
                EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                        LOG_IMPORT_END(v_import_id, 0, 0, 0, 'FAILED', 'ربط الجدول غير موجود أو غير نشط');
                        RAISE_APPLICATION_ERROR(-20201, 'ربط الجدول غير موجود أو غير نشط');
                    WHEN OTHERS THEN
                        LOG_IMPORT_END(v_import_id, v_total_records, v_imported_records, 
                                      v_failed_records, 'FAILED', SQLERRM);
                        RAISE_APPLICATION_ERROR(-20202, 'خطأ في تنفيذ الاستيراد: ' || SQLERRM);
                END EXECUTE_IMPORT;
                
                FUNCTION VALIDATE_IMPORT_DATA(
                    p_mapping_id NUMBER
                ) RETURN VARCHAR2 IS
                    v_result VARCHAR2(4000);
                    v_mapping_count NUMBER;
                    v_field_count NUMBER;
                BEGIN
                    -- التحقق من وجود ربط الجدول
                    SELECT COUNT(*) INTO v_mapping_count
                    FROM ERP_TABLE_MAPPING
                    WHERE MAPPING_ID = p_mapping_id AND IS_ACTIVE = 1;
                    
                    IF v_mapping_count = 0 THEN
                        RETURN 'خطأ: ربط الجدول غير موجود أو غير نشط';
                    END IF;
                    
                    -- التحقق من وجود ربط الحقول
                    SELECT COUNT(*) INTO v_field_count
                    FROM ERP_FIELD_MAPPING
                    WHERE TABLE_MAPPING_ID = p_mapping_id;
                    
                    IF v_field_count = 0 THEN
                        RETURN 'تحذير: لا يوجد ربط حقول محدد';
                    END IF;
                    
                    v_result := 'تم التحقق بنجاح - جاهز للاستيراد';
                    RETURN v_result;
                EXCEPTION
                    WHEN OTHERS THEN
                        RETURN 'خطأ في التحقق: ' || SQLERRM;
                END VALIDATE_IMPORT_DATA;
                
                PROCEDURE CREATE_BACKUP(
                    p_table_name VARCHAR2,
                    p_backup_name VARCHAR2
                ) IS
                    v_sql VARCHAR2(4000);
                BEGIN
                    -- إنشاء نسخة احتياطية من الجدول
                    v_sql := 'CREATE TABLE ' || p_backup_name || ' AS SELECT * FROM ' || p_table_name;
                    EXECUTE IMMEDIATE v_sql;
                    
                    COMMIT;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20203, 'خطأ في إنشاء النسخة الاحتياطية: ' || SQLERRM);
                END CREATE_BACKUP;
                
                FUNCTION LOG_IMPORT_START(
                    p_mapping_id NUMBER,
                    p_import_type VARCHAR2,
                    p_executed_by VARCHAR2
                ) RETURN NUMBER IS
                    v_import_id NUMBER;
                BEGIN
                    INSERT INTO ERP_IMPORT_HISTORY (
                        IMPORT_ID, TABLE_MAPPING_ID, IMPORT_DATE, IMPORT_TYPE,
                        IMPORT_STATUS, IMPORTED_BY, START_TIME
                    ) VALUES (
                        SEQ_IMPORT_HISTORY.NEXTVAL, p_mapping_id, SYSDATE, p_import_type,
                        'RUNNING', p_executed_by, SYSTIMESTAMP
                    ) RETURNING IMPORT_ID INTO v_import_id;
                    
                    COMMIT;
                    RETURN v_import_id;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20204, 'خطأ في تسجيل بداية الاستيراد: ' || SQLERRM);
                END LOG_IMPORT_START;
                
                PROCEDURE LOG_IMPORT_END(
                    p_import_id NUMBER,
                    p_total_records NUMBER,
                    p_imported_records NUMBER,
                    p_failed_records NUMBER,
                    p_status VARCHAR2,
                    p_error_message CLOB DEFAULT NULL
                ) IS
                    v_duration NUMBER;
                BEGIN
                    -- حساب مدة التنفيذ
                    SELECT EXTRACT(SECOND FROM (SYSTIMESTAMP - START_TIME))
                    INTO v_duration
                    FROM ERP_IMPORT_HISTORY
                    WHERE IMPORT_ID = p_import_id;
                    
                    UPDATE ERP_IMPORT_HISTORY SET
                        TOTAL_RECORDS = p_total_records,
                        IMPORTED_RECORDS = p_imported_records,
                        FAILED_RECORDS = p_failed_records,
                        IMPORT_STATUS = p_status,
                        ERROR_MESSAGE = p_error_message,
                        END_TIME = SYSTIMESTAMP,
                        IMPORT_DURATION = v_duration
                    WHERE IMPORT_ID = p_import_id;
                    
                    COMMIT;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20205, 'خطأ في تسجيل انتهاء الاستيراد: ' || SQLERRM);
                END LOG_IMPORT_END;
                
                FUNCTION GET_IMPORT_STATISTICS(
                    p_mapping_id NUMBER,
                    p_days_back NUMBER DEFAULT 30
                ) RETURN SYS_REFCURSOR IS
                    v_cursor SYS_REFCURSOR;
                BEGIN
                    OPEN v_cursor FOR
                        SELECT IMPORT_DATE, IMPORT_TYPE, TOTAL_RECORDS, IMPORTED_RECORDS,
                               FAILED_RECORDS, IMPORT_STATUS, IMPORT_DURATION
                        FROM ERP_IMPORT_HISTORY
                        WHERE TABLE_MAPPING_ID = p_mapping_id
                          AND IMPORT_DATE >= SYSDATE - p_days_back
                        ORDER BY IMPORT_DATE DESC;
                    
                    RETURN v_cursor;
                END GET_IMPORT_STATISTICS;
                
                PROCEDURE CANCEL_IMPORT(p_import_id NUMBER) IS
                BEGIN
                    UPDATE ERP_IMPORT_HISTORY
                    SET IMPORT_STATUS = 'CANCELLED',
                        END_TIME = SYSTIMESTAMP,
                        ERROR_MESSAGE = 'تم إلغاء العملية بواسطة المستخدم'
                    WHERE IMPORT_ID = p_import_id
                      AND IMPORT_STATUS = 'RUNNING';
                    
                    IF SQL%ROWCOUNT = 0 THEN
                        RAISE_APPLICATION_ERROR(-20206, 'عملية الاستيراد غير موجودة أو منتهية');
                    END IF;
                    
                    COMMIT;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20207, 'خطأ في إلغاء الاستيراد: ' || SQLERRM);
                END CANCEL_IMPORT;
                
            END PKG_IMPORT_EXECUTION;
        """;
        
        executeSQL(conn, packageBody, "Package Body - تنفيذ الاستيراد");
    }
    
    /**
     * إنشاء Package الإعدادات
     */
    private static void createConfigurationPackage(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء Package الإعدادات...");
        
        // Package Specification
        String packageSpec = """
            CREATE OR REPLACE PACKAGE PKG_CONFIGURATION AS
                -- إدارة إعدادات النظام
                
                -- الحصول على قيمة إعداد
                FUNCTION GET_CONFIG_VALUE(p_config_name VARCHAR2) RETURN VARCHAR2;
                
                -- تحديث قيمة إعداد
                PROCEDURE SET_CONFIG_VALUE(
                    p_config_name VARCHAR2,
                    p_config_value VARCHAR2,
                    p_updated_by VARCHAR2 DEFAULT 'SYSTEM'
                );
                
                -- إضافة إعداد جديد
                FUNCTION ADD_CONFIG(
                    p_config_name VARCHAR2,
                    p_config_type VARCHAR2,
                    p_config_value VARCHAR2,
                    p_config_description VARCHAR2,
                    p_is_system_config NUMBER DEFAULT 0,
                    p_created_by VARCHAR2 DEFAULT 'SYSTEM'
                ) RETURN NUMBER;
                
                -- حذف إعداد
                PROCEDURE DELETE_CONFIG(p_config_id NUMBER);
                
                -- الحصول على جميع الإعدادات
                FUNCTION GET_ALL_CONFIGS RETURN SYS_REFCURSOR;
                
                -- الحصول على الإعدادات النشطة فقط
                FUNCTION GET_ACTIVE_CONFIGS RETURN SYS_REFCURSOR;
                
                -- تفعيل/إلغاء تفعيل إعداد
                PROCEDURE TOGGLE_CONFIG_STATUS(
                    p_config_id NUMBER,
                    p_is_active NUMBER
                );
                
            END PKG_CONFIGURATION;
        """;
        
        executeSQL(conn, packageSpec, "Package Specification - الإعدادات");
        
        // Package Body
        String packageBody = """
            CREATE OR REPLACE PACKAGE BODY PKG_CONFIGURATION AS
                
                FUNCTION GET_CONFIG_VALUE(p_config_name VARCHAR2) RETURN VARCHAR2 IS
                    v_config_value VARCHAR2(500);
                BEGIN
                    SELECT CONFIG_VALUE INTO v_config_value
                    FROM ERP_IMPORT_CONFIG
                    WHERE CONFIG_NAME = p_config_name AND IS_ACTIVE = 1;
                    
                    RETURN v_config_value;
                EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                        RETURN NULL;
                    WHEN OTHERS THEN
                        RAISE_APPLICATION_ERROR(-20301, 'خطأ في الحصول على الإعداد: ' || SQLERRM);
                END GET_CONFIG_VALUE;
                
                PROCEDURE SET_CONFIG_VALUE(
                    p_config_name VARCHAR2,
                    p_config_value VARCHAR2,
                    p_updated_by VARCHAR2 DEFAULT 'SYSTEM'
                ) IS
                BEGIN
                    UPDATE ERP_IMPORT_CONFIG
                    SET CONFIG_VALUE = p_config_value,
                        UPDATED_BY = p_updated_by,
                        UPDATED_DATE = SYSDATE
                    WHERE CONFIG_NAME = p_config_name;
                    
                    IF SQL%ROWCOUNT = 0 THEN
                        RAISE_APPLICATION_ERROR(-20302, 'الإعداد غير موجود');
                    END IF;
                    
                    COMMIT;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20303, 'خطأ في تحديث الإعداد: ' || SQLERRM);
                END SET_CONFIG_VALUE;
                
                FUNCTION ADD_CONFIG(
                    p_config_name VARCHAR2,
                    p_config_type VARCHAR2,
                    p_config_value VARCHAR2,
                    p_config_description VARCHAR2,
                    p_is_system_config NUMBER DEFAULT 0,
                    p_created_by VARCHAR2 DEFAULT 'SYSTEM'
                ) RETURN NUMBER IS
                    v_config_id NUMBER;
                    v_count NUMBER;
                BEGIN
                    -- التحقق من عدم تكرار اسم الإعداد
                    SELECT COUNT(*) INTO v_count
                    FROM ERP_IMPORT_CONFIG
                    WHERE CONFIG_NAME = p_config_name;
                    
                    IF v_count > 0 THEN
                        RAISE_APPLICATION_ERROR(-20304, 'اسم الإعداد موجود مسبقاً');
                    END IF;
                    
                    INSERT INTO ERP_IMPORT_CONFIG (
                        CONFIG_ID, CONFIG_NAME, CONFIG_TYPE, CONFIG_VALUE,
                        CONFIG_DESCRIPTION, IS_SYSTEM_CONFIG, IS_ACTIVE,
                        CREATED_BY, CREATED_DATE
                    ) VALUES (
                        SEQ_IMPORT_CONFIG.NEXTVAL, p_config_name, p_config_type,
                        p_config_value, p_config_description, p_is_system_config,
                        1, p_created_by, SYSDATE
                    ) RETURNING CONFIG_ID INTO v_config_id;
                    
                    COMMIT;
                    RETURN v_config_id;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20305, 'خطأ في إضافة الإعداد: ' || SQLERRM);
                END ADD_CONFIG;
                
                PROCEDURE DELETE_CONFIG(p_config_id NUMBER) IS
                    v_is_system NUMBER;
                BEGIN
                    -- التحقق من أن الإعداد ليس إعداد نظام
                    SELECT IS_SYSTEM_CONFIG INTO v_is_system
                    FROM ERP_IMPORT_CONFIG
                    WHERE CONFIG_ID = p_config_id;
                    
                    IF v_is_system = 1 THEN
                        RAISE_APPLICATION_ERROR(-20306, 'لا يمكن حذف إعدادات النظام');
                    END IF;
                    
                    DELETE FROM ERP_IMPORT_CONFIG
                    WHERE CONFIG_ID = p_config_id;
                    
                    IF SQL%ROWCOUNT = 0 THEN
                        RAISE_APPLICATION_ERROR(-20307, 'الإعداد غير موجود');
                    END IF;
                    
                    COMMIT;
                EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                        RAISE_APPLICATION_ERROR(-20308, 'الإعداد غير موجود');
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20309, 'خطأ في حذف الإعداد: ' || SQLERRM);
                END DELETE_CONFIG;
                
                FUNCTION GET_ALL_CONFIGS RETURN SYS_REFCURSOR IS
                    v_cursor SYS_REFCURSOR;
                BEGIN
                    OPEN v_cursor FOR
                        SELECT CONFIG_ID, CONFIG_NAME, CONFIG_TYPE, CONFIG_VALUE,
                               CONFIG_DESCRIPTION, IS_SYSTEM_CONFIG, IS_ACTIVE,
                               CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE
                        FROM ERP_IMPORT_CONFIG
                        ORDER BY CONFIG_NAME;
                    
                    RETURN v_cursor;
                END GET_ALL_CONFIGS;
                
                FUNCTION GET_ACTIVE_CONFIGS RETURN SYS_REFCURSOR IS
                    v_cursor SYS_REFCURSOR;
                BEGIN
                    OPEN v_cursor FOR
                        SELECT CONFIG_ID, CONFIG_NAME, CONFIG_TYPE, CONFIG_VALUE,
                               CONFIG_DESCRIPTION, IS_SYSTEM_CONFIG
                        FROM ERP_IMPORT_CONFIG
                        WHERE IS_ACTIVE = 1
                        ORDER BY CONFIG_NAME;
                    
                    RETURN v_cursor;
                END GET_ACTIVE_CONFIGS;
                
                PROCEDURE TOGGLE_CONFIG_STATUS(
                    p_config_id NUMBER,
                    p_is_active NUMBER
                ) IS
                BEGIN
                    UPDATE ERP_IMPORT_CONFIG
                    SET IS_ACTIVE = p_is_active,
                        UPDATED_DATE = SYSDATE
                    WHERE CONFIG_ID = p_config_id;
                    
                    IF SQL%ROWCOUNT = 0 THEN
                        RAISE_APPLICATION_ERROR(-20310, 'الإعداد غير موجود');
                    END IF;
                    
                    COMMIT;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20311, 'خطأ في تغيير حالة الإعداد: ' || SQLERRM);
                END TOGGLE_CONFIG_STATUS;
                
            END PKG_CONFIGURATION;
        """;
        
        executeSQL(conn, packageBody, "Package Body - الإعدادات");
    }
    
    /**
     * تنفيذ SQL مع معالجة الأخطاء
     */
    private static void executeSQL(Connection conn, String sql, String description) throws SQLException {
        try {
            Statement stmt = conn.createStatement();
            stmt.execute(sql);
            System.out.println("✅ تم إنشاء " + description);
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) { // ORA-00955: name is already used
                System.out.println("⚠️ " + description + " موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إنشاء " + description + ": " + e.getMessage());
                throw e;
            }
        }
    }
}
