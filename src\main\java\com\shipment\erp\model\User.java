package com.shipment.erp.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * كيان المستخدم
 * يمثل مستخدمي النظام
 */
@Entity
@Table(name = "USERS", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"USERNAME"})
})
@SequenceGenerator(name = "user_seq", sequenceName = "SEQ_USER", allocationSize = 1)
public class User extends BaseEntity {

    @Column(name = "USERNAME", nullable = false, unique = true, length = 50)
    @NotBlank(message = "اسم المستخدم مطلوب")
    @Size(min = 3, max = 50, message = "اسم المستخدم يجب أن يكون بين 3 و 50 حرف")
    private String username;

    @Column(name = "PASSWORD_HASH", nullable = false, length = 255)
    @NotBlank(message = "كلمة المرور مطلوبة")
    private String passwordHash;

    @Column(name = "FULL_NAME", nullable = false, length = 200)
    @NotBlank(message = "الاسم الكامل مطلوب")
    @Size(max = 200, message = "الاسم الكامل يجب أن يكون أقل من 200 حرف")
    private String fullName;

    @Column(name = "EMAIL", length = 100)
    @Email(message = "البريد الإلكتروني غير صحيح")
    @Size(max = 100, message = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")
    private String email;

    @Column(name = "PHONE", length = 50)
    @Size(max = 50, message = "رقم الهاتف يجب أن يكون أقل من 50 حرف")
    private String phone;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "ROLE_ID")
    private Role role;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BRANCH_ID")
    private Branch branch;

    @Column(name = "IS_ACTIVE", nullable = false)
    private Boolean isActive = true;

    @Column(name = "LAST_LOGIN")
    private LocalDateTime lastLogin;

    @Column(name = "LOGIN_ATTEMPTS", nullable = false)
    private Integer loginAttempts = 0;

    @Column(name = "LOCKED_UNTIL")
    private LocalDateTime lockedUntil;

    @Column(name = "PASSWORD_CHANGED_DATE")
    private LocalDateTime passwordChangedDate;

    /**
     * Constructor افتراضي
     */
    public User() {
        super();
        this.passwordChangedDate = LocalDateTime.now();
    }

    /**
     * Constructor مع اسم المستخدم والاسم الكامل
     */
    public User(String username, String fullName) {
        this();
        this.username = username;
        this.fullName = fullName;
    }

    /**
     * Constructor مع اسم المستخدم وكلمة المرور والاسم الكامل
     */
    public User(String username, String passwordHash, String fullName) {
        this(username, fullName);
        this.passwordHash = passwordHash;
    }

    // Getters and Setters

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPasswordHash() {
        return passwordHash;
    }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
        this.passwordChangedDate = LocalDateTime.now();
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Role getRole() {
        return role;
    }

    public void setRole(Role role) {
        this.role = role;
    }

    public Branch getBranch() {
        return branch;
    }

    public void setBranch(Branch branch) {
        this.branch = branch;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public LocalDateTime getLastLogin() {
        return lastLogin;
    }

    public void setLastLogin(LocalDateTime lastLogin) {
        this.lastLogin = lastLogin;
    }

    public Integer getLoginAttempts() {
        return loginAttempts;
    }

    public void setLoginAttempts(Integer loginAttempts) {
        this.loginAttempts = loginAttempts;
    }

    public LocalDateTime getLockedUntil() {
        return lockedUntil;
    }

    public void setLockedUntil(LocalDateTime lockedUntil) {
        this.lockedUntil = lockedUntil;
    }

    public LocalDateTime getPasswordChangedDate() {
        return passwordChangedDate;
    }

    public void setPasswordChangedDate(LocalDateTime passwordChangedDate) {
        this.passwordChangedDate = passwordChangedDate;
    }

    /**
     * التحقق من قفل الحساب
     */
    public boolean isLocked() {
        return lockedUntil != null && lockedUntil.isAfter(LocalDateTime.now());
    }

    /**
     * قفل الحساب لفترة معينة (بالدقائق)
     */
    public void lockAccount(int minutes) {
        this.lockedUntil = LocalDateTime.now().plusMinutes(minutes);
    }

    /**
     * إلغاء قفل الحساب
     */
    public void unlockAccount() {
        this.lockedUntil = null;
        this.loginAttempts = 0;
    }

    /**
     * زيادة عدد محاولات تسجيل الدخول الفاشلة
     */
    public void incrementLoginAttempts() {
        this.loginAttempts++;
    }

    /**
     * إعادة تعيين محاولات تسجيل الدخول
     */
    public void resetLoginAttempts() {
        this.loginAttempts = 0;
    }

    /**
     * تسجيل دخول ناجح
     */
    public void recordSuccessfulLogin() {
        this.lastLogin = LocalDateTime.now();
        resetLoginAttempts();
        unlockAccount();
    }

    /**
     * التحقق من صلاحية معينة
     */
    public boolean hasPermission(String moduleName, String permissionType) {
        return role != null && role.hasPermission(moduleName, permissionType);
    }

    /**
     * التحقق من صلاحية القراءة
     */
    public boolean canRead(String moduleName) {
        return hasPermission(moduleName, "READ");
    }

    /**
     * التحقق من صلاحية الكتابة
     */
    public boolean canWrite(String moduleName) {
        return hasPermission(moduleName, "WRITE");
    }

    /**
     * التحقق من صلاحية الحذف
     */
    public boolean canDelete(String moduleName) {
        return hasPermission(moduleName, "DELETE");
    }

    /**
     * التحقق من صلاحية الطباعة
     */
    public boolean canPrint(String moduleName) {
        return hasPermission(moduleName, "PRINT");
    }

    /**
     * التحقق من صلاحية التصدير
     */
    public boolean canExport(String moduleName) {
        return hasPermission(moduleName, "EXPORT");
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + getId() +
                ", username='" + username + '\'' +
                ", fullName='" + fullName + '\'' +
                ", email='" + email + '\'' +
                ", isActive=" + isActive +
                ", lastLogin=" + lastLogin +
                '}';
    }
}
