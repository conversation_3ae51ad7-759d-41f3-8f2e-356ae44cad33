@echo off
title Quick Problem Diagnosis

echo ====================================
echo    Quick Problem Diagnosis
echo    Oracle JDBC and IAS Tables
echo ====================================
echo.

echo [1/4] Checking Oracle libraries...
echo.

if exist "lib\ojdbc11.jar" (
    for %%A in ("lib\ojdbc11.jar") do (
        echo [OK] ojdbc11.jar found - Size: %%~zA bytes
    )
) else (
    echo [ERROR] ojdbc11.jar missing!
    echo Solution: java LibraryDownloader
    set MISSING_LIBS=1
)

if exist "lib\orai18n.jar" (
    for %%A in ("lib\orai18n.jar") do (
        echo [OK] orai18n.jar found - Size: %%~zA bytes
    )
) else (
    echo [ERROR] orai18n.jar missing!
    echo Solution: java LibraryDownloader
    echo Note: Required to fix ORA-17056 error
    set MISSING_LIBS=1
)

echo.

echo [2/4] اختبار تحميل المكتبات...
if defined MISSING_LIBS (
    echo ⚠️ تخطي الاختبار - مكتبات مفقودة
) else (
    java -cp "lib/*;." QuickLibraryTest
)

echo.

echo [3/4] اختبار الاتصال بـ Oracle...
if defined MISSING_LIBS (
    echo ⚠️ تخطي الاختبار - مكتبات مفقودة
) else (
    java -Duser.language=en -Duser.country=US -cp "lib/*;." OracleConnectionFixer
)

echo.

echo [4/4] اختبار الجداول المحددة...
if defined MISSING_LIBS (
    echo ⚠️ تخطي الاختبار - مكتبات مفقودة
) else (
    echo فحص جداول IAS_ITM_MST و IAS_ITM_DTL...
    java -Duser.language=en -Duser.country=US -cp "lib/*;." IASTablesTest
)

echo.
echo ====================================
echo    ملخص التشخيص
echo ====================================

if defined MISSING_LIBS (
    echo ❌ مشكلة: مكتبات Oracle مفقودة
    echo.
    echo الحلول المقترحة:
    echo 1. تشغيل LibraryDownloader:
    echo    java LibraryDownloader
    echo.
    echo 2. إعادة تشغيل النظام مع المكتبات:
    echo    java -cp "lib/*;." CompleteSystemTest
    echo.
    echo 3. استخدام ملف التشغيل المحسن:
    echo    START_ERP_WITH_ORACLE.bat
    echo.
    echo 4. التأكد من وجود الملفات المطلوبة:
    echo    - lib/ojdbc11.jar (Oracle JDBC Driver)
    echo    - lib/orai18n.jar (دعم الأحرف العربية)
    echo.
    echo ملاحظة: مكتبة orai18n.jar مطلوبة لحل خطأ:
    echo ORA-17056: مجموعة أحرف غير مدعومة AR8MSWIN1256
    echo.
    echo يجب إعادة تشغيل النظام بالكامل بعد تحميل المكتبات
) else (
    echo ✅ جميع المكتبات موجودة ومحملة
    echo ✅ النظام جاهز للاستخدام
    echo.
    echo للتشغيل:
    echo START_ERP_WITH_ORACLE.bat
    echo.
    echo للاستيراد من الجداول المحددة:
    echo 1. شغّل النظام
    echo 2. اذهب إلى: إدارة الأصناف → ربط النظام واستيراد البيانات
    echo 3. اضغط زر "📥 استيراد IAS"
    echo 4. ستحصل على استيراد 4630 صنف من IAS_ITM_MST و IAS_ITM_DTL
)

echo.
echo ====================================
echo    انتهى التشخيص
echo ====================================

pause
