/* ملف CSS لدعم اللغة العربية و RTL */

/* إعدادات عامة للعربية */
.root {
    -fx-font-family: "Noto Sans Arabic", "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 14px;
    -fx-node-orientation: right-to-left;
    -fx-text-alignment: right;
}

/* إعدادات النصوص */
.label {
    -fx-text-alignment: right;
    -fx-alignment: center-right;
    -fx-node-orientation: right-to-left;
}

.text-field {
    -fx-alignment: center-right;
    -fx-node-orientation: right-to-left;
    -fx-text-alignment: right;
}

.text-area {
    -fx-node-orientation: right-to-left;
    -fx-text-alignment: right;
}

.combo-box {
    -fx-node-orientation: right-to-left;
    -fx-alignment: center-right;
}

.choice-box {
    -fx-node-orientation: right-to-left;
    -fx-alignment: center-right;
}

/* إعدادات الجداول */
.table-view {
    -fx-node-orientation: right-to-left;
}

.table-column {
    -fx-alignment: center-right;
}

.table-cell {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

/* إعدادات القوائم */
.list-view {
    -fx-node-orientation: right-to-left;
}

.list-cell {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.tree-view {
    -fx-node-orientation: right-to-left;
}

.tree-cell {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

/* إعدادات الأزرار */
.button {
    -fx-alignment: center;
    -fx-text-alignment: center;
    -fx-font-weight: bold;
}

.menu-bar {
    -fx-node-orientation: right-to-left;
}

.menu {
    -fx-node-orientation: right-to-left;
}

.menu-item {
    -fx-node-orientation: right-to-left;
    -fx-alignment: center-right;
}

/* إعدادات التبويبات */
.tab-pane {
    -fx-node-orientation: right-to-left;
}

.tab {
    -fx-alignment: center;
}

.tab-header-area {
    -fx-node-orientation: right-to-left;
}

/* إعدادات النوافذ المنبثقة */
.dialog-pane {
    -fx-node-orientation: right-to-left;
}

.alert {
    -fx-node-orientation: right-to-left;
}

/* إعدادات شريط التقدم */
.progress-bar {
    -fx-node-orientation: left-to-right; /* يبقى من اليسار لليمين */
}

.progress-indicator {
    -fx-node-orientation: left-to-right;
}

/* إعدادات التواريخ */
.date-picker {
    -fx-node-orientation: right-to-left;
    -fx-alignment: center-right;
}

/* إعدادات الألوان والثيم */
.main-window {
    -fx-background-color: #f5f5f5;
}

.header {
    -fx-background-color: #2c3e50;
    -fx-text-fill: white;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-padding: 10px;
}

.sidebar {
    -fx-background-color: #34495e;
    -fx-text-fill: white;
}

.content-area {
    -fx-background-color: white;
    -fx-padding: 20px;
}

.form-container {
    -fx-spacing: 10px;
    -fx-padding: 20px;
    -fx-background-color: white;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1px;
    -fx-border-radius: 5px;
}

.form-label {
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.required-field {
    -fx-border-color: #e74c3c;
    -fx-border-width: 2px;
}

.success-message {
    -fx-text-fill: #27ae60;
    -fx-font-weight: bold;
}

.error-message {
    -fx-text-fill: #e74c3c;
    -fx-font-weight: bold;
}

.warning-message {
    -fx-text-fill: #f39c12;
    -fx-font-weight: bold;
}

/* إعدادات الأيقونات */
.icon {
    -fx-node-orientation: left-to-right; /* الأيقونات تبقى كما هي */
}

/* إعدادات خاصة للأرقام */
.number-field {
    -fx-node-orientation: left-to-right;
    -fx-alignment: center-left;
    -fx-text-alignment: left;
}

/* إعدادات خاصة للعملات */
.currency-field {
    -fx-node-orientation: left-to-right;
    -fx-alignment: center-left;
    -fx-text-alignment: left;
}

/* إعدادات الطباعة */
@media print {
    .root {
        -fx-font-size: 12px;
    }
    
    .no-print {
        -fx-opacity: 0;
    }
}
