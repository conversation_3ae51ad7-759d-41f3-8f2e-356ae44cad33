import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Font;
import java.awt.GridLayout;
import java.awt.event.ActionListener;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.Date;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Locale;
import java.util.Properties;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.SwingUtilities;
import javax.swing.SwingWorker;

/**
 * أداة التحليل الفوري لقاعدة البيانات IAS20251 Instant Database Analyzer for IAS20251
 */
public class InstantDatabaseAnalyzer extends JFrame {

    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 12);

    private JTextArea resultArea;
    private JProgressBar progressBar;
    private Connection connection;

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new InstantDatabaseAnalyzer().setVisible(true);
        });
    }

    public InstantDatabaseAnalyzer() {
        // إصلاح regex فوراً
        fixRegexIssue();

        initializeComponents();
        connectToDatabase();
    }

    /**
     * إصلاح مشكلة regex فوراً
     */
    private void fixRegexIssue() {
        try {
            Locale.setDefault(Locale.ENGLISH);
            System.setProperty("user.language", "en");
            System.setProperty("user.country", "US");
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("oracle.jdbc.autoCommitSpecCompliant", "false");
            System.setProperty("oracle.jdbc.timezoneAsRegion", "false");
        } catch (Exception e) {
            // تجاهل الأخطاء
        }
    }

    private void initializeComponents() {
        setTitle("🔍 أداة التحليل الفوري لقاعدة البيانات IAS20251");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // لوحة الأزرار
        JPanel buttonPanel = createButtonPanel();
        mainPanel.add(buttonPanel, BorderLayout.NORTH);

        // منطقة النتائج
        resultArea = new JTextArea();
        resultArea.setFont(arabicFont);
        resultArea.setEditable(false);
        resultArea.setBackground(Color.BLACK);
        resultArea.setForeground(Color.GREEN);

        JScrollPane scrollPane = new JScrollPane(resultArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        mainPanel.add(scrollPane, BorderLayout.CENTER);

        // شريط التقدم
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setFont(arabicFont);
        mainPanel.add(progressBar, BorderLayout.SOUTH);

        add(mainPanel);
    }

    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 4, 10, 10));
        panel.setBorder(BorderFactory.createTitledBorder("أدوات التحليل الفوري"));

        JButton allTablesBtn = createButton("📋 جميع الجداول", e -> analyzeAllTables());
        JButton importantTablesBtn =
                createButton("🎯 الجداول المهمة", e -> analyzeImportantTables());
        JButton relationshipsBtn = createButton("🔗 العلاقات", e -> analyzeRelationships());
        JButton statisticsBtn = createButton("📊 الإحصائيات", e -> analyzeStatistics());

        JButton itemsBtn = createButton("🏷️ تحليل الأصناف", e -> analyzeItems());
        JButton structureBtn = createButton("🏗️ بنية الجداول", e -> analyzeStructure());
        JButton reconnectBtn = createButton("🔄 إعادة الاتصال", e -> reconnect());
        JButton clearBtn = createButton("🗑️ مسح", e -> resultArea.setText(""));

        panel.add(allTablesBtn);
        panel.add(importantTablesBtn);
        panel.add(relationshipsBtn);
        panel.add(statisticsBtn);
        panel.add(itemsBtn);
        panel.add(structureBtn);
        panel.add(reconnectBtn);
        panel.add(clearBtn);

        return panel;
    }

    private JButton createButton(String text, ActionListener action) {
        JButton button = new JButton(text);
        button.setFont(arabicBoldFont);
        button.addActionListener(action);
        return button;
    }

    private void connectToDatabase() {
        appendResult("🔄 بدء الاتصال الفوري بقاعدة البيانات IAS20251...\n");
        appendResult("=" + "=".repeat(60) + "\n");

        SwingWorker<Boolean, String> worker = new SwingWorker<Boolean, String>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                try {
                    // إعدادات الاتصال
                    String url = "*************************************";
                    String username = "ias20251";
                    String password = "ys123";

                    publish("📋 إعدادات الاتصال:");
                    publish("   URL: " + url);
                    publish("   المستخدم: " + username);
                    publish("   كلمة المرور: " + password);
                    publish("");

                    // تحميل تعريف Oracle
                    Class.forName("oracle.jdbc.driver.OracleDriver");
                    publish("✅ تم تحميل تعريف Oracle JDBC");

                    // إعداد خصائص الاتصال
                    Properties props = new Properties();
                    props.setProperty("user", username);
                    props.setProperty("password", password);
                    props.setProperty("oracle.jdbc.ReadTimeout", "30000");
                    props.setProperty("oracle.net.CONNECT_TIMEOUT", "30000");

                    publish("🔄 جاري الاتصال...");
                    connection = DriverManager.getConnection(url, props);

                    if (connection != null && !connection.isClosed()) {
                        publish("✅ نجح الاتصال!");

                        // اختبار استعلام بسيط
                        try (Statement stmt = connection.createStatement();
                                ResultSet rs = stmt.executeQuery("SELECT 1 FROM DUAL")) {

                            if (rs.next()) {
                                publish("✅ استعلام DUAL نجح");
                            }
                        }

                        // معلومات قاعدة البيانات
                        DatabaseMetaData metaData = connection.getMetaData();
                        publish("📋 معلومات قاعدة البيانات:");
                        publish("   المنتج: " + metaData.getDatabaseProductName());
                        publish("   الإصدار: " + metaData.getDatabaseProductVersion());
                        publish("   المستخدم: " + metaData.getUserName());
                        publish("");

                        // اختبار الوصول للجداول
                        testTableAccess();

                        return true;
                    } else {
                        publish("❌ فشل في إنشاء الاتصال");
                        return false;
                    }

                } catch (Exception e) {
                    publish("❌ خطأ في الاتصال: " + e.getMessage());
                    e.printStackTrace();
                    return false;
                }
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String message : chunks) {
                    appendResult(message + "\n");
                }
            }

            @Override
            protected void done() {
                try {
                    boolean success = get();
                    if (success) {
                        appendResult("🎉 الاتصال جاهز! استخدم الأزرار لبدء التحليل\n\n");
                    } else {
                        appendResult("❌ فشل الاتصال - تحقق من إعدادات قاعدة البيانات\n\n");
                    }
                } catch (Exception e) {
                    appendResult("❌ خطأ: " + e.getMessage() + "\n\n");
                }
            }
        };

        worker.execute();
    }

    private void testTableAccess() throws SQLException {
        // اختبار الوصول لجدول IAS_ITM_MST
        String query = "SELECT COUNT(*) as total FROM IAS20251.IAS_ITM_MST";

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                int total = rs.getInt("total");
                appendResult("✅ تم الوصول لجدول IAS_ITM_MST بنجاح\n");
                appendResult("📊 عدد الأصناف: " + String.format("%,d", total) + "\n");
            }

        } catch (SQLException e) {
            appendResult("⚠️ تحذير في الوصول لجدول IAS_ITM_MST: " + e.getMessage() + "\n");
        }
    }

    private void analyzeAllTables() {
        if (connection == null) {
            appendResult("❌ لا يوجد اتصال بقاعدة البيانات\n");
            return;
        }

        appendResult("📋 تحليل جميع الجداول في IAS20251...\n");
        appendResult("=" + "=".repeat(50) + "\n");

        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                String query = """
                            SELECT table_name, num_rows, blocks, avg_row_len
                            FROM all_tables
                            WHERE owner = 'IAS20251'
                            ORDER BY table_name
                        """;

                try (PreparedStatement stmt = connection.prepareStatement(query);
                        ResultSet rs = stmt.executeQuery()) {

                    publish(String.format("%-30s %15s %10s %15s", "اسم الجدول", "عدد الصفوف",
                            "الكتل", "متوسط الطول"));
                    publish("-".repeat(80));

                    int count = 0;
                    while (rs.next()) {
                        String tableName = rs.getString("table_name");
                        String numRows = rs.getString("num_rows");
                        String blocks = rs.getString("blocks");
                        String avgRowLen = rs.getString("avg_row_len");

                        publish(String.format("%-30s %15s %10s %15s", tableName,
                                numRows != null ? String.format("%,d", Integer.parseInt(numRows))
                                        : "غير محدد",
                                blocks != null ? blocks : "-",
                                avgRowLen != null ? avgRowLen : "-"));

                        count++;
                    }

                    publish("");
                    publish("📊 إجمالي الجداول: " + count);
                }

                return null;
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String chunk : chunks) {
                    appendResult(chunk + "\n");
                }
            }

            @Override
            protected void done() {
                appendResult("✅ تم إكمال تحليل جميع الجداول!\n\n");
            }
        };

        worker.execute();
    }

    private void analyzeImportantTables() {
        if (connection == null) {
            appendResult("❌ لا يوجد اتصال بقاعدة البيانات\n");
            return;
        }

        appendResult("🎯 تحليل الجداول المهمة...\n");
        appendResult("=" + "=".repeat(30) + "\n");

        String[] importantTables = {"IAS_ITM_MST", "IAS_ITM_DTL", "IAS_CUSTOMER", "IAS_VENDOR",
                "IAS_INVOICE", "IAS_RECEIPT", "IAS_PAYMENT", "IAS_STOCK"};

        for (String tableName : importantTables) {
            try {
                String query =
                        String.format("SELECT COUNT(*) as total FROM IAS20251.%s", tableName);
                try (PreparedStatement stmt = connection.prepareStatement(query);
                        ResultSet rs = stmt.executeQuery()) {

                    if (rs.next()) {
                        int total = rs.getInt("total");
                        appendResult(String.format("%-20s: %,d صف\n", tableName, total));
                    }
                }
            } catch (SQLException e) {
                appendResult(String.format("%-20s: غير متاح (%s)\n", tableName, e.getMessage()));
            }
        }

        appendResult("\n✅ تم إكمال تحليل الجداول المهمة!\n\n");
    }

    private void analyzeRelationships() {
        if (connection == null) {
            appendResult("❌ لا يوجد اتصال بقاعدة البيانات\n");
            return;
        }

        appendResult("🔗 تحليل العلاقات والمفاتيح الخارجية...\n");
        appendResult("=" + "=".repeat(50) + "\n");

        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                // تحليل المفاتيح الخارجية
                String query =
                        """
                                    SELECT
                                        a.constraint_name,
                                        a.table_name,
                                        a.column_name,
                                        c_pk.table_name r_table_name,
                                        c_pk.column_name r_column_name
                                    FROM all_cons_columns a
                                    JOIN all_constraints c ON a.owner = c.owner AND a.constraint_name = c.constraint_name
                                    JOIN all_constraints c_pk ON c.r_owner = c_pk.owner AND c.r_constraint_name = c_pk.constraint_name
                                    JOIN all_cons_columns c_pk ON c_pk.owner = c_pk.owner AND c_pk.constraint_name = c_pk.constraint_name
                                    WHERE c.constraint_type = 'R'
                                    AND a.owner = 'IAS20251'
                                    ORDER BY a.table_name, a.constraint_name
                                """;

                try (PreparedStatement stmt = connection.prepareStatement(query);
                        ResultSet rs = stmt.executeQuery()) {

                    publish(String.format("%-25s %-15s -> %-25s %-15s", "الجدول", "العمود",
                            "يشير إلى جدول", "يشير إلى عمود"));
                    publish("-".repeat(90));

                    int count = 0;
                    while (rs.next()) {
                        publish(String.format("%-25s %-15s -> %-25s %-15s",
                                rs.getString("table_name"), rs.getString("column_name"),
                                rs.getString("r_table_name"), rs.getString("r_column_name")));
                        count++;
                    }

                    publish("");
                    publish("📊 إجمالي المفاتيح الخارجية: " + count);

                    if (count == 0) {
                        publish("ℹ️ لا توجد مفاتيح خارجية مُعرَّفة في schema IAS20251");
                    }
                }

                // تحليل المفاتيح الأساسية
                publish("");
                publish("🔑 تحليل المفاتيح الأساسية:");
                publish("-".repeat(40));

                String pkQuery =
                        """
                                    SELECT
                                        c.table_name,
                                        c.constraint_name,
                                        cc.column_name,
                                        cc.position
                                    FROM all_constraints c
                                    JOIN all_cons_columns cc ON c.owner = cc.owner AND c.constraint_name = cc.constraint_name
                                    WHERE c.constraint_type = 'P'
                                    AND c.owner = 'IAS20251'
                                    ORDER BY c.table_name, cc.position
                                """;

                try (PreparedStatement stmt = connection.prepareStatement(pkQuery);
                        ResultSet rs = stmt.executeQuery()) {

                    String currentTable = "";
                    int pkCount = 0;

                    while (rs.next()) {
                        String tableName = rs.getString("table_name");
                        String columnName = rs.getString("column_name");
                        int position = rs.getInt("position");

                        if (!tableName.equals(currentTable)) {
                            if (!currentTable.isEmpty()) {
                                publish("");
                            }
                            publish("📋 جدول: " + tableName);
                            currentTable = tableName;
                            pkCount++;
                        }

                        publish("   🔑 " + columnName + " (موضع " + position + ")");
                    }

                    publish("");
                    publish("📊 إجمالي الجداول مع مفاتيح أساسية: " + pkCount);
                }

                return null;
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String chunk : chunks) {
                    appendResult(chunk + "\n");
                }
            }

            @Override
            protected void done() {
                appendResult("✅ تم إكمال تحليل العلاقات!\n\n");
            }
        };

        worker.execute();
    }

    private void analyzeStatistics() {
        if (connection == null) {
            appendResult("❌ لا يوجد اتصال بقاعدة البيانات\n");
            return;
        }

        appendResult("📊 تحليل الإحصائيات المتقدم...\n");
        appendResult("=" + "=".repeat(50) + "\n");

        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                // إحصائيات عامة لقاعدة البيانات
                publish("🏢 إحصائيات عامة لقاعدة البيانات:");
                publish("-".repeat(40));

                // عدد الجداول الكلي
                String tablesQuery =
                        "SELECT COUNT(*) as table_count FROM all_tables WHERE owner = 'IAS20251'";
                try (PreparedStatement stmt = connection.prepareStatement(tablesQuery);
                        ResultSet rs = stmt.executeQuery()) {

                    if (rs.next()) {
                        int tableCount = rs.getInt("table_count");
                        publish("📋 إجمالي الجداول: " + tableCount);
                    }
                }

                // إحصائيات الجداول الرئيسية
                publish("");
                publish("📊 إحصائيات الجداول الرئيسية:");
                publish("-".repeat(40));

                String[] mainTables = {"IAS_ITM_MST", "IAS_ITM_DTL", "IAS_CUSTOMER", "IAS_VENDOR",
                        "IAS_INVOICE", "IAS_RECEIPT", "IAS_PAYMENT", "IAS_STOCK"};

                long totalRows = 0;
                int existingTables = 0;

                for (String tableName : mainTables) {
                    try {
                        String query = String.format("SELECT COUNT(*) as total FROM IAS20251.%s",
                                tableName);
                        try (PreparedStatement stmt = connection.prepareStatement(query);
                                ResultSet rs = stmt.executeQuery()) {

                            if (rs.next()) {
                                int total = rs.getInt("total");
                                totalRows += total;
                                existingTables++;

                                String status = total > 0 ? "✅" : "⚪";
                                publish(String.format("%s %-20s: %,d صف", status, tableName,
                                        total));
                            }
                        }
                    } catch (SQLException e) {
                        publish(String.format("❌ %-20s: غير متاح", tableName));
                    }
                }

                publish("");
                publish("📈 ملخص الإحصائيات:");
                publish("   الجداول الموجودة: " + existingTables + "/" + mainTables.length);
                publish("   إجمالي الصفوف: " + String.format("%,d", totalRows));

                // تحليل توزيع البيانات
                if (totalRows > 0) {
                    publish("");
                    publish("📊 توزيع البيانات:");
                    publish("-".repeat(30));

                    for (String tableName : mainTables) {
                        try {
                            String query = String
                                    .format("SELECT COUNT(*) as total FROM IAS20251.%s", tableName);
                            try (PreparedStatement stmt = connection.prepareStatement(query);
                                    ResultSet rs = stmt.executeQuery()) {

                                if (rs.next()) {
                                    int total = rs.getInt("total");
                                    if (total > 0) {
                                        double percentage = (double) total / totalRows * 100;
                                        String bar = "█".repeat((int) (percentage / 5)); // كل 5% =
                                                                                         // █
                                        publish(String.format("%-15s %6.1f%% %s",
                                                tableName.replace("IAS_", ""), percentage, bar));
                                    }
                                }
                            }
                        } catch (SQLException e) {
                            // تجاهل الأخطاء
                        }
                    }
                }

                // تحليل خاص بجدول الأصناف
                analyzeItemsStatistics();

                return null;
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String chunk : chunks) {
                    appendResult(chunk + "\n");
                }
            }

            @Override
            protected void done() {
                appendResult("✅ تم إكمال تحليل الإحصائيات!\n\n");
            }
        };

        worker.execute();
    }

    private void analyzeItemsStatistics() throws SQLException {
        appendResult("");
        appendResult("🏷️ إحصائيات مفصلة للأصناف:");
        appendResult("-".repeat(35));

        try {
            // إحصائيات أساسية
            String query =
                    """
                                SELECT
                                    COUNT(*) as total_items,
                                    COUNT(CASE WHEN INACTIVE = 0 THEN 1 END) as active_items,
                                    COUNT(CASE WHEN INACTIVE = 1 THEN 1 END) as inactive_items,
                                    COUNT(CASE WHEN I_NAME IS NOT NULL AND LENGTH(TRIM(I_NAME)) > 0 THEN 1 END) as named_items,
                                    COUNT(CASE WHEN I_DESC IS NOT NULL AND LENGTH(TRIM(I_DESC)) > 0 THEN 1 END) as described_items,
                                    MIN(AD_DATE) as oldest_date,
                                    MAX(AD_DATE) as newest_date
                                FROM IAS20251.IAS_ITM_MST
                            """;

            try (PreparedStatement stmt = connection.prepareStatement(query);
                    ResultSet rs = stmt.executeQuery()) {

                if (rs.next()) {
                    int total = rs.getInt("total_items");
                    int active = rs.getInt("active_items");
                    int inactive = rs.getInt("inactive_items");
                    int named = rs.getInt("named_items");
                    int described = rs.getInt("described_items");

                    appendResult("📊 الإحصائيات الأساسية:");
                    appendResult("   إجمالي الأصناف: " + String.format("%,d", total));
                    appendResult("   الأصناف النشطة: " + String.format("%,d", active)
                            + String.format(" (%.1f%%)", (double) active / total * 100));
                    appendResult("   الأصناف غير النشطة: " + String.format("%,d", inactive)
                            + String.format(" (%.1f%%)", (double) inactive / total * 100));
                    appendResult("   أصناف بأسماء: " + String.format("%,d", named)
                            + String.format(" (%.1f%%)", (double) named / total * 100));
                    appendResult("   أصناف بوصف: " + String.format("%,d", described)
                            + String.format(" (%.1f%%)", (double) described / total * 100));

                    Date oldest = rs.getDate("oldest_date");
                    Date newest = rs.getDate("newest_date");
                    if (oldest != null)
                        appendResult("   أقدم صنف: " + oldest);
                    if (newest != null)
                        appendResult("   أحدث صنف: " + newest);
                }
            }

            // إحصائيات المجموعات
            appendResult("");
            appendResult("📂 إحصائيات المجموعات:");
            String groupQuery = """
                        SELECT
                            G_CODE,
                            COUNT(*) as item_count,
                            COUNT(CASE WHEN INACTIVE = 0 THEN 1 END) as active_count
                        FROM IAS20251.IAS_ITM_MST
                        GROUP BY G_CODE
                        ORDER BY item_count DESC
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(groupQuery);
                    ResultSet rs = stmt.executeQuery()) {

                int groupCount = 0;
                while (rs.next() && groupCount < 10) { // أول 10 مجموعات
                    String groupCode = rs.getString("G_CODE");
                    int itemCount = rs.getInt("item_count");
                    int activeCount = rs.getInt("active_count");

                    appendResult(String.format("   %s: %,d صنف (%,d نشط)",
                            groupCode != null ? groupCode : "غير محدد", itemCount, activeCount));
                    groupCount++;
                }

                if (groupCount == 10) {
                    appendResult("   ... (والمزيد)");
                }
            }

        } catch (SQLException e) {
            appendResult("❌ خطأ في تحليل إحصائيات الأصناف: " + e.getMessage());
        }
    }

    private void analyzeItems() {
        if (connection == null) {
            appendResult("❌ لا يوجد اتصال بقاعدة البيانات\n");
            return;
        }

        appendResult("🏷️ تحليل مفصل للأصناف...\n");
        appendResult("=" + "=".repeat(30) + "\n");

        try {
            // إحصائيات الأصناف
            String query = """
                        SELECT
                            COUNT(*) as total_items,
                            COUNT(CASE WHEN INACTIVE = 0 THEN 1 END) as active_items,
                            COUNT(CASE WHEN INACTIVE = 1 THEN 1 END) as inactive_items,
                            MIN(AD_DATE) as oldest_item,
                            MAX(AD_DATE) as newest_item
                        FROM IAS20251.IAS_ITM_MST
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(query);
                    ResultSet rs = stmt.executeQuery()) {

                if (rs.next()) {
                    appendResult("📊 إحصائيات الأصناف:\n");
                    appendResult("   إجمالي الأصناف: "
                            + String.format("%,d", rs.getInt("total_items")) + "\n");
                    appendResult("   الأصناف النشطة: "
                            + String.format("%,d", rs.getInt("active_items")) + "\n");
                    appendResult("   الأصناف غير النشطة: "
                            + String.format("%,d", rs.getInt("inactive_items")) + "\n");

                    Date oldest = rs.getDate("oldest_item");
                    Date newest = rs.getDate("newest_item");
                    if (oldest != null)
                        appendResult("   أقدم صنف: " + oldest + "\n");
                    if (newest != null)
                        appendResult("   أحدث صنف: " + newest + "\n");
                }
            }

            appendResult("\n✅ تم إكمال تحليل الأصناف!\n\n");

        } catch (SQLException e) {
            appendResult("❌ خطأ في تحليل الأصناف: " + e.getMessage() + "\n\n");
        }
    }

    private void analyzeStructure() {
        if (connection == null) {
            appendResult("❌ لا يوجد اتصال بقاعدة البيانات\n");
            return;
        }

        appendResult("🏗️ تحليل بنية الجداول الرئيسية...\n");
        appendResult("=" + "=".repeat(50) + "\n");

        String[] mainTables = {"IAS_ITM_MST", "IAS_ITM_DTL", "IAS_CUSTOMER", "IAS_VENDOR"};

        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                for (String tableName : mainTables) {
                    analyzeTableStructure(tableName);
                }
                return null;
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String chunk : chunks) {
                    appendResult(chunk + "\n");
                }
            }

            @Override
            protected void done() {
                appendResult("✅ تم إكمال تحليل بنية الجداول!\n\n");
            }
        };

        worker.execute();
    }

    private void analyzeTableStructure(String tableName) throws SQLException {
        appendResult("📋 تحليل بنية جدول: " + tableName + "\n");
        appendResult("-".repeat(60) + "\n");

        // فحص وجود الجدول أولاً
        String checkQuery =
                "SELECT COUNT(*) as count FROM all_tables WHERE owner = 'IAS20251' AND table_name = ?";

        try (PreparedStatement checkStmt = connection.prepareStatement(checkQuery)) {
            checkStmt.setString(1, tableName);
            try (ResultSet checkRs = checkStmt.executeQuery()) {
                if (checkRs.next() && checkRs.getInt("count") == 0) {
                    appendResult("❌ الجدول " + tableName + " غير موجود\n\n");
                    return;
                }
            }
        }

        // تحليل أعمدة الجدول
        String query = """
                    SELECT
                        column_name,
                        data_type,
                        data_length,
                        data_precision,
                        data_scale,
                        nullable,
                        data_default,
                        column_id
                    FROM all_tab_columns
                    WHERE owner = 'IAS20251' AND table_name = ?
                    ORDER BY column_id
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, tableName);

            try (ResultSet rs = stmt.executeQuery()) {
                appendResult(String.format("%-25s %-20s %-10s %-8s %-15s", "اسم العمود",
                        "نوع البيانات", "الطول", "NULL", "القيمة الافتراضية"));
                appendResult("-".repeat(80) + "\n");

                int columnCount = 0;
                while (rs.next()) {
                    String columnName = rs.getString("column_name");
                    String dataType = rs.getString("data_type");
                    String dataLength = rs.getString("data_length");
                    String dataPrecision = rs.getString("data_precision");
                    String dataScale = rs.getString("data_scale");
                    String nullable = rs.getString("nullable");
                    String dataDefault = rs.getString("data_default");

                    // تنسيق نوع البيانات
                    String typeInfo = dataType;
                    if (dataPrecision != null) {
                        typeInfo += "(" + dataPrecision;
                        if (dataScale != null && !dataScale.equals("0")) {
                            typeInfo += "," + dataScale;
                        }
                        typeInfo += ")";
                    } else if (dataLength != null && !dataType.equals("DATE")) {
                        typeInfo += "(" + dataLength + ")";
                    }

                    String defaultValue = dataDefault != null
                            ? (dataDefault.length() > 12 ? dataDefault.substring(0, 12) + "..."
                                    : dataDefault)
                            : "-";

                    appendResult(String.format("%-25s %-20s %-10s %-8s %-15s", columnName, typeInfo,
                            dataLength != null ? dataLength : "-",
                            "Y".equals(nullable) ? "نعم" : "لا", defaultValue));

                    columnCount++;
                }

                appendResult("\n📊 إجمالي الأعمدة: " + columnCount + "\n");

                // إحصائيات الجدول
                String statsQuery = "SELECT COUNT(*) as row_count FROM IAS20251." + tableName;
                try (PreparedStatement statsStmt = connection.prepareStatement(statsQuery);
                        ResultSet statsRs = statsStmt.executeQuery()) {

                    if (statsRs.next()) {
                        int rowCount = statsRs.getInt("row_count");
                        appendResult("📊 عدد الصفوف: " + String.format("%,d", rowCount) + "\n");
                    }
                }

                // عينة من البيانات
                appendResult("\n📄 عينة من البيانات (أول 3 صفوف):\n");
                String sampleQuery = "SELECT * FROM IAS20251." + tableName + " WHERE ROWNUM <= 3";

                try (PreparedStatement sampleStmt = connection.prepareStatement(sampleQuery);
                        ResultSet sampleRs = sampleStmt.executeQuery()) {

                    ResultSetMetaData metaData = sampleRs.getMetaData();
                    int colCount = metaData.getColumnCount();

                    // عرض أسماء الأعمدة (أول 5 فقط)
                    for (int i = 1; i <= Math.min(colCount, 5); i++) {
                        appendResult(String.format("%-15s ", metaData.getColumnName(i)));
                    }
                    if (colCount > 5)
                        appendResult("...");
                    appendResult("\n");

                    appendResult("-".repeat(Math.min(colCount * 16, 80)) + "\n");

                    // عرض البيانات
                    int rowNum = 0;
                    while (sampleRs.next() && rowNum < 3) {
                        for (int i = 1; i <= Math.min(colCount, 5); i++) {
                            Object value = sampleRs.getObject(i);
                            String displayValue = value != null ? value.toString() : "NULL";
                            if (displayValue.length() > 12) {
                                displayValue = displayValue.substring(0, 12) + "...";
                            }
                            appendResult(String.format("%-15s ", displayValue));
                        }
                        if (colCount > 5)
                            appendResult("...");
                        appendResult("\n");
                        rowNum++;
                    }

                    if (rowNum == 0) {
                        appendResult("(الجدول فارغ)\n");
                    }
                }

                appendResult("\n" + "=".repeat(60) + "\n\n");
            }
        } catch (SQLException e) {
            appendResult("❌ خطأ في تحليل جدول " + tableName + ": " + e.getMessage() + "\n\n");
        }
    }

    private void reconnect() {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                // تجاهل
            }
        }

        resultArea.setText("");
        connectToDatabase();
    }

    private void appendResult(String text) {
        SwingUtilities.invokeLater(() -> {
            resultArea.append(text);
            resultArea.setCaretPosition(resultArea.getDocument().getLength());
        });
    }

    @Override
    public void dispose() {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                // تجاهل
            }
        }
        super.dispose();
    }
}
