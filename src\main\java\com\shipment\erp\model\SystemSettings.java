package com.shipment.erp.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * كيان إعدادات النظام
 * يمثل الإعدادات العامة للنظام
 */
@Entity
@Table(name = "SYSTEM_SETTINGS", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"SETTING_KEY"})
})
@SequenceGenerator(name = "system_settings_seq", sequenceName = "SEQ_SYSTEM_SETTINGS", allocationSize = 1)
public class SystemSettings extends BaseEntity {

    @Column(name = "SETTING_KEY", nullable = false, unique = true, length = 100)
    @NotBlank(message = "مفتاح الإعداد مطلوب")
    @Size(max = 100, message = "مفتاح الإعداد يجب أن يكون أقل من 100 حرف")
    private String settingKey;

    @Column(name = "SETTING_VALUE", length = 1000)
    @Size(max = 1000, message = "قيمة الإعداد يجب أن تكون أقل من 1000 حرف")
    private String settingValue;

    @Column(name = "SETTING_TYPE", length = 20)
    @Size(max = 20, message = "نوع الإعداد يجب أن يكون أقل من 20 حرف")
    private String settingType = "STRING";

    @Column(name = "DESCRIPTION", length = 500)
    @Size(max = 500, message = "الوصف يجب أن يكون أقل من 500 حرف")
    private String description;

    @Column(name = "IS_SYSTEM", nullable = false)
    private Boolean isSystem = false;

    /**
     * أنواع الإعدادات المدعومة
     */
    public enum SettingType {
        STRING("STRING"),
        INTEGER("INTEGER"),
        DECIMAL("DECIMAL"),
        BOOLEAN("BOOLEAN"),
        DATE("DATE"),
        TIME("TIME"),
        DATETIME("DATETIME");

        private final String value;

        SettingType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static SettingType fromValue(String value) {
            for (SettingType type : values()) {
                if (type.value.equals(value)) {
                    return type;
                }
            }
            return STRING;
        }
    }

    /**
     * Constructor افتراضي
     */
    public SystemSettings() {
        super();
    }

    /**
     * Constructor مع المفتاح والقيمة
     */
    public SystemSettings(String settingKey, String settingValue) {
        this();
        this.settingKey = settingKey;
        this.settingValue = settingValue;
    }

    /**
     * Constructor مع المفتاح والقيمة والنوع
     */
    public SystemSettings(String settingKey, String settingValue, SettingType settingType) {
        this(settingKey, settingValue);
        this.settingType = settingType.getValue();
    }

    /**
     * Constructor مع المفتاح والقيمة والنوع والوصف
     */
    public SystemSettings(String settingKey, String settingValue, SettingType settingType, String description) {
        this(settingKey, settingValue, settingType);
        this.description = description;
    }

    // Getters and Setters

    public String getSettingKey() {
        return settingKey;
    }

    public void setSettingKey(String settingKey) {
        this.settingKey = settingKey;
    }

    public String getSettingValue() {
        return settingValue;
    }

    public void setSettingValue(String settingValue) {
        this.settingValue = settingValue;
    }

    public String getSettingType() {
        return settingType;
    }

    public void setSettingType(String settingType) {
        this.settingType = settingType;
    }

    public void setSettingType(SettingType settingType) {
        this.settingType = settingType.getValue();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }

    /**
     * الحصول على نوع الإعداد كـ enum
     */
    public SettingType getSettingTypeEnum() {
        return SettingType.fromValue(settingType);
    }

    /**
     * الحصول على القيمة كـ String
     */
    public String getStringValue() {
        return settingValue;
    }

    /**
     * الحصول على القيمة كـ Integer
     */
    public Integer getIntegerValue() {
        try {
            return settingValue != null ? Integer.parseInt(settingValue) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * الحصول على القيمة كـ Double
     */
    public Double getDoubleValue() {
        try {
            return settingValue != null ? Double.parseDouble(settingValue) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * الحصول على القيمة كـ Boolean
     */
    public Boolean getBooleanValue() {
        if (settingValue == null) {
            return null;
        }
        return "true".equalsIgnoreCase(settingValue) || "1".equals(settingValue);
    }

    /**
     * تعيين قيمة Integer
     */
    public void setIntegerValue(Integer value) {
        this.settingValue = value != null ? value.toString() : null;
        this.settingType = SettingType.INTEGER.getValue();
    }

    /**
     * تعيين قيمة Double
     */
    public void setDoubleValue(Double value) {
        this.settingValue = value != null ? value.toString() : null;
        this.settingType = SettingType.DECIMAL.getValue();
    }

    /**
     * تعيين قيمة Boolean
     */
    public void setBooleanValue(Boolean value) {
        this.settingValue = value != null ? value.toString() : null;
        this.settingType = SettingType.BOOLEAN.getValue();
    }

    /**
     * التحقق من صحة القيمة حسب النوع
     */
    public boolean isValidValue() {
        if (settingValue == null) {
            return true;
        }

        try {
            switch (getSettingTypeEnum()) {
                case INTEGER:
                    Integer.parseInt(settingValue);
                    return true;
                case DECIMAL:
                    Double.parseDouble(settingValue);
                    return true;
                case BOOLEAN:
                    return "true".equalsIgnoreCase(settingValue) || 
                           "false".equalsIgnoreCase(settingValue) ||
                           "1".equals(settingValue) || 
                           "0".equals(settingValue);
                case STRING:
                case DATE:
                case TIME:
                case DATETIME:
                default:
                    return true;
            }
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * الحصول على القيمة الافتراضية حسب النوع
     */
    public String getDefaultValue() {
        switch (getSettingTypeEnum()) {
            case INTEGER:
                return "0";
            case DECIMAL:
                return "0.0";
            case BOOLEAN:
                return "false";
            case STRING:
            case DATE:
            case TIME:
            case DATETIME:
            default:
                return "";
        }
    }

    @Override
    public String toString() {
        return "SystemSettings{" +
                "id=" + getId() +
                ", settingKey='" + settingKey + '\'' +
                ", settingValue='" + settingValue + '\'' +
                ", settingType='" + settingType + '\'' +
                ", description='" + description + '\'' +
                ", isSystem=" + isSystem +
                '}';
    }
}
