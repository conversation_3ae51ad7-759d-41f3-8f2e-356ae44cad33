import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.Frame;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComponent;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JTextField;

/**
 * نافذة إضافة مجموعة رئيسية محسنة Improved Add Main Group Dialog
 */
public class ImprovedAddMainGroupDialog extends JDialog {

    private Connection connection;
    private boolean saved = false;

    // مكونات الواجهة
    private JTextField codeField;
    private JTextField arabicNameField;
    private JTextField englishNameField;
    private JTextField taxPercentField;
    private JTextField quantityLimitField;
    private JTextField imageCodeField;
    private JTextField orderField;
    private JCheckBox webSyncCheckBox;
    private JCheckBox useSalePriceCheckBox;
    private JCheckBox allowDiscountCheckBox;
    private JCheckBox allowPIDiscountCheckBox;
    private JCheckBox activeCheckBox;

    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 14);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 14);

    public ImprovedAddMainGroupDialog(Frame parent, Connection connection) {
        super(parent, "إضافة مجموعة رئيسية جديدة", true);
        this.connection = connection;

        initializeComponents();
        setupLayout();
        setDefaultValues();

        setSize(800, 700);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }

    private void initializeComponents() {
        // إنشاء الحقول مع أحجام محسنة
        codeField = createTextField(true); // قابل للتعديل (إدخال يدوي)
        arabicNameField = createTextField(true);
        englishNameField = createTextField(true);
        taxPercentField = createTextField(true);
        quantityLimitField = createTextField(true);
        imageCodeField = createTextField(true);
        orderField = createTextField(true);

        // إنشاء صناديق الاختيار
        webSyncCheckBox = createCheckBox("مزامنة الويب");
        useSalePriceCheckBox = createCheckBox("استخدام سعر البيع كسعر شراء");
        allowDiscountCheckBox = createCheckBox("السماح بالخصم");
        allowPIDiscountCheckBox = createCheckBox("السماح بخصم PI");
        activeCheckBox = createCheckBox("نشط");
    }

    private JTextField createTextField(boolean enabled) {
        JTextField field = new JTextField();
        field.setFont(arabicFont);
        field.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        field.setPreferredSize(new Dimension(300, 35));
        field.setEnabled(enabled);
        field.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(enabled ? Color.GRAY : Color.LIGHT_GRAY, 1),
                BorderFactory.createEmptyBorder(8, 12, 8, 12)));
        if (!enabled) {
            field.setBackground(new Color(245, 245, 245));
        }
        return field;
    }

    private JCheckBox createCheckBox(String text) {
        JCheckBox checkBox = new JCheckBox(text);
        checkBox.setFont(arabicFont);
        checkBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        checkBox.setPreferredSize(new Dimension(350, 30));
        return checkBox;
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(12, 12, 12, 12);
        gbc.anchor = GridBagConstraints.EAST;
        gbc.fill = GridBagConstraints.HORIZONTAL;

        int row = 0;

        // إضافة الحقول
        addFieldRow(mainPanel, gbc, row++, "كود المجموعة *:", codeField);
        addFieldRow(mainPanel, gbc, row++, "الاسم العربي *:", arabicNameField);
        addFieldRow(mainPanel, gbc, row++, "الاسم الإنجليزي:", englishNameField);
        addFieldRow(mainPanel, gbc, row++, "نسبة الضريبة الافتراضية:", taxPercentField);
        addFieldRow(mainPanel, gbc, row++, "حد الكمية:", quantityLimitField);
        addFieldRow(mainPanel, gbc, row++, "كود الصورة:", imageCodeField);
        addFieldRow(mainPanel, gbc, row++, "الترتيب:", orderField);

        // إضافة صناديق الاختيار
        gbc.gridx = 0;
        gbc.gridy = row++;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        mainPanel.add(webSyncCheckBox, gbc);

        gbc.gridy = row++;
        mainPanel.add(useSalePriceCheckBox, gbc);

        gbc.gridy = row++;
        mainPanel.add(allowDiscountCheckBox, gbc);

        gbc.gridy = row++;
        mainPanel.add(allowPIDiscountCheckBox, gbc);

        gbc.gridy = row++;
        mainPanel.add(activeCheckBox, gbc);

        add(mainPanel, BorderLayout.CENTER);

        // لوحة الأزرار
        JPanel buttonPanel = createButtonPanel();
        add(buttonPanel, BorderLayout.SOUTH);
    }

    private void addFieldRow(JPanel panel, GridBagConstraints gbc, int row, String labelText,
            JComponent field) {
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.3;
        gbc.gridwidth = 1;
        panel.add(createLabel(labelText), gbc);

        gbc.gridx = 1;
        gbc.weightx = 0.7;
        panel.add(field, gbc);
    }

    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicBoldFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        label.setPreferredSize(new Dimension(200, 35));
        return label;
    }

    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER, 20, 15));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));

        JButton saveButton = new JButton("💾 حفظ");
        saveButton.setFont(arabicBoldFont);
        saveButton.setBackground(new Color(39, 174, 96));
        saveButton.setForeground(Color.WHITE);
        saveButton.setPreferredSize(new Dimension(120, 40));
        saveButton.setFocusPainted(false);
        saveButton.addActionListener(e -> saveMainGroup());

        JButton cancelButton = new JButton("❌ إلغاء");
        cancelButton.setFont(arabicBoldFont);
        cancelButton.setBackground(new Color(231, 76, 60));
        cancelButton.setForeground(Color.WHITE);
        cancelButton.setPreferredSize(new Dimension(120, 40));
        cancelButton.setFocusPainted(false);
        cancelButton.addActionListener(e -> dispose());

        panel.add(saveButton);
        panel.add(cancelButton);

        return panel;
    }

    private void setDefaultValues() {
        // القيم الافتراضية
        taxPercentField.setText("0");
        quantityLimitField.setText("0");
        orderField.setText("1");
        activeCheckBox.setSelected(true);
        webSyncCheckBox.setSelected(false);
        useSalePriceCheckBox.setSelected(false);
        allowDiscountCheckBox.setSelected(true);
        allowPIDiscountCheckBox.setSelected(false);
    }



    private void saveMainGroup() {
        // التحقق من صحة البيانات
        if (!validateInput()) {
            return;
        }

        try {
            String sql = """
                        INSERT INTO ERP_GROUP_DETAILS
                        (G_CODE, G_A_NAME, G_E_NAME, TAX_PRCNT_DFLT, ROL_LMT_QTY, G_I_CODE,
                         SYNCHRNZ_TO_WEB_FLG, USE_SAL_PRICE_AS_PUR_PRICE, ALLOW_DISC_FLG,
                         ALLOW_DISC_PI_FLG, G_ORDR, IS_ACTIVE, CREATED_BY, CREATED_DATE)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'USER', SYSDATE)
                    """;

            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, codeField.getText().trim());
            pstmt.setString(2, arabicNameField.getText().trim());
            pstmt.setString(3, englishNameField.getText().trim());

            // نسبة الضريبة
            String taxText = taxPercentField.getText().trim();
            if (taxText.isEmpty()) {
                pstmt.setNull(4, Types.DECIMAL);
            } else {
                pstmt.setBigDecimal(4, new java.math.BigDecimal(taxText));
            }

            // حد الكمية
            String qtyText = quantityLimitField.getText().trim();
            if (qtyText.isEmpty()) {
                pstmt.setNull(5, Types.DECIMAL);
            } else {
                pstmt.setBigDecimal(5, new java.math.BigDecimal(qtyText));
            }

            pstmt.setString(6, imageCodeField.getText().trim());
            pstmt.setInt(7, webSyncCheckBox.isSelected() ? 1 : 0);
            pstmt.setInt(8, useSalePriceCheckBox.isSelected() ? 1 : 0);
            pstmt.setInt(9, allowDiscountCheckBox.isSelected() ? 1 : 0);
            pstmt.setInt(10, allowPIDiscountCheckBox.isSelected() ? 1 : 0);

            // الترتيب
            String orderText = orderField.getText().trim();
            if (orderText.isEmpty()) {
                pstmt.setNull(11, Types.INTEGER);
            } else {
                pstmt.setInt(11, Integer.parseInt(orderText));
            }

            pstmt.setInt(12, activeCheckBox.isSelected() ? 1 : 0);

            pstmt.executeUpdate();
            connection.commit();

            saved = true;
            JOptionPane.showMessageDialog(this, "تم حفظ المجموعة الرئيسية بنجاح!", "نجح الحفظ",
                    JOptionPane.INFORMATION_MESSAGE);

            dispose();

        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }

            JOptionPane.showMessageDialog(this, "خطأ في حفظ المجموعة الرئيسية:\n" + e.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    private boolean validateInput() {
        // التحقق من الحقول المطلوبة
        if (codeField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "كود المجموعة مطلوب", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            codeField.requestFocus();
            return false;
        }

        if (arabicNameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "الاسم العربي مطلوب", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            arabicNameField.requestFocus();
            return false;
        }

        // التحقق من صحة الأرقام
        try {
            String taxText = taxPercentField.getText().trim();
            if (!taxText.isEmpty()) {
                Double.parseDouble(taxText);
            }

            String qtyText = quantityLimitField.getText().trim();
            if (!qtyText.isEmpty()) {
                Double.parseDouble(qtyText);
            }

            String orderText = orderField.getText().trim();
            if (!orderText.isEmpty()) {
                Integer.parseInt(orderText);
            }

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال أرقام صحيحة", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        // التحقق من عدم تكرار الكود
        try {
            String sql = "SELECT COUNT(*) FROM ERP_GROUP_DETAILS WHERE G_CODE = ?";
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, codeField.getText().trim());
            ResultSet rs = pstmt.executeQuery();

            if (rs.next() && rs.getInt(1) > 0) {
                JOptionPane.showMessageDialog(this, "كود المجموعة موجود مسبقاً", "خطأ",
                        JOptionPane.ERROR_MESSAGE);
                codeField.requestFocus();
                return false;
            }

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في التحقق من الكود", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        return true;
    }

    public boolean isSaved() {
        return saved;
    }
}
