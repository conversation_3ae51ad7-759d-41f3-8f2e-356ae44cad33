@echo off
title Oracle Problem FIXED Test

echo ====================================
echo    Oracle Problem FIXED Test
echo ====================================
echo.

echo Testing if Oracle libraries error is fixed...
echo.

echo [1/2] Checking libraries...
if exist "lib\ojdbc11.jar" (
    echo [OK] ojdbc11.jar found
) else (
    echo [ERROR] ojdbc11.jar missing!
    goto :error
)

if exist "lib\orai18n.jar" (
    echo [OK] orai18n.jar found
) else (
    echo [ERROR] orai18n.jar missing!
    goto :error
)

echo.

echo [2/2] Testing Oracle library loading...
java -cp "lib/*;." QuickLibraryTest > test_result.txt 2>&1

if errorlevel 1 (
    echo [ERROR] Library test failed
    type test_result.txt
    goto :error
) else (
    echo [OK] Library test passed
    echo Oracle JDBC Driver loaded successfully
    echo orai18n.jar available in classpath
)

echo.
echo ====================================
echo    TEST RESULT: SUCCESS
echo ====================================
echo.
echo The Oracle libraries error should be FIXED now!
echo.
echo The system should show:
echo - "Oracle JDBC libraries loaded successfully"
echo - NO MORE error message about missing libraries
echo.
echo To verify:
echo 1. Go to Item Management - System Integration
echo 2. You should see "Oracle libraries loaded successfully"
echo 3. NO error dialog should appear
echo.
goto :end

:error
echo.
echo ====================================
echo    TEST RESULT: FAILED
echo ====================================
echo.
echo The problem is NOT fixed yet.
echo.
echo SOLUTIONS:
echo 1. Run: java LibraryDownloader
echo 2. Run: java -cp "lib/*;." CompleteSystemTest
echo 3. Check: SIMPLE_DIAGNOSIS.bat
echo.

:end
echo ====================================
pause
