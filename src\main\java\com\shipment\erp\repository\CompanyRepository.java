package com.shipment.erp.repository;

import com.shipment.erp.model.Company;
import java.util.List;
import java.util.Optional;

/**
 * Repository للشركات
 */
public interface CompanyRepository extends BaseRepository<Company> {

    /**
     * البحث عن شركة بالاسم
     */
    Optional<Company> findByName(String name);

    /**
     * البحث عن شركة بالاسم الإنجليزي
     */
    Optional<Company> findByNameEn(String nameEn);

    /**
     * البحث عن شركة بالرقم الضريبي
     */
    Optional<Company> findByTaxNumber(String taxNumber);

    /**
     * البحث عن شركة بالسجل التجاري
     */
    Optional<Company> findByCommercialRegister(String commercialRegister);

    /**
     * البحث عن الشركات بالمدينة
     */
    List<Company> findByCity(String city);

    /**
     * البحث عن الشركات بالبلد
     */
    List<Company> findByCountry(String country);

    /**
     * البحث عن الشركات بالاسم (جزئي)
     */
    List<Company> findByNameContaining(String name);

    /**
     * البحث عن الشركات النشطة
     */
    List<Company> findActiveCompanies();

    /**
     * البحث عن الشركات غير النشطة
     */
    List<Company> findInactiveCompanies();

    /**
     * التحقق من وجود شركة بالاسم
     */
    boolean existsByName(String name);

    /**
     * التحقق من وجود شركة بالرقم الضريبي
     */
    boolean existsByTaxNumber(String taxNumber);

    /**
     * التحقق من وجود شركة بالسجل التجاري
     */
    boolean existsByCommercialRegister(String commercialRegister);

    /**
     * الحصول على عدد الشركات النشطة
     */
    long countActiveCompanies();

    /**
     * الحصول على عدد الشركات غير النشطة
     */
    long countInactiveCompanies();

    /**
     * البحث المتقدم في الشركات
     */
    List<Company> advancedSearch(String name, String city, String country, Boolean isActive);

    /**
     * الحصول على الشركات مع الفروع
     */
    List<Company> findCompaniesWithBranches();

    /**
     * الحصول على الشركات بدون فروع
     */
    List<Company> findCompaniesWithoutBranches();
}
