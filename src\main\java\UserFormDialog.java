import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Calendar;

/**
 * نافذة إضافة/تعديل المستخدم
 * User Add/Edit Form Dialog
 */
public class UserFormDialog extends JDialog {
    
    private Font arabicFont;
    private boolean confirmed = false;
    private User user;
    private User originalUser;
    
    // حقول النموذج
    private JTextField userIdField;
    private JTextField fullNameField;
    private JTextField usernameField;
    private JTextField emailField;
    private JComboBox<String> roleCombo;
    private JComboBox<String> departmentCombo;
    private JComboBox<String> statusCombo;
    private JComboBox<String> cityCombo;
    private JTextField phoneField;
    private JTextArea addressArea;
    private JTextField nationalIdField;
    private JTextField emergencyContactField;
    private J<PERSON>pinner expiryDateSpinner;
    private JTextArea notesArea;
    
    public UserFormDialog(JDialog parent, String title, User user) {
        super(parent, title, true);
        
        this.originalUser = user;
        this.arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        initializeComponents();
        setupLayout();
        loadUserData();
        
        setSize(800, 700);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(false);
    }
    
    /**
     * تهيئة المكونات
     */
    private void initializeComponents() {
        // الحقول الأساسية
        userIdField = createTextField();
        fullNameField = createTextField();
        usernameField = createTextField();
        emailField = createTextField();
        phoneField = createTextField();
        nationalIdField = createTextField();
        emergencyContactField = createTextField();
        
        // القوائم المنسدلة
        roleCombo = new JComboBox<>(new String[]{
            "مدير النظام", "محاسب", "مشرف الشحن", "موظف إدخال بيانات", 
            "مدير المبيعات", "موظف خدمة العملاء", "مدير المخزون"
        });
        setupComboBox(roleCombo);
        
        departmentCombo = new JComboBox<>(new String[]{
            "تقنية المعلومات", "المحاسبة", "العمليات", "المبيعات", 
            "الموارد البشرية", "خدمة العملاء", "المخازن"
        });
        setupComboBox(departmentCombo);
        
        statusCombo = new JComboBox<>(new String[]{
            "نشط", "غير نشط", "معلق"
        });
        setupComboBox(statusCombo);
        
        cityCombo = new JComboBox<>(new String[]{
            "الرياض", "جدة", "الدمام", "مكة المكرمة", "المدينة المنورة", 
            "الطائف", "تبوك", "بريدة", "خميس مشيط", "حائل"
        });
        setupComboBox(cityCombo);
        
        // منطقة العنوان
        addressArea = new JTextArea(3, 20);
        addressArea.setFont(arabicFont);
        addressArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        addressArea.setLineWrap(true);
        addressArea.setWrapStyleWord(true);
        
        // منطقة الملاحظات
        notesArea = new JTextArea(3, 20);
        notesArea.setFont(arabicFont);
        notesArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        notesArea.setLineWrap(true);
        notesArea.setWrapStyleWord(true);
        
        // تاريخ الانتهاء
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, 1); // سنة من الآن
        Date initialDate = calendar.getTime();
        
        expiryDateSpinner = new JSpinner(new SpinnerDateModel(initialDate, new Date(), null, Calendar.DAY_OF_MONTH));
        JSpinner.DateEditor dateEditor = new JSpinner.DateEditor(expiryDateSpinner, "yyyy-MM-dd");
        expiryDateSpinner.setEditor(dateEditor);
        expiryDateSpinner.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }
    
    /**
     * إنشاء حقل نص
     */
    private JTextField createTextField() {
        JTextField field = new JTextField();
        field.setFont(arabicFont);
        field.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return field;
    }
    
    /**
     * إعداد ComboBox
     */
    private void setupComboBox(JComboBox<String> comboBox) {
        comboBox.setFont(arabicFont);
        comboBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        ((JLabel) comboBox.getRenderer()).setHorizontalAlignment(SwingConstants.RIGHT);
    }
    
    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // الرأس
        add(createHeaderPanel(), BorderLayout.NORTH);
        
        // المحتوى الرئيسي
        add(createMainPanel(), BorderLayout.CENTER);
        
        // الأزرار
        add(createButtonPanel(), BorderLayout.SOUTH);
    }
    
    /**
     * إنشاء لوحة الرأس
     */
    private JPanel createHeaderPanel() {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(52, 152, 219));
        headerPanel.setBorder(BorderFactory.createEmptyBorder(15, 20, 15, 20));
        headerPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel titleLabel = new JLabel(getTitle());
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 18));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        headerPanel.add(titleLabel, BorderLayout.CENTER);
        
        return headerPanel;
    }
    
    /**
     * إنشاء اللوحة الرئيسية
     */
    private JPanel createMainPanel() {
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JTabbedPane tabbedPane = new JTabbedPane();
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        tabbedPane.setFont(arabicFont);
        
        // تبويب البيانات الأساسية
        tabbedPane.addTab("البيانات الأساسية", createBasicInfoPanel());
        
        // تبويب البيانات الإضافية
        tabbedPane.addTab("البيانات الإضافية", createAdditionalInfoPanel());
        
        // تبويب الملاحظات
        tabbedPane.addTab("الملاحظات", createNotesPanel());
        
        mainPanel.add(tabbedPane, BorderLayout.CENTER);
        
        return mainPanel;
    }
    
    /**
     * إنشاء لوحة البيانات الأساسية
     */
    private JPanel createBasicInfoPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;
        
        int row = 0;
        
        // رقم المستخدم
        addFieldRow(panel, "رقم المستخدم:", userIdField, gbc, row++);
        
        // الاسم الكامل
        addFieldRow(panel, "الاسم الكامل:", fullNameField, gbc, row++);
        
        // اسم المستخدم
        addFieldRow(panel, "اسم المستخدم:", usernameField, gbc, row++);
        
        // البريد الإلكتروني
        addFieldRow(panel, "البريد الإلكتروني:", emailField, gbc, row++);
        
        // الدور
        addFieldRow(panel, "الدور:", roleCombo, gbc, row++);
        
        // القسم
        addFieldRow(panel, "القسم:", departmentCombo, gbc, row++);
        
        // الحالة
        addFieldRow(panel, "الحالة:", statusCombo, gbc, row++);
        
        // تاريخ الانتهاء
        addFieldRow(panel, "تاريخ انتهاء الصلاحية:", expiryDateSpinner, gbc, row++);
        
        return panel;
    }
    
    /**
     * إنشاء لوحة البيانات الإضافية
     */
    private JPanel createAdditionalInfoPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;
        
        int row = 0;
        
        // رقم الهاتف
        addFieldRow(panel, "رقم الهاتف:", phoneField, gbc, row++);
        
        // المدينة
        addFieldRow(panel, "المدينة:", cityCombo, gbc, row++);
        
        // رقم الهوية
        addFieldRow(panel, "رقم الهوية الوطنية:", nationalIdField, gbc, row++);
        
        // جهة الاتصال في الطوارئ
        addFieldRow(panel, "جهة الاتصال في الطوارئ:", emergencyContactField, gbc, row++);
        
        // العنوان
        gbc.gridx = 1; gbc.gridy = row; gbc.gridwidth = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(createLabel("العنوان:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.BOTH; gbc.weightx = 1.0; gbc.weighty = 1.0;
        panel.add(new JScrollPane(addressArea), gbc);
        
        return panel;
    }
    
    /**
     * إنشاء لوحة الملاحظات
     */
    private JPanel createNotesPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        JLabel label = createLabel("ملاحظات إضافية:");
        panel.add(label, BorderLayout.NORTH);
        
        JScrollPane scrollPane = new JScrollPane(notesArea);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }

    /**
     * إضافة صف حقل
     */
    private void addFieldRow(JPanel panel, String labelText, JComponent component, GridBagConstraints gbc, int row) {
        gbc.gridx = 1; gbc.gridy = row; gbc.gridwidth = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0; gbc.weighty = 0;
        panel.add(createLabel(labelText), gbc);

        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        panel.add(component, gbc);
    }

    /**
     * إنشاء تسمية
     */
    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }

    /**
     * إنشاء لوحة الأزرار
     */
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 15));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(new Color(248, 249, 250));
        panel.setBorder(BorderFactory.createMatteBorder(1, 0, 0, 0, new Color(222, 226, 230)));

        JButton saveButton = createStyledButton("حفظ", new Color(40, 167, 69), Color.WHITE);
        saveButton.addActionListener(e -> saveUser());

        JButton cancelButton = createStyledButton("إلغاء", new Color(108, 117, 125), Color.WHITE);
        cancelButton.addActionListener(e -> dispose());

        JButton resetButton = createStyledButton("إعادة تعيين", new Color(255, 193, 7), Color.BLACK);
        resetButton.addActionListener(e -> resetForm());

        panel.add(saveButton);
        panel.add(cancelButton);
        panel.add(resetButton);

        return panel;
    }

    /**
     * إنشاء زر مُنسق
     */
    private JButton createStyledButton(String text, Color bgColor, Color fgColor) {
        JButton button = new JButton(text);
        button.setFont(arabicFont);
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        button.setBackground(bgColor);
        button.setForeground(fgColor);
        button.setPreferredSize(new Dimension(120, 35));
        button.setFocusPainted(false);
        button.setBorderPainted(false);

        return button;
    }

    /**
     * تحميل بيانات المستخدم
     */
    private void loadUserData() {
        if (originalUser != null) {
            userIdField.setText(originalUser.getUserId());
            userIdField.setEditable(false); // لا يمكن تعديل رقم المستخدم

            fullNameField.setText(originalUser.getFullName());
            usernameField.setText(originalUser.getUsername());
            emailField.setText(originalUser.getEmail());

            // تعيين القوائم المنسدلة
            setComboBoxValue(roleCombo, originalUser.getRole());
            setComboBoxValue(departmentCombo, originalUser.getDepartment());
            setComboBoxValue(statusCombo, originalUser.getStatus());
            setComboBoxValue(cityCombo, originalUser.getCity());

            // البيانات الإضافية
            phoneField.setText(originalUser.getPhoneNumber() != null ? originalUser.getPhoneNumber() : "");
            nationalIdField.setText(originalUser.getNationalId() != null ? originalUser.getNationalId() : "");
            emergencyContactField.setText(originalUser.getEmergencyContact() != null ? originalUser.getEmergencyContact() : "");
            addressArea.setText(originalUser.getAddress() != null ? originalUser.getAddress() : "");
            notesArea.setText(originalUser.getNotes() != null ? originalUser.getNotes() : "");

            // تاريخ الانتهاء
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Date expiryDate = sdf.parse(originalUser.getExpiryDate());
                expiryDateSpinner.setValue(expiryDate);
            } catch (Exception e) {
                // استخدام التاريخ الافتراضي
            }
        } else {
            // إنشاء مستخدم جديد - توليد رقم مستخدم تلقائي
            userIdField.setText(generateNewUserId());
        }
    }

    /**
     * تعيين قيمة ComboBox
     */
    private void setComboBoxValue(JComboBox<String> comboBox, String value) {
        if (value != null) {
            for (int i = 0; i < comboBox.getItemCount(); i++) {
                if (comboBox.getItemAt(i).equals(value)) {
                    comboBox.setSelectedIndex(i);
                    break;
                }
            }
        }
    }

    /**
     * توليد رقم مستخدم جديد
     */
    private String generateNewUserId() {
        return "U" + String.format("%03d", (int)(Math.random() * 1000) + 100);
    }

    /**
     * حفظ المستخدم
     */
    private void saveUser() {
        if (validateForm()) {
            try {
                // إنشاء كائن المستخدم
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String expiryDate = sdf.format((Date) expiryDateSpinner.getValue());
                String createdDate = originalUser != null ? originalUser.getCreatedDate() : sdf.format(new Date());

                user = new User(
                    userIdField.getText().trim(),
                    fullNameField.getText().trim(),
                    usernameField.getText().trim(),
                    emailField.getText().trim(),
                    (String) roleCombo.getSelectedItem(),
                    (String) departmentCombo.getSelectedItem(),
                    (String) statusCombo.getSelectedItem(),
                    createdDate,
                    expiryDate,
                    (String) cityCombo.getSelectedItem(),
                    phoneField.getText().trim(),
                    addressArea.getText().trim(),
                    nationalIdField.getText().trim(),
                    emergencyContactField.getText().trim()
                );

                user.setNotes(notesArea.getText().trim());

                // نسخ الصلاحيات من المستخدم الأصلي إذا كان موجوداً
                if (originalUser != null) {
                    user.setPermissions(originalUser.getPermissions());
                    user.setLastLoginDate(originalUser.getLastLoginDate());
                    user.setFirstLogin(originalUser.isFirstLogin());
                }

                confirmed = true;
                dispose();

            } catch (Exception e) {
                JOptionPane.showMessageDialog(this,
                    "حدث خطأ في حفظ البيانات: " + e.getMessage(),
                    "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * التحقق من صحة النموذج
     */
    private boolean validateForm() {
        // التحقق من الحقول المطلوبة
        if (userIdField.getText().trim().isEmpty()) {
            showValidationError("رقم المستخدم مطلوب");
            return false;
        }

        if (fullNameField.getText().trim().isEmpty()) {
            showValidationError("الاسم الكامل مطلوب");
            fullNameField.requestFocus();
            return false;
        }

        if (usernameField.getText().trim().isEmpty()) {
            showValidationError("اسم المستخدم مطلوب");
            usernameField.requestFocus();
            return false;
        }

        if (emailField.getText().trim().isEmpty()) {
            showValidationError("البريد الإلكتروني مطلوب");
            emailField.requestFocus();
            return false;
        }

        // التحقق من صحة البريد الإلكتروني
        if (!isValidEmail(emailField.getText().trim())) {
            showValidationError("يرجى إدخال بريد إلكتروني صحيح");
            emailField.requestFocus();
            return false;
        }

        return true;
    }

    /**
     * عرض رسالة خطأ التحقق
     */
    private void showValidationError(String message) {
        JOptionPane.showMessageDialog(this, message, "خطأ في البيانات", JOptionPane.ERROR_MESSAGE);
    }

    /**
     * التحقق من صحة البريد الإلكتروني
     */
    private boolean isValidEmail(String email) {
        return email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }

    /**
     * إعادة تعيين النموذج
     */
    private void resetForm() {
        if (originalUser != null) {
            loadUserData();
        } else {
            // مسح جميع الحقول
            userIdField.setText(generateNewUserId());
            fullNameField.setText("");
            usernameField.setText("");
            emailField.setText("");
            phoneField.setText("");
            nationalIdField.setText("");
            emergencyContactField.setText("");
            addressArea.setText("");
            notesArea.setText("");

            roleCombo.setSelectedIndex(0);
            departmentCombo.setSelectedIndex(0);
            statusCombo.setSelectedIndex(0);
            cityCombo.setSelectedIndex(0);

            // إعادة تعيين تاريخ الانتهاء
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.YEAR, 1);
            expiryDateSpinner.setValue(calendar.getTime());
        }
    }

    // Getters
    public boolean isConfirmed() {
        return confirmed;
    }

    public User getUser() {
        return user;
    }
}
