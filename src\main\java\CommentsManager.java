import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * مدير التعليقات - نظام شامل لإدارة التعليقات في عمليات الاستيراد يتم استخدامه كمعيار موحد لجميع
 * عمليات الاستيراد من قواعد البيانات المختلفة
 */
public class CommentsManager {

    // خريطة التعليقات المحملة من قاعدة البيانات
    private static Map<Integer, String> commentsMap = new ConcurrentHashMap<>();

    // خريطة ربط الحقول بالتعليقات لكل جدول
    private static Map<String, Map<String, CommentInfo>> tableFieldComments =
            new ConcurrentHashMap<>();

    // معلومات الاتصال بقاعدة البيانات الرئيسية (ship_erp)
    private static final String MAIN_DB_URL = "*************************************";
    private static final String MAIN_DB_USER = "ship_erp";
    private static final String MAIN_DB_PASSWORD = "ship_erp_password";

    // معلومات الاتصال بقاعدة البيانات المصدر (ias20251)
    private static final String SOURCE_DB_URL = "*************************************";
    private static final String SOURCE_DB_USER = "ias20251";
    private static final String SOURCE_DB_PASSWORD = "ys123";

    /**
     * فئة لحفظ معلومات التعليق
     */
    public static class CommentInfo {
        private int commentNumber;
        private String commentText;
        private String fieldName;
        private String tableName;

        public CommentInfo(int commentNumber, String commentText, String fieldName,
                String tableName) {
            this.commentNumber = commentNumber;
            this.commentText = commentText;
            this.fieldName = fieldName;
            this.tableName = tableName;
        }

        // Getters
        public int getCommentNumber() {
            return commentNumber;
        }

        public String getCommentText() {
            return commentText;
        }

        public String getFieldName() {
            return fieldName;
        }

        public String getTableName() {
            return tableName;
        }

        @Override
        public String toString() {
            return commentNumber + " - " + commentText;
        }
    }

    /**
     * تهيئة مدير التعليقات - يجب استدعاؤها عند بدء التطبيق
     */
    public static void initialize() {
        try {
            loadCommentsFromDatabase();
            System.out.println("✅ تم تهيئة مدير التعليقات بنجاح");
        } catch (Exception e) {
            System.err.println("❌ خطأ في تهيئة مدير التعليقات: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * تحميل جميع التعليقات من قاعدة البيانات الرئيسية
     */
    private static void loadCommentsFromDatabase() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
        } catch (ClassNotFoundException e) {
            throw new SQLException("تعذر العثور على تعريف Oracle JDBC", e);
        }

        try (Connection conn =
                DriverManager.getConnection(MAIN_DB_URL, MAIN_DB_USER, MAIN_DB_PASSWORD)) {
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(
                    "SELECT COMMENTS_NO, COMMENTS_NAME FROM COMMENTS ORDER BY COMMENTS_NO");

            commentsMap.clear();
            int count = 0;

            while (rs.next()) {
                int commentNo = rs.getInt("COMMENTS_NO");
                String commentName = rs.getString("COMMENTS_NAME");
                commentsMap.put(commentNo, commentName);
                count++;
            }

            rs.close();
            stmt.close();

            System.out.println("📋 تم تحميل " + count + " تعليق من قاعدة البيانات");
        }
    }

    /**
     * تحليل تعليقات جدول محدد من قاعدة بيانات مصدر
     */
    public static void analyzeTableComments(String tableName, String sourceDbUser,
            String sourceDbPassword) {
        try (Connection sourceConn =
                DriverManager.getConnection(SOURCE_DB_URL, sourceDbUser, sourceDbPassword)) {

            Map<String, CommentInfo> fieldComments = new HashMap<>();

            // استعلام تعليقات الحقول
            String sql = "SELECT COLUMN_NAME, COMMENTS FROM USER_COL_COMMENTS "
                    + "WHERE TABLE_NAME = ? AND COMMENTS IS NOT NULL ORDER BY COLUMN_NAME";

            PreparedStatement stmt = sourceConn.prepareStatement(sql);
            stmt.setString(1, tableName);
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                String columnName = rs.getString("COLUMN_NAME");
                String comments = rs.getString("COMMENTS");

                if (comments != null && !comments.trim().isEmpty()) {
                    try {
                        int commentNo = Integer.parseInt(comments.trim());
                        String commentText = commentsMap.getOrDefault(commentNo, "تعليق غير موجود");

                        CommentInfo commentInfo =
                                new CommentInfo(commentNo, commentText, columnName, tableName);
                        fieldComments.put(columnName, commentInfo);

                    } catch (NumberFormatException e) {
                        // التعليق ليس رقماً، احفظه كما هو
                        CommentInfo commentInfo =
                                new CommentInfo(-1, comments, columnName, tableName);
                        fieldComments.put(columnName, commentInfo);
                    }
                }
            }

            rs.close();
            stmt.close();

            // حفظ تعليقات الجدول
            tableFieldComments.put(tableName, fieldComments);

            System.out.println("📊 تم تحليل " + fieldComments.size() + " حقل في جدول " + tableName);

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحليل تعليقات جدول " + tableName + ": " + e.getMessage());
        }
    }

    /**
     * الحصول على تعليق حقل محدد
     */
    public static String getFieldComment(String tableName, String fieldName) {
        Map<String, CommentInfo> tableComments = tableFieldComments.get(tableName);
        if (tableComments != null) {
            CommentInfo commentInfo = tableComments.get(fieldName);
            if (commentInfo != null) {
                return commentInfo.getCommentText();
            }
        }
        return fieldName; // إرجاع اسم الحقل إذا لم يوجد تعليق
    }

    /**
     * الحصول على معلومات التعليق الكاملة لحقل محدد
     */
    public static CommentInfo getFieldCommentInfo(String tableName, String fieldName) {
        Map<String, CommentInfo> tableComments = tableFieldComments.get(tableName);
        if (tableComments != null) {
            return tableComments.get(fieldName);
        }
        return null;
    }

    /**
     * الحصول على جميع تعليقات جدول محدد
     */
    public static Map<String, CommentInfo> getTableComments(String tableName) {
        return tableFieldComments.getOrDefault(tableName, new HashMap<>());
    }

    /**
     * الحصول على تعليق برقمه
     */
    public static String getCommentByNumber(int commentNumber) {
        return commentsMap.getOrDefault(commentNumber, "تعليق غير موجود");
    }

    /**
     * إنشاء تقرير شامل لتعليقات جدول
     */
    public static void generateTableCommentsReport(String tableName) {
        Map<String, CommentInfo> comments = getTableComments(tableName);

        System.out.println("\n" + "=".repeat(80));
        System.out.println("📋 تقرير تعليقات جدول: " + tableName);
        System.out.println("=".repeat(80));

        if (comments.isEmpty()) {
            System.out.println("❌ لا توجد تعليقات لهذا الجدول");
            return;
        }

        System.out.printf("%-30s %-15s %-50s\n", "اسم الحقل", "رقم التعليق", "معنى التعليق");
        System.out.println("-".repeat(95));

        for (Map.Entry<String, CommentInfo> entry : comments.entrySet()) {
            String fieldName = entry.getKey();
            CommentInfo commentInfo = entry.getValue();

            String commentText = commentInfo.getCommentText();
            if (commentText.length() > 48) {
                commentText = commentText.substring(0, 45) + "...";
            }

            System.out.printf("%-30s %-15s %-50s\n", fieldName,
                    commentInfo.getCommentNumber() > 0
                            ? String.valueOf(commentInfo.getCommentNumber())
                            : "نص مباشر",
                    commentText);
        }

        System.out.println("-".repeat(95));
        System.out.println("📊 إجمالي الحقول: " + comments.size());
    }

    /**
     * إنشاء خريطة ربط للحقول المهمة في عملية الاستيراد
     */
    public static Map<String, String> createFieldMappingForImport(String tableName,
            String[] importantFields) {
        Map<String, String> fieldMapping = new HashMap<>();

        for (String fieldName : importantFields) {
            String comment = getFieldComment(tableName, fieldName);
            fieldMapping.put(fieldName, comment);
        }

        return fieldMapping;
    }

    /**
     * التحقق من تحديث التعليقات
     */
    public static void refreshComments() {
        try {
            loadCommentsFromDatabase();
            System.out.println("✅ تم تحديث التعليقات بنجاح");
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحديث التعليقات: " + e.getMessage());
        }
    }

    /**
     * إحصائيات التعليقات
     */
    public static void printStatistics() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("📈 إحصائيات مدير التعليقات");
        System.out.println("=".repeat(60));
        System.out.println("📋 إجمالي التعليقات المحملة: " + commentsMap.size());
        System.out.println("📊 إجمالي الجداول المحللة: " + tableFieldComments.size());

        int totalFields = 0;
        for (Map<String, CommentInfo> tableComments : tableFieldComments.values()) {
            totalFields += tableComments.size();
        }
        System.out.println("🔗 إجمالي الحقول التي لها تعليقات: " + totalFields);

        System.out.println("\n📋 الجداول المحللة:");
        for (String tableName : tableFieldComments.keySet()) {
            int fieldCount = tableFieldComments.get(tableName).size();
            System.out.println("  - " + tableName + ": " + fieldCount + " حقل");
        }
    }
}
