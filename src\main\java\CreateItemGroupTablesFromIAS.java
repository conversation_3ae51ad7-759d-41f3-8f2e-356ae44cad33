import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * إنشاء جداول مجموعات الأصناف في SHIP_ERP بنفس بنية جداول IAS20251
 */
public class CreateItemGroupTablesFromIAS {

    public static void main(String[] args) {
        try {
            // الاتصال بقاعدة بيانات SHIP_ERP
            Class.forName("oracle.jdbc.driver.OracleDriver");

            System.out.println("🔗 محاولة الاتصال بـ SHIP_ERP...");
            Connection shipErpConn = DriverManager.getConnection(
                    "*************************************", "ship_erp", "ship_erp_password");
            System.out.println("✅ تم الاتصال بـ SHIP_ERP");

            System.out.println("🔗 محاولة الاتصال بـ IAS20251...");
            // الاتصال بقاعدة بيانات IAS20251
            Connection ias20251Conn = DriverManager
                    .getConnection("*************************************", "ias20251", "ys123");
            System.out.println("✅ تم الاتصال بـ IAS20251");

            System.out.println("✅ تم الاتصال بقواعد البيانات");

            // إنشاء الجداول
            createGroupDetailsTable(shipErpConn, ias20251Conn);
            createMainSubGroupTable(shipErpConn, ias20251Conn);
            createSubGroupTable(shipErpConn, ias20251Conn);
            createAssistantGroupTable(shipErpConn, ias20251Conn);
            createDetailGroupTable(shipErpConn, ias20251Conn);

            shipErpConn.close();
            ias20251Conn.close();

            System.out.println("🎉 تم إنشاء جميع الجداول بنجاح!");

        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * إنشاء جدول المجموعات الرئيسية بنفس بنية GROUP_DETAILS
     */
    private static void createGroupDetailsTable(Connection shipErpConn, Connection ias20251Conn)
            throws SQLException {
        System.out.println("📋 إنشاء جدول ERP_GROUP_DETAILS...");

        Statement stmt = shipErpConn.createStatement();

        // حذف الجدول إن وجد
        try {
            stmt.execute("DROP TABLE ERP_GROUP_DETAILS CASCADE CONSTRAINTS");
            System.out.println("🗑️ تم حذف الجدول القديم");
        } catch (SQLException e) {
            // تجاهل خطأ "الجدول غير موجود"
        }

        // إنشاء الجدول الجديد
        String createSQL = """
                    CREATE TABLE ERP_GROUP_DETAILS (
                        G_CODE VARCHAR2(10) NOT NULL,
                        G_A_NAME VARCHAR2(100),
                        G_E_NAME VARCHAR2(100),
                        TAX_PRCNT_DFLT NUMBER(5,2),
                        ROL_LMT_QTY NUMBER(15,3),
                        G_I_CODE VARCHAR2(20),
                        SYNCHRNZ_TO_WEB_FLG NUMBER(1) DEFAULT 0,
                        USE_SAL_PRICE_AS_PUR_PRICE NUMBER(1) DEFAULT 0,
                        ALLOW_DISC_FLG NUMBER(1) DEFAULT 1,
                        ALLOW_DISC_PI_FLG NUMBER(1) DEFAULT 1,
                        G_ORDR NUMBER(10),
                        IS_ACTIVE NUMBER(1) DEFAULT 1,
                        CREATED_BY VARCHAR2(50),
                        CREATED_DATE DATE DEFAULT SYSDATE,
                        UPDATED_BY VARCHAR2(50),
                        UPDATED_DATE DATE,
                        CONSTRAINT PK_ERP_GROUP_DETAILS PRIMARY KEY (G_CODE)
                    )
                """;

        stmt.execute(createSQL);
        // لا نحتاج commit مع AutoCommit
        System.out.println("✅ تم إنشاء جدول ERP_GROUP_DETAILS");
    }

    /**
     * إنشاء جدول المجموعات الفرعية بنفس بنية IAS_MAINSUB_GRP_DTL
     */
    private static void createMainSubGroupTable(Connection shipErpConn, Connection ias20251Conn)
            throws SQLException {
        System.out.println("📋 إنشاء جدول ERP_MAINSUB_GRP_DTL...");

        Statement stmt = shipErpConn.createStatement();

        // حذف الجدول إن وجد
        try {
            stmt.execute("DROP TABLE ERP_MAINSUB_GRP_DTL CASCADE CONSTRAINTS");
            System.out.println("🗑️ تم حذف الجدول القديم");
        } catch (SQLException e) {
            // تجاهل خطأ "الجدول غير موجود"
        }

        // إنشاء الجدول الجديد
        String createSQL =
                """
                            CREATE TABLE ERP_MAINSUB_GRP_DTL (
                                G_CODE VARCHAR2(10) NOT NULL,
                                MNG_CODE VARCHAR2(20) NOT NULL,
                                MNG_A_NAME VARCHAR2(100),
                                MNG_E_NAME VARCHAR2(100),
                                MNG_I_CODE VARCHAR2(20),
                                SYNCHRNZ_TO_WEB_FLG NUMBER(1) DEFAULT 0,
                                MNG_ORDR NUMBER(10),
                                IS_ACTIVE NUMBER(1) DEFAULT 1,
                                CREATED_BY VARCHAR2(50),
                                CREATED_DATE DATE DEFAULT SYSDATE,
                                UPDATED_BY VARCHAR2(50),
                                UPDATED_DATE DATE,
                                CONSTRAINT PK_ERP_MAINSUB_GRP_DTL PRIMARY KEY (G_CODE, MNG_CODE),
                                CONSTRAINT FK_ERP_MAINSUB_G_CODE FOREIGN KEY (G_CODE) REFERENCES ERP_GROUP_DETAILS(G_CODE)
                            )
                        """;

        stmt.execute(createSQL);
        // لا نحتاج commit مع AutoCommit
        System.out.println("✅ تم إنشاء جدول ERP_MAINSUB_GRP_DTL");
    }

    /**
     * إنشاء جدول المجموعات تحت فرعية بنفس بنية IAS_SUB_GRP_DTL
     */
    private static void createSubGroupTable(Connection shipErpConn, Connection ias20251Conn)
            throws SQLException {
        System.out.println("📋 إنشاء جدول ERP_SUB_GRP_DTL...");

        Statement stmt = shipErpConn.createStatement();

        // حذف الجدول إن وجد
        try {
            stmt.execute("DROP TABLE ERP_SUB_GRP_DTL CASCADE CONSTRAINTS");
            System.out.println("🗑️ تم حذف الجدول القديم");
        } catch (SQLException e) {
            // تجاهل خطأ "الجدول غير موجود"
        }

        // إنشاء الجدول الجديد
        String createSQL =
                """
                            CREATE TABLE ERP_SUB_GRP_DTL (
                                G_CODE VARCHAR2(10) NOT NULL,
                                MNG_CODE VARCHAR2(20) NOT NULL,
                                SUBG_CODE VARCHAR2(30) NOT NULL,
                                SUBG_A_NAME VARCHAR2(100),
                                SUBG_E_NAME VARCHAR2(100),
                                SUBG_I_CODE VARCHAR2(20),
                                SYNCHRNZ_TO_WEB_FLG NUMBER(1) DEFAULT 0,
                                SUBG_ORDR NUMBER(10),
                                IS_ACTIVE NUMBER(1) DEFAULT 1,
                                CREATED_BY VARCHAR2(50),
                                CREATED_DATE DATE DEFAULT SYSDATE,
                                UPDATED_BY VARCHAR2(50),
                                UPDATED_DATE DATE,
                                CONSTRAINT PK_ERP_SUB_GRP_DTL PRIMARY KEY (G_CODE, MNG_CODE, SUBG_CODE),
                                CONSTRAINT FK_ERP_SUB_MAIN FOREIGN KEY (G_CODE, MNG_CODE) REFERENCES ERP_MAINSUB_GRP_DTL(G_CODE, MNG_CODE)
                            )
                        """;

        stmt.execute(createSQL);
        // لا نحتاج commit مع AutoCommit
        System.out.println("✅ تم إنشاء جدول ERP_SUB_GRP_DTL");
    }

    /**
     * إنشاء جدول المجموعات المساعدة بنفس بنية IAS_ASSISTANT_GROUP
     */
    private static void createAssistantGroupTable(Connection shipErpConn, Connection ias20251Conn)
            throws SQLException {
        System.out.println("📋 إنشاء جدول ERP_ASSISTANT_GROUP...");

        Statement stmt = shipErpConn.createStatement();

        // حذف الجدول إن وجد
        try {
            stmt.execute("DROP TABLE ERP_ASSISTANT_GROUP CASCADE CONSTRAINTS");
            System.out.println("🗑️ تم حذف الجدول القديم");
        } catch (SQLException e) {
            // تجاهل خطأ "الجدول غير موجود"
        }

        // إنشاء الجدول الجديد
        String createSQL =
                """
                            CREATE TABLE ERP_ASSISTANT_GROUP (
                                G_CODE VARCHAR2(10) NOT NULL,
                                MNG_CODE VARCHAR2(20) NOT NULL,
                                SUBG_CODE VARCHAR2(30) NOT NULL,
                                ASSISTANT_NO VARCHAR2(40) NOT NULL,
                                ASSISTANT_A_NAME VARCHAR2(100),
                                ASSISTANT_E_NAME VARCHAR2(100),
                                ASSISTANT_I_CODE VARCHAR2(20),
                                SYNCHRNZ_TO_WEB_FLG NUMBER(1) DEFAULT 0,
                                ASSISTANT_ORDR NUMBER(10),
                                IS_ACTIVE NUMBER(1) DEFAULT 1,
                                CREATED_BY VARCHAR2(50),
                                CREATED_DATE DATE DEFAULT SYSDATE,
                                UPDATED_BY VARCHAR2(50),
                                UPDATED_DATE DATE,
                                CONSTRAINT PK_ERP_ASSISTANT_GROUP PRIMARY KEY (G_CODE, MNG_CODE, SUBG_CODE, ASSISTANT_NO),
                                CONSTRAINT FK_ERP_ASSIST_SUB FOREIGN KEY (G_CODE, MNG_CODE, SUBG_CODE) REFERENCES ERP_SUB_GRP_DTL(G_CODE, MNG_CODE, SUBG_CODE)
                            )
                        """;

        stmt.execute(createSQL);
        // لا نحتاج commit مع AutoCommit
        System.out.println("✅ تم إنشاء جدول ERP_ASSISTANT_GROUP");
    }

    /**
     * إنشاء جدول المجموعات التفصيلية بنفس بنية IAS_DETAIL_GROUP
     */
    private static void createDetailGroupTable(Connection shipErpConn, Connection ias20251Conn)
            throws SQLException {
        System.out.println("📋 إنشاء جدول ERP_DETAIL_GROUP...");

        Statement stmt = shipErpConn.createStatement();

        // حذف الجدول إن وجد
        try {
            stmt.execute("DROP TABLE ERP_DETAIL_GROUP CASCADE CONSTRAINTS");
            System.out.println("🗑️ تم حذف الجدول القديم");
        } catch (SQLException e) {
            // تجاهل خطأ "الجدول غير موجود"
        }

        // إنشاء الجدول الجديد
        String createSQL =
                """
                            CREATE TABLE ERP_DETAIL_GROUP (
                                G_CODE VARCHAR2(10) NOT NULL,
                                MNG_CODE VARCHAR2(20) NOT NULL,
                                SUBG_CODE VARCHAR2(30) NOT NULL,
                                ASSISTANT_NO VARCHAR2(40) NOT NULL,
                                DETAIL_NO VARCHAR2(50) NOT NULL,
                                DETAIL_A_NAME VARCHAR2(100),
                                DETAIL_E_NAME VARCHAR2(100),
                                DETAIL_I_CODE VARCHAR2(20),
                                SYNCHRNZ_TO_WEB_FLG NUMBER(1) DEFAULT 0,
                                DETAIL_ORDR NUMBER(10),
                                IS_ACTIVE NUMBER(1) DEFAULT 1,
                                CREATED_BY VARCHAR2(50),
                                CREATED_DATE DATE DEFAULT SYSDATE,
                                UPDATED_BY VARCHAR2(50),
                                UPDATED_DATE DATE,
                                CONSTRAINT PK_ERP_DETAIL_GROUP PRIMARY KEY (G_CODE, MNG_CODE, SUBG_CODE, ASSISTANT_NO, DETAIL_NO),
                                CONSTRAINT FK_ERP_DETAIL_ASSIST FOREIGN KEY (G_CODE, MNG_CODE, SUBG_CODE, ASSISTANT_NO) REFERENCES ERP_ASSISTANT_GROUP(G_CODE, MNG_CODE, SUBG_CODE, ASSISTANT_NO)
                            )
                        """;

        stmt.execute(createSQL);
        // لا نحتاج commit مع AutoCommit
        System.out.println("✅ تم إنشاء جدول ERP_DETAIL_GROUP");
    }
}
