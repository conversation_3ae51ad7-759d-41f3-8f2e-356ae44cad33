import java.sql.*;

/**
 * أداة سريعة لتحليل الجداول عبر سطر الأوامر
 */
public class QuickTableAnalyzer {
    
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            System.out.println("🔍 أداة التحليل السريع للجداول");
            System.out.println("=====================================");
            
            // تحليل SHIP_ERP
            analyzeDatabase("SHIP_ERP", "ship_erp", "ship_erp_password");
            
            // تحليل IAS20251
            analyzeDatabase("IAS20251", "ias20251", "ys123");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * تحليل قاعدة بيانات
     */
    private static void analyzeDatabase(String dbName, String username, String password) {
        System.out.println("\n📊 تحليل قاعدة البيانات: " + dbName);
        System.out.println("=====================================");
        
        try {
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                username, 
                password
            );
            
            System.out.println("✅ تم الاتصال بـ " + dbName);
            
            // البحث عن جداول المجموعات والأصناف
            findRelevantTables(conn, dbName);
            
            conn.close();
            
        } catch (SQLException e) {
            System.err.println("❌ فشل الاتصال بـ " + dbName + ": " + e.getMessage());
        }
    }
    
    /**
     * البحث عن الجداول ذات الصلة
     */
    private static void findRelevantTables(Connection conn, String dbName) throws SQLException {
        DatabaseMetaData metaData = conn.getMetaData();
        String schema = dbName;
        
        System.out.println("\n🔍 البحث عن جداول المجموعات والأصناف...");
        
        // كلمات البحث
        String[] searchTerms = {"GROUP", "GRP", "ITEM", "ITM", "CATEGORY", "CAT"};
        
        ResultSet tables = metaData.getTables(null, schema, "%", new String[]{"TABLE"});
        
        boolean foundRelevant = false;
        while (tables.next()) {
            String tableName = tables.getString("TABLE_NAME");
            
            // التحقق من وجود كلمات البحث
            boolean isRelevant = false;
            for (String term : searchTerms) {
                if (tableName.toUpperCase().contains(term)) {
                    isRelevant = true;
                    break;
                }
            }
            
            if (isRelevant) {
                foundRelevant = true;
                analyzeTable(conn, schema, tableName);
            }
        }
        tables.close();
        
        if (!foundRelevant) {
            System.out.println("⚠️ لم يتم العثور على جداول ذات صلة");
            
            // عرض جميع الجداول
            System.out.println("\n📋 جميع الجداول الموجودة:");
            ResultSet allTables = metaData.getTables(null, schema, "%", new String[]{"TABLE"});
            int count = 0;
            while (allTables.next() && count < 20) {
                String tableName = allTables.getString("TABLE_NAME");
                System.out.println("  - " + tableName);
                count++;
            }
            if (count == 20) {
                System.out.println("  ... والمزيد");
            }
            allTables.close();
        }
    }
    
    /**
     * تحليل جدول محدد
     */
    private static void analyzeTable(Connection conn, String schema, String tableName) throws SQLException {
        System.out.println("\n📋 الجدول: " + tableName);
        System.out.println("-----------------------------------");
        
        // الحصول على عدد الصفوف
        try {
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName);
            if (rs.next()) {
                int rowCount = rs.getInt(1);
                System.out.println("📊 عدد الصفوف: " + rowCount);
            }
            rs.close();
            stmt.close();
        } catch (SQLException e) {
            System.out.println("⚠️ لا يمكن قراءة عدد الصفوف: " + e.getMessage());
        }
        
        // الحصول على الأعمدة
        DatabaseMetaData metaData = conn.getMetaData();
        ResultSet columns = metaData.getColumns(null, schema, tableName, null);
        
        System.out.println("📋 الأعمدة:");
        while (columns.next()) {
            String columnName = columns.getString("COLUMN_NAME");
            String dataType = columns.getString("TYPE_NAME");
            int columnSize = columns.getInt("COLUMN_SIZE");
            String nullable = columns.getString("IS_NULLABLE");
            
            System.out.printf("  %-20s %-15s %s\n", 
                columnName, 
                dataType + (columnSize > 0 ? "(" + columnSize + ")" : ""),
                nullable.equals("YES") ? "NULL" : "NOT NULL"
            );
        }
        columns.close();
        
        // عرض عينة من البيانات
        System.out.println("📄 عينة من البيانات:");
        try {
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(
                "SELECT * FROM " + tableName + " WHERE ROWNUM <= 3"
            );
            
            ResultSetMetaData rsmd = rs.getMetaData();
            int columnCount = rsmd.getColumnCount();
            
            // عرض أسماء الأعمدة
            System.out.print("  ");
            for (int i = 1; i <= columnCount; i++) {
                System.out.printf("%-15s ", rsmd.getColumnName(i));
            }
            System.out.println();
            
            // عرض البيانات
            while (rs.next()) {
                System.out.print("  ");
                for (int i = 1; i <= columnCount; i++) {
                    String value = rs.getString(i);
                    if (value != null && value.length() > 12) {
                        value = value.substring(0, 12) + "...";
                    }
                    System.out.printf("%-15s ", value != null ? value : "NULL");
                }
                System.out.println();
            }
            rs.close();
            stmt.close();
        } catch (SQLException e) {
            System.out.println("  ⚠️ لا يمكن قراءة البيانات: " + e.getMessage());
        }
    }
}
