package com.shipment.erp.repository;

import com.shipment.erp.model.ItemCategory;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * تنفيذ مستودع مجموعات الأصناف
 * Item Category Repository Implementation
 */
@Repository
public class ItemCategoryRepositoryImpl extends BaseRepositoryImpl<ItemCategory, Long> 
        implements ItemCategoryRepository {
    
    @Autowired
    private SessionFactory sessionFactory;
    
    public ItemCategoryRepositoryImpl() {
        super(ItemCategory.class);
    }
    
    @Override
    public Optional<ItemCategory> findByCode(String code) {
        Session session = sessionFactory.getCurrentSession();
        Query<ItemCategory> query = session.createQuery(
            "FROM ItemCategory c WHERE c.code = :code", ItemCategory.class);
        query.setParameter("code", code);
        return query.uniqueResultOptional();
    }
    
    @Override
    public Optional<ItemCategory> findByNameAr(String nameAr) {
        Session session = sessionFactory.getCurrentSession();
        Query<ItemCategory> query = session.createQuery(
            "FROM ItemCategory c WHERE c.nameAr = :nameAr", ItemCategory.class);
        query.setParameter("nameAr", nameAr);
        return query.uniqueResultOptional();
    }
    
    @Override
    public List<ItemCategory> findRootCategories() {
        Session session = sessionFactory.getCurrentSession();
        Query<ItemCategory> query = session.createQuery(
            "FROM ItemCategory c WHERE c.parentCategory IS NULL AND c.isActive = true " +
            "ORDER BY c.sortOrder, c.nameAr", ItemCategory.class);
        return query.getResultList();
    }
    
    @Override
    public List<ItemCategory> findByParentCategory(ItemCategory parentCategory) {
        Session session = sessionFactory.getCurrentSession();
        Query<ItemCategory> query = session.createQuery(
            "FROM ItemCategory c WHERE c.parentCategory = :parentCategory AND c.isActive = true " +
            "ORDER BY c.sortOrder, c.nameAr", ItemCategory.class);
        query.setParameter("parentCategory", parentCategory);
        return query.getResultList();
    }
    
    @Override
    public List<ItemCategory> findByIsActiveTrue() {
        Session session = sessionFactory.getCurrentSession();
        Query<ItemCategory> query = session.createQuery(
            "FROM ItemCategory c WHERE c.isActive = true ORDER BY c.level, c.sortOrder, c.nameAr", 
            ItemCategory.class);
        return query.getResultList();
    }
    
    @Override
    public List<ItemCategory> findByLevel(Integer level) {
        Session session = sessionFactory.getCurrentSession();
        Query<ItemCategory> query = session.createQuery(
            "FROM ItemCategory c WHERE c.level = :level AND c.isActive = true " +
            "ORDER BY c.sortOrder, c.nameAr", ItemCategory.class);
        query.setParameter("level", level);
        return query.getResultList();
    }
    
    @Override
    public List<ItemCategory> searchByText(String searchText) {
        Session session = sessionFactory.getCurrentSession();
        String searchPattern = "%" + searchText.toLowerCase() + "%";
        Query<ItemCategory> query = session.createQuery(
            "FROM ItemCategory c WHERE " +
            "(LOWER(c.nameAr) LIKE :searchText OR " +
            "LOWER(c.nameEn) LIKE :searchText OR " +
            "LOWER(c.code) LIKE :searchText OR " +
            "LOWER(c.description) LIKE :searchText) " +
            "AND c.isActive = true " +
            "ORDER BY c.level, c.sortOrder, c.nameAr", 
            ItemCategory.class);
        query.setParameter("searchText", searchPattern);
        return query.getResultList();
    }
    
    @Override
    public List<ItemCategory> findAllOrderBySortOrder() {
        Session session = sessionFactory.getCurrentSession();
        Query<ItemCategory> query = session.createQuery(
            "FROM ItemCategory c ORDER BY c.level, c.sortOrder, c.nameAr", ItemCategory.class);
        return query.getResultList();
    }
    
    @Override
    public List<ItemCategory> findByHasItemsTrue() {
        Session session = sessionFactory.getCurrentSession();
        Query<ItemCategory> query = session.createQuery(
            "FROM ItemCategory c WHERE c.hasItems = true AND c.isActive = true " +
            "ORDER BY c.level, c.sortOrder, c.nameAr", ItemCategory.class);
        return query.getResultList();
    }
    
    @Override
    public boolean existsByCode(String code) {
        Session session = sessionFactory.getCurrentSession();
        Query<Long> query = session.createQuery(
            "SELECT COUNT(c) FROM ItemCategory c WHERE c.code = :code", Long.class);
        query.setParameter("code", code);
        return query.uniqueResult() > 0;
    }
    
    @Override
    public boolean existsByNameAr(String nameAr) {
        Session session = sessionFactory.getCurrentSession();
        Query<Long> query = session.createQuery(
            "SELECT COUNT(c) FROM ItemCategory c WHERE c.nameAr = :nameAr", Long.class);
        query.setParameter("nameAr", nameAr);
        return query.uniqueResult() > 0;
    }
    
    @Override
    public long countByIsActiveTrue() {
        Session session = sessionFactory.getCurrentSession();
        Query<Long> query = session.createQuery(
            "SELECT COUNT(c) FROM ItemCategory c WHERE c.isActive = true", Long.class);
        return query.uniqueResult();
    }
    
    @Override
    public long countByParentCategory(ItemCategory parentCategory) {
        Session session = sessionFactory.getCurrentSession();
        Query<Long> query = session.createQuery(
            "SELECT COUNT(c) FROM ItemCategory c WHERE c.parentCategory = :parentCategory", Long.class);
        query.setParameter("parentCategory", parentCategory);
        return query.uniqueResult();
    }
    
    @Override
    public String getCategoryPath(Long categoryId) {
        Session session = sessionFactory.getCurrentSession();
        Query<String> query = session.createQuery(
            "SELECT c.path FROM ItemCategory c WHERE c.id = :categoryId", String.class);
        query.setParameter("categoryId", categoryId);
        return query.uniqueResult();
    }
}
