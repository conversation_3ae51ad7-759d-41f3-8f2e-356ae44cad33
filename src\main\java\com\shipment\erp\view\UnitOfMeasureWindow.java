package com.shipment.erp.view;

import com.shipment.erp.model.UnitOfMeasure;
import com.shipment.erp.service.UnitOfMeasureService;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableRowSorter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;

/**
 * نافذة إدارة وحدات القياس
 * Unit of Measure Management Window
 */
public class UnitOfMeasureWindow extends JFrame {
    
    private Font arabicFont;
    private UnitOfMeasureService unitService;
    
    // UI Components
    private JTable unitsTable;
    private DefaultTableModel tableModel;
    private TableRowSorter<DefaultTableModel> tableSorter;
    private JTextField searchField;
    private JButton addButton, editButton, deleteButton, refreshButton;
    private JLabel statusLabel;
    
    // Form Components
    private JTextField codeField, nameArField, nameEnField, symbolField;
    private JTextArea descriptionArea, notesArea;
    private JCheckBox isActiveCheck, isBaseUnitCheck;
    private JComboBox<UnitOfMeasure> baseUnitCombo;
    private JSpinner conversionFactorSpinner, sortOrderSpinner;
    
    public UnitOfMeasureWindow() {
        this.arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        // this.unitService = unitService; // سيتم حقنها لاحقاً
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        loadUnitsData();
        
        setTitle("إدارة وحدات القياس - Unit of Measure Management");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setExtendedState(JFrame.MAXIMIZED_BOTH);
        setMinimumSize(new Dimension(1000, 700));
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }
    
    private void initializeComponents() {
        // إعداد نموذج الجدول
        String[] columnNames = {
            "الكود", "الاسم العربي", "الاسم الإنجليزي", "الرمز", 
            "وحدة أساسية", "معامل التحويل", "نشط", "الترتيب"
        };
        
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // منع التعديل المباشر في الجدول
            }
            
            @Override
            public Class<?> getColumnClass(int columnIndex) {
                switch (columnIndex) {
                    case 5: return Double.class; // معامل التحويل
                    case 6: return Boolean.class; // نشط
                    case 7: return Integer.class; // الترتيب
                    default: return String.class;
                }
            }
        };
        
        unitsTable = new JTable(tableModel);
        unitsTable.setFont(arabicFont);
        unitsTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        unitsTable.setRowHeight(25);
        unitsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        unitsTable.setAutoCreateRowSorter(true);
        
        // إعداد مرشح الجدول
        tableSorter = new TableRowSorter<>(tableModel);
        unitsTable.setRowSorter(tableSorter);
        
        // حقل البحث
        searchField = new JTextField();
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.setPreferredSize(new Dimension(200, 30));
        
        // الأزرار
        addButton = createButton("إضافة جديد", "add", new Color(40, 167, 69));
        editButton = createButton("تعديل", "edit", new Color(255, 193, 7));
        deleteButton = createButton("حذف", "delete", new Color(220, 53, 69));
        refreshButton = createButton("تحديث", "refresh", new Color(0, 123, 255));
        
        editButton.setEnabled(false);
        deleteButton.setEnabled(false);
        
        // شريط الحالة
        statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);
        statusLabel.setBorder(new EmptyBorder(5, 10, 5, 10));
        
        // مكونات النموذج
        initializeFormComponents();
    }
    
    private void initializeFormComponents() {
        codeField = new JTextField();
        codeField.setFont(arabicFont);
        codeField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        nameArField = new JTextField();
        nameArField.setFont(arabicFont);
        nameArField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        nameEnField = new JTextField();
        nameEnField.setFont(arabicFont);
        nameEnField.setComponentOrientation(ComponentOrientation.LEFT_TO_RIGHT);
        
        symbolField = new JTextField();
        symbolField.setFont(arabicFont);
        symbolField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        descriptionArea = new JTextArea(3, 20);
        descriptionArea.setFont(arabicFont);
        descriptionArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        descriptionArea.setLineWrap(true);
        descriptionArea.setWrapStyleWord(true);
        
        notesArea = new JTextArea(3, 20);
        notesArea.setFont(arabicFont);
        notesArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        notesArea.setLineWrap(true);
        notesArea.setWrapStyleWord(true);
        
        isActiveCheck = new JCheckBox("نشط");
        isActiveCheck.setFont(arabicFont);
        isActiveCheck.setSelected(true);
        
        isBaseUnitCheck = new JCheckBox("وحدة أساسية");
        isBaseUnitCheck.setFont(arabicFont);
        
        baseUnitCombo = new JComboBox<>();
        baseUnitCombo.setFont(arabicFont);
        baseUnitCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        conversionFactorSpinner = new JSpinner(new SpinnerNumberModel(1.0, 0.001, 999999.0, 0.001));
        conversionFactorSpinner.setFont(arabicFont);
        
        sortOrderSpinner = new JSpinner(new SpinnerNumberModel(0, 0, 9999, 1));
        sortOrderSpinner.setFont(arabicFont);
    }
    
    private JButton createButton(String text, String actionCommand, Color backgroundColor) {
        JButton button = new JButton(text);
        button.setFont(arabicFont);
        button.setActionCommand(actionCommand);
        button.setBackground(backgroundColor);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(100, 35));
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        return button;
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // اللوحة العلوية - البحث والأزرار
        JPanel topPanel = createTopPanel();
        add(topPanel, BorderLayout.NORTH);
        
        // اللوحة الوسطى - الجدول
        JPanel centerPanel = createCenterPanel();
        add(centerPanel, BorderLayout.CENTER);
        
        // اللوحة السفلى - شريط الحالة
        JPanel bottomPanel = createBottomPanel();
        add(bottomPanel, BorderLayout.SOUTH);
    }
    
    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(new EmptyBorder(10, 10, 10, 10));
        panel.setBackground(new Color(248, 249, 250));
        
        // لوحة البحث
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchPanel.setOpaque(false);
        
        JLabel searchLabel = new JLabel("البحث:");
        searchLabel.setFont(arabicFont);
        
        searchPanel.add(searchLabel);
        searchPanel.add(searchField);
        
        // لوحة الأزرار
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        buttonPanel.setOpaque(false);
        
        buttonPanel.add(addButton);
        buttonPanel.add(editButton);
        buttonPanel.add(deleteButton);
        buttonPanel.add(refreshButton);
        
        panel.add(searchPanel, BorderLayout.EAST);
        panel.add(buttonPanel, BorderLayout.WEST);
        
        return panel;
    }
    
    private JPanel createCenterPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(new EmptyBorder(0, 10, 10, 10));
        
        // الجدول مع شريط التمرير
        JScrollPane scrollPane = new JScrollPane(unitsTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(BorderFactory.createTitledBorder("قائمة وحدات القياس"));
        
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createMatteBorder(1, 0, 0, 0, Color.LIGHT_GRAY));
        panel.setBackground(new Color(248, 249, 250));
        
        panel.add(statusLabel, BorderLayout.WEST);
        
        return panel;
    }
    
    private void setupEventHandlers() {
        // البحث في الجدول
        searchField.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyReleased(java.awt.event.KeyEvent e) {
                filterTable();
            }
        });
        
        // تحديد صف في الجدول
        unitsTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                boolean hasSelection = unitsTable.getSelectedRow() != -1;
                editButton.setEnabled(hasSelection);
                deleteButton.setEnabled(hasSelection);
            }
        });
        
        // النقر المزدوج على الجدول للتعديل
        unitsTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2 && unitsTable.getSelectedRow() != -1) {
                    editUnit();
                }
            }
        });
        
        // أحداث الأزرار
        addButton.addActionListener(e -> addUnit());
        editButton.addActionListener(e -> editUnit());
        deleteButton.addActionListener(e -> deleteUnit());
        refreshButton.addActionListener(e -> loadUnitsData());
    }
    
    private void filterTable() {
        String searchText = searchField.getText().trim();
        if (searchText.isEmpty()) {
            tableSorter.setRowFilter(null);
        } else {
            tableSorter.setRowFilter(RowFilter.regexFilter("(?i)" + searchText));
        }
        updateStatusLabel();
    }
    
    private void loadUnitsData() {
        try {
            // مسح البيانات الحالية
            tableModel.setRowCount(0);
            
            // تحميل البيانات الجديدة
            // List<UnitOfMeasure> units = unitService.findActiveUnits();
            // مؤقتاً سنضع بيانات تجريبية
            addSampleData();
            
            updateStatusLabel();
            statusLabel.setText("تم تحميل البيانات بنجاح");
            
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, 
                "خطأ في تحميل البيانات: " + e.getMessage(), 
                "خطأ", JOptionPane.ERROR_MESSAGE);
            statusLabel.setText("خطأ في تحميل البيانات");
        }
    }
    
    private void addSampleData() {
        // بيانات تجريبية
        Object[][] sampleData = {
            {"KG", "كيلوجرام", "Kilogram", "كجم", "نعم", 1.0, true, 1},
            {"G", "جرام", "Gram", "جم", "لا", 0.001, true, 2},
            {"L", "لتر", "Liter", "لتر", "نعم", 1.0, true, 3},
            {"ML", "مليلتر", "Milliliter", "مل", "لا", 0.001, true, 4},
            {"M", "متر", "Meter", "م", "نعم", 1.0, true, 5},
            {"CM", "سنتيمتر", "Centimeter", "سم", "لا", 0.01, true, 6}
        };
        
        for (Object[] row : sampleData) {
            tableModel.addRow(row);
        }
    }
    
    private void updateStatusLabel() {
        int totalRows = tableModel.getRowCount();
        int visibleRows = unitsTable.getRowCount();
        
        if (totalRows == visibleRows) {
            statusLabel.setText("إجمالي الوحدات: " + totalRows);
        } else {
            statusLabel.setText("عرض " + visibleRows + " من " + totalRows + " وحدة");
        }
    }
    
    private void addUnit() {
        UnitOfMeasureFormDialog dialog = new UnitOfMeasureFormDialog(this, "إضافة وحدة قياس جديدة", true);
        dialog.setVisible(true);
        
        if (dialog.isConfirmed()) {
            // إضافة الوحدة الجديدة
            loadUnitsData();
        }
    }
    
    private void editUnit() {
        int selectedRow = unitsTable.getSelectedRow();
        if (selectedRow == -1) return;
        
        // تحويل فهرس الصف المرئي إلى فهرس النموذج
        int modelRow = unitsTable.convertRowIndexToModel(selectedRow);
        
        UnitOfMeasureFormDialog dialog = new UnitOfMeasureFormDialog(this, "تعديل وحدة القياس", false);
        
        // تحميل البيانات في النموذج
        dialog.loadUnitData(modelRow, tableModel);
        dialog.setVisible(true);
        
        if (dialog.isConfirmed()) {
            // تحديث البيانات
            loadUnitsData();
        }
    }
    
    private void deleteUnit() {
        int selectedRow = unitsTable.getSelectedRow();
        if (selectedRow == -1) return;
        
        int modelRow = unitsTable.convertRowIndexToModel(selectedRow);
        String unitName = (String) tableModel.getValueAt(modelRow, 1);
        
        int result = JOptionPane.showConfirmDialog(this,
            "هل أنت متأكد من حذف وحدة القياس: " + unitName + "؟\n" +
            "هذا الإجراء لا يمكن التراجع عنه.",
            "تأكيد الحذف",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.WARNING_MESSAGE);
        
        if (result == JOptionPane.YES_OPTION) {
            try {
                // حذف الوحدة
                tableModel.removeRow(modelRow);
                statusLabel.setText("تم حذف وحدة القياس بنجاح");
                updateStatusLabel();
                
            } catch (Exception e) {
                JOptionPane.showMessageDialog(this,
                    "خطأ في حذف وحدة القياس: " + e.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
}
