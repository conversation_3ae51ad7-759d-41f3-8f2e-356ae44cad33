@echo off
chcp 65001 > nul
title تثبيت مكتبات الربط مع قواعد البيانات

echo ====================================
echo    تثبيت مكتبات الربط مع Oracle
echo    Installing Oracle Database Libraries
echo ====================================
echo.

echo [1/5] إنشاء مجلد المكتبات...
if not exist "lib" mkdir lib
cd lib

echo [2/5] تحميل مكتبة Oracle JDBC...
echo تحميل ojdbc11.jar...
echo يرجى تحميل المكتبة من:
echo https://download.oracle.com/otn-pub/otn_software/jdbc/233/ojdbc11.jar
echo.

echo [3/5] تحميل مكتبة Apache Commons...
echo تحميل commons-dbcp2.jar...
echo يرجى تحميل المكتبة من:
echo https://repo1.maven.org/maven2/org/apache/commons/commons-dbcp2/2.9.0/commons-dbcp2-2.9.0.jar
echo.

echo [4/5] تحميل مكتبة Apache Commons Pool...
echo تحميل commons-pool2.jar...
echo يرجى تحميل المكتبة من:
echo https://repo1.maven.org/maven2/org/apache/commons/commons-pool2/2.11.1/commons-pool2-2.11.1.jar
echo.

echo [5/5] تحميل مكتبة JSON...
echo تحميل json.jar...
echo يرجى تحميل المكتبة من:
echo https://repo1.maven.org/maven2/org/json/json/20230227/json-20230227.jar
echo.

echo ====================================
echo    ملاحظات مهمة
echo    Important Notes
echo ====================================
echo.
echo 1. ضع جميع ملفات .jar في مجلد lib
echo 2. تأكد من وجود Oracle Database متاح
echo 3. احصل على بيانات الاتصال الصحيحة
echo.
echo الملفات المطلوبة:
echo - ojdbc11.jar (Oracle JDBC Driver)
echo - commons-dbcp2-2.9.0.jar (Connection Pooling)
echo - commons-pool2-2.11.1.jar (Object Pooling)
echo - json-20230227.jar (JSON Processing)
echo.

cd ..

echo إنشاء ملف التجميع المحسن...
echo @echo off > compile_with_libs.bat
echo chcp 65001 ^> nul >> compile_with_libs.bat
echo echo تجميع النظام مع المكتبات... >> compile_with_libs.bat
echo javac -encoding UTF-8 -cp "lib/*;." *.java >> compile_with_libs.bat
echo if errorlevel 1 ( >> compile_with_libs.bat
echo     echo خطأ في التجميع! >> compile_with_libs.bat
echo     pause >> compile_with_libs.bat
echo     exit /b 1 >> compile_with_libs.bat
echo ) >> compile_with_libs.bat
echo echo تم التجميع بنجاح! >> compile_with_libs.bat
echo pause >> compile_with_libs.bat

echo إنشاء ملف التشغيل المحسن...
echo @echo off > run_with_libs.bat
echo chcp 65001 ^> nul >> run_with_libs.bat
echo echo تشغيل النظام مع المكتبات... >> run_with_libs.bat
echo java -Dfile.encoding=UTF-8 -Duser.language=ar -Duser.country=SA -cp "lib/*;." CompleteSystemTest >> run_with_libs.bat
echo pause >> run_with_libs.bat

echo.
echo ====================================
echo    تم إنشاء ملفات الإعداد
echo    Setup Files Created
echo ====================================
echo.
echo استخدم الملفات التالية:
echo 1. compile_with_libs.bat - للتجميع مع المكتبات
echo 2. run_with_libs.bat - للتشغيل مع المكتبات
echo.

pause
