import java.awt.ComponentOrientation;
import java.awt.Font;
import java.awt.GraphicsEnvironment;
import java.awt.font.TextAttribute;
import java.util.HashMap;
import java.util.Map;
import javax.swing.AbstractButton;
import javax.swing.JComponent;
import javax.swing.JLabel;
import javax.swing.JTextField;
import javax.swing.JTree;
import javax.swing.SwingConstants;
import javax.swing.tree.DefaultTreeCellRenderer;

/**
 * مدير النصوص العربية - حل شامل لمشاكل عرض النص العربي Arabic Text Manager - Comprehensive solution
 * for Arabic text display issues
 */
public class ArabicTextManager {

    private static ArabicTextManager instance;
    private Font arabicFont;
    private Font arabicBoldFont;
    private Font arabicLargeFont;
    private boolean fontsLoaded = false;

    // خطوط احتياطية للنص العربي
    private static final String[] ARABIC_FONT_NAMES = {"Tahoma", // الأفضل للعربية في Windows
            "Arial Unicode MS", // دعم شامل للعربية
            "Segoe UI", // خط Windows الحديث
            "DejaVu Sans", // خط مفتوح المصدر
            "Noto Sans Arabic", // خط Google
            "Traditional Arabic", // خط عربي تقليدي
            "Arial", // احتياطي
            "SansSerif" // احتياطي أخير
    };

    private ArabicTextManager() {
        initializeFonts();
        setupSystemProperties();
    }

    public static ArabicTextManager getInstance() {
        if (instance == null) {
            instance = new ArabicTextManager();
        }
        return instance;
    }

    /**
     * إعداد خصائص النظام للنص العربي
     */
    private void setupSystemProperties() {
        try {
            // إعداد ترميز UTF-8
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("sun.jnu.encoding", "UTF-8");

            // إعداد اللغة والمنطقة
            System.setProperty("user.language", "ar");
            System.setProperty("user.country", "SA");
            System.setProperty("user.region", "SA");

            // إعداد اتجاه النص
            System.setProperty("awt.useSystemAAFontSettings", "on");
            System.setProperty("swing.aatext", "true");

            // تفعيل مكافحة التشويش للنص
            System.setProperty("awt.useSystemAAFontSettings", "lcd");
            System.setProperty("swing.useSystemAAFontSettings", "lcd");

        } catch (Exception e) {
            System.err.println("خطأ في إعداد خصائص النظام: " + e.getMessage());
        }
    }

    /**
     * تهيئة الخطوط العربية
     */
    private void initializeFonts() {
        try {
            // البحث عن أفضل خط متاح للعربية
            Font bestFont = findBestArabicFont();

            if (bestFont != null) {
                arabicFont = bestFont.deriveFont(Font.PLAIN, 12f);
                arabicBoldFont = bestFont.deriveFont(Font.BOLD, 12f);
                arabicLargeFont = bestFont.deriveFont(Font.PLAIN, 14f);
                fontsLoaded = true;

                System.out.println("تم تحميل الخط العربي: " + bestFont.getFontName());
            } else {
                // استخدام خط افتراضي
                arabicFont = new Font("SansSerif", Font.PLAIN, 12);
                arabicBoldFont = new Font("SansSerif", Font.BOLD, 12);
                arabicLargeFont = new Font("SansSerif", Font.PLAIN, 14);

                System.out.println("تم استخدام الخط الافتراضي");
            }

            // تطبيق خصائص إضافية للخط
            enhanceFontRendering();

        } catch (Exception e) {
            System.err.println("خطأ في تهيئة الخطوط: " + e.getMessage());
            // خط احتياطي
            arabicFont = new Font("Dialog", Font.PLAIN, 12);
            arabicBoldFont = new Font("Dialog", Font.BOLD, 12);
            arabicLargeFont = new Font("Dialog", Font.PLAIN, 14);
        }
    }

    /**
     * البحث عن أفضل خط عربي متاح
     */
    private Font findBestArabicFont() {
        GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
        String[] availableFonts = ge.getAvailableFontFamilyNames();

        // اختبار النص العربي
        String testText = "النص العربي";

        for (String fontName : ARABIC_FONT_NAMES) {
            for (String availableFont : availableFonts) {
                if (availableFont.equals(fontName)) {
                    Font font = new Font(fontName, Font.PLAIN, 12);
                    if (font.canDisplayUpTo(testText) == -1) {
                        System.out.println("تم العثور على خط مناسب: " + fontName);
                        return font;
                    }
                }
            }
        }

        return null;
    }

    /**
     * تحسين عرض الخط
     */
    private void enhanceFontRendering() {
        try {
            // إعداد خصائص الخط المتقدمة
            Map<TextAttribute, Object> attributes = new HashMap<>();
            attributes.put(TextAttribute.KERNING, TextAttribute.KERNING_ON);
            attributes.put(TextAttribute.LIGATURES, TextAttribute.LIGATURES_ON);

            arabicFont = arabicFont.deriveFont(attributes);
            arabicBoldFont = arabicBoldFont.deriveFont(attributes);
            arabicLargeFont = arabicLargeFont.deriveFont(attributes);

        } catch (Exception e) {
            System.err.println("خطأ في تحسين عرض الخط: " + e.getMessage());
        }
    }

    /**
     * الحصول على الخط العربي الأساسي
     */
    public Font getArabicFont() {
        return arabicFont;
    }

    /**
     * الحصول على الخط العربي العريض
     */
    public Font getArabicBoldFont() {
        return arabicBoldFont;
    }

    /**
     * الحصول على الخط العربي الكبير
     */
    public Font getArabicLargeFont() {
        return arabicLargeFont;
    }

    /**
     * الحصول على خط بحجم مخصص
     */
    public Font getArabicFont(float size) {
        return arabicFont.deriveFont(size);
    }

    /**
     * الحصول على خط بنمط وحجم مخصص
     */
    public Font getArabicFont(int style, float size) {
        return arabicFont.deriveFont(style, size);
    }

    /**
     * إعداد مكون Swing للنص العربي
     */
    public void setupArabicComponent(JComponent component) {
        if (component == null)
            return;

        try {
            // إعداد الخط
            component.setFont(arabicFont);

            // إعداد اتجاه المكون
            component.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

            // إعداد محاذاة النص للتسميات
            if (component instanceof JLabel) {
                JLabel label = (JLabel) component;
                label.setHorizontalAlignment(SwingConstants.RIGHT);
                label.setHorizontalTextPosition(SwingConstants.RIGHT);
            }

            // إعداد محاذاة النص للأزرار
            if (component instanceof AbstractButton) {
                AbstractButton button = (AbstractButton) component;
                button.setHorizontalAlignment(SwingConstants.CENTER);
                button.setHorizontalTextPosition(SwingConstants.CENTER);
            }

            // إعداد حقول النص
            if (component instanceof JTextField) {
                JTextField textField = (JTextField) component;
                textField.setHorizontalAlignment(SwingConstants.RIGHT);
            }

        } catch (Exception e) {
            System.err.println("خطأ في إعداد المكون العربي: " + e.getMessage());
        }
    }

    /**
     * إعداد شجرة القوائم للنص العربي
     */
    public void setupArabicTree(JTree tree) {
        if (tree == null)
            return;

        try {
            // إعداد الخط
            tree.setFont(arabicFont);

            // إعداد اتجاه الشجرة
            tree.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

            // إعداد عارض الخلايا المخصص
            DefaultTreeCellRenderer renderer = new DefaultTreeCellRenderer();
            renderer.setFont(arabicFont);
            renderer.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            tree.setCellRenderer(renderer);

            // إعداد ارتفاع الصفوف
            tree.setRowHeight(20);

        } catch (Exception e) {
            System.err.println("خطأ في إعداد الشجرة العربية: " + e.getMessage());
        }
    }



    /**
     * اختبار عرض النص العربي
     */
    public boolean testArabicDisplay() {
        try {
            String testText = "اختبار النص العربي - إدارة الأصناف";
            return arabicFont.canDisplayUpTo(testText) == -1;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * طباعة معلومات الخطوط المتاحة
     */
    public void printAvailableFonts() {
        System.out.println("=== الخطوط المتاحة في النظام ===");
        GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
        String[] fonts = ge.getAvailableFontFamilyNames();

        for (String font : fonts) {
            if (font.toLowerCase().contains("arab") || font.toLowerCase().contains("tahoma")
                    || font.toLowerCase().contains("arial")) {
                System.out.println("خط متاح: " + font);
            }
        }

        System.out.println("=== الخط المستخدم حالياً ===");
        System.out.println("اسم الخط: " + arabicFont.getFontName());
        System.out.println("عائلة الخط: " + arabicFont.getFamily());
        System.out.println("حجم الخط: " + arabicFont.getSize());
        System.out.println("اختبار العرض: " + (testArabicDisplay() ? "نجح" : "فشل"));
    }
}
