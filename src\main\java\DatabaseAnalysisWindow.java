import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * نافذة تحليل قاعدة البيانات IAS20251
 * Database Analysis Window for IAS20251
 */
public class DatabaseAnalysisWindow extends JFrame {
    
    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 12);
    
    private JTextArea resultArea;
    private JProgressBar progressBar;
    private Connection connection;
    
    public DatabaseAnalysisWindow() {
        initializeComponents();
        connectToDatabase();
    }
    
    private void initializeComponents() {
        setTitle("تحليل قاعدة البيانات IAS20251 - Database Analysis");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(1000, 700);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // لوحة الأزرار
        JPanel buttonPanel = createButtonPanel();
        mainPanel.add(buttonPanel, BorderLayout.NORTH);
        
        // منطقة النتائج
        resultArea = new JTextArea();
        resultArea.setFont(arabicFont);
        resultArea.setEditable(false);
        resultArea.setBackground(Color.BLACK);
        resultArea.setForeground(Color.GREEN);
        
        JScrollPane scrollPane = new JScrollPane(resultArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        mainPanel.add(scrollPane, BorderLayout.CENTER);
        
        // شريط التقدم
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setFont(arabicFont);
        mainPanel.add(progressBar, BorderLayout.SOUTH);
        
        add(mainPanel);
    }
    
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 4, 10, 10));
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(),
            "أدوات التحليل",
            TitledBorder.RIGHT,
            TitledBorder.TOP,
            arabicBoldFont
        ));
        
        // الأزرار
        JButton allTablesBtn = createButton("📋 جميع الجداول", e -> analyzeAllTables());
        JButton importantTablesBtn = createButton("🎯 الجداول المهمة", e -> analyzeImportantTables());
        JButton relationshipsBtn = createButton("🔗 العلاقات", e -> analyzeRelationships());
        JButton statisticsBtn = createButton("📊 الإحصائيات", e -> analyzeStatistics());
        
        JButton indexesBtn = createButton("📇 الفهارس", e -> analyzeIndexes());
        JButton viewsBtn = createButton("👁️ المشاهد", e -> analyzeViews());
        JButton sequencesBtn = createButton("🔢 المتسلسلات", e -> analyzeSequences());
        JButton clearBtn = createButton("🗑️ مسح", e -> resultArea.setText(""));
        
        panel.add(allTablesBtn);
        panel.add(importantTablesBtn);
        panel.add(relationshipsBtn);
        panel.add(statisticsBtn);
        panel.add(indexesBtn);
        panel.add(viewsBtn);
        panel.add(sequencesBtn);
        panel.add(clearBtn);
        
        return panel;
    }
    
    private JButton createButton(String text, ActionListener action) {
        JButton button = new JButton(text);
        button.setFont(arabicBoldFont);
        button.addActionListener(action);
        return button;
    }
    
    private void connectToDatabase() {
        try {
            String url = "*************************************";
            String username = "ias20251";
            String password = "ys123";
            
            connection = DriverManager.getConnection(url, username, password);
            appendResult("✅ تم الاتصال بقاعدة البيانات IAS20251 بنجاح!\n");
            appendResult("🔗 الاتصال: " + url + "\n");
            appendResult("👤 المستخدم: " + username + "\n\n");
            
        } catch (SQLException e) {
            appendResult("❌ فشل في الاتصال بقاعدة البيانات: " + e.getMessage() + "\n");
        }
    }
    
    private void analyzeAllTables() {
        appendResult("📋 تحليل جميع الجداول في IAS20251...\n");
        appendResult("=" + "=".repeat(50) + "\n");
        
        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                String query = """
                    SELECT table_name, num_rows, blocks, avg_row_len
                    FROM all_tables 
                    WHERE owner = 'IAS20251' 
                    ORDER BY table_name
                """;
                
                try (PreparedStatement stmt = connection.prepareStatement(query);
                     ResultSet rs = stmt.executeQuery()) {
                    
                    int count = 0;
                    publish(String.format("%-30s %15s %10s %15s\n", "اسم الجدول", "عدد الصفوف", "الكتل", "متوسط الطول"));
                    publish("-".repeat(80) + "\n");
                    
                    while (rs.next()) {
                        String tableName = rs.getString("table_name");
                        String numRows = rs.getString("num_rows");
                        String blocks = rs.getString("blocks");
                        String avgRowLen = rs.getString("avg_row_len");
                        
                        publish(String.format("%-30s %15s %10s %15s\n",
                            tableName,
                            numRows != null ? String.format("%,d", Integer.parseInt(numRows)) : "غير محدد",
                            blocks != null ? blocks : "-",
                            avgRowLen != null ? avgRowLen : "-"
                        ));
                        
                        count++;
                        progressBar.setValue((count * 100) / 200); // تقدير
                    }
                    
                    publish("\n📊 إجمالي الجداول: " + count + "\n\n");
                }
                
                return null;
            }
            
            @Override
            protected void process(List<String> chunks) {
                for (String chunk : chunks) {
                    appendResult(chunk);
                }
            }
            
            @Override
            protected void done() {
                progressBar.setValue(0);
                appendResult("✅ تم إكمال تحليل جميع الجداول!\n\n");
            }
        };
        
        worker.execute();
    }
    
    private void analyzeImportantTables() {
        appendResult("🎯 تحليل الجداول المهمة...\n");
        appendResult("=" + "=".repeat(30) + "\n");
        
        String[] importantTables = {
            "IAS_ITM_MST", "IAS_ITM_DTL", "IAS_CUSTOMER", "IAS_VENDOR", 
            "IAS_INVOICE", "IAS_RECEIPT", "IAS_PAYMENT", "IAS_STOCK"
        };
        
        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                for (int i = 0; i < importantTables.length; i++) {
                    String tableName = importantTables[i];
                    publish("\n🔍 جدول: " + tableName + "\n");
                    publish("-".repeat(40) + "\n");
                    
                    try {
                        // عدد الصفوف
                        String countQuery = String.format("SELECT COUNT(*) as total FROM IAS20251.%s", tableName);
                        try (PreparedStatement stmt = connection.prepareStatement(countQuery);
                             ResultSet rs = stmt.executeQuery()) {
                            
                            if (rs.next()) {
                                int total = rs.getInt("total");
                                publish(String.format("📊 عدد الصفوف: %,d\n", total));
                            }
                        }
                        
                        // عدد الأعمدة
                        String columnsQuery = """
                            SELECT COUNT(*) as column_count 
                            FROM all_tab_columns 
                            WHERE owner = 'IAS20251' AND table_name = ?
                        """;
                        
                        try (PreparedStatement stmt = connection.prepareStatement(columnsQuery)) {
                            stmt.setString(1, tableName);
                            try (ResultSet rs = stmt.executeQuery()) {
                                if (rs.next()) {
                                    int columnCount = rs.getInt("column_count");
                                    publish("📋 عدد الأعمدة: " + columnCount + "\n");
                                }
                            }
                        }
                        
                    } catch (SQLException e) {
                        publish("❌ الجدول غير موجود: " + e.getMessage() + "\n");
                    }
                    
                    progressBar.setValue(((i + 1) * 100) / importantTables.length);
                }
                
                return null;
            }
            
            @Override
            protected void process(List<String> chunks) {
                for (String chunk : chunks) {
                    appendResult(chunk);
                }
            }
            
            @Override
            protected void done() {
                progressBar.setValue(0);
                appendResult("✅ تم إكمال تحليل الجداول المهمة!\n\n");
            }
        };
        
        worker.execute();
    }
    
    private void analyzeRelationships() {
        appendResult("🔗 تحليل العلاقات والمفاتيح الخارجية...\n");
        appendResult("=" + "=".repeat(40) + "\n");
        
        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                String query = """
                    SELECT 
                        a.constraint_name,
                        a.table_name,
                        a.column_name,
                        c_pk.table_name r_table_name,
                        c_pk.column_name r_column_name
                    FROM all_cons_columns a
                    JOIN all_constraints c ON a.owner = c.owner AND a.constraint_name = c.constraint_name
                    JOIN all_constraints c_pk ON c.r_owner = c_pk.owner AND c.r_constraint_name = c_pk.constraint_name
                    JOIN all_cons_columns c_pk ON c_pk.owner = c_pk.owner AND c_pk.constraint_name = c_pk.constraint_name
                    WHERE c.constraint_type = 'R'
                    AND a.owner = 'IAS20251'
                    ORDER BY a.table_name, a.constraint_name
                """;
                
                try (PreparedStatement stmt = connection.prepareStatement(query);
                     ResultSet rs = stmt.executeQuery()) {
                    
                    publish(String.format("%-20s %-15s -> %-20s %-15s\n", "الجدول", "العمود", "يشير إلى جدول", "يشير إلى عمود"));
                    publish("-".repeat(80) + "\n");
                    
                    int count = 0;
                    while (rs.next()) {
                        publish(String.format("%-20s %-15s -> %-20s %-15s\n",
                            rs.getString("table_name"),
                            rs.getString("column_name"),
                            rs.getString("r_table_name"),
                            rs.getString("r_column_name")
                        ));
                        count++;
                    }
                    
                    publish("\n📊 إجمالي المفاتيح الخارجية: " + count + "\n");
                }
                
                return null;
            }
            
            @Override
            protected void process(List<String> chunks) {
                for (String chunk : chunks) {
                    appendResult(chunk);
                }
            }
            
            @Override
            protected void done() {
                appendResult("✅ تم إكمال تحليل العلاقات!\n\n");
            }
        };
        
        worker.execute();
    }
    
    private void analyzeStatistics() {
        appendResult("📊 تحليل إحصائيات قاعدة البيانات...\n");
        appendResult("=" + "=".repeat(35) + "\n");
        
        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                // إحصائيات عامة
                publish("### إحصائيات عامة:\n\n");
                
                String[] tables = {"IAS_ITM_MST", "IAS_ITM_DTL", "IAS_CUSTOMER", "IAS_VENDOR", "IAS_INVOICE"};
                
                for (String tableName : tables) {
                    try {
                        String query = String.format("SELECT COUNT(*) as total_rows FROM IAS20251.%s", tableName);
                        try (PreparedStatement stmt = connection.prepareStatement(query);
                             ResultSet rs = stmt.executeQuery()) {
                            
                            if (rs.next()) {
                                int totalRows = rs.getInt("total_rows");
                                publish(String.format("%-20s: %,d صف\n", tableName, totalRows));
                            }
                        }
                    } catch (SQLException e) {
                        publish(String.format("%-20s: غير متاح\n", tableName));
                    }
                }
                
                return null;
            }
            
            @Override
            protected void process(List<String> chunks) {
                for (String chunk : chunks) {
                    appendResult(chunk);
                }
            }
            
            @Override
            protected void done() {
                appendResult("✅ تم إكمال تحليل الإحصائيات!\n\n");
            }
        };
        
        worker.execute();
    }
    
    private void analyzeIndexes() {
        appendResult("📇 تحليل الفهارس...\n");
        // تنفيذ مشابه للدوال الأخرى
        appendResult("قيد التطوير...\n\n");
    }
    
    private void analyzeViews() {
        appendResult("👁️ تحليل المشاهد...\n");
        // تنفيذ مشابه للدوال الأخرى
        appendResult("قيد التطوير...\n\n");
    }
    
    private void analyzeSequences() {
        appendResult("🔢 تحليل المتسلسلات...\n");
        // تنفيذ مشابه للدوال الأخرى
        appendResult("قيد التطوير...\n\n");
    }
    
    private void appendResult(String text) {
        SwingUtilities.invokeLater(() -> {
            resultArea.append(text);
            resultArea.setCaretPosition(resultArea.getDocument().getLength());
        });
    }
    
    @Override
    public void dispose() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        super.dispose();
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new DatabaseAnalysisWindow().setVisible(true);
        });
    }
}
