import java.sql.*;

/**
 * إنشاء Database Link حقيقي
 */
public class CreateRealDatabaseLink {
    
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            System.out.println("🔗 إنشاء Database Link حقيقي");
            System.out.println("=====================================");
            
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            System.out.println("✅ تم الاتصال بـ SHIP_ERP");
            
            Statement stmt = conn.createStatement();
            
            // حذف Database Link القديم
            try {
                stmt.execute("DROP DATABASE LINK IAS20251_LINK");
                System.out.println("🗑️ تم حذف Database Link القديم");
            } catch (SQLException e) {
                System.out.println("ℹ️ Database Link غير موجود مسبقاً");
            }
            
            // إنشاء Database Link جديد
            String createLinkSQL = """
                CREATE DATABASE LINK IAS20251_LINK
                CONNECT TO ias20251 IDENTIFIED BY ys123
                USING 'localhost:1521/orcl'
            """;
            
            stmt.execute(createLinkSQL);
            System.out.println("✅ تم إنشاء Database Link: IAS20251_LINK");
            
            // اختبار Database Link
            System.out.println("\n🧪 اختبار Database Link...");
            
            try {
                // اختبار جدول المجموعات الرئيسية
                ResultSet rs1 = stmt.executeQuery("SELECT COUNT(*) FROM GROUP_DETAILS@IAS20251_LINK");
                if (rs1.next()) {
                    int count = rs1.getInt(1);
                    System.out.println("✅ GROUP_DETAILS: " + count + " مجموعة رئيسية");
                }
                rs1.close();
                
                // اختبار جدول المجموعات الفرعية
                ResultSet rs2 = stmt.executeQuery("SELECT COUNT(*) FROM IAS_MAINSUB_GRP_DTL@IAS20251_LINK");
                if (rs2.next()) {
                    int count = rs2.getInt(1);
                    System.out.println("✅ IAS_MAINSUB_GRP_DTL: " + count + " مجموعة فرعية");
                }
                rs2.close();
                
                // اختبار جدول الأصناف
                ResultSet rs3 = stmt.executeQuery("SELECT COUNT(*) FROM IAS_ITM_MST@IAS20251_LINK");
                if (rs3.next()) {
                    int count = rs3.getInt(1);
                    System.out.println("✅ IAS_ITM_MST: " + count + " صنف");
                }
                rs3.close();
                
                // عرض عينة من البيانات
                System.out.println("\n📄 عينة من البيانات:");
                
                System.out.println("\n📋 المجموعات الرئيسية:");
                ResultSet rs4 = stmt.executeQuery(
                    "SELECT G_CODE, G_A_NAME, G_E_NAME FROM GROUP_DETAILS@IAS20251_LINK WHERE ROWNUM <= 5"
                );
                while (rs4.next()) {
                    System.out.println("  " + rs4.getString("G_CODE") + " - " + 
                                     rs4.getString("G_A_NAME") + " (" + rs4.getString("G_E_NAME") + ")");
                }
                rs4.close();
                
                System.out.println("\n📋 المجموعات الفرعية:");
                ResultSet rs5 = stmt.executeQuery(
                    "SELECT G_CODE, MNG_CODE, MNG_A_NAME FROM IAS_MAINSUB_GRP_DTL@IAS20251_LINK WHERE ROWNUM <= 5"
                );
                while (rs5.next()) {
                    System.out.println("  " + rs5.getString("G_CODE") + "/" + rs5.getString("MNG_CODE") + 
                                     " - " + rs5.getString("MNG_A_NAME"));
                }
                rs5.close();
                
                System.out.println("\n📋 الأصناف:");
                ResultSet rs6 = stmt.executeQuery(
                    "SELECT I_CODE, I_A_NAME, G_CODE FROM IAS_ITM_MST@IAS20251_LINK WHERE ROWNUM <= 5"
                );
                while (rs6.next()) {
                    System.out.println("  " + rs6.getString("I_CODE") + " - " + 
                                     rs6.getString("I_A_NAME") + " (مجموعة: " + rs6.getString("G_CODE") + ")");
                }
                rs6.close();
                
                System.out.println("\n🎉 Database Link يعمل بنجاح!");
                
            } catch (SQLException e) {
                System.err.println("❌ خطأ في اختبار Database Link: " + e.getMessage());
                e.printStackTrace();
            }
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ عام: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
