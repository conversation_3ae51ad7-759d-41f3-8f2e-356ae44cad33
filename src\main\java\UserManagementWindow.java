import javax.swing.*;
import javax.swing.table.*;
import java.awt.*;
import java.awt.event.*;
import java.util.*;
import java.util.List;
import java.text.SimpleDateFormat;

/**
 * نافذة إدارة المستخدمين الشاملة والمتقدمة
 * Comprehensive and Advanced User Management Window
 */
public class UserManagementWindow extends JDialog {
    
    private Font arabicFont;
    private JTable usersTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JComboBox<String> roleFilterCombo;
    private JComboBox<String> statusFilterCombo;
    private JComboBox<String> departmentFilterCombo;
    
    // بيانات المستخدمين (في التطبيق الحقيقي ستأتي من قاعدة البيانات)
    private List<User> usersList;
    
    public UserManagementWindow(JFrame parent) {
        super(parent, "نظام إدارة المستخدمين المتقدم", true);
        
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        initializeData();
        initializeComponents();
        setupLayout();
        loadUsersData();
        
        setSize(1400, 900);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(true);
        setMinimumSize(new Dimension(1200, 700));
    }
    
    /**
     * تهيئة البيانات التجريبية
     */
    private void initializeData() {
        usersList = new ArrayList<>();
        
        // إضافة مستخدمين تجريبيين
        usersList.add(new User("U001", "أحمد محمد علي", "ahmed.ali", "<EMAIL>", 
            "مدير النظام", "تقنية المعلومات", "نشط", "2023-01-15", "2024-12-31", "الرياض"));
        usersList.add(new User("U002", "فاطمة أحمد", "fatima.ahmed", "<EMAIL>", 
            "محاسب", "المحاسبة", "نشط", "2023-03-20", "2024-12-31", "جدة"));
        usersList.add(new User("U003", "محمد عبدالله", "mohammed.abdullah", "<EMAIL>", 
            "مشرف الشحن", "العمليات", "معلق", "2023-05-10", "2024-12-31", "الدمام"));
        usersList.add(new User("U004", "نورا سالم", "nora.salem", "<EMAIL>", 
            "موظف إدخال بيانات", "العمليات", "نشط", "2023-07-01", "2024-12-31", "الرياض"));
        usersList.add(new User("U005", "خالد الأحمد", "khalid.ahmed", "<EMAIL>", 
            "مدير المبيعات", "المبيعات", "غير نشط", "2022-12-01", "2024-06-30", "جدة"));
    }
    
    /**
     * تهيئة المكونات
     */
    private void initializeComponents() {
        // إعداد الجدول
        String[] columnNames = {
            "رقم المستخدم", "الاسم الكامل", "اسم المستخدم", "البريد الإلكتروني", 
            "الدور", "القسم", "الحالة", "تاريخ الإنشاء", "تاريخ الانتهاء", "المدينة", "العمليات"
        };
        
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return column == 10; // عمود العمليات فقط قابل للتفاعل
            }
        };
        
        usersTable = new JTable(tableModel);
        usersTable.setFont(arabicFont);
        usersTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        usersTable.setRowHeight(35);
        usersTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        // تخصيص عرض الأعمدة
        usersTable.getColumnModel().getColumn(0).setPreferredWidth(100); // رقم المستخدم
        usersTable.getColumnModel().getColumn(1).setPreferredWidth(150); // الاسم
        usersTable.getColumnModel().getColumn(2).setPreferredWidth(120); // اسم المستخدم
        usersTable.getColumnModel().getColumn(3).setPreferredWidth(180); // البريد
        usersTable.getColumnModel().getColumn(4).setPreferredWidth(120); // الدور
        usersTable.getColumnModel().getColumn(5).setPreferredWidth(100); // القسم
        usersTable.getColumnModel().getColumn(6).setPreferredWidth(80);  // الحالة
        usersTable.getColumnModel().getColumn(7).setPreferredWidth(100); // تاريخ الإنشاء
        usersTable.getColumnModel().getColumn(8).setPreferredWidth(100); // تاريخ الانتهاء
        usersTable.getColumnModel().getColumn(9).setPreferredWidth(80);  // المدينة
        usersTable.getColumnModel().getColumn(10).setPreferredWidth(150); // العمليات
        
        // إعداد عمود العمليات
        usersTable.getColumnModel().getColumn(10).setCellRenderer(new ButtonRenderer());
        usersTable.getColumnModel().getColumn(10).setCellEditor(new ButtonEditor());
        
        // حقل البحث
        searchField = new JTextField();
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.addKeyListener(new KeyAdapter() {
            @Override
            public void keyReleased(KeyEvent e) {
                filterUsers();
            }
        });
        
        // قوائم التصفية
        roleFilterCombo = new JComboBox<>(new String[]{
            "جميع الأدوار", "مدير النظام", "محاسب", "مشرف الشحن", "موظف إدخال بيانات", "مدير المبيعات"
        });
        setupComboBox(roleFilterCombo);
        
        statusFilterCombo = new JComboBox<>(new String[]{
            "جميع الحالات", "نشط", "غير نشط", "معلق"
        });
        setupComboBox(statusFilterCombo);
        
        departmentFilterCombo = new JComboBox<>(new String[]{
            "جميع الأقسام", "تقنية المعلومات", "المحاسبة", "العمليات", "المبيعات", "الموارد البشرية"
        });
        setupComboBox(departmentFilterCombo);
        
        // إضافة مستمعات للتصفية
        roleFilterCombo.addActionListener(e -> filterUsers());
        statusFilterCombo.addActionListener(e -> filterUsers());
        departmentFilterCombo.addActionListener(e -> filterUsers());
    }
    
    /**
     * إعداد ComboBox
     */
    private void setupComboBox(JComboBox<String> comboBox) {
        comboBox.setFont(arabicFont);
        comboBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        ((JLabel) comboBox.getRenderer()).setHorizontalAlignment(SwingConstants.RIGHT);
    }
    
    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // الرأس
        add(createHeaderPanel(), BorderLayout.NORTH);
        
        // المحتوى الرئيسي
        add(createMainPanel(), BorderLayout.CENTER);
        
        // الأزرار السفلية
        add(createBottomPanel(), BorderLayout.SOUTH);
    }
    
    /**
     * إنشاء لوحة الرأس
     */
    private JPanel createHeaderPanel() {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(52, 152, 219));
        headerPanel.setBorder(BorderFactory.createEmptyBorder(20, 30, 20, 30));
        headerPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel titleLabel = new JLabel("نظام إدارة المستخدمين المتقدم");
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 24));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel subtitleLabel = new JLabel("إدارة شاملة لجميع مستخدمي النظام مع صلاحيات متقدمة");
        subtitleLabel.setFont(new Font("Tahoma", Font.PLAIN, 14));
        subtitleLabel.setForeground(new Color(236, 240, 241));
        subtitleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JPanel textPanel = new JPanel(new GridLayout(2, 1, 0, 5));
        textPanel.setOpaque(false);
        textPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        textPanel.add(titleLabel);
        textPanel.add(subtitleLabel);
        
        headerPanel.add(textPanel, BorderLayout.CENTER);
        
        return headerPanel;
    }
    
    /**
     * إنشاء اللوحة الرئيسية
     */
    private JPanel createMainPanel() {
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // لوحة البحث والتصفية
        mainPanel.add(createSearchAndFilterPanel(), BorderLayout.NORTH);
        
        // جدول المستخدمين
        JScrollPane scrollPane = new JScrollPane(usersTable);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(BorderFactory.createTitledBorder("قائمة المستخدمين"));
        mainPanel.add(scrollPane, BorderLayout.CENTER);
        
        return mainPanel;
    }
    
    /**
     * إنشاء لوحة البحث والتصفية
     */
    private JPanel createSearchAndFilterPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));
        
        // لوحة البحث
        JPanel searchPanel = new JPanel(new BorderLayout(10, 0));
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel searchLabel = new JLabel("البحث:");
        searchLabel.setFont(arabicFont);
        searchLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        searchPanel.add(searchLabel, BorderLayout.EAST);
        searchPanel.add(searchField, BorderLayout.CENTER);
        
        // لوحة التصفية
        JPanel filterPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 0));
        filterPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        filterPanel.add(createLabel("الدور:"));
        filterPanel.add(roleFilterCombo);
        filterPanel.add(createLabel("الحالة:"));
        filterPanel.add(statusFilterCombo);
        filterPanel.add(createLabel("القسم:"));
        filterPanel.add(departmentFilterCombo);
        
        panel.add(searchPanel, BorderLayout.NORTH);
        panel.add(filterPanel, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }

    /**
     * إنشاء لوحة الأزرار السفلية
     */
    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(new Color(248, 249, 250));
        panel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createMatteBorder(1, 0, 0, 0, new Color(222, 226, 230)),
            BorderFactory.createEmptyBorder(15, 20, 15, 20)
        ));

        // الأزرار الرئيسية
        JPanel mainButtonsPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 0));
        mainButtonsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainButtonsPanel.setOpaque(false);

        JButton addUserBtn = createStyledButton("إضافة مستخدم", new Color(40, 167, 69), Color.WHITE);
        addUserBtn.addActionListener(e -> showAddUserDialog());

        JButton editUserBtn = createStyledButton("تعديل مستخدم", new Color(0, 123, 255), Color.WHITE);
        editUserBtn.addActionListener(e -> editSelectedUser());

        JButton deleteUserBtn = createStyledButton("حذف مستخدم", new Color(220, 53, 69), Color.WHITE);
        deleteUserBtn.addActionListener(e -> deleteSelectedUser());

        JButton permissionsBtn = createStyledButton("إدارة الصلاحيات", new Color(111, 66, 193), Color.WHITE);
        permissionsBtn.addActionListener(e -> manageUserPermissions());

        JButton exportBtn = createStyledButton("تصدير البيانات", new Color(23, 162, 184), Color.WHITE);
        exportBtn.addActionListener(e -> exportUsersData());

        mainButtonsPanel.add(addUserBtn);
        mainButtonsPanel.add(editUserBtn);
        mainButtonsPanel.add(deleteUserBtn);
        mainButtonsPanel.add(permissionsBtn);
        mainButtonsPanel.add(exportBtn);

        // معلومات إحصائية
        JPanel statsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 0));
        statsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statsPanel.setOpaque(false);

        JLabel statsLabel = new JLabel("إجمالي المستخدمين: " + usersList.size());
        statsLabel.setFont(arabicFont);
        statsLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statsPanel.add(statsLabel);

        panel.add(mainButtonsPanel, BorderLayout.CENTER);
        panel.add(statsPanel, BorderLayout.WEST);

        return panel;
    }

    /**
     * إنشاء زر مُنسق
     */
    private JButton createStyledButton(String text, Color bgColor, Color fgColor) {
        JButton button = new JButton(text);
        button.setFont(arabicFont);
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        button.setBackground(bgColor);
        button.setForeground(fgColor);
        button.setPreferredSize(new Dimension(140, 40));
        button.setFocusPainted(false);
        button.setBorderPainted(false);

        // تأثيرات التفاعل
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            Color originalBg = bgColor;

            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(bgColor.darker());
            }

            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(originalBg);
            }
        });

        return button;
    }

    /**
     * تحميل بيانات المستخدمين في الجدول
     */
    private void loadUsersData() {
        tableModel.setRowCount(0);

        for (User user : usersList) {
            Object[] rowData = {
                user.getUserId(),
                user.getFullName(),
                user.getUsername(),
                user.getEmail(),
                user.getRole(),
                user.getDepartment(),
                user.getStatus(),
                user.getCreatedDate(),
                user.getExpiryDate(),
                user.getCity(),
                "عمليات" // سيتم استبداله بأزرار
            };
            tableModel.addRow(rowData);
        }
    }

    /**
     * تصفية المستخدمين
     */
    private void filterUsers() {
        String searchText = searchField.getText().toLowerCase();
        String roleFilter = (String) roleFilterCombo.getSelectedItem();
        String statusFilter = (String) statusFilterCombo.getSelectedItem();
        String departmentFilter = (String) departmentFilterCombo.getSelectedItem();

        tableModel.setRowCount(0);

        for (User user : usersList) {
            boolean matches = true;

            // تصفية البحث النصي
            if (!searchText.isEmpty()) {
                matches = user.getFullName().toLowerCase().contains(searchText) ||
                         user.getUsername().toLowerCase().contains(searchText) ||
                         user.getEmail().toLowerCase().contains(searchText);
            }

            // تصفية الدور
            if (matches && !roleFilter.equals("جميع الأدوار")) {
                matches = user.getRole().equals(roleFilter);
            }

            // تصفية الحالة
            if (matches && !statusFilter.equals("جميع الحالات")) {
                matches = user.getStatus().equals(statusFilter);
            }

            // تصفية القسم
            if (matches && !departmentFilter.equals("جميع الأقسام")) {
                matches = user.getDepartment().equals(departmentFilter);
            }

            if (matches) {
                Object[] rowData = {
                    user.getUserId(),
                    user.getFullName(),
                    user.getUsername(),
                    user.getEmail(),
                    user.getRole(),
                    user.getDepartment(),
                    user.getStatus(),
                    user.getCreatedDate(),
                    user.getExpiryDate(),
                    user.getCity(),
                    "عمليات"
                };
                tableModel.addRow(rowData);
            }
        }
    }

    /**
     * إظهار نافذة إضافة مستخدم جديد
     */
    private void showAddUserDialog() {
        UserFormDialog dialog = new UserFormDialog(this, "إضافة مستخدم جديد", null);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            User newUser = dialog.getUser();
            usersList.add(newUser);
            loadUsersData();

            JOptionPane.showMessageDialog(this,
                "تم إضافة المستخدم بنجاح!",
                "نجح",
                JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * تعديل المستخدم المحدد
     */
    private void editSelectedUser() {
        int selectedRow = usersTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this,
                "يرجى اختيار مستخدم للتعديل",
                "تنبيه",
                JOptionPane.WARNING_MESSAGE);
            return;
        }

        String userId = (String) tableModel.getValueAt(selectedRow, 0);
        User user = findUserById(userId);

        if (user != null) {
            UserFormDialog dialog = new UserFormDialog(this, "تعديل بيانات المستخدم", user);
            dialog.setVisible(true);

            if (dialog.isConfirmed()) {
                User updatedUser = dialog.getUser();
                // تحديث البيانات في القائمة
                int index = usersList.indexOf(user);
                usersList.set(index, updatedUser);
                loadUsersData();

                JOptionPane.showMessageDialog(this,
                    "تم تحديث بيانات المستخدم بنجاح!",
                    "نجح",
                    JOptionPane.INFORMATION_MESSAGE);
            }
        }
    }

    /**
     * حذف المستخدم المحدد
     */
    private void deleteSelectedUser() {
        int selectedRow = usersTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this,
                "يرجى اختيار مستخدم للحذف",
                "تنبيه",
                JOptionPane.WARNING_MESSAGE);
            return;
        }

        String userId = (String) tableModel.getValueAt(selectedRow, 0);
        String userName = (String) tableModel.getValueAt(selectedRow, 1);

        int result = JOptionPane.showConfirmDialog(this,
            "هل أنت متأكد من حذف المستخدم: " + userName + "؟\nهذه العملية لا يمكن التراجع عنها.",
            "تأكيد الحذف",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.WARNING_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            User user = findUserById(userId);
            if (user != null) {
                usersList.remove(user);
                loadUsersData();

                JOptionPane.showMessageDialog(this,
                    "تم حذف المستخدم بنجاح!",
                    "نجح",
                    JOptionPane.INFORMATION_MESSAGE);
            }
        }
    }

    /**
     * إدارة صلاحيات المستخدم
     */
    private void manageUserPermissions() {
        int selectedRow = usersTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this,
                "يرجى اختيار مستخدم لإدارة صلاحياته",
                "تنبيه",
                JOptionPane.WARNING_MESSAGE);
            return;
        }

        String userId = (String) tableModel.getValueAt(selectedRow, 0);
        String userName = (String) tableModel.getValueAt(selectedRow, 1);

        UserPermissionsDialog dialog = new UserPermissionsDialog(this, userName, userId);
        dialog.setVisible(true);
    }

    /**
     * تصدير بيانات المستخدمين
     */
    private void exportUsersData() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("تصدير بيانات المستخدمين");
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("CSV Files (*.csv)", "csv"));
        fileChooser.setSelectedFile(new java.io.File("users_data.csv"));

        if (fileChooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
            try {
                // محاكاة تصدير البيانات
                Thread.sleep(1000);

                JOptionPane.showMessageDialog(this,
                    "تم تصدير بيانات المستخدمين بنجاح إلى:\n" + fileChooser.getSelectedFile().getAbsolutePath(),
                    "تصدير ناجح",
                    JOptionPane.INFORMATION_MESSAGE);

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * البحث عن مستخدم بالرقم
     */
    private User findUserById(String userId) {
        return usersList.stream()
            .filter(user -> user.getUserId().equals(userId))
            .findFirst()
            .orElse(null);
    }

    /**
     * فئة عرض الأزرار في الجدول
     */
    class ButtonRenderer extends JPanel implements TableCellRenderer {
        private JButton editBtn, deleteBtn, permissionsBtn;

        public ButtonRenderer() {
            setLayout(new FlowLayout(FlowLayout.CENTER, 2, 2));
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

            editBtn = new JButton("تعديل");
            editBtn.setFont(new Font("Tahoma", Font.PLAIN, 10));
            editBtn.setBackground(new Color(0, 123, 255));
            editBtn.setForeground(Color.WHITE);
            editBtn.setPreferredSize(new Dimension(45, 25));
            editBtn.setBorderPainted(false);

            deleteBtn = new JButton("حذف");
            deleteBtn.setFont(new Font("Tahoma", Font.PLAIN, 10));
            deleteBtn.setBackground(new Color(220, 53, 69));
            deleteBtn.setForeground(Color.WHITE);
            deleteBtn.setPreferredSize(new Dimension(40, 25));
            deleteBtn.setBorderPainted(false);

            permissionsBtn = new JButton("صلاحيات");
            permissionsBtn.setFont(new Font("Tahoma", Font.PLAIN, 10));
            permissionsBtn.setBackground(new Color(111, 66, 193));
            permissionsBtn.setForeground(Color.WHITE);
            permissionsBtn.setPreferredSize(new Dimension(55, 25));
            permissionsBtn.setBorderPainted(false);

            add(editBtn);
            add(deleteBtn);
            add(permissionsBtn);
        }

        @Override
        public Component getTableCellRendererComponent(JTable table, Object value,
                boolean isSelected, boolean hasFocus, int row, int column) {
            return this;
        }
    }

    /**
     * فئة تحرير الأزرار في الجدول
     */
    class ButtonEditor extends DefaultCellEditor {
        private JPanel panel;
        private JButton editBtn, deleteBtn, permissionsBtn;
        private int currentRow;

        public ButtonEditor() {
            super(new JCheckBox());

            panel = new JPanel(new FlowLayout(FlowLayout.CENTER, 2, 2));
            panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

            editBtn = new JButton("تعديل");
            editBtn.setFont(new Font("Tahoma", Font.PLAIN, 10));
            editBtn.setBackground(new Color(0, 123, 255));
            editBtn.setForeground(Color.WHITE);
            editBtn.setPreferredSize(new Dimension(45, 25));
            editBtn.setBorderPainted(false);
            editBtn.addActionListener(e -> {
                usersTable.setRowSelectionInterval(currentRow, currentRow);
                editSelectedUser();
                fireEditingStopped();
            });

            deleteBtn = new JButton("حذف");
            deleteBtn.setFont(new Font("Tahoma", Font.PLAIN, 10));
            deleteBtn.setBackground(new Color(220, 53, 69));
            deleteBtn.setForeground(Color.WHITE);
            deleteBtn.setPreferredSize(new Dimension(40, 25));
            deleteBtn.setBorderPainted(false);
            deleteBtn.addActionListener(e -> {
                usersTable.setRowSelectionInterval(currentRow, currentRow);
                deleteSelectedUser();
                fireEditingStopped();
            });

            permissionsBtn = new JButton("صلاحيات");
            permissionsBtn.setFont(new Font("Tahoma", Font.PLAIN, 10));
            permissionsBtn.setBackground(new Color(111, 66, 193));
            permissionsBtn.setForeground(Color.WHITE);
            permissionsBtn.setPreferredSize(new Dimension(55, 25));
            permissionsBtn.setBorderPainted(false);
            permissionsBtn.addActionListener(e -> {
                usersTable.setRowSelectionInterval(currentRow, currentRow);
                manageUserPermissions();
                fireEditingStopped();
            });

            panel.add(editBtn);
            panel.add(deleteBtn);
            panel.add(permissionsBtn);
        }

        @Override
        public Component getTableCellEditorComponent(JTable table, Object value,
                boolean isSelected, int row, int column) {
            currentRow = row;
            return panel;
        }

        @Override
        public Object getCellEditorValue() {
            return "عمليات";
        }
    }
}
