<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.shipment.erp.controller.settings.UserManagementController">
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
   
   <!-- عنوان الصفحة -->
   <HBox alignment="CENTER_LEFT" spacing="10.0">
      <Label styleClass="page-title" text="%user.management.title">
         <font>
            <Font size="20.0" />
         </font>
      </Label>
      <Region HBox.hgrow="ALWAYS" />
      <Button fx:id="addUserButton" onAction="#handleAddUser" styleClass="primary-button" text="%button.add.user" />
   </HBox>
   
   <Separator />
   
   <!-- شريط البحث والفلترة -->
   <HBox alignment="CENTER_LEFT" spacing="10.0">
      <Label text="%search" />
      <TextField fx:id="searchField" onKeyReleased="#handleSearch" prefWidth="200.0" promptText="%search.users" />
      <Label text="%filter.by.role" />
      <ComboBox fx:id="roleFilterCombo" onAction="#handleRoleFilter" prefWidth="150.0" />
      <Label text="%filter.by.status" />
      <ComboBox fx:id="statusFilterCombo" onAction="#handleStatusFilter" prefWidth="100.0" />
      <Button fx:id="refreshButton" onAction="#handleRefresh" text="%button.refresh" />
   </HBox>
   
   <Separator />
   
   <!-- جدول المستخدمين -->
   <TableView fx:id="usersTable" VBox.vgrow="ALWAYS">
      <columns>
         <TableColumn fx:id="usernameColumn" prefWidth="120.0" text="%user.username" />
         <TableColumn fx:id="fullNameColumn" prefWidth="200.0" text="%user.full.name" />
         <TableColumn fx:id="emailColumn" prefWidth="180.0" text="%user.email" />
         <TableColumn fx:id="roleColumn" prefWidth="120.0" text="%user.role" />
         <TableColumn fx:id="branchColumn" prefWidth="120.0" text="%user.branch" />
         <TableColumn fx:id="statusColumn" prefWidth="80.0" text="%status" />
         <TableColumn fx:id="lastLoginColumn" prefWidth="140.0" text="%user.last.login" />
         <TableColumn fx:id="actionsColumn" prefWidth="150.0" text="%actions" />
      </columns>
      <contextMenu>
         <ContextMenu>
            <items>
               <MenuItem fx:id="editMenuItem" onAction="#handleEditUser" text="%button.edit" />
               <MenuItem fx:id="deleteMenuItem" onAction="#handleDeleteUser" text="%button.delete" />
               <SeparatorMenuItem />
               <MenuItem fx:id="resetPasswordMenuItem" onAction="#handleResetPassword" text="%user.reset.password" />
               <MenuItem fx:id="lockUserMenuItem" onAction="#handleLockUser" text="%user.lock" />
               <MenuItem fx:id="unlockUserMenuItem" onAction="#handleUnlockUser" text="%user.unlock" />
            </items>
         </ContextMenu>
      </contextMenu>
   </TableView>
   
   <!-- شريط الحالة -->
   <HBox alignment="CENTER_LEFT" spacing="10.0">
      <Label fx:id="statusLabel" text="%status.ready" />
      <Region HBox.hgrow="ALWAYS" />
      <Label fx:id="recordCountLabel" text="" />
   </HBox>
</VBox>
