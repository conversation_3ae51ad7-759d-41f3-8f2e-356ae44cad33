import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import javax.imageio.ImageIO;

/**
 * مولد الأيقونات للنظام
 * Icon Generator for the System
 */
public class IconGenerator {
    
    private static final int ICON_SIZE = 24;
    private static final Color ICON_COLOR = new Color(0, 102, 204);
    private static final Color BACKGROUND_COLOR = Color.WHITE;
    
    public static void main(String[] args) {
        try {
            generateAllIcons();
            System.out.println("تم إنشاء جميع الأيقونات بنجاح!");
        } catch (Exception e) {
            System.err.println("خطأ في إنشاء الأيقونات: " + e.getMessage());
        }
    }
    
    private static void generateAllIcons() throws Exception {
        // إنشاء مجلد الأيقونات
        File iconsDir = new File("icons");
        if (!iconsDir.exists()) {
            iconsDir.mkdirs();
        }
        
        // إنشاء الأيقونات
        generateNewIcon();
        generateSaveIcon();
        generateDeleteIcon();
        generatePrintIcon();
        generatePreviewIcon();
        generateSearchIcon();
        generateFilterIcon();
        generateFirstIcon();
        generatePreviousIcon();
        generateNextIcon();
        generateLastIcon();
        generateExcelIcon();
        generateImageIcon();
    }
    
    private static void generateNewIcon() throws Exception {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        setupGraphics(g2d);
        
        // رسم أيقونة "جديد" - علامة زائد
        g2d.setStroke(new BasicStroke(2));
        g2d.drawLine(ICON_SIZE/2, 4, ICON_SIZE/2, ICON_SIZE-4);
        g2d.drawLine(4, ICON_SIZE/2, ICON_SIZE-4, ICON_SIZE/2);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("icons/new.png"));
    }
    
    private static void generateSaveIcon() throws Exception {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        setupGraphics(g2d);
        
        // رسم أيقونة "حفظ" - قرص مرن
        g2d.fillRect(3, 3, ICON_SIZE-6, ICON_SIZE-6);
        g2d.setColor(BACKGROUND_COLOR);
        g2d.fillRect(5, 5, ICON_SIZE-10, ICON_SIZE-12);
        g2d.setColor(ICON_COLOR);
        g2d.fillRect(6, 3, ICON_SIZE-12, 4);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("icons/save.png"));
    }
    
    private static void generateDeleteIcon() throws Exception {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        setupGraphics(g2d);
        
        // رسم أيقونة "حذف" - سلة مهملات
        g2d.setStroke(new BasicStroke(2));
        g2d.drawRect(6, 8, ICON_SIZE-12, ICON_SIZE-10);
        g2d.drawLine(4, 6, ICON_SIZE-4, 6);
        g2d.drawLine(8, 4, ICON_SIZE-8, 4);
        g2d.drawLine(10, 8, 10, ICON_SIZE-4);
        g2d.drawLine(14, 8, 14, ICON_SIZE-4);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("icons/delete.png"));
    }
    
    private static void generatePrintIcon() throws Exception {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        setupGraphics(g2d);
        
        // رسم أيقونة "طباعة" - طابعة
        g2d.drawRect(4, 6, ICON_SIZE-8, ICON_SIZE-8);
        g2d.fillRect(6, 3, ICON_SIZE-12, 4);
        g2d.drawRect(6, 10, ICON_SIZE-12, 6);
        g2d.fillRect(8, 12, ICON_SIZE-16, 2);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("icons/print.png"));
    }
    
    private static void generatePreviewIcon() throws Exception {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        setupGraphics(g2d);
        
        // رسم أيقونة "معاينة" - عين
        g2d.drawOval(6, 8, ICON_SIZE-12, 8);
        g2d.fillOval(10, 10, 4, 4);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("icons/preview.png"));
    }
    
    private static void generateSearchIcon() throws Exception {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        setupGraphics(g2d);
        
        // رسم أيقونة "بحث" - عدسة مكبرة
        g2d.setStroke(new BasicStroke(2));
        g2d.drawOval(4, 4, 12, 12);
        g2d.drawLine(14, 14, 20, 20);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("icons/search.png"));
    }
    
    private static void generateFilterIcon() throws Exception {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        setupGraphics(g2d);
        
        // رسم أيقونة "تصفية" - قمع
        g2d.setStroke(new BasicStroke(2));
        g2d.drawLine(4, 6, ICON_SIZE-4, 6);
        g2d.drawLine(8, 10, ICON_SIZE-8, 10);
        g2d.drawLine(10, 14, ICON_SIZE-10, 14);
        g2d.drawLine(ICON_SIZE/2, 14, ICON_SIZE/2, 20);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("icons/filter.png"));
    }
    
    private static void generateFirstIcon() throws Exception {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        setupGraphics(g2d);
        
        // رسم أيقونة "الأول" - خطان وسهم
        g2d.setStroke(new BasicStroke(2));
        g2d.drawLine(4, 6, 4, ICON_SIZE-6);
        g2d.drawLine(6, 6, 6, ICON_SIZE-6);
        int[] xPoints = {8, 16, 8};
        int[] yPoints = {6, ICON_SIZE/2, ICON_SIZE-6};
        g2d.fillPolygon(xPoints, yPoints, 3);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("icons/first.png"));
    }
    
    private static void generatePreviousIcon() throws Exception {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        setupGraphics(g2d);
        
        // رسم أيقونة "السابق" - سهم يسار
        int[] xPoints = {6, 14, 14};
        int[] yPoints = {ICON_SIZE/2, 6, ICON_SIZE-6};
        g2d.fillPolygon(xPoints, yPoints, 3);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("icons/previous.png"));
    }
    
    private static void generateNextIcon() throws Exception {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        setupGraphics(g2d);
        
        // رسم أيقونة "التالي" - سهم يمين
        int[] xPoints = {10, 18, 10};
        int[] yPoints = {6, ICON_SIZE/2, ICON_SIZE-6};
        g2d.fillPolygon(xPoints, yPoints, 3);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("icons/next.png"));
    }
    
    private static void generateLastIcon() throws Exception {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        setupGraphics(g2d);
        
        // رسم أيقونة "الأخير" - سهم وخطان
        g2d.setStroke(new BasicStroke(2));
        g2d.drawLine(18, 6, 18, ICON_SIZE-6);
        g2d.drawLine(20, 6, 20, ICON_SIZE-6);
        int[] xPoints = {8, 16, 16};
        int[] yPoints = {ICON_SIZE/2, 6, ICON_SIZE-6};
        g2d.fillPolygon(xPoints, yPoints, 3);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("icons/last.png"));
    }
    
    private static void generateExcelIcon() throws Exception {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        setupGraphics(g2d);
        
        // رسم أيقونة "Excel" - جدول
        g2d.setColor(new Color(0, 128, 0)); // أخضر Excel
        g2d.fillRect(4, 4, ICON_SIZE-8, ICON_SIZE-8);
        g2d.setColor(BACKGROUND_COLOR);
        g2d.drawLine(4, 8, ICON_SIZE-4, 8);
        g2d.drawLine(4, 12, ICON_SIZE-4, 12);
        g2d.drawLine(4, 16, ICON_SIZE-4, 16);
        g2d.drawLine(8, 4, 8, ICON_SIZE-4);
        g2d.drawLine(12, 4, 12, ICON_SIZE-4);
        g2d.drawLine(16, 4, 16, ICON_SIZE-4);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("icons/excel.png"));
    }
    
    private static void generateImageIcon() throws Exception {
        BufferedImage image = new BufferedImage(ICON_SIZE, ICON_SIZE, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        setupGraphics(g2d);
        
        // رسم أيقونة "صورة" - إطار مع جبل وشمس
        g2d.drawRect(3, 3, ICON_SIZE-6, ICON_SIZE-6);
        g2d.fillOval(6, 6, 4, 4); // شمس
        g2d.drawLine(4, 16, 8, 12); // جبل
        g2d.drawLine(8, 12, 12, 14);
        g2d.drawLine(12, 14, 16, 10);
        g2d.drawLine(16, 10, 20, 12);
        
        g2d.dispose();
        ImageIO.write(image, "PNG", new File("icons/image.png"));
    }
    
    private static void setupGraphics(Graphics2D g2d) {
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setColor(ICON_COLOR);
    }
}
