/**
 * اختبار الاتصال التلقائي بقاعدة البيانات Automatic Database Connection Test
 */
public class AutoDatabaseConnectionTest {

    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("   اختبار الاتصال التلقائي بقاعدة البيانات");
        System.out.println("   Automatic Database Connection Test");
        System.out.println("====================================");
        System.out.println();

        testDatabaseConnection();
    }

    /**
     * فحص الاتصال التلقائي بقاعدة البيانات
     */
    private static void testDatabaseConnection() {
        try {
            System.out.println("🔄 فحص مكتبات Oracle...");

            // فحص تحميل مكتبة Oracle JDBC
            try {
                Class.forName("oracle.jdbc.driver.OracleDriver");
                System.out.println("✅ Oracle JDBC Driver محمل بنجاح");
            } catch (ClassNotFoundException e) {
                System.out.println("❌ Oracle JDBC Driver غير محمل!");
                System.out.println("الحل: تشغيل java LibraryDownloader");
                return;
            }

            // فحص وجود مكتبة orai18n.jar
            String classpath = System.getProperty("java.class.path");
            if (classpath.contains("orai18n.jar")) {
                System.out.println("✅ مكتبة orai18n.jar محملة (دعم الأحرف العربية)");
            } else {
                System.out.println(
                        "⚠️ مكتبة orai18n.jar غير محملة - قد تواجه مشاكل مع الأحرف العربية");
            }

            System.out.println("🔄 اختبار الاتصال بقاعدة البيانات...");

            // إنشاء كائن DatabaseConfig
            DatabaseConfig dbConfig = new DatabaseConfig();
            dbConfig.setHost("localhost");
            dbConfig.setPort("1521");
            dbConfig.setServiceName("orcl");
            dbConfig.setUsername("ship_erp");
            dbConfig.setPassword("ship_erp_password");

            // إنشاء كائن OracleItemImporter واختبار الاتصال
            OracleItemImporter importer = new OracleItemImporter(dbConfig);
            boolean connected = importer.connect();

            if (connected) {
                System.out.println("✅ تم الاتصال بقاعدة البيانات بنجاح!");

                // فحص وجود جداول IAS
                System.out.println("🔄 فحص وجود جداول IAS...");
                boolean tablesExist = importer.checkIASTablesExist();

                if (tablesExist) {
                    System.out.println("✅ جداول IAS_ITM_MST و IAS_ITM_DTL موجودة");

                    // اختبار الاستعلام الموحد
                    System.out.println("🔄 اختبار الاستعلام الموحد...");
                    try {
                        java.util.List<OracleItemImporter.ImportedItem> items =
                                importer.importFromIASItemTables();
                        System.out.println("✅ الاستعلام الموحد يعمل بنجاح!");
                        System.out.println(
                                "📊 تم العثور على " + items.size() + " صنف في قاعدة البيانات");

                        if (!items.isEmpty()) {
                            OracleItemImporter.ImportedItem firstItem = items.get(0);
                            System.out.println("📋 عينة من البيانات:");
                            System.out.println("   - كود الصنف: " + firstItem.getField("ITM_CODE"));
                            System.out.println("   - اسم الصنف: " + firstItem.getField("ITM_NAME"));
                            System.out.println("   - حالة النشاط: "
                                    + (firstItem.getField("IS_ACTIVE").equals(1) ? "نشط"
                                            : "غير نشط"));

                            if (items.size() >= 3) {
                                System.out.println("📋 عينات إضافية:");
                                for (int i = 1; i < Math.min(4, items.size()); i++) {
                                    OracleItemImporter.ImportedItem item = items.get(i);
                                    System.out.println(
                                            "   " + (i + 1) + ". " + item.getField("ITM_CODE")
                                                    + " - " + item.getField("ITM_NAME"));
                                }
                            }
                        }

                        System.out.println("🎯 النتيجة: النظام جاهز للاستيراد!");
                        System.out
                                .println("💡 يمكنك الآن استخدام زر 'استيراد IAS' في واجهة النظام");

                    } catch (Exception e) {
                        System.out.println("❌ خطأ في الاستعلام الموحد: " + e.getMessage());
                        System.out.println("💡 تفاصيل الخطأ: " + e.getClass().getSimpleName());

                        if (e.getMessage() != null) {
                            if (e.getMessage().contains("ORA-00904")) {
                                System.out
                                        .println("💡 مشكلة: عمود غير موجود - تحقق من هيكل الجداول");
                            } else if (e.getMessage().contains("ORA-00918")) {
                                System.out.println("💡 مشكلة: عمود مكرر - تحقق من الاستعلام");
                            } else if (e.getMessage().contains("ORA-17006")) {
                                System.out.println("💡 مشكلة: اسم عمود غير صالح");
                            }
                        }
                    }

                } else {
                    System.out.println("⚠️ جداول IAS غير موجودة أو غير متاحة");
                    System.out.println("💡 تأكد من وجود الجداول في المستخدم IAS20251");
                }

                // إغلاق الاتصال
                importer.disconnect();
                System.out.println("🔒 تم إغلاق الاتصال بقاعدة البيانات");

            } else {
                System.out.println("❌ فشل في الاتصال بقاعدة البيانات!");
                System.out.println("💡 تأكد من:");
                System.out.println("   - تشغيل خادم Oracle");
                System.out.println("   - صحة بيانات الاتصال (localhost:1521:orcl)");
                System.out
                        .println("   - صحة اسم المستخدم وكلمة المرور (ship_erp/ship_erp_password)");
            }

        } catch (Exception e) {
            System.out.println("❌ خطأ في فحص قاعدة البيانات: " + e.getMessage());
            System.out.println("💡 السبب المحتمل: " + e.getClass().getSimpleName());

            if (e.getMessage() != null && e.getMessage().contains("regex")) {
                System.out.println("💡 مشكلة regex - تأكد من إعدادات اللغة");
            }

            // طباعة stack trace للتشخيص
            e.printStackTrace();
        }

        System.out.println();
        System.out.println("=== انتهى فحص قاعدة البيانات ===");
    }
}
