import java.sql.*;
import java.util.*;

/**
 * محلل البنية المتقدم للجداول IAS_ITM_MST و IAS_ITM_DTL
 * Advanced Structure Analyzer for IAS_ITM_MST and IAS_ITM_DTL tables
 */
public class AdvancedTableStructureAnalyzer {
    
    private Connection ias20251Connection;
    private Connection shipErpConnection;
    
    public static void main(String[] args) {
        try {
            new AdvancedTableStructureAnalyzer().analyzeAndCreate();
        } catch (Exception e) {
            System.err.println("❌ خطأ في التحليل: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public AdvancedTableStructureAnalyzer() throws Exception {
        initializeConnections();
    }
    
    /**
     * تهيئة الاتصالات
     */
    private void initializeConnections() throws Exception {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        
        // اتصال IAS20251
        ias20251Connection = DriverManager.getConnection(
            "*************************************", 
            "ias20251", 
            "ys123"
        );
        System.out.println("✅ تم الاتصال بـ IAS20251");
        
        // اتصال SHIP_ERP
        shipErpConnection = DriverManager.getConnection(
            "*************************************", 
            "ship_erp", 
            "ship_erp_password"
        );
        System.out.println("✅ تم الاتصال بـ SHIP_ERP");
    }
    
    /**
     * تحليل وإنشاء الجداول
     */
    public void analyzeAndCreate() throws SQLException {
        System.out.println("\n🔍 بدء التحليل المتقدم للجداول...");
        
        // تحليل IAS_ITM_MST
        TableStructure iasMstStructure = analyzeTableStructure("IAS_ITM_MST");
        System.out.println("\n📋 تحليل IAS_ITM_MST مكتمل");
        
        // تحليل IAS_ITM_DTL
        TableStructure iasDtlStructure = analyzeTableStructure("IAS_ITM_DTL");
        System.out.println("📋 تحليل IAS_ITM_DTL مكتمل");
        
        // إنشاء الجداول في SHIP_ERP
        createShipErpTables(iasMstStructure, iasDtlStructure);
        
        // إنشاء العلاقات والقيود
        createConstraintsAndRelations(iasMstStructure, iasDtlStructure);
        
        // إنشاء الفهارس
        createIndexes();
        
        System.out.println("\n🎉 تم إنشاء الجداول بنجاح في SHIP_ERP!");
    }
    
    /**
     * تحليل بنية جدول
     */
    private TableStructure analyzeTableStructure(String tableName) throws SQLException {
        System.out.println("\n🔍 تحليل جدول: " + tableName);
        
        TableStructure structure = new TableStructure(tableName);
        
        // الحصول على معلومات الأعمدة
        DatabaseMetaData metaData = ias20251Connection.getMetaData();
        ResultSet columns = metaData.getColumns(null, "IAS20251", tableName, null);
        
        while (columns.next()) {
            ColumnInfo column = new ColumnInfo();
            column.name = columns.getString("COLUMN_NAME");
            column.dataType = columns.getString("TYPE_NAME");
            column.size = columns.getInt("COLUMN_SIZE");
            column.nullable = columns.getString("IS_NULLABLE").equals("YES");
            column.defaultValue = columns.getString("COLUMN_DEF");
            
            structure.columns.add(column);
            System.out.println("  📋 " + column.name + " (" + column.dataType + 
                             (column.size > 0 ? "(" + column.size + ")" : "") + 
                             (column.nullable ? " NULL" : " NOT NULL") + ")");
        }
        columns.close();
        
        // الحصول على المفاتيح الأساسية
        ResultSet primaryKeys = metaData.getPrimaryKeys(null, "IAS20251", tableName);
        while (primaryKeys.next()) {
            String pkColumn = primaryKeys.getString("COLUMN_NAME");
            structure.primaryKeys.add(pkColumn);
            System.out.println("  🔑 مفتاح أساسي: " + pkColumn);
        }
        primaryKeys.close();
        
        // الحصول على المفاتيح الخارجية
        ResultSet foreignKeys = metaData.getImportedKeys(null, "IAS20251", tableName);
        while (foreignKeys.next()) {
            ForeignKeyInfo fk = new ForeignKeyInfo();
            fk.columnName = foreignKeys.getString("FKCOLUMN_NAME");
            fk.referencedTable = foreignKeys.getString("PKTABLE_NAME");
            fk.referencedColumn = foreignKeys.getString("PKCOLUMN_NAME");
            fk.constraintName = foreignKeys.getString("FK_NAME");
            
            structure.foreignKeys.add(fk);
            System.out.println("  🔗 مفتاح خارجي: " + fk.columnName + " → " + 
                             fk.referencedTable + "." + fk.referencedColumn);
        }
        foreignKeys.close();
        
        // فحص البيانات الفعلية
        analyzeSampleData(tableName, structure);
        
        return structure;
    }
    
    /**
     * تحليل عينة من البيانات
     */
    private void analyzeSampleData(String tableName, TableStructure structure) throws SQLException {
        System.out.println("  📊 تحليل عينة البيانات...");
        
        Statement stmt = ias20251Connection.createStatement();
        ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName);
        rs.next();
        int totalRows = rs.getInt(1);
        structure.totalRows = totalRows;
        rs.close();
        
        System.out.println("  📈 إجمالي الصفوف: " + totalRows);
        
        // عرض عينة من البيانات
        rs = stmt.executeQuery("SELECT * FROM " + tableName + " WHERE ROWNUM <= 3");
        ResultSetMetaData rsmd = rs.getMetaData();
        
        System.out.println("  📄 عينة من البيانات:");
        while (rs.next()) {
            System.out.print("    ");
            for (int i = 1; i <= rsmd.getColumnCount(); i++) {
                String value = rs.getString(i);
                if (value != null && value.length() > 15) {
                    value = value.substring(0, 15) + "...";
                }
                System.out.print(rsmd.getColumnName(i) + "=" + 
                    (value != null ? value : "NULL") + " | ");
            }
            System.out.println();
        }
        rs.close();
        stmt.close();
    }
    
    /**
     * إنشاء الجداول في SHIP_ERP
     */
    private void createShipErpTables(TableStructure mstStructure, TableStructure dtlStructure) throws SQLException {
        System.out.println("\n🏗️ إنشاء الجداول في SHIP_ERP...");
        
        // إنشاء جدول SHIP_ITM_MST
        createTable("SHIP_ITM_MST", mstStructure);
        
        // إنشاء جدول SHIP_ITM_DTL
        createTable("SHIP_ITM_DTL", dtlStructure);
    }
    
    /**
     * إنشاء جدول
     */
    private void createTable(String tableName, TableStructure structure) throws SQLException {
        System.out.println("🔨 إنشاء جدول: " + tableName);
        
        Statement stmt = shipErpConnection.createStatement();
        
        // حذف الجدول إذا كان موجوداً
        try {
            stmt.execute("DROP TABLE " + tableName + " CASCADE CONSTRAINTS");
            System.out.println("  🗑️ تم حذف الجدول القديم");
        } catch (SQLException e) {
            // الجدول غير موجود
        }
        
        // بناء SQL لإنشاء الجدول
        StringBuilder createSQL = new StringBuilder();
        createSQL.append("CREATE TABLE ").append(tableName).append(" (\n");
        
        for (int i = 0; i < structure.columns.size(); i++) {
            ColumnInfo column = structure.columns.get(i);
            createSQL.append("  ").append(column.name).append(" ");
            
            // تحويل نوع البيانات
            String oracleType = convertDataType(column.dataType, column.size);
            createSQL.append(oracleType);
            
            // NULL/NOT NULL
            if (!column.nullable) {
                createSQL.append(" NOT NULL");
            }
            
            // القيمة الافتراضية
            if (column.defaultValue != null && !column.defaultValue.trim().isEmpty()) {
                createSQL.append(" DEFAULT ").append(column.defaultValue);
            }
            
            if (i < structure.columns.size() - 1) {
                createSQL.append(",");
            }
            createSQL.append("\n");
        }
        
        createSQL.append(")");
        
        System.out.println("  📝 SQL: " + createSQL.toString());
        stmt.execute(createSQL.toString());
        System.out.println("  ✅ تم إنشاء الجدول بنجاح");
        
        stmt.close();
    }
    
    /**
     * تحويل نوع البيانات
     */
    private String convertDataType(String dataType, int size) {
        switch (dataType.toUpperCase()) {
            case "VARCHAR2":
                return "VARCHAR2(" + size + ")";
            case "NUMBER":
                return size > 0 ? "NUMBER(" + size + ")" : "NUMBER";
            case "DATE":
                return "DATE";
            case "CHAR":
                return "CHAR(" + size + ")";
            case "CLOB":
                return "CLOB";
            case "BLOB":
                return "BLOB";
            default:
                return dataType + (size > 0 ? "(" + size + ")" : "");
        }
    }
    
    /**
     * إنشاء القيود والعلاقات
     */
    private void createConstraintsAndRelations(TableStructure mstStructure, TableStructure dtlStructure) throws SQLException {
        System.out.println("\n🔗 إنشاء القيود والعلاقات...");
        
        Statement stmt = shipErpConnection.createStatement();
        
        // إنشاء المفاتيح الأساسية
        if (!mstStructure.primaryKeys.isEmpty()) {
            String pkSQL = "ALTER TABLE SHIP_ITM_MST ADD CONSTRAINT PK_SHIP_ITM_MST PRIMARY KEY (" +
                          String.join(", ", mstStructure.primaryKeys) + ")";
            stmt.execute(pkSQL);
            System.out.println("  🔑 تم إنشاء المفتاح الأساسي لـ SHIP_ITM_MST");
        }
        
        if (!dtlStructure.primaryKeys.isEmpty()) {
            String pkSQL = "ALTER TABLE SHIP_ITM_DTL ADD CONSTRAINT PK_SHIP_ITM_DTL PRIMARY KEY (" +
                          String.join(", ", dtlStructure.primaryKeys) + ")";
            stmt.execute(pkSQL);
            System.out.println("  🔑 تم إنشاء المفتاح الأساسي لـ SHIP_ITM_DTL");
        }
        
        // إنشاء العلاقة بين الجدولين
        String fkSQL = "ALTER TABLE SHIP_ITM_DTL ADD CONSTRAINT FK_SHIP_ITM_DTL_MST " +
                      "FOREIGN KEY (I_CODE) REFERENCES SHIP_ITM_MST(I_CODE)";
        try {
            stmt.execute(fkSQL);
            System.out.println("  🔗 تم إنشاء العلاقة بين الجدولين");
        } catch (SQLException e) {
            System.out.println("  ⚠️ تحذير في إنشاء العلاقة: " + e.getMessage());
        }
        
        stmt.close();
    }
    
    /**
     * إنشاء الفهارس
     */
    private void createIndexes() throws SQLException {
        System.out.println("\n📇 إنشاء الفهارس...");
        
        Statement stmt = shipErpConnection.createStatement();
        
        // فهارس للبحث السريع
        String[] indexes = {
            "CREATE INDEX IDX_SHIP_ITM_MST_NAME ON SHIP_ITM_MST(I_A_NAME)",
            "CREATE INDEX IDX_SHIP_ITM_MST_GROUP ON SHIP_ITM_MST(G_CODE)",
            "CREATE INDEX IDX_SHIP_ITM_DTL_CODE ON SHIP_ITM_DTL(I_CODE)"
        };
        
        for (String indexSQL : indexes) {
            try {
                stmt.execute(indexSQL);
                System.out.println("  📇 تم إنشاء فهرس");
            } catch (SQLException e) {
                System.out.println("  ⚠️ تحذير في إنشاء الفهرس: " + e.getMessage());
            }
        }
        
        stmt.close();
    }
    
    /**
     * فئة بنية الجدول
     */
    static class TableStructure {
        String tableName;
        List<ColumnInfo> columns = new ArrayList<>();
        List<String> primaryKeys = new ArrayList<>();
        List<ForeignKeyInfo> foreignKeys = new ArrayList<>();
        int totalRows;
        
        TableStructure(String tableName) {
            this.tableName = tableName;
        }
    }
    
    /**
     * فئة معلومات العمود
     */
    static class ColumnInfo {
        String name;
        String dataType;
        int size;
        boolean nullable;
        String defaultValue;
    }
    
    /**
     * فئة معلومات المفتاح الخارجي
     */
    static class ForeignKeyInfo {
        String columnName;
        String referencedTable;
        String referencedColumn;
        String constraintName;
    }
}
