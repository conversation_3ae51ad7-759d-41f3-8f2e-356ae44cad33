import java.sql.*;
import java.util.Properties;
import java.util.Locale;

/**
 * إصلاح مشكلة الاتصال بـ Oracle مع الأرقام العربية
 * Oracle Connection Fixer for Arabic Numbers Issue
 */
public class OracleConnectionFixer {
    
    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("   إصلاح مشكلة الاتصال بـ Oracle");
        System.out.println("   Oracle Connection Fixer");
        System.out.println("====================================");
        System.out.println();
        
        // إصلاح إعدادات اللغة والمنطقة
        fixLocaleSettings();
        
        // اختبار الاتصال
        testOracleConnection();
    }
    
    /**
     * إصلاح إعدادات اللغة والمنطقة
     */
    private static void fixLocaleSettings() {
        System.out.println("🔧 إصلاح إعدادات اللغة والمنطقة...");
        
        // تعيين اللغة الإنجليزية للنظام لتجنب مشاكل regex
        System.setProperty("user.language", "en");
        System.setProperty("user.country", "US");
        System.setProperty("user.region", "US");
        
        // تعيين الترميز
        System.setProperty("file.encoding", "UTF-8");
        
        // تعيين إعدادات Oracle
        System.setProperty("oracle.jdbc.autoCommitSpecCompliant", "false");
        System.setProperty("oracle.net.disableOob", "true");
        
        // تعيين Locale الافتراضي
        Locale.setDefault(Locale.US);
        
        System.out.println("✅ تم إصلاح إعدادات اللغة والمنطقة");
        System.out.println();
    }
    
    /**
     * اختبار الاتصال بـ Oracle
     */
    private static void testOracleConnection() {
        System.out.println("🔄 اختبار الاتصال بـ Oracle...");
        
        // بيانات الاتصال الافتراضية
        String host = "localhost";
        String port = "1521";
        String serviceName = "orcl";
        String username = "ysdba2";
        String password = "ys123";
        
        String url = String.format("**************************", host, port, serviceName);
        
        System.out.println("رابط الاتصال: " + url);
        System.out.println("المستخدم: " + username);
        System.out.println();
        
        Connection connection = null;
        
        try {
            // تحميل تعريف Oracle JDBC
            Class.forName("oracle.jdbc.driver.OracleDriver");
            System.out.println("✅ تم تحميل تعريف Oracle JDBC بنجاح");
            
            // إعداد خصائص الاتصال
            Properties props = new Properties();
            props.setProperty("user", username);
            props.setProperty("password", password);
            
            // إعدادات إضافية لتجنب المشاكل
            props.setProperty("oracle.jdbc.autoCommitSpecCompliant", "false");
            props.setProperty("oracle.net.CONNECT_TIMEOUT", "10000");
            props.setProperty("oracle.jdbc.ReadTimeout", "30000");
            props.setProperty("oracle.net.disableOob", "true");
            
            System.out.println("🔄 محاولة الاتصال...");
            
            // إنشاء الاتصال
            connection = DriverManager.getConnection(url, props);
            
            System.out.println("✅ نجح الاتصال!");
            System.out.println();
            
            // الحصول على معلومات قاعدة البيانات
            DatabaseMetaData metaData = connection.getMetaData();
            
            System.out.println("====================================");
            System.out.println("   معلومات قاعدة البيانات");
            System.out.println("====================================");
            System.out.println("اسم قاعدة البيانات: " + metaData.getDatabaseProductName());
            System.out.println("إصدار قاعدة البيانات: " + metaData.getDatabaseProductVersion());
            System.out.println("اسم التعريف: " + metaData.getDriverName());
            System.out.println("إصدار التعريف: " + metaData.getDriverVersion());
            System.out.println("المستخدم الحالي: " + metaData.getUserName());
            System.out.println();
            
            // اختبار استعلام بسيط
            System.out.println("🔄 اختبار استعلام بسيط...");
            try (Statement stmt = connection.createStatement();
                 ResultSet rs = stmt.executeQuery("SELECT SYSDATE FROM DUAL")) {
                
                if (rs.next()) {
                    System.out.println("✅ الاستعلام نجح!");
                    System.out.println("التاريخ والوقت الحالي: " + rs.getTimestamp(1));
                }
            }
            System.out.println();
            
            // البحث عن جداول الأصناف
            System.out.println("🔍 البحث عن جداول الأصناف...");
            searchForItemTables(connection);
            
        } catch (ClassNotFoundException e) {
            System.err.println("❌ لم يتم العثور على تعريف Oracle JDBC!");
            System.err.println("تأكد من وجود ملف ojdbc11.jar في مجلد lib");
            
        } catch (SQLException e) {
            System.err.println("❌ فشل الاتصال!");
            System.err.println("رمز الخطأ: " + e.getErrorCode());
            System.err.println("رسالة الخطأ: " + e.getMessage());
            System.err.println();
            
            // اقتراحات لحل المشاكل الشائعة
            suggestSolutions(e);
            
        } finally {
            // إغلاق الاتصال
            if (connection != null) {
                try {
                    connection.close();
                    System.out.println("🔒 تم إغلاق الاتصال");
                } catch (SQLException e) {
                    System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * البحث عن جداول الأصناف
     */
    private static void searchForItemTables(Connection connection) throws SQLException {
        String query = """
            SELECT table_name, num_rows 
            FROM user_tables 
            WHERE UPPER(table_name) LIKE '%ITEM%' 
               OR UPPER(table_name) LIKE '%PRODUCT%' 
               OR UPPER(table_name) LIKE '%INVENTORY%'
            ORDER BY table_name
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(query);
             ResultSet rs = stmt.executeQuery()) {
            
            boolean found = false;
            System.out.println("الجداول المتاحة للأصناف:");
            System.out.println("-------------------------");
            
            while (rs.next()) {
                String tableName = rs.getString("table_name");
                Object numRows = rs.getObject("num_rows");
                
                System.out.printf("📋 %s", tableName);
                if (numRows != null) {
                    System.out.printf(" (%s سجل)", numRows);
                }
                System.out.println();
                
                found = true;
            }
            
            if (!found) {
                System.out.println("❌ لم يتم العثور على جداول أصناف");
                System.out.println("تأكد من وجود جداول تحتوي على ITEM أو PRODUCT في اسمها");
            } else {
                System.out.println("✅ تم العثور على جداول أصناف");
            }
            
        } catch (SQLException e) {
            System.err.println("خطأ في البحث عن الجداول: " + e.getMessage());
        }
    }
    
    /**
     * اقتراح حلول للمشاكل الشائعة
     */
    private static void suggestSolutions(SQLException e) {
        System.out.println("====================================");
        System.out.println("   اقتراحات لحل المشكلة");
        System.out.println("====================================");
        
        int errorCode = e.getErrorCode();
        String message = e.getMessage().toLowerCase();
        
        if (errorCode == 17002 || message.contains("io exception") || message.contains("connection refused")) {
            System.out.println("🔧 مشكلة في الاتصال بالخادم:");
            System.out.println("   • تأكد من أن خادم Oracle يعمل");
            System.out.println("   • تحقق من عنوان الخادم والمنفذ");
            System.out.println("   • تأكد من عدم وجود جدار حماية يحجب الاتصال");
            
        } else if (errorCode == 1017 || message.contains("invalid username/password")) {
            System.out.println("🔧 مشكلة في اسم المستخدم أو كلمة المرور:");
            System.out.println("   • تحقق من صحة اسم المستخدم وكلمة المرور");
            System.out.println("   • تأكد من أن الحساب غير مقفل");
            System.out.println("   • جرب الاتصال باستخدام SQL*Plus أولاً");
            
        } else if (errorCode == 12505 || message.contains("invalid sid")) {
            System.out.println("🔧 مشكلة في اسم الخدمة:");
            System.out.println("   • تحقق من صحة اسم الخدمة (SID)");
            System.out.println("   • جرب استخدام XE أو ORCL");
            System.out.println("   • تأكد من أن الخدمة تعمل");
            
        } else if (message.contains("listener")) {
            System.out.println("🔧 مشكلة في Oracle Listener:");
            System.out.println("   • تأكد من تشغيل Oracle Listener");
            System.out.println("   • تحقق من إعدادات listener.ora");
            System.out.println("   • جرب إعادة تشغيل Listener");
            
        } else {
            System.out.println("🔧 نصائح عامة:");
            System.out.println("   • تأكد من تثبيت Oracle Database بشكل صحيح");
            System.out.println("   • تحقق من متغيرات البيئة (ORACLE_HOME, PATH)");
            System.out.println("   • راجع سجلات Oracle للمزيد من التفاصيل");
        }
        
        System.out.println();
        System.out.println("💡 للمساعدة الإضافية:");
        System.out.println("   • راجع دليل تثبيت Oracle Database");
        System.out.println("   • تحقق من حالة خدمات Oracle في Windows Services");
        System.out.println("   • استخدم Oracle SQL Developer للاختبار");
    }
}
