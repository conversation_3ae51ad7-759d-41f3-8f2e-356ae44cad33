package com.shipment.erp.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * كيان الدور
 * يمثل أدوار المستخدمين في النظام
 */
@Entity
@Table(name = "ROLES")
@SequenceGenerator(name = "role_seq", sequenceName = "SEQ_ROLE", allocationSize = 1)
public class Role extends BaseEntity {

    @Column(name = "NAME", nullable = false, length = 100)
    @NotBlank(message = "اسم الدور مطلوب")
    @Size(max = 100, message = "اسم الدور يجب أن يكون أقل من 100 حرف")
    private String name;

    @Column(name = "NAME_EN", length = 100)
    @Size(max = 100, message = "الاسم الإنجليزي يجب أن يكون أقل من 100 حرف")
    private String nameEn;

    @Column(name = "DESCRIPTION", length = 500)
    @Size(max = 500, message = "الوصف يجب أن يكون أقل من 500 حرف")
    private String description;

    @Column(name = "IS_ACTIVE", nullable = false)
    private Boolean isActive = true;

    @OneToMany(mappedBy = "role", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<User> users = new ArrayList<>();

    @OneToMany(mappedBy = "role", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    private List<Permission> permissions = new ArrayList<>();

    /**
     * Constructor افتراضي
     */
    public Role() {
        super();
    }

    /**
     * Constructor مع الاسم
     */
    public Role(String name) {
        this();
        this.name = name;
    }

    /**
     * Constructor مع الاسم والوصف
     */
    public Role(String name, String description) {
        this(name);
        this.description = description;
    }

    // Getters and Setters

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public List<User> getUsers() {
        return users;
    }

    public void setUsers(List<User> users) {
        this.users = users;
    }

    public List<Permission> getPermissions() {
        return permissions;
    }

    public void setPermissions(List<Permission> permissions) {
        this.permissions = permissions;
    }

    /**
     * إضافة مستخدم جديد
     */
    public void addUser(User user) {
        users.add(user);
        user.setRole(this);
    }

    /**
     * إزالة مستخدم
     */
    public void removeUser(User user) {
        users.remove(user);
        user.setRole(null);
    }

    /**
     * إضافة صلاحية جديدة
     */
    public void addPermission(Permission permission) {
        permissions.add(permission);
        permission.setRole(this);
    }

    /**
     * إزالة صلاحية
     */
    public void removePermission(Permission permission) {
        permissions.remove(permission);
        permission.setRole(null);
    }

    /**
     * التحقق من وجود صلاحية معينة
     */
    public boolean hasPermission(String moduleName, String permissionType) {
        return permissions.stream()
                .anyMatch(p -> p.getModuleName().equals(moduleName) && 
                              p.hasPermission(permissionType));
    }

    /**
     * التحقق من صلاحية القراءة لوحدة معينة
     */
    public boolean canRead(String moduleName) {
        return hasPermission(moduleName, "READ");
    }

    /**
     * التحقق من صلاحية الكتابة لوحدة معينة
     */
    public boolean canWrite(String moduleName) {
        return hasPermission(moduleName, "WRITE");
    }

    /**
     * التحقق من صلاحية الحذف لوحدة معينة
     */
    public boolean canDelete(String moduleName) {
        return hasPermission(moduleName, "DELETE");
    }

    /**
     * التحقق من صلاحية الطباعة لوحدة معينة
     */
    public boolean canPrint(String moduleName) {
        return hasPermission(moduleName, "PRINT");
    }

    /**
     * التحقق من صلاحية التصدير لوحدة معينة
     */
    public boolean canExport(String moduleName) {
        return hasPermission(moduleName, "EXPORT");
    }

    @Override
    public String toString() {
        return "Role{" +
                "id=" + getId() +
                ", name='" + name + '\'' +
                ", nameEn='" + nameEn + '\'' +
                ", description='" + description + '\'' +
                ", isActive=" + isActive +
                '}';
    }
}
