import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;

/**
 * استكشاف جداول قاعدة البيانات للعثور على جداول الأصناف Database Table Explorer to find item tables
 */
public class DatabaseTableExplorer {

    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("   استكشاف جداول قاعدة البيانات");
        System.out.println("   Database Table Explorer");
        System.out.println("====================================");
        System.out.println();

        DatabaseTableExplorer explorer = new DatabaseTableExplorer();
        explorer.exploreDatabase();
    }

    /**
     * استكشاف قاعدة البيانات
     */
    public void exploreDatabase() {
        // بيانات الاتصال
        String url = "*************************************";
        String username = "ysdba2";
        String password = "ys123";

        Connection connection = null;

        try {
            System.out.println("🔄 الاتصال بقاعدة البيانات...");
            connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ تم الاتصال بنجاح!");
            System.out.println();

            // 1. عرض جميع الجداول
            listAllTables(connection);

            // 2. البحث عن جداول الأصناف
            searchItemTables(connection);

            // 3. البحث عن الجداول المحددة
            checkSpecificTables(connection);

            // 4. البحث في جداول المستخدمين الآخرين
            searchOtherUserTables(connection);

        } catch (SQLException e) {
            System.err.println("❌ خطأ في الاتصال: " + e.getMessage());
            e.printStackTrace();

        } finally {
            if (connection != null) {
                try {
                    connection.close();
                    System.out.println("🔒 تم إغلاق الاتصال");
                } catch (SQLException e) {
                    System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
                }
            }
        }
    }

    /**
     * عرض جميع الجداول
     */
    private void listAllTables(Connection connection) throws SQLException {
        System.out.println("📋 جميع الجداول في قاعدة البيانات:");
        System.out.println("=====================================");

        String query = """
                    SELECT table_name, num_rows, last_analyzed
                    FROM user_tables
                    ORDER BY table_name
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            int count = 0;
            while (rs.next()) {
                String tableName = rs.getString("table_name");
                Object numRows = rs.getObject("num_rows");
                java.sql.Date lastAnalyzed = rs.getDate("last_analyzed");

                System.out.printf("📋 %s", tableName);
                if (numRows != null) {
                    System.out.printf(" (%s سجل)", numRows);
                }
                if (lastAnalyzed != null) {
                    System.out.printf(" - %s", lastAnalyzed);
                }
                System.out.println();
                count++;
            }

            System.out.println("\nإجمالي الجداول: " + count);
        }

        System.out.println();
    }

    /**
     * البحث عن جداول الأصناف
     */
    private void searchItemTables(Connection connection) throws SQLException {
        System.out.println("🔍 البحث عن جداول الأصناف:");
        System.out.println("============================");

        String[] searchTerms =
                {"ITEM", "ITM", "PRODUCT", "PROD", "INVENTORY", "INV", "STOCK", "IAS"};

        for (String term : searchTerms) {
            System.out.println("\n🔍 البحث عن جداول تحتوي على: " + term);

            String query = """
                        SELECT table_name, num_rows
                        FROM user_tables
                        WHERE UPPER(table_name) LIKE ?
                        ORDER BY table_name
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(query)) {
                stmt.setString(1, "%" + term + "%");

                try (ResultSet rs = stmt.executeQuery()) {
                    boolean found = false;
                    while (rs.next()) {
                        String tableName = rs.getString("table_name");
                        Object numRows = rs.getObject("num_rows");

                        System.out.printf("  📋 %s", tableName);
                        if (numRows != null) {
                            System.out.printf(" (%s سجل)", numRows);
                        }
                        System.out.println();
                        found = true;
                    }

                    if (!found) {
                        System.out.println("  ❌ لم يتم العثور على جداول");
                    }
                }
            }
        }

        System.out.println();
    }

    /**
     * فحص الجداول المحددة
     */
    private void checkSpecificTables(Connection connection) throws SQLException {
        System.out.println("🎯 فحص الجداول المحددة:");
        System.out.println("========================");

        String[] targetTables = {"IAS_ITM_MST", "IAS_ITM_DTL"};

        for (String tableName : targetTables) {
            System.out.println("\n🔍 فحص جدول: " + tableName);

            // فحص وجود الجدول
            String checkQuery = """
                        SELECT COUNT(*) as table_exists
                        FROM user_tables
                        WHERE table_name = ?
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(checkQuery)) {
                stmt.setString(1, tableName);

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next() && rs.getInt("table_exists") > 0) {
                        System.out.println("  ✅ الجدول موجود");

                        // عرض هيكل الجدول
                        showTableStructure(connection, tableName);

                        // عرض عينة من البيانات
                        showSampleData(connection, tableName);

                    } else {
                        System.out.println("  ❌ الجدول غير موجود");
                    }
                }
            }
        }

        System.out.println();
    }

    /**
     * البحث في جداول المستخدمين الآخرين
     */
    private void searchOtherUserTables(Connection connection) throws SQLException {
        System.out.println("🔍 البحث في جداول المستخدمين الآخرين:");
        System.out.println("=========================================");

        String query = """
                    SELECT owner, table_name, num_rows
                    FROM all_tables
                    WHERE (UPPER(table_name) LIKE '%IAS%'
                       OR UPPER(table_name) LIKE '%ITM%'
                       OR UPPER(table_name) LIKE '%ITEM%')
                    AND owner != USER
                    ORDER BY owner, table_name
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            boolean found = false;
            String currentOwner = "";

            while (rs.next()) {
                String owner = rs.getString("owner");
                String tableName = rs.getString("table_name");
                Object numRows = rs.getObject("num_rows");

                if (!owner.equals(currentOwner)) {
                    System.out.println("\n👤 المستخدم: " + owner);
                    currentOwner = owner;
                }

                System.out.printf("  📋 %s", tableName);
                if (numRows != null) {
                    System.out.printf(" (%s سجل)", numRows);
                }
                System.out.println();
                found = true;
            }

            if (!found) {
                System.out.println("❌ لم يتم العثور على جداول أصناف في مستخدمين آخرين");
            }
        }

        System.out.println();
    }

    /**
     * عرض هيكل الجدول
     */
    private void showTableStructure(Connection connection, String tableName) throws SQLException {
        System.out.println("    📋 هيكل الجدول:");

        String query = """
                    SELECT column_name, data_type, data_length, nullable
                    FROM user_tab_columns
                    WHERE table_name = ?
                    ORDER BY column_id
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, tableName);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String columnName = rs.getString("column_name");
                    String dataType = rs.getString("data_type");
                    int dataLength = rs.getInt("data_length");
                    String nullable = rs.getString("nullable");

                    System.out.printf("      📌 %s: %s(%d) %s%n", columnName, dataType, dataLength,
                            "Y".equals(nullable) ? "NULL" : "NOT NULL");
                }
            }
        }
    }

    /**
     * عرض عينة من البيانات
     */
    private void showSampleData(Connection connection, String tableName) throws SQLException {
        System.out.println("    📊 عينة من البيانات:");

        String query = "SELECT * FROM " + tableName + " WHERE ROWNUM <= 3";

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            // عرض أسماء الأعمدة
            System.out.print("      ");
            for (int i = 1; i <= columnCount; i++) {
                System.out.printf("%-15s", metaData.getColumnName(i));
            }
            System.out.println();

            // عرض البيانات
            int rowCount = 0;
            while (rs.next() && rowCount < 3) {
                System.out.print("      ");
                for (int i = 1; i <= columnCount; i++) {
                    Object value = rs.getObject(i);
                    String displayValue = value != null ? value.toString() : "NULL";
                    if (displayValue.length() > 12) {
                        displayValue = displayValue.substring(0, 12) + "...";
                    }
                    System.out.printf("%-15s", displayValue);
                }
                System.out.println();
                rowCount++;
            }

            if (rowCount == 0) {
                System.out.println("      ❌ الجدول فارغ");
            }

        } catch (SQLException e) {
            System.out.println("      ❌ خطأ في قراءة البيانات: " + e.getMessage());
        }
    }
}
