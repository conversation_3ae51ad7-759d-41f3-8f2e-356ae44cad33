package com.shipment.erp.service;

import com.shipment.erp.model.User;
import com.shipment.erp.model.Role;
import com.shipment.erp.model.Branch;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service للمستخدمين
 */
public interface UserService extends BaseService<User> {

    /**
     * البحث عن مستخدم باسم المستخدم
     */
    Optional<User> findByUsername(String username);

    /**
     * البحث عن مستخدم بالبريد الإلكتروني
     */
    Optional<User> findByEmail(String email);

    /**
     * البحث عن المستخدمين بالدور
     */
    List<User> findByRole(Role role);

    /**
     * البحث عن المستخدمين بالفرع
     */
    List<User> findByBranch(Branch branch);

    /**
     * البحث عن المستخدمين بالاسم الكامل
     */
    List<User> findByFullNameContaining(String fullName);

    /**
     * البحث عن المستخدمين النشطين
     */
    List<User> findActiveUsers();

    /**
     * البحث عن المستخدمين غير النشطين
     */
    List<User> findInactiveUsers();

    /**
     * البحث عن المستخدمين المقفلين
     */
    List<User> findLockedUsers();

    /**
     * التحقق من وجود مستخدم باسم المستخدم
     */
    boolean existsByUsername(String username);

    /**
     * التحقق من وجود مستخدم بالبريد الإلكتروني
     */
    boolean existsByEmail(String email);

    /**
     * إنشاء مستخدم جديد
     */
    User createUser(String username, String password, String fullName, String email, Role role, Branch branch);

    /**
     * تحديث كلمة مرور المستخدم
     */
    void updatePassword(Long userId, String oldPassword, String newPassword);

    /**
     * إعادة تعيين كلمة مرور المستخدم
     */
    String resetPassword(Long userId);

    /**
     * تغيير كلمة مرور المستخدم (بواسطة المدير)
     */
    void changePassword(Long userId, String newPassword);

    /**
     * تسجيل دخول المستخدم
     */
    LoginResult login(String username, String password, String ipAddress, String userAgent);

    /**
     * تسجيل خروج المستخدم
     */
    void logout(Long userId);

    /**
     * قفل حساب المستخدم
     */
    void lockUser(Long userId, int minutes);

    /**
     * إلغاء قفل حساب المستخدم
     */
    void unlockUser(Long userId);

    /**
     * إلغاء قفل جميع المستخدمين المنتهية صلاحية قفلهم
     */
    int unlockExpiredUsers();

    /**
     * تحديث آخر تسجيل دخول
     */
    void updateLastLogin(Long userId);

    /**
     * التحقق من صحة كلمة المرور
     */
    boolean validatePassword(String password);

    /**
     * التحقق من قوة كلمة المرور
     */
    PasswordStrength checkPasswordStrength(String password);

    /**
     * تشفير كلمة المرور
     */
    String encryptPassword(String password);

    /**
     * التحقق من تطابق كلمة المرور
     */
    boolean matchesPassword(String rawPassword, String encodedPassword);

    /**
     * البحث المتقدم في المستخدمين
     */
    List<User> advancedSearch(String username, String fullName, String email, 
                             Role role, Branch branch, Boolean isActive);

    /**
     * الحصول على المستخدمين الذين يحتاجون لتغيير كلمة المرور
     */
    List<User> findUsersNeedingPasswordChange(int daysOld);

    /**
     * الحصول على إحصائيات المستخدمين
     */
    UserStatistics getUserStatistics();

    /**
     * تصدير بيانات المستخدمين
     */
    byte[] exportUsers(String format, List<Long> userIds);

    /**
     * استيراد بيانات المستخدمين
     */
    void importUsers(byte[] data, String format, boolean updateExisting);

    /**
     * نتيجة تسجيل الدخول
     */
    class LoginResult {
        private boolean success;
        private String message;
        private User user;
        private String token;

        public LoginResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public LoginResult(boolean success, String message, User user) {
            this(success, message);
            this.user = user;
        }

        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public User getUser() { return user; }
        public void setUser(User user) { this.user = user; }

        public String getToken() { return token; }
        public void setToken(String token) { this.token = token; }
    }

    /**
     * قوة كلمة المرور
     */
    enum PasswordStrength {
        VERY_WEAK("ضعيف جداً"),
        WEAK("ضعيف"),
        MEDIUM("متوسط"),
        STRONG("قوي"),
        VERY_STRONG("قوي جداً");

        private final String description;

        PasswordStrength(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * إحصائيات المستخدمين
     */
    class UserStatistics {
        private long totalUsers;
        private long activeUsers;
        private long inactiveUsers;
        private long lockedUsers;
        private long usersNeedingPasswordChange;

        public UserStatistics() {}

        public UserStatistics(long totalUsers, long activeUsers, long inactiveUsers,
                            long lockedUsers, long usersNeedingPasswordChange) {
            this.totalUsers = totalUsers;
            this.activeUsers = activeUsers;
            this.inactiveUsers = inactiveUsers;
            this.lockedUsers = lockedUsers;
            this.usersNeedingPasswordChange = usersNeedingPasswordChange;
        }

        // Getters and setters
        public long getTotalUsers() { return totalUsers; }
        public void setTotalUsers(long totalUsers) { this.totalUsers = totalUsers; }

        public long getActiveUsers() { return activeUsers; }
        public void setActiveUsers(long activeUsers) { this.activeUsers = activeUsers; }

        public long getInactiveUsers() { return inactiveUsers; }
        public void setInactiveUsers(long inactiveUsers) { this.inactiveUsers = inactiveUsers; }

        public long getLockedUsers() { return lockedUsers; }
        public void setLockedUsers(long lockedUsers) { this.lockedUsers = lockedUsers; }

        public long getUsersNeedingPasswordChange() { return usersNeedingPasswordChange; }
        public void setUsersNeedingPasswordChange(long usersNeedingPasswordChange) { 
            this.usersNeedingPasswordChange = usersNeedingPasswordChange; 
        }
    }
}
