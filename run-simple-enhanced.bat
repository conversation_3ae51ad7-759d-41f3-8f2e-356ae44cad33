@echo off
echo ========================================
echo    Enhanced Ship ERP System
echo ========================================
echo.

echo [INFO] Starting Enhanced System...
echo.

REM Check Java
echo [1/3] Checking Java...
java -version > nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java not installed or not in PATH
    pause
    exit /b 1
)
echo [OK] Java available

REM Check files
echo [2/3] Checking files...
if not exist "src\main\java\EnhancedShipERP.java" (
    echo [ERROR] Enhanced application file not found
    pause
    exit /b 1
)
echo [OK] Files available

REM Compile and run
echo [3/3] Compiling and running...
cd src\main\java

echo [INFO] Compiling without external libraries...
javac -encoding UTF-8 ArabicDemo.java GeneralSettingsWindow.java
if %errorlevel% neq 0 (
    echo [ERROR] Compilation failed
    cd ..\..\..
    pause
    exit /b 1
)

echo [OK] Compilation successful
echo.
echo ========================================
echo    System Started Successfully!
echo ========================================
echo.

echo [INFO] System is now running...
echo [INFO] To exit, close the application window

java -Dfile.encoding=UTF-8 -Duser.language=ar -Duser.country=SA ArabicDemo

echo.
echo [INFO] Application closed
cd ..\..\..

pause
