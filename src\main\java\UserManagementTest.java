import javax.swing.*;
import java.awt.*;

/**
 * اختبار نظام إدارة المستخدمين
 * User Management System Test
 */
public class UserManagementTest {
    
    public static void main(String[] args) {
        // تعيين الترميز والمحلية العربية
        System.setProperty("file.encoding", "UTF-8");
        
        SwingUtilities.invokeLater(() -> {
            // تطبيق المظهر المحفوظ
            SettingsManager.applyStoredTheme();
            
            // إنشاء نافذة رئيسية بسيطة للاختبار
            JFrame testFrame = new JFrame("اختبار نظام إدارة المستخدمين");
            testFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            testFrame.setSize(400, 300);
            testFrame.setLocationRelativeTo(null);
            testFrame.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            
            // إنشاء لوحة بسيطة مع زر لفتح إدارة المستخدمين
            JPanel panel = new JPanel(new BorderLayout());
            panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            panel.setBorder(BorderFactory.createEmptyBorder(50, 50, 50, 50));
            
            JLabel titleLabel = new JLabel("نظام إدارة المستخدمين المتقدم");
            titleLabel.setFont(new Font("Tahoma", Font.BOLD, 18));
            titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
            
            JButton openUsersBtn = new JButton("فتح إدارة المستخدمين");
            openUsersBtn.setFont(new Font("Tahoma", Font.PLAIN, 14));
            openUsersBtn.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            openUsersBtn.setPreferredSize(new Dimension(200, 50));
            openUsersBtn.setBackground(new Color(52, 152, 219));
            openUsersBtn.setForeground(Color.WHITE);
            openUsersBtn.setFocusPainted(false);
            openUsersBtn.setBorderPainted(false);
            
            openUsersBtn.addActionListener(e -> {
                UserManagementWindow usersWindow = new UserManagementWindow(testFrame);
                usersWindow.setVisible(true);
            });
            
            JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
            buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            buttonPanel.add(openUsersBtn);
            
            panel.add(titleLabel, BorderLayout.NORTH);
            panel.add(buttonPanel, BorderLayout.CENTER);
            
            testFrame.add(panel);
            testFrame.setVisible(true);
            
            System.out.println("تم تشغيل اختبار نظام إدارة المستخدمين بنجاح!");
        });
    }
}
