<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.shipment.erp.controller.settings.GeneralSettingsController">
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
   
   <!-- عنوان الصفحة -->
   <Label styleClass="page-title" text="%settings.general.title">
      <font>
         <Font size="20.0" />
      </font>
   </Label>
   
   <Separator />
   
   <!-- محتوى الإعدادات -->
   <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
      <VBox spacing="20.0">
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
         
         <!-- إعدادات الشركة -->
         <VBox spacing="10.0" styleClass="settings-group">
            <Label styleClass="group-title" text="%settings.company.info">
               <font>
                  <Font size="16.0" />
               </font>
            </Label>
            
            <GridPane hgap="10.0" vgap="10.0">
               <columnConstraints>
                  <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                  <ColumnConstraints hgrow="ALWAYS" />
               </columnConstraints>
               
               <Label text="%general.company.name" GridPane.columnIndex="0" GridPane.rowIndex="0" />
               <TextField fx:id="companyNameField" GridPane.columnIndex="1" GridPane.rowIndex="0" />
               
               <Label text="%general.company.address" GridPane.columnIndex="0" GridPane.rowIndex="1" />
               <TextArea fx:id="companyAddressField" prefRowCount="2" GridPane.columnIndex="1" GridPane.rowIndex="1" />
               
               <Label text="%general.company.phone" GridPane.columnIndex="0" GridPane.rowIndex="2" />
               <TextField fx:id="companyPhoneField" GridPane.columnIndex="1" GridPane.rowIndex="2" />
               
               <Label text="%general.company.email" GridPane.columnIndex="0" GridPane.rowIndex="3" />
               <TextField fx:id="companyEmailField" GridPane.columnIndex="1" GridPane.rowIndex="3" />
               
               <Label text="%general.company.tax.number" GridPane.columnIndex="0" GridPane.rowIndex="4" />
               <TextField fx:id="taxNumberField" GridPane.columnIndex="1" GridPane.rowIndex="4" />
               
               <Label text="%general.company.commercial.register" GridPane.columnIndex="0" GridPane.rowIndex="5" />
               <TextField fx:id="commercialRegisterField" GridPane.columnIndex="1" GridPane.rowIndex="5" />
            </GridPane>
         </VBox>
         
         <!-- إعدادات النظام -->
         <VBox spacing="10.0" styleClass="settings-group">
            <Label styleClass="group-title" text="%settings.system.config">
               <font>
                  <Font size="16.0" />
               </font>
            </Label>
            
            <GridPane hgap="10.0" vgap="10.0">
               <columnConstraints>
                  <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                  <ColumnConstraints hgrow="ALWAYS" />
               </columnConstraints>
               
               <Label text="%general.default.currency" GridPane.columnIndex="0" GridPane.rowIndex="0" />
               <ComboBox fx:id="defaultCurrencyCombo" prefWidth="200.0" GridPane.columnIndex="1" GridPane.rowIndex="0" />
               
               <Label text="%general.decimal.places" GridPane.columnIndex="0" GridPane.rowIndex="1" />
               <Spinner fx:id="decimalPlacesSpinner" prefWidth="100.0" GridPane.columnIndex="1" GridPane.rowIndex="1" />
               
               <Label text="%general.date.format" GridPane.columnIndex="0" GridPane.rowIndex="2" />
               <ComboBox fx:id="dateFormatCombo" prefWidth="200.0" GridPane.columnIndex="1" GridPane.rowIndex="2" />
            </GridPane>
         </VBox>
         
         <!-- إعدادات النسخ الاحتياطي -->
         <VBox spacing="10.0" styleClass="settings-group">
            <Label styleClass="group-title" text="%settings.backup.config">
               <font>
                  <Font size="16.0" />
               </font>
            </Label>
            
            <GridPane hgap="10.0" vgap="10.0">
               <columnConstraints>
                  <ColumnConstraints hgrow="NEVER" minWidth="150.0" />
                  <ColumnConstraints hgrow="ALWAYS" />
               </columnConstraints>
               
               <Label text="%general.backup.enabled" GridPane.columnIndex="0" GridPane.rowIndex="0" />
               <CheckBox fx:id="backupEnabledCheck" GridPane.columnIndex="1" GridPane.rowIndex="0" />
               
               <Label text="%general.backup.interval" GridPane.columnIndex="0" GridPane.rowIndex="1" />
               <HBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="1">
                  <Spinner fx:id="backupIntervalSpinner" prefWidth="100.0" />
                  <Label text="%hours" />
               </HBox>
               
               <Label text="%backup.directory" GridPane.columnIndex="0" GridPane.rowIndex="2" />
               <HBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="2">
                  <TextField fx:id="backupDirectoryField" HBox.hgrow="ALWAYS" />
                  <Button onAction="#handleBrowseBackupDirectory" text="%button.browse" />
               </HBox>
            </GridPane>
         </VBox>
         
         <!-- أزرار الحفظ والإلغاء -->
         <HBox alignment="CENTER_RIGHT" spacing="10.0">
            <Button fx:id="saveButton" onAction="#handleSave" prefWidth="100.0" styleClass="primary-button" text="%button.save" />
            <Button fx:id="cancelButton" onAction="#handleCancel" prefWidth="100.0" text="%button.cancel" />
            <Button fx:id="resetButton" onAction="#handleReset" prefWidth="100.0" text="%button.reset" />
         </HBox>
      </VBox>
   </ScrollPane>
</VBox>
