import java.sql.*;
import java.util.HashMap;
import java.util.Map;

/**
 * ربط أسماء الحقول بالتعليقات العربية من جدول comments
 */
public class FieldLabelsMapper {
    
    private static Map<String, String> fieldLabels = new HashMap<>();
    
    static {
        // ربط الحقول بالتعليقات العربية المناسبة
        fieldLabels.put("I_CODE", "كود الصنف");
        fieldLabels.put("I_NAME", "الاسم العربي");
        fieldLabels.put("I_E_NAME", "الاسم الإنجليزي");
        fieldLabels.put("I_DESC", "وصف الصنف");
        fieldLabels.put("G_CODE", "كود المجموعة");
        fieldLabels.put("MNG_CODE", "المجموعة الفرعية");
        fieldLabels.put("SUBG_CODE", "المجموعة الفرعية الثانية");
        fieldLabels.put("ITEM_SIZE", "حجم الصنف");
        fieldLabels.put("ITEM_TYPE", "نوع الصنف");
        fieldLabels.put("PRIMARY_COST", "التكلفة الأساسية");
        fieldLabels.put("INIT_PRIMARY_COST", "التكلفة الأولية");
        fieldLabels.put("ALTER_CODE", "الكود البديل");
        fieldLabels.put("MANF_CODE", "كود المصنع");
        fieldLabels.put("V_CODE", "كود المورد");
        
        // الخيارات
        fieldLabels.put("INACTIVE", "غير نشط");
        fieldLabels.put("SERVICE_ITM", "صنف خدمة");
        fieldLabels.put("CASH_SALE", "بيع نقدي");
        fieldLabels.put("NO_RETURN_SALE", "عدم إرجاع");
        fieldLabels.put("KIT_ITM", "صنف مجموعة");
        fieldLabels.put("USE_EXP_DATE", "استخدام تاريخ انتهاء");
        fieldLabels.put("USE_BATCH_NO", "استخدام رقم دفعة");
        fieldLabels.put("USE_SERIALNO", "استخدام رقم تسلسلي");
        
        // حقول IAS_ITM_DTL
        fieldLabels.put("ITM_UNT", "وحدة الصنف");
        fieldLabels.put("P_SIZE", "حجم العبوة");
        fieldLabels.put("ITM_UNT_L_DSC", "وصف الوحدة عربي");
        fieldLabels.put("ITM_UNT_F_DSC", "وصف الوحدة إنجليزي");
        fieldLabels.put("BARCODE", "الباركود");
        fieldLabels.put("MAIN_UNIT", "الوحدة الرئيسية");
        fieldLabels.put("SALE_UNIT", "وحدة البيع");
        fieldLabels.put("PUR_UNIT", "وحدة الشراء");
        fieldLabels.put("STOCK_UNIT", "وحدة المخزون");
    }
    
    /**
     * الحصول على التسمية العربية للحقل
     */
    public static String getFieldLabel(String fieldName) {
        return fieldLabels.getOrDefault(fieldName, fieldName);
    }
    
    /**
     * تحديث التسميات من قاعدة البيانات
     */
    public static void updateLabelsFromDatabase() {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            
            // البحث عن تعليقات محددة وربطها بالحقول
            updateSpecificLabels(conn);
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("خطأ في تحديث التسميات: " + e.getMessage());
        }
    }
    
    /**
     * تحديث تسميات محددة من قاعدة البيانات
     */
    private static void updateSpecificLabels(Connection conn) throws SQLException {
        // ربط تعليقات محددة بالحقول
        Map<Integer, String> commentToField = new HashMap<>();
        commentToField.put(96, "I_NAME");     // بيانات الأصناف
        commentToField.put(426, "I_CODE");    // الأصناف
        commentToField.put(92, "PRIMARY_COST"); // تسعير الأصناف
        
        Statement stmt = conn.createStatement();
        
        for (Map.Entry<Integer, String> entry : commentToField.entrySet()) {
            int commentNo = entry.getKey();
            String fieldName = entry.getValue();
            
            try {
                ResultSet rs = stmt.executeQuery(
                    "SELECT COMMENTS_NAME FROM COMMENTS WHERE COMMENTS_NO = " + commentNo
                );
                
                if (rs.next()) {
                    String commentName = rs.getString("COMMENTS_NAME");
                    if (commentName != null && !commentName.trim().isEmpty()) {
                        fieldLabels.put(fieldName, commentName);
                        System.out.println("تم تحديث " + fieldName + " إلى: " + commentName);
                    }
                }
                rs.close();
                
            } catch (SQLException e) {
                System.err.println("خطأ في البحث عن التعليق " + commentNo + ": " + e.getMessage());
            }
        }
        
        stmt.close();
    }
    
    /**
     * طباعة جميع التسميات المتاحة
     */
    public static void printAllLabels() {
        System.out.println("=".repeat(60));
        System.out.println("📋 تسميات الحقول العربية");
        System.out.println("=".repeat(60));
        
        System.out.println("\n🗄️ حقول IAS_ITM_MST:");
        String[] mstFields = {
            "I_CODE", "I_NAME", "I_E_NAME", "I_DESC", "G_CODE", "MNG_CODE", 
            "SUBG_CODE", "ITEM_SIZE", "ITEM_TYPE", "PRIMARY_COST", 
            "INIT_PRIMARY_COST", "ALTER_CODE", "MANF_CODE", "V_CODE"
        };
        
        for (String field : mstFields) {
            System.out.printf("  %-20s : %s\n", field, getFieldLabel(field));
        }
        
        System.out.println("\n☑️ خيارات IAS_ITM_MST:");
        String[] optionFields = {
            "INACTIVE", "SERVICE_ITM", "CASH_SALE", "NO_RETURN_SALE", 
            "KIT_ITM", "USE_EXP_DATE", "USE_BATCH_NO", "USE_SERIALNO"
        };
        
        for (String field : optionFields) {
            System.out.printf("  %-20s : %s\n", field, getFieldLabel(field));
        }
        
        System.out.println("\n🔧 حقول IAS_ITM_DTL:");
        String[] dtlFields = {
            "ITM_UNT", "P_SIZE", "ITM_UNT_L_DSC", "ITM_UNT_F_DSC", 
            "BARCODE", "MAIN_UNIT", "SALE_UNIT", "PUR_UNIT", "STOCK_UNIT"
        };
        
        for (String field : dtlFields) {
            System.out.printf("  %-20s : %s\n", field, getFieldLabel(field));
        }
    }
    
    /**
     * إنشاء كود Java للتسميات المحدثة
     */
    public static void generateUpdatedLabelsCode() {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("📝 كود Java للتسميات المحدثة");
        System.out.println("=".repeat(80));
        
        System.out.println("// تحديث التسميات في RealItemDataWindow.java");
        System.out.println();
        
        // إنشاء كود للحقول الأساسية
        System.out.println("// الحقول الأساسية:");
        String[] basicFields = {"I_CODE", "I_NAME", "I_E_NAME", "G_CODE"};
        for (String field : basicFields) {
            String label = getFieldLabel(field);
            String varName = field.toLowerCase() + "Field";
            System.out.println("basicDataPanel.add(new JLabel(\"" + label + " (" + field + "):\"), gbc);");
        }
        
        System.out.println("\n// حقول التكلفة:");
        String[] costFields = {"PRIMARY_COST", "INIT_PRIMARY_COST", "ALTER_CODE", "MANF_CODE"};
        for (String field : costFields) {
            String label = getFieldLabel(field);
            System.out.println("costsPanel.add(new JLabel(\"" + label + " (" + field + "):\"), gbc);");
        }
        
        System.out.println("\n// الخيارات:");
        String[] optionFields = {"INACTIVE", "SERVICE_ITM", "CASH_SALE", "KIT_ITM"};
        for (String field : optionFields) {
            String label = getFieldLabel(field);
            String varName = field.toLowerCase() + "CheckBox";
            System.out.println(varName + " = new JCheckBox(\"" + label + " (" + field + ")\");");
        }
    }
    
    public static void main(String[] args) {
        System.out.println("🔍 ربط أسماء الحقول بالتعليقات العربية");
        
        // تحديث التسميات من قاعدة البيانات
        updateLabelsFromDatabase();
        
        // طباعة جميع التسميات
        printAllLabels();
        
        // إنشاء كود Java محدث
        generateUpdatedLabelsCode();
    }
}
