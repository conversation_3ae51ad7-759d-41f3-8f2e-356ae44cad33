import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * تحميل وتثبيت المكتبات المطلوبة تلقائياً Automatic Library Downloader and Installer
 */
public class LibraryDownloader {

    private static final String LIB_DIR = "lib";
    private static final Map<String, String> REQUIRED_LIBRARIES = new LinkedHashMap<>();

    static {
        // Oracle JDBC Driver
        REQUIRED_LIBRARIES.put("ojdbc11.jar",
                "https://download.oracle.com/otn-pub/otn_software/jdbc/233/ojdbc11.jar");

        // Oracle Internationalization (مطلوب للأحرف العربية)
        REQUIRED_LIBRARIES.put("orai18n.jar",
                "https://download.oracle.com/otn-pub/otn_software/jdbc/233/orai18n.jar");

        // Apache Commons DBCP2
        REQUIRED_LIBRARIES.put("commons-dbcp2-2.9.0.jar",
                "https://repo1.maven.org/maven2/org/apache/commons/commons-dbcp2/2.9.0/commons-dbcp2-2.9.0.jar");

        // Apache Commons Pool2
        REQUIRED_LIBRARIES.put("commons-pool2-2.11.1.jar",
                "https://repo1.maven.org/maven2/org/apache/commons/commons-pool2/2.11.1/commons-pool2-2.11.1.jar");

        // Apache Commons Logging
        REQUIRED_LIBRARIES.put("commons-logging-1.2.jar",
                "https://repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar");

        // JSON Library
        REQUIRED_LIBRARIES.put("json-20230227.jar",
                "https://repo1.maven.org/maven2/org/json/json/20230227/json-20230227.jar");

        // H2 Database (للاختبار المحلي)
        REQUIRED_LIBRARIES.put("h2-2.2.224.jar",
                "https://repo1.maven.org/maven2/com/h2database/h2/2.2.224/h2-2.2.224.jar");
    }

    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("   تحميل وتثبيت المكتبات المطلوبة");
        System.out.println("   Downloading Required Libraries");
        System.out.println("====================================");

        LibraryDownloader downloader = new LibraryDownloader();
        downloader.downloadAllLibraries();
        downloader.createBatchFiles();

        System.out.println("\n====================================");
        System.out.println("   تم الانتهاء من التثبيت!");
        System.out.println("   Installation Complete!");
        System.out.println("====================================");
        System.out.println("\nاستخدم الملفات التالية:");
        System.out.println("1. compile_with_oracle.bat - للتجميع");
        System.out.println("2. run_with_oracle.bat - للتشغيل");
    }

    /**
     * تحميل جميع المكتبات المطلوبة
     */
    public void downloadAllLibraries() {
        // إنشاء مجلد lib إذا لم يكن موجوداً
        createLibDirectory();

        for (Map.Entry<String, String> library : REQUIRED_LIBRARIES.entrySet()) {
            String fileName = library.getKey();
            String url = library.getValue();

            File libFile = new File(LIB_DIR, fileName);

            if (libFile.exists()) {
                System.out.println("✅ " + fileName + " موجود مسبقاً");
                continue;
            }

            System.out.println("📥 تحميل " + fileName + "...");

            try {
                downloadFile(url, libFile);
                System.out.println("✅ تم تحميل " + fileName + " بنجاح");
            } catch (Exception e) {
                System.err.println("❌ فشل تحميل " + fileName + ": " + e.getMessage());

                // محاولة تحميل من مصدر بديل
                if (tryAlternativeDownload(fileName, libFile)) {
                    System.out.println("✅ تم تحميل " + fileName + " من مصدر بديل");
                } else {
                    System.err.println("❌ فشل تحميل " + fileName + " من جميع المصادر");
                }
            }
        }
    }

    /**
     * إنشاء مجلد المكتبات
     */
    private void createLibDirectory() {
        File libDir = new File(LIB_DIR);
        if (!libDir.exists()) {
            if (libDir.mkdirs()) {
                System.out.println("📁 تم إنشاء مجلد " + LIB_DIR);
            } else {
                System.err.println("❌ فشل في إنشاء مجلد " + LIB_DIR);
            }
        }
    }

    /**
     * تحميل ملف من URL
     */
    private void downloadFile(String urlString, File outputFile) throws IOException {
        URL url = new URL(urlString);

        // إعداد الاتصال
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(30000); // 30 ثانية
        connection.setReadTimeout(60000); // 60 ثانية

        // إضافة User-Agent لتجنب الحظر
        connection.setRequestProperty("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

        // التحقق من رمز الاستجابة
        int responseCode = connection.getResponseCode();
        if (responseCode != HttpURLConnection.HTTP_OK) {
            throw new IOException("HTTP " + responseCode + ": " + connection.getResponseMessage());
        }

        // الحصول على حجم الملف
        long fileSize = connection.getContentLengthLong();

        // تحميل الملف
        try (InputStream inputStream = connection.getInputStream();
                FileOutputStream outputStream = new FileOutputStream(outputFile);
                BufferedInputStream bufferedInput = new BufferedInputStream(inputStream);
                BufferedOutputStream bufferedOutput = new BufferedOutputStream(outputStream)) {

            byte[] buffer = new byte[8192];
            long totalBytesRead = 0;
            int bytesRead;

            while ((bytesRead = bufferedInput.read(buffer)) != -1) {
                bufferedOutput.write(buffer, 0, bytesRead);
                totalBytesRead += bytesRead;

                // عرض التقدم
                if (fileSize > 0) {
                    int progress = (int) ((totalBytesRead * 100) / fileSize);
                    System.out.print("\r   التقدم: " + progress + "% ("
                            + formatBytes(totalBytesRead) + "/" + formatBytes(fileSize) + ")");
                }
            }

            System.out.println(); // سطر جديد بعد التقدم
        }

        connection.disconnect();
    }

    /**
     * محاولة تحميل من مصدر بديل
     */
    private boolean tryAlternativeDownload(String fileName, File outputFile) {
        // مصادر بديلة للمكتبات
        Map<String, String> alternativeUrls = new HashMap<>();

        // Oracle JDBC من مصادر بديلة
        if (fileName.startsWith("ojdbc")) {
            alternativeUrls.put(fileName,
                    "https://repo1.maven.org/maven2/com/oracle/database/jdbc/ojdbc11/21.7.0.0/ojdbc11-21.7.0.0.jar");
        }

        // Oracle Internationalization من مصادر بديلة
        if (fileName.startsWith("orai18n")) {
            alternativeUrls.put(fileName,
                    "https://repo1.maven.org/maven2/com/oracle/database/nls/orai18n/21.7.0.0/orai18n-21.7.0.0.jar");
        }

        String alternativeUrl = alternativeUrls.get(fileName);
        if (alternativeUrl != null) {
            try {
                downloadFile(alternativeUrl, outputFile);
                return true;
            } catch (Exception e) {
                System.err.println("   فشل المصدر البديل: " + e.getMessage());
            }
        }

        return false;
    }

    /**
     * تنسيق حجم الملف
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024)
            return bytes + " B";
        if (bytes < 1024 * 1024)
            return String.format("%.1f KB", bytes / 1024.0);
        return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
    }

    /**
     * إنشاء ملفات batch للتجميع والتشغيل
     */
    public void createBatchFiles() {
        createCompileBatchFile();
        createRunBatchFile();
        createTestBatchFile();
    }

    /**
     * إنشاء ملف التجميع
     */
    private void createCompileBatchFile() {
        try (PrintWriter writer = new PrintWriter(new FileWriter("compile_with_oracle.bat"))) {
            writer.println("@echo off");
            writer.println("chcp 65001 > nul");
            writer.println("title تجميع النظام مع مكتبات Oracle");
            writer.println("echo ====================================");
            writer.println("echo    تجميع النظام مع مكتبات Oracle");
            writer.println("echo    Compiling with Oracle Libraries");
            writer.println("echo ====================================");
            writer.println("echo.");
            writer.println();
            writer.println("echo [1/3] فحص المكتبات...");
            writer.println("if not exist \"lib\\ojdbc11.jar\" (");
            writer.println("    echo ❌ مكتبة Oracle JDBC مفقودة!");
            writer.println("    echo يرجى تشغيل LibraryDownloader أولاً");
            writer.println("    pause");
            writer.println("    exit /b 1");
            writer.println(")");
            writer.println();
            writer.println("echo ✅ المكتبات موجودة");
            writer.println("echo.");
            writer.println("echo [2/3] تجميع الملفات...");
            writer.println("javac -encoding UTF-8 -cp \"lib/*;.\" *.java");
            writer.println();
            writer.println("if errorlevel 1 (");
            writer.println("    echo ❌ فشل في التجميع!");
            writer.println("    pause");
            writer.println("    exit /b 1");
            writer.println(")");
            writer.println();
            writer.println("echo ✅ تم التجميع بنجاح!");
            writer.println("echo.");
            writer.println("echo [3/3] النظام جاهز للتشغيل");
            writer.println("echo استخدم run_with_oracle.bat للتشغيل");
            writer.println("pause");
        } catch (IOException e) {
            System.err.println("خطأ في إنشاء ملف التجميع: " + e.getMessage());
        }
    }

    /**
     * إنشاء ملف التشغيل
     */
    private void createRunBatchFile() {
        try (PrintWriter writer = new PrintWriter(new FileWriter("run_with_oracle.bat"))) {
            writer.println("@echo off");
            writer.println("chcp 65001 > nul");
            writer.println("title تشغيل النظام مع مكتبات Oracle");
            writer.println("echo ====================================");
            writer.println("echo    تشغيل النظام مع مكتبات Oracle");
            writer.println("echo    Running with Oracle Libraries");
            writer.println("echo ====================================");
            writer.println("echo.");
            writer.println();
            writer.println("echo فحص المكتبات...");
            writer.println("if not exist \"lib\\ojdbc11.jar\" (");
            writer.println("    echo ❌ مكتبة Oracle JDBC مفقودة!");
            writer.println("    pause");
            writer.println("    exit /b 1");
            writer.println(")");
            writer.println();
            writer.println("echo ✅ بدء تشغيل النظام...");
            writer.println("echo.");
            writer.println(
                    "java -Dfile.encoding=UTF-8 -Duser.language=ar -Duser.country=SA -Dawt.useSystemAAFontSettings=lcd -Dswing.aatext=true -cp \"lib/*;.\" CompleteSystemTest");
            writer.println();
            writer.println("if errorlevel 1 (");
            writer.println("    echo ❌ خطأ في تشغيل النظام!");
            writer.println("    pause");
            writer.println(")");
        } catch (IOException e) {
            System.err.println("خطأ في إنشاء ملف التشغيل: " + e.getMessage());
        }
    }

    /**
     * إنشاء ملف اختبار الاتصال
     */
    private void createTestBatchFile() {
        try (PrintWriter writer = new PrintWriter(new FileWriter("test_oracle_connection.bat"))) {
            writer.println("@echo off");
            writer.println("chcp 65001 > nul");
            writer.println("title اختبار الاتصال بـ Oracle");
            writer.println("echo ====================================");
            writer.println("echo    اختبار الاتصال بـ Oracle");
            writer.println("echo    Oracle Connection Test");
            writer.println("echo ====================================");
            writer.println("echo.");
            writer.println();
            writer.println("java -cp \"lib/*;.\" DatabaseConnectionTest");
            writer.println("pause");
        } catch (IOException e) {
            System.err.println("خطأ في إنشاء ملف الاختبار: " + e.getMessage());
        }
    }

    /**
     * فحص المكتبات المثبتة
     */
    public boolean checkLibraries() {
        System.out.println("فحص المكتبات المطلوبة...");
        boolean allPresent = true;

        for (String fileName : REQUIRED_LIBRARIES.keySet()) {
            File libFile = new File(LIB_DIR, fileName);
            if (libFile.exists()) {
                System.out.println("✅ " + fileName + " - " + formatBytes(libFile.length()));
            } else {
                System.out.println("❌ " + fileName + " - مفقود");
                allPresent = false;
            }
        }

        return allPresent;
    }
}
