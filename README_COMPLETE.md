# نظام إدارة الشحنات - Ship ERP System

نظام إدارة شحنات متكامل وشامل ومتقدم مبني بـ Java مع دعم كامل للغة العربية و RTL.

## 🚀 الميزات الرئيسية

### ✅ المتوفر حالياً
- **نظام الإعدادات العامة** - إدارة إعدادات الشركة والنظام
- **إدارة المستخدمين والصلاحيات** - نظام أمان متقدم مع أدوار مرنة
- **إدارة الشركات والفروع** - هيكل تنظيمي متعدد المستويات
- **إدارة العملات** - دعم عملات متعددة مع أسعار صرف
- **إدارة السنوات المالية** - نظام مالي مرن
- **دعم كامل للعربية و RTL** - واجهة عربية بالكامل
- **نظام أمان متقدم** - تشفير وحماية البيانات
- **سجل تدقيق شامل** - تتبع جميع العمليات

### 🚧 قيد التطوير
- **نظام إدارة الأصناف** - كتالوج شامل للمنتجات
- **نظام إدارة الموردين** - قاعدة بيانات الموردين
- **نظام متابعة الشحنات** - تتبع الشحنات في الوقت الفعلي
- **نظام الإدخالات الجمركية** - إدارة الإجراءات الجمركية
- **نظام إدارة التكاليف** - حساب وتتبع التكاليف
- **نظام التقارير المتقدم** - تقارير تفصيلية وتحليلية

## 🛠️ التقنيات المستخدمة

| التقنية | الإصدار | الغرض |
|---------|---------|--------|
| **Java** | 17+ | لغة البرمجة الأساسية |
| **JavaFX** | 19.0.2.1 | واجهة المستخدم الرسومية |
| **Spring Framework** | 6.0.11 | إطار العمل الأساسي |
| **Hibernate ORM** | 6.2.7.Final | ORM لقاعدة البيانات |
| **Oracle Database** | 19c+ | قاعدة البيانات الرئيسية |
| **Apache Maven** | 3.8+ | إدارة المشروع والتبعيات |
| **Logback** | 1.4.8 | نظام التسجيل |
| **BCrypt** | - | تشفير كلمات المرور |
| **Jackson** | 2.15.2 | معالجة JSON |
| **iText** | 7.2.5 | إنشاء ملفات PDF |
| **Apache POI** | 5.2.3 | معالجة ملفات Excel |

## 📋 متطلبات النظام

### البرمجيات المطلوبة
- ☑️ **Java JDK 17** أو أحدث
- ☑️ **Apache Maven 3.8** أو أحدث  
- ☑️ **Oracle Database 19c** أو أحدث (أو Oracle XE)
- ☑️ **4 GB RAM** كحد أدنى (8 GB مُنصح به)
- ☑️ **2 GB مساحة قرص صلب** للتطبيق
- ☑️ **10 GB مساحة إضافية** لقاعدة البيانات

### أنظمة التشغيل المدعومة
- 🪟 **Windows 10/11** (مُختبر)
- 🍎 **macOS 10.15+** (مدعوم)
- 🐧 **Linux** (Ubuntu 20.04+, CentOS 8+)

## ⚡ التثبيت السريع

### 1️⃣ فحص المتطلبات
```bash
# تشغيل فحص المتطلبات
check-requirements.bat
```

### 2️⃣ إعداد قاعدة البيانات
```bash
# تشغيل سكريبت إعداد قاعدة البيانات
setup-database.bat
```

### 3️⃣ تشغيل التطبيق
```bash
# تشغيل التطبيق مباشرة
run-ship-erp.bat

# أو بناء وتشغيل يدوياً
mvn clean compile
mvn javafx:run
```

## 📖 دليل التثبيت المفصل

### تثبيت Java JDK 17
```bash
# تحميل من الموقع الرسمي
https://adoptium.net/

# التحقق من التثبيت
java -version
javac -version

# تعيين متغير البيئة JAVA_HOME
# Windows: set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-17.x.x
# Linux/Mac: export JAVA_HOME=/usr/lib/jvm/java-17-openjdk
```

### تثبيت Apache Maven
```bash
# تحميل من الموقع الرسمي
https://maven.apache.org/download.cgi

# استخراج الملف وإضافة bin إلى PATH
# Windows: set PATH=%PATH%;C:\apache-maven-3.9.x\bin
# Linux/Mac: export PATH=$PATH:/opt/apache-maven-3.9.x/bin

# التحقق من التثبيت
mvn -version
```

### تثبيت Oracle Database
```bash
# تحميل Oracle XE (مجاني للتطوير)
https://www.oracle.com/database/technologies/xe-downloads.html

# أو Oracle Database 19c (للإنتاج)
https://www.oracle.com/database/technologies/oracle19c-downloads.html

# بعد التثبيت، تأكد من تشغيل الخدمة
# Windows: net start OracleServiceXE
# Linux: sudo systemctl start oracle-xe
```

## 🏃‍♂️ بناء وتشغيل المشروع

### البناء الأساسي
```bash
# تنظيف وبناء المشروع
mvn clean compile

# تشغيل الاختبارات
mvn test

# إنشاء JAR قابل للتنفيذ
mvn clean package

# تشغيل التطبيق
java -jar target/ship-erp-system-1.0.0.jar
```

### التشغيل مع Maven
```bash
# تشغيل مباشر مع Maven
mvn javafx:run

# تشغيل مع خصائص مخصصة
mvn javafx:run -Djavafx.args="--debug"
```

### التشغيل في بيئة التطوير
```bash
# تشغيل مع إعدادات التطوير
mvn javafx:run -Dspring.profiles.active=dev

# تشغيل مع تفعيل التصحيح
mvn javafx:run -Dlogging.level.com.shipment.erp=DEBUG
```

## 🎯 الاستخدام

### تسجيل الدخول الافتراضي
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

### الواجهة الرئيسية
- 🌳 **القائمة الشجرية** على اليسار للتنقل
- 📑 **منطقة المحتوى** الرئيسية مع التبويبات
- 📊 **شريط الحالة** في الأسفل

### نظام الإعدادات
1. **المتغيرات العامة** - إعدادات الشركة والنظام
2. **السنة المالية** - إدارة السنوات المالية
3. **العملات** - إدارة العملات وأسعار الصرف
4. **بيانات الشركة** - معلومات الشركة والفروع
5. **المستخدمين** - إدارة المستخدمين
6. **الصلاحيات** - إدارة الأدوار والصلاحيات

## 📁 هيكل المشروع

```
ship-erp-system/
├── 📂 src/main/java/              # الكود المصدري
│   └── 📂 com/shipment/erp/
│       ├── 📂 model/              # نماذج البيانات (Entity Classes)
│       ├── 📂 repository/         # طبقة الوصول للبيانات (Repository Layer)
│       ├── 📂 service/            # طبقة منطق الأعمال (Business Logic)
│       ├── 📂 controller/         # تحكم الواجهات (UI Controllers)
│       ├── 📂 security/           # نظام الأمان (Security System)
│       └── 📂 util/               # الأدوات المساعدة (Utility Classes)
├── 📂 src/main/resources/         # الموارد
│   ├── 📂 fxml/                   # ملفات الواجهات (FXML Files)
│   ├── 📂 styles/                 # ملفات CSS (Stylesheets)
│   ├── 📂 images/                 # الصور والأيقونات (Images & Icons)
│   ├── 📄 messages_ar.properties  # النصوص العربية (Arabic Text)
│   ├── 📄 application.properties  # إعدادات التطبيق
│   ├── 📄 hibernate.cfg.xml       # إعدادات Hibernate
│   └── 📄 applicationContext.xml  # إعدادات Spring
├── 📂 src/test/java/              # اختبارات الوحدة (Unit Tests)
├── 📂 scripts/                    # سكريبتات قاعدة البيانات (DB Scripts)
├── 📂 logs/                       # ملفات السجل (Log Files)
├── 📂 backup/                     # النسخ الاحتياطية (Backups)
├── 📂 target/                     # ملفات البناء (Build Output)
├── 📄 pom.xml                     # إعدادات Maven
├── 📄 README.md                   # دليل المشروع
├── 📄 check-requirements.bat      # فحص المتطلبات
├── 📄 setup-database.bat          # إعداد قاعدة البيانات
└── 📄 run-ship-erp.bat           # تشغيل التطبيق
```

## 🔒 الأمان والحماية

### نظام الصلاحيات
- 🔐 **تشفير كلمات المرور** باستخدام BCrypt
- 👥 **نظام أدوار مرن** مع صلاحيات متدرجة
- 🔒 **قفل الحسابات** بعد محاولات فاشلة
- 📝 **سجل تدقيق شامل** لجميع العمليات

### حماية البيانات
- 🛡️ **تشفير البيانات الحساسة**
- 🔄 **نسخ احتياطي تلقائي**
- 🚫 **حماية من SQL Injection**
- 🔍 **تتبع محاولات الاختراق**

## 📊 قاعدة البيانات

### معلومات الاتصال الافتراضية
```properties
URL: ***********************************
Username: ship_erp
Password: ship_erp_password
```

### الجداول الرئيسية
- 🏢 **COMPANIES** - بيانات الشركات
- 🏪 **BRANCHES** - فروع الشركات
- 👤 **USERS** - المستخدمين
- 🎭 **ROLES** - الأدوار
- 🔑 **PERMISSIONS** - الصلاحيات
- 💰 **CURRENCIES** - العملات
- 📅 **FISCAL_YEARS** - السنوات المالية
- ⚙️ **SYSTEM_SETTINGS** - إعدادات النظام
- 📋 **AUDIT_LOG** - سجل التدقيق

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

#### Java غير موجود
```bash
# تحقق من تثبيت Java
java -version

# تحقق من JAVA_HOME
echo %JAVA_HOME%
```

#### Maven غير موجود
```bash
# تحقق من تثبيت Maven
mvn -version

# تحقق من M2_HOME
echo %M2_HOME%
```

#### مشاكل قاعدة البيانات
```bash
# تحقق من خدمة Oracle
net start OracleServiceXE

# اختبار الاتصال
sqlplus system/password@localhost:1521/XE
```

#### مشاكل الترميز
- تأكد من حفظ الملفات بترميز UTF-8
- تحقق من إعدادات IDE
- استخدم `chcp 65001` في Command Prompt

## 📝 السجلات

### مواقع ملفات السجل
- `logs/ship-erp.log` - السجل العام
- `logs/ship-erp-error.log` - سجل الأخطاء
- `logs/ship-erp-audit.log` - سجل التدقيق

### مستويات التسجيل
- **DEBUG** - تفاصيل التطوير
- **INFO** - معلومات عامة
- **WARN** - تحذيرات
- **ERROR** - أخطاء

## 🔄 النسخ الاحتياطي

### نسخ تلقائي
- ⏰ كل 24 ساعة افتراضياً
- 💾 حفظ في مجلد `backup/`
- 🗓️ الاحتفاظ لمدة 30 يوم

### نسخ يدوي
```bash
# إنشاء نسخة احتياطية يدوية
# استخدام VS Code Task: Create Backup
```

## 🎯 الميزات الحالية

### ✅ متوفر
- ✔️ نظام الإعدادات العامة
- ✔️ إدارة المستخدمين والصلاحيات
- ✔️ إدارة الشركات والفروع
- ✔️ إدارة العملات
- ✔️ إدارة السنوات المالية
- ✔️ دعم كامل للعربية و RTL

### 🚧 قيد التطوير
- 🔨 نظام إدارة الأصناف
- 🔨 نظام إدارة الموردين
- 🔨 نظام متابعة الشحنات
- 🔨 نظام الإدخالات الجمركية
- 🔨 نظام إدارة التكاليف

## 🔮 الإصدارات القادمة

### v1.1.0
- 📦 نظام إدارة الأصناف
- ⚡ تحسينات الأداء
- 📊 تقارير أساسية

### v1.2.0
- 🏭 نظام إدارة الموردين
- 🎨 واجهة محسنة
- 📤 تصدير البيانات

### v2.0.0
- 🚚 نظام متابعة الشحنات
- 📈 لوحة تحكم تفاعلية
- 📱 تطبيق الهاتف المحمول

## 📞 الدعم

### الوثائق
- 📖 `README.md` - دليل شامل
- 📋 `INSTALLATION_GUIDE.md` - دليل التثبيت المفصل
- 📚 `docs/` - وثائق إضافية

### الإبلاغ عن المشاكل
1. تحقق من ملفات السجل
2. راجع الأخطاء الشائعة
3. أنشئ تقرير مشكلة مع التفاصيل

---

**جميع الحقوق محفوظة © 2025 - نظام إدارة الشحنات**
