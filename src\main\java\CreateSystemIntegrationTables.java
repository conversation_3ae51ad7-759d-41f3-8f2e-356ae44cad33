import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * إنشاء جداول نظام الربط والاستيراد Create System Integration Tables
 */
public class CreateSystemIntegrationTables {

    public static void main(String[] args) {
        try {
            // تحميل Oracle JDBC driver
            try {
                Class.forName("oracle.jdbc.OracleDriver");
                System.out.println("✅ تم تحميل Oracle JDBC driver بنجاح");
            } catch (ClassNotFoundException e) {
                System.err.println("❌ فشل في تحميل Oracle JDBC driver: " + e.getMessage());
                throw e;
            }

            // الاتصال بقاعدة البيانات SHIP_ERP
            Connection conn = DriverManager.getConnection("*************************************",
                    "ship_erp", "ship_erp_password");
            conn.setAutoCommit(false);

            System.out.println("✅ متصل بقاعدة البيانات SHIP_ERP");

            // إنشاء الجداول
            createSystemConnectionsTable(conn);
            createTableMappingTable(conn);
            createFieldMappingTable(conn);
            createImportHistoryTable(conn);
            createImportConfigTable(conn);

            // إدراج البيانات الأساسية
            insertBasicData(conn);

            conn.commit();
            System.out.println("🎉 تم إنشاء نظام الربط والاستيراد بنجاح!");

            conn.close();

        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * إنشاء جدول اتصالات الأنظمة الخارجية
     */
    private static void createSystemConnectionsTable(Connection conn) throws SQLException {
        String sql = """
                    CREATE TABLE ERP_SYSTEM_CONNECTIONS (
                        CONNECTION_ID NUMBER(10) NOT NULL,
                        CONNECTION_NAME VARCHAR2(100) NOT NULL,
                        CONNECTION_TYPE VARCHAR2(20) NOT NULL, -- ORACLE, MYSQL, SQLSERVER, etc
                        HOST_NAME VARCHAR2(100) NOT NULL,
                        PORT_NUMBER NUMBER(5) DEFAULT 1521,
                        DATABASE_NAME VARCHAR2(50) NOT NULL,
                        USERNAME VARCHAR2(50) NOT NULL,
                        PASSWORD VARCHAR2(100) NOT NULL,
                        SCHEMA_NAME VARCHAR2(50),
                        CONNECTION_STRING VARCHAR2(500),
                        IS_ACTIVE NUMBER(1) DEFAULT 1,
                        TEST_QUERY VARCHAR2(200) DEFAULT 'SELECT 1 FROM DUAL',
                        DESCRIPTION VARCHAR2(500),
                        CREATED_BY VARCHAR2(50),
                        CREATED_DATE DATE DEFAULT SYSDATE,
                        UPDATED_BY VARCHAR2(50),
                        UPDATED_DATE DATE,
                        CONSTRAINT PK_SYSTEM_CONNECTIONS PRIMARY KEY (CONNECTION_ID)
                    )
                """;

        executeSQL(conn, sql, "جدول اتصالات الأنظمة الخارجية");

        // إنشاء sequence
        executeSQL(conn, "CREATE SEQUENCE SEQ_SYSTEM_CONNECTIONS START WITH 1 INCREMENT BY 1",
                "تسلسل اتصالات الأنظمة");
    }

    /**
     * إنشاء جدول ربط الجداول
     */
    private static void createTableMappingTable(Connection conn) throws SQLException {
        String sql = """
                    CREATE TABLE ERP_TABLE_MAPPING (
                        MAPPING_ID NUMBER(10) NOT NULL,
                        CONNECTION_ID NUMBER(10) NOT NULL,
                        SOURCE_TABLE_NAME VARCHAR2(100) NOT NULL,
                        TARGET_TABLE_NAME VARCHAR2(100) NOT NULL,
                        MAPPING_NAME VARCHAR2(100) NOT NULL,
                        MAPPING_TYPE VARCHAR2(20) DEFAULT 'IMPORT', -- IMPORT, EXPORT, SYNC
                        WHERE_CONDITION VARCHAR2(1000),
                        ORDER_BY_CLAUSE VARCHAR2(200),
                        IS_ACTIVE NUMBER(1) DEFAULT 1,
                        AUTO_IMPORT NUMBER(1) DEFAULT 0,
                        IMPORT_FREQUENCY VARCHAR2(20), -- DAILY, WEEKLY, MONTHLY, MANUAL
                        LAST_IMPORT_DATE DATE,
                        LAST_IMPORT_COUNT NUMBER(10),
                        DESCRIPTION VARCHAR2(500),
                        CREATED_BY VARCHAR2(50),
                        CREATED_DATE DATE DEFAULT SYSDATE,
                        UPDATED_BY VARCHAR2(50),
                        UPDATED_DATE DATE,
                        CONSTRAINT PK_TABLE_MAPPING PRIMARY KEY (MAPPING_ID),
                        CONSTRAINT FK_TABLE_MAPPING_CONN FOREIGN KEY (CONNECTION_ID)
                            REFERENCES ERP_SYSTEM_CONNECTIONS(CONNECTION_ID)
                    )
                """;

        executeSQL(conn, sql, "جدول ربط الجداول");

        executeSQL(conn, "CREATE SEQUENCE SEQ_TABLE_MAPPING START WITH 1 INCREMENT BY 1",
                "تسلسل ربط الجداول");
    }

    /**
     * إنشاء جدول ربط الحقول
     */
    private static void createFieldMappingTable(Connection conn) throws SQLException {
        String sql = """
                    CREATE TABLE ERP_FIELD_MAPPING (
                        FIELD_MAPPING_ID NUMBER(10) NOT NULL,
                        TABLE_MAPPING_ID NUMBER(10) NOT NULL,
                        SOURCE_FIELD_NAME VARCHAR2(100) NOT NULL,
                        TARGET_FIELD_NAME VARCHAR2(100) NOT NULL,
                        DATA_TYPE VARCHAR2(20), -- VARCHAR2, NUMBER, DATE, etc
                        DATA_TRANSFORMATION VARCHAR2(500), -- SQL expression for transformation
                        IS_KEY_FIELD NUMBER(1) DEFAULT 0,
                        IS_REQUIRED NUMBER(1) DEFAULT 0,
                        DEFAULT_VALUE VARCHAR2(200),
                        VALIDATION_RULE VARCHAR2(500),
                        FIELD_ORDER NUMBER(3),
                        DESCRIPTION VARCHAR2(500),
                        CREATED_BY VARCHAR2(50),
                        CREATED_DATE DATE DEFAULT SYSDATE,
                        CONSTRAINT PK_FIELD_MAPPING PRIMARY KEY (FIELD_MAPPING_ID),
                        CONSTRAINT FK_FIELD_MAPPING_TABLE FOREIGN KEY (TABLE_MAPPING_ID)
                            REFERENCES ERP_TABLE_MAPPING(MAPPING_ID)
                    )
                """;

        executeSQL(conn, sql, "جدول ربط الحقول");

        executeSQL(conn, "CREATE SEQUENCE SEQ_FIELD_MAPPING START WITH 1 INCREMENT BY 1",
                "تسلسل ربط الحقول");
    }

    /**
     * إنشاء جدول تاريخ الاستيراد
     */
    private static void createImportHistoryTable(Connection conn) throws SQLException {
        String sql = """
                    CREATE TABLE ERP_IMPORT_HISTORY (
                        IMPORT_ID NUMBER(10) NOT NULL,
                        TABLE_MAPPING_ID NUMBER(10) NOT NULL,
                        IMPORT_DATE DATE DEFAULT SYSDATE,
                        IMPORT_TYPE VARCHAR2(20), -- FULL, INCREMENTAL, MANUAL
                        TOTAL_RECORDS NUMBER(10),
                        IMPORTED_RECORDS NUMBER(10),
                        FAILED_RECORDS NUMBER(10),
                        IMPORT_STATUS VARCHAR2(20), -- SUCCESS, FAILED, PARTIAL
                        ERROR_MESSAGE CLOB,
                        IMPORT_DURATION NUMBER(10), -- in seconds
                        IMPORTED_BY VARCHAR2(50),
                        START_TIME TIMESTAMP,
                        END_TIME TIMESTAMP,
                        LOG_FILE_PATH VARCHAR2(500),
                        CONSTRAINT PK_IMPORT_HISTORY PRIMARY KEY (IMPORT_ID),
                        CONSTRAINT FK_IMPORT_HISTORY_MAPPING FOREIGN KEY (TABLE_MAPPING_ID)
                            REFERENCES ERP_TABLE_MAPPING(MAPPING_ID)
                    )
                """;

        executeSQL(conn, sql, "جدول تاريخ الاستيراد");

        executeSQL(conn, "CREATE SEQUENCE SEQ_IMPORT_HISTORY START WITH 1 INCREMENT BY 1",
                "تسلسل تاريخ الاستيراد");
    }

    /**
     * إنشاء جدول إعدادات الاستيراد
     */
    private static void createImportConfigTable(Connection conn) throws SQLException {
        String sql = """
                    CREATE TABLE ERP_IMPORT_CONFIG (
                        CONFIG_ID NUMBER(10) NOT NULL,
                        CONFIG_NAME VARCHAR2(100) NOT NULL,
                        CONFIG_TYPE VARCHAR2(50) NOT NULL, -- BATCH_SIZE, TIMEOUT, etc
                        CONFIG_VALUE VARCHAR2(500) NOT NULL,
                        CONFIG_DESCRIPTION VARCHAR2(500),
                        IS_SYSTEM_CONFIG NUMBER(1) DEFAULT 0,
                        IS_ACTIVE NUMBER(1) DEFAULT 1,
                        CREATED_BY VARCHAR2(50),
                        CREATED_DATE DATE DEFAULT SYSDATE,
                        UPDATED_BY VARCHAR2(50),
                        UPDATED_DATE DATE,
                        CONSTRAINT PK_IMPORT_CONFIG PRIMARY KEY (CONFIG_ID),
                        CONSTRAINT UK_IMPORT_CONFIG_NAME UNIQUE (CONFIG_NAME)
                    )
                """;

        executeSQL(conn, sql, "جدول إعدادات الاستيراد");

        executeSQL(conn, "CREATE SEQUENCE SEQ_IMPORT_CONFIG START WITH 1 INCREMENT BY 1",
                "تسلسل إعدادات الاستيراد");
    }

    /**
     * إدراج البيانات الأساسية
     */
    private static void insertBasicData(Connection conn) throws SQLException {
        // إدراج اتصال IAS20251
        String insertConnection = """
                    INSERT INTO ERP_SYSTEM_CONNECTIONS
                    (CONNECTION_ID, CONNECTION_NAME, CONNECTION_TYPE, HOST_NAME, PORT_NUMBER,
                     DATABASE_NAME, USERNAME, PASSWORD, SCHEMA_NAME, DESCRIPTION, CREATED_BY)
                    VALUES
                    (SEQ_SYSTEM_CONNECTIONS.NEXTVAL, 'نظام IAS20251', 'ORACLE', 'localhost', 1521,
                     'orcl', 'ias20251', 'ys123', 'IAS20251', 'النظام المحاسبي الأصلي', 'SYSTEM')
                """;

        executeSQL(conn, insertConnection, "إدراج اتصال IAS20251");

        // الحصول على CONNECTION_ID للنظام المدرج
        int connectionId = 0;
        try {
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(
                    "SELECT CONNECTION_ID FROM ERP_SYSTEM_CONNECTIONS WHERE CONNECTION_NAME = 'نظام IAS20251'");
            if (rs.next()) {
                connectionId = rs.getInt("CONNECTION_ID");
                System.out.println("✅ تم العثور على CONNECTION_ID: " + connectionId);
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في الحصول على CONNECTION_ID: " + e.getMessage());
            return;
        }

        // إدراج ربط جدول وحدات القياس
        String insertTableMapping = "INSERT INTO ERP_TABLE_MAPPING "
                + "(MAPPING_ID, CONNECTION_ID, SOURCE_TABLE_NAME, TARGET_TABLE_NAME, "
                + "MAPPING_NAME, MAPPING_TYPE, DESCRIPTION, CREATED_BY) " + "VALUES "
                + "(SEQ_TABLE_MAPPING.NEXTVAL, " + connectionId
                + ", 'MEASUREMENT', 'ERP_MEASUREMENT', "
                + "'استيراد وحدات القياس', 'IMPORT', 'استيراد وحدات القياس من النظام الأصلي', 'SYSTEM')";

        executeSQL(conn, insertTableMapping, "إدراج ربط جدول وحدات القياس");

        // الحصول على TABLE_MAPPING_ID
        int tableMappingId = 0;
        try {
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(
                    "SELECT MAPPING_ID FROM ERP_TABLE_MAPPING WHERE MAPPING_NAME = 'استيراد وحدات القياس'");
            if (rs.next()) {
                tableMappingId = rs.getInt("MAPPING_ID");
                System.out.println("✅ تم العثور على TABLE_MAPPING_ID: " + tableMappingId);
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في الحصول على TABLE_MAPPING_ID: " + e.getMessage());
            return;
        }

        // إدراج ربط الحقول واحد تلو الآخر
        insertFieldMapping(conn, tableMappingId, "MEASURE_CODE", "MEASURE_CODE", "VARCHAR2", null,
                1, 1, null, null, 1);
        insertFieldMapping(conn, tableMappingId, "MEASURE", "MEASURE", "VARCHAR2", null, 0, 1, null,
                null, 2);
        insertFieldMapping(conn, tableMappingId, "MEASURE_F_NM", "MEASURE_F_NM", "VARCHAR2", null,
                0, 0, null, null, 3);
        insertFieldMapping(conn, tableMappingId, "MEASURE_CODE_GB", "MEASURE_CODE_GB", "VARCHAR2",
                null, 0, 0, null, null, 4);
        insertFieldMapping(conn, tableMappingId, "MEASURE_TYPE", "MEASURE_TYPE", "NUMBER", null, 0,
                0, "1", null, 5);
        insertFieldMapping(conn, tableMappingId, "MEASURE_WT_TYPE", "MEASURE_WT_TYPE", "NUMBER",
                null, 0, 0, null, null, 6);
        insertFieldMapping(conn, tableMappingId, "MEASURE_WT_CONN", "MEASURE_WT_CONN", "NUMBER",
                null, 0, 0, "0", null, 7);
        insertFieldMapping(conn, tableMappingId, "DFLT_SIZE", "DFLT_SIZE", "NUMBER", null, 0, 0,
                null, null, 8);
        insertFieldMapping(conn, tableMappingId, "ALLOW_UPD", "ALLOW_UPD", "NUMBER", null, 0, 0,
                "1", null, 9);
        insertFieldMapping(conn, tableMappingId, "UNT_SALE_TYP", "UNT_SALE_TYP", "NUMBER", null, 0,
                0, "3", null, 10);

        // إدراج إعدادات الاستيراد الأساسية
        insertConfig(conn, "BATCH_SIZE", "حجم الدفعة", "1000", "عدد السجلات في كل دفعة استيراد");
        insertConfig(conn, "CONNECTION_TIMEOUT", "مهلة الاتصال", "30", "مهلة الاتصال بالثواني");
        insertConfig(conn, "IMPORT_LOG_LEVEL", "مستوى السجل", "INFO",
                "مستوى تفصيل السجل: DEBUG, INFO, WARN, ERROR");
        insertConfig(conn, "AUTO_BACKUP", "نسخ احتياطي تلقائي", "1",
                "إنشاء نسخة احتياطية قبل الاستيراد");
        insertConfig(conn, "VALIDATE_DATA", "التحقق من البيانات", "1",
                "التحقق من صحة البيانات قبل الاستيراد");

        System.out.println("✅ تم إدراج البيانات الأساسية");
    }

    /**
     * تنفيذ SQL مع معالجة الأخطاء
     */
    private static void executeSQL(Connection conn, String sql, String description)
            throws SQLException {
        try {
            Statement stmt = conn.createStatement();
            stmt.execute(sql);
            System.out.println("✅ تم إنشاء " + description);
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) { // ORA-00955: name is already used by an existing object
                System.out.println("⚠️ " + description + " موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إنشاء " + description + ": " + e.getMessage());
                throw e;
            }
        }
    }

    /**
     * إدراج ربط حقل واحد
     */
    private static void insertFieldMapping(Connection conn, int tableMappingId, String sourceField,
            String targetField, String dataType, String transformation, int isKeyField,
            int isRequired, String defaultValue, String validationRule, int fieldOrder)
            throws SQLException {

        String sql = "INSERT INTO ERP_FIELD_MAPPING "
                + "(FIELD_MAPPING_ID, TABLE_MAPPING_ID, SOURCE_FIELD_NAME, TARGET_FIELD_NAME, "
                + "DATA_TYPE, DATA_TRANSFORMATION, IS_KEY_FIELD, IS_REQUIRED, DEFAULT_VALUE, "
                + "VALIDATION_RULE, FIELD_ORDER) "
                + "VALUES (SEQ_FIELD_MAPPING.NEXTVAL, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try {
            PreparedStatement pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, tableMappingId);
            pstmt.setString(2, sourceField);
            pstmt.setString(3, targetField);
            pstmt.setString(4, dataType);
            pstmt.setString(5, transformation);
            pstmt.setInt(6, isKeyField);
            pstmt.setInt(7, isRequired);
            pstmt.setString(8, defaultValue);
            pstmt.setString(9, validationRule);
            pstmt.setInt(10, fieldOrder);

            pstmt.executeUpdate();
            System.out.println("✅ تم إدراج ربط حقل: " + sourceField);

        } catch (SQLException e) {
            if (e.getErrorCode() == 1) { // ORA-00001: unique constraint violated
                System.out.println("⚠️ ربط حقل " + sourceField + " موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إدراج ربط حقل " + sourceField + ": " + e.getMessage());
                throw e;
            }
        }
    }

    /**
     * إدراج إعداد واحد
     */
    private static void insertConfig(Connection conn, String configName, String configType,
            String configValue, String configDescription) throws SQLException {

        String sql = "INSERT INTO ERP_IMPORT_CONFIG "
                + "(CONFIG_ID, CONFIG_NAME, CONFIG_TYPE, CONFIG_VALUE, CONFIG_DESCRIPTION, "
                + "IS_SYSTEM_CONFIG, CREATED_BY) "
                + "VALUES (SEQ_IMPORT_CONFIG.NEXTVAL, ?, ?, ?, ?, 1, 'SYSTEM')";

        try {
            PreparedStatement pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, configName);
            pstmt.setString(2, configType);
            pstmt.setString(3, configValue);
            pstmt.setString(4, configDescription);

            pstmt.executeUpdate();
            System.out.println("✅ تم إدراج إعداد: " + configName);

        } catch (SQLException e) {
            if (e.getErrorCode() == 1) { // ORA-00001: unique constraint violated
                System.out.println("⚠️ إعداد " + configName + " موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إدراج إعداد " + configName + ": " + e.getMessage());
                throw e;
            }
        }
    }
}
