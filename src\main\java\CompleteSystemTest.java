import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Font;
import java.awt.GridLayout;
import java.util.Locale;
import javax.swing.BorderFactory;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JWindow;
import javax.swing.SwingConstants;
import javax.swing.SwingUtilities;
import javax.swing.Timer;

/**
 * اختبار النظام الكامل مع جميع الميزات Complete System Test with All Features
 */
public class CompleteSystemTest {

    public static void main(String[] args) {
        // تعيين الترميز والمحلية العربية
        System.setProperty("file.encoding", "UTF-8");
        Locale.setDefault(new Locale("ar", "SA"));

        SwingUtilities.invokeLater(() -> {
            try {
                // تطبيق المظهر المحفوظ
                SettingsManager.applyStoredTheme();

                // عرض شاشة البداية
                showSplashScreen();

                // إنشاء النافذة الرئيسية المحسنة
                EnhancedMainWindow mainWindow = new EnhancedMainWindow();
                mainWindow.setVisible(true);

                System.out.println("=== تم تشغيل النظام الكامل بنجاح! ===");
                System.out.println("الميزات المتاحة:");
                System.out.println("• القائمة الشجرية المتقدمة");
                System.out.println("• نظام البحث الفوري");
                System.out.println("• إدارة المستخدمين الشاملة");
                System.out.println("• الإعدادات العامة المتقدمة");
                System.out.println("• مكتبة الأدوات والأيقونات");
                System.out.println("• دعم كامل للغة العربية");

                // فحص الاتصال التلقائي بقاعدة البيانات
                System.out.println("\n=== فحص الاتصال بقاعدة البيانات ===");
                testDatabaseConnection();

            } catch (Exception e) {
                e.printStackTrace();
                UIUtils.showErrorMessage(null, "حدث خطأ في تشغيل التطبيق: " + e.getMessage());
            }
        });
    }

    /**
     * عرض شاشة البداية
     */
    private static void showSplashScreen() {
        JWindow splash = new JWindow();
        splash.setSize(500, 300);
        splash.setLocationRelativeTo(null);

        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(UIUtils.PRIMARY_COLOR);
        panel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));

        // العنوان
        JLabel titleLabel = new JLabel("نظام إدارة الشحنات المتقدم");
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 24));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // العنوان الفرعي
        JLabel subtitleLabel = new JLabel("الإصدار 2.0 - واجهة شجرية متقدمة");
        subtitleLabel.setFont(UIUtils.ARABIC_FONT_LARGE);
        subtitleLabel.setForeground(new Color(236, 240, 241));
        subtitleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        subtitleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط التقدم
        JProgressBar progressBar = UIUtils.createStyledProgressBar();
        progressBar.setIndeterminate(true);
        progressBar.setString("جاري التحميل...");

        // الميزات
        JPanel featuresPanel = new JPanel(new GridLayout(3, 2, 10, 5));
        featuresPanel.setOpaque(false);
        featuresPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        String[] features = {"• قائمة شجرية تفاعلية", "• بحث فوري متقدم", "• إدارة مستخدمين شاملة",
                "• إعدادات متقدمة", "• دعم كامل للعربية", "• واجهة احترافية"};

        for (String feature : features) {
            JLabel featureLabel = new JLabel(feature);
            featureLabel.setFont(UIUtils.ARABIC_FONT);
            featureLabel.setForeground(Color.WHITE);
            featureLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            featuresPanel.add(featureLabel);
        }

        JPanel textPanel = new JPanel(new GridLayout(2, 1, 0, 10));
        textPanel.setOpaque(false);
        textPanel.add(titleLabel);
        textPanel.add(subtitleLabel);

        panel.add(textPanel, BorderLayout.NORTH);
        panel.add(featuresPanel, BorderLayout.CENTER);
        panel.add(progressBar, BorderLayout.SOUTH);

        splash.add(panel);
        splash.setVisible(true);

        // إخفاء شاشة البداية بعد 3 ثوان
        Timer timer = new Timer(3000, e -> splash.dispose());
        timer.setRepeats(false);
        timer.start();

        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * فحص الاتصال التلقائي بقاعدة البيانات
     */
    private static void testDatabaseConnection() {
        try {
            System.out.println("🔄 فحص مكتبات Oracle...");

            // فحص تحميل مكتبة Oracle JDBC
            try {
                Class.forName("oracle.jdbc.driver.OracleDriver");
                System.out.println("✅ Oracle JDBC Driver محمل بنجاح");
            } catch (ClassNotFoundException e) {
                System.out.println("❌ Oracle JDBC Driver غير محمل!");
                System.out.println("الحل: تشغيل java LibraryDownloader");
                return;
            }

            // فحص وجود مكتبة orai18n.jar
            String classpath = System.getProperty("java.class.path");
            if (classpath.contains("orai18n.jar")) {
                System.out.println("✅ مكتبة orai18n.jar محملة (دعم الأحرف العربية)");
            } else {
                System.out.println(
                        "⚠️ مكتبة orai18n.jar غير محملة - قد تواجه مشاكل مع الأحرف العربية");
            }

            System.out.println("🔄 اختبار الاتصال بقاعدة البيانات...");

            // إنشاء كائن DatabaseConfig
            DatabaseConfig dbConfig = new DatabaseConfig();
            dbConfig.setHost("localhost");
            dbConfig.setPort("1521");
            dbConfig.setServiceName("orcl");
            dbConfig.setUsername("ship_erp");
            dbConfig.setPassword("ship_erp_password");

            // إنشاء كائن OracleItemImporter واختبار الاتصال
            OracleItemImporter importer = new OracleItemImporter(dbConfig);
            boolean connected = importer.connect();

            if (connected) {
                System.out.println("✅ تم الاتصال بقاعدة البيانات بنجاح!");

                // فحص وجود جداول IAS
                System.out.println("🔄 فحص وجود جداول IAS...");
                boolean tablesExist = importer.checkIASTablesExist();

                if (tablesExist) {
                    System.out.println("✅ جداول IAS_ITM_MST و IAS_ITM_DTL موجودة");

                    // اختبار الاستعلام الموحد
                    System.out.println("🔄 اختبار الاستعلام الموحد...");
                    try {
                        java.util.List<OracleItemImporter.ImportedItem> items =
                                importer.importFromIASItemTables();
                        System.out.println("✅ الاستعلام الموحد يعمل بنجاح!");
                        System.out.println(
                                "📊 تم العثور على " + items.size() + " صنف في قاعدة البيانات");

                        if (!items.isEmpty()) {
                            OracleItemImporter.ImportedItem firstItem = items.get(0);
                            System.out.println("📋 عينة من البيانات:");
                            System.out.println("   - كود الصنف: " + firstItem.getField("ITM_CODE"));
                            System.out.println("   - اسم الصنف: " + firstItem.getField("ITM_NAME"));
                            System.out.println("   - حالة النشاط: "
                                    + (firstItem.getField("IS_ACTIVE").equals(1) ? "نشط"
                                            : "غير نشط"));
                        }

                    } catch (Exception e) {
                        System.out.println("❌ خطأ في الاستعلام الموحد: " + e.getMessage());
                        System.out.println("💡 قد تحتاج إلى تحديث الاستعلام أو فحص هيكل الجداول");
                    }

                } else {
                    System.out.println("⚠️ جداول IAS غير موجودة أو غير متاحة");
                }

                // إغلاق الاتصال
                importer.disconnect();
                System.out.println("🔒 تم إغلاق الاتصال بقاعدة البيانات");

            } else {
                System.out.println("❌ فشل في الاتصال بقاعدة البيانات!");
                System.out.println("💡 تأكد من:");
                System.out.println("   - تشغيل خادم Oracle");
                System.out.println("   - صحة بيانات الاتصال (localhost:1521:orcl)");
                System.out
                        .println("   - صحة اسم المستخدم وكلمة المرور (ship_erp/ship_erp_password)");
            }

        } catch (Exception e) {
            System.out.println("❌ خطأ في فحص قاعدة البيانات: " + e.getMessage());
            System.out.println("💡 السبب المحتمل: " + e.getClass().getSimpleName());

            if (e.getMessage() != null && e.getMessage().contains("regex")) {
                System.out.println("💡 مشكلة regex - تأكد من إعدادات اللغة");
            }
        }

        System.out.println("=== انتهى فحص قاعدة البيانات ===\n");
    }
}
