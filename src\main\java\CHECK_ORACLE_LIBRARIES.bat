@echo off
chcp 65001 > nul
title فحص مكتبات Oracle

echo ====================================
echo    فحص مكتبات Oracle
echo    Oracle Libraries Check
echo ====================================
echo.

echo فحص وجود المكتبات...
echo.

if exist "lib\ojdbc11.jar" (
    for %%A in ("lib\ojdbc11.jar") do (
        echo ✅ ojdbc11.jar موجود - الحجم: %%~zA bytes
    )
) else (
    echo ❌ ojdbc11.jar مفقود!
    set MISSING=1
)

if exist "lib\orai18n.jar" (
    for %%A in ("lib\orai18n.jar") do (
        echo ✅ orai18n.jar موجود - الحجم: %%~zA bytes
    )
) else (
    echo ❌ orai18n.jar مفقود!
    set MISSING=1
)

if exist "lib\commons-dbcp2-2.9.0.jar" (
    echo ✅ commons-dbcp2-2.9.0.jar موجود
) else (
    echo ⚠️ commons-dbcp2-2.9.0.jar مفقود (اختياري)
)

if exist "lib\commons-pool2-2.11.1.jar" (
    echo ✅ commons-pool2-2.11.1.jar موجود
) else (
    echo ⚠️ commons-pool2-2.11.1.jar مفقود (اختياري)
)

echo.

if defined MISSING (
    echo ❌ مكتبات مطلوبة مفقودة!
    echo.
    echo الحلول:
    echo 1. تشغيل: java LibraryDownloader
    echo 2. تشغيل: START_ERP_WITH_ORACLE.bat
    echo.
) else (
    echo ✅ جميع المكتبات المطلوبة موجودة!
    echo.
    echo يمكنك الآن تشغيل النظام بأمان:
    echo START_ERP_WITH_ORACLE.bat
    echo.
)

echo اختبار تحميل مكتبة Oracle JDBC...
java -cp "lib/*;." -Duser.language=en -Duser.country=US OracleConnectionFixer

pause
