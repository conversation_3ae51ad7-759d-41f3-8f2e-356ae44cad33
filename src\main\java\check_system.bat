@echo off
echo Checking Ship ERP System...
echo.

echo [1] Checking Java...
java -version
if errorlevel 1 (
    echo ERROR: Java not found!
    pause
    exit /b 1
)

echo.
echo [2] Checking main file...
if exist "CompleteSystemTest.java" (
    echo OK: CompleteSystemTest.java found
) else (
    echo ERROR: CompleteSystemTest.java not found!
    pause
    exit /b 1
)

echo.
echo [3] Testing compilation...
javac -encoding UTF-8 CompleteSystemTest.java
if errorlevel 1 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo OK: Compilation successful
echo.
echo [4] Testing simple run...
echo Starting system test...
java CompleteSystemTest
echo.
echo System check complete!
pause
