import java.sql.*;

/**
 * إنشاء جدول وحدات القياس في قاعدة البيانات Oracle SHIP_ERP
 * Create Measurement Table in Oracle SHIP_ERP Database
 */
public class CreateMeasurementTableOracle {
    
    public static void main(String[] args) {
        createMeasurementTable();
    }
    
    public static void createMeasurementTable() {
        String url = "*************************************";
        String username = "ship_erp";
        String password = "ship_erp_password";
        
        try {
            // تحميل Oracle JDBC driver
            Class.forName("oracle.jdbc.driver.OracleDriver");
            System.out.println("✅ تم تحميل Oracle JDBC driver بنجاح");
            
            try (Connection connection = DriverManager.getConnection(url, username, password)) {
                System.out.println("✅ تم الاتصال بقاعدة البيانات SHIP_ERP بنجاح");
                
                // إنشاء جدول وحدات القياس
                createTable(connection);
                
                // إدراج البيانات التجريبية
                insertSampleData(connection);
                
                System.out.println("🎉 تم إنشاء جدول وحدات القياس بنجاح في قاعدة البيانات SHIP_ERP!");
                
            }
            
        } catch (ClassNotFoundException e) {
            System.err.println("❌ فشل في تحميل Oracle JDBC driver: " + e.getMessage());
            System.err.println("تأكد من وجود ملف ojdbc11.jar في مجلد lib");
        } catch (SQLException e) {
            System.err.println("❌ خطأ في قاعدة البيانات: " + e.getMessage());
            
            if (e.getMessage().contains("invalid username")) {
                System.err.println("💡 تأكد من وجود المستخدم ship_erp في قاعدة البيانات Oracle");
                System.err.println("💡 يمكنك إنشاء المستخدم باستخدام:");
                System.err.println("   CREATE USER ship_erp IDENTIFIED BY ship_erp_password;");
                System.err.println("   GRANT CONNECT, RESOURCE TO ship_erp;");
            } else if (e.getMessage().contains("Connection refused")) {
                System.err.println("💡 تأكد من تشغيل Oracle Database على المنفذ 1521");
            }
        }
    }
    
    private static void createTable(Connection connection) throws SQLException {
        System.out.println("🔄 إنشاء جدول ERP_MEASUREMENT...");
        
        // حذف الجدول إذا كان موجوداً
        try {
            String dropSQL = "DROP TABLE ERP_MEASUREMENT CASCADE CONSTRAINTS";
            try (Statement stmt = connection.createStatement()) {
                stmt.execute(dropSQL);
                System.out.println("ℹ️ تم حذف الجدول الموجود مسبقاً");
            }
        } catch (SQLException e) {
            // تجاهل خطأ عدم وجود الجدول
        }
        
        // إنشاء الجدول الجديد
        String createTableSQL = """
            CREATE TABLE ERP_MEASUREMENT (
                MEASURE_CODE VARCHAR2(10) NOT NULL,
                MEASURE VARCHAR2(100) NOT NULL,
                MEASURE_F_NM VARCHAR2(100),
                MEASURE_CODE_GB VARCHAR2(10),
                MEASURE_TYPE NUMBER(1) DEFAULT 1,
                MEASURE_WT_TYPE NUMBER(2),
                MEASURE_WT_CONN NUMBER(1) DEFAULT 0,
                DFLT_SIZE NUMBER(22,4),
                ALLOW_UPD NUMBER(1) DEFAULT 1,
                UNT_SALE_TYP NUMBER(2) DEFAULT 3,
                AD_U_ID NUMBER(5),
                AD_DATE DATE DEFAULT SYSDATE,
                UP_U_ID NUMBER(5),
                UP_DATE DATE,
                UP_CNT NUMBER(10) DEFAULT 0,
                AD_TRMNL_NM VARCHAR2(50),
                UP_TRMNL_NM VARCHAR2(50),
                CONSTRAINT PK_ERP_MEASUREMENT PRIMARY KEY (MEASURE_CODE)
            )
        """;
        
        try (Statement stmt = connection.createStatement()) {
            stmt.execute(createTableSQL);
            System.out.println("✅ تم إنشاء جدول ERP_MEASUREMENT بنجاح");
        }
        
        // إضافة التعليقات
        addComments(connection);
        
        // إنشاء الفهارس
        createIndexes(connection);
    }
    
    private static void addComments(Connection connection) throws SQLException {
        System.out.println("🔄 إضافة التعليقات...");
        
        String[] comments = {
            "COMMENT ON TABLE ERP_MEASUREMENT IS 'جدول وحدات القياس - مبني على جدول MEASUREMENT الأصلي'",
            "COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_CODE IS 'كود وحدة القياس (مفتاح أساسي)'",
            "COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE IS 'اسم وحدة القياس بالعربية'",
            "COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_F_NM IS 'اسم وحدة القياس بالإنجليزية'",
            "COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_CODE_GB IS 'الكود العالمي لوحدة القياس'",
            "COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_TYPE IS 'نوع وحدة القياس (1=عادي، 2=وزن، 3=حجم، 4=طول، 5=مساحة)'",
            "COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_WT_TYPE IS 'نوع الوزن'",
            "COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_WT_CONN IS 'ربط الوزن (0=لا، 1=نعم)'",
            "COMMENT ON COLUMN ERP_MEASUREMENT.DFLT_SIZE IS 'الحجم الافتراضي'",
            "COMMENT ON COLUMN ERP_MEASUREMENT.ALLOW_UPD IS 'السماح بالتحديث (1=نعم، 0=لا)'",
            "COMMENT ON COLUMN ERP_MEASUREMENT.UNT_SALE_TYP IS 'نوع وحدة البيع (1=بالقطعة، 2=بالوزن، 3=عادي)'"
        };
        
        try (Statement stmt = connection.createStatement()) {
            for (String comment : comments) {
                stmt.execute(comment);
            }
            System.out.println("✅ تم إضافة التعليقات بنجاح");
        }
    }
    
    private static void createIndexes(Connection connection) throws SQLException {
        System.out.println("🔄 إنشاء الفهارس...");
        
        String[] indexes = {
            "CREATE INDEX IDX_ERP_MEASURE_NAME ON ERP_MEASUREMENT(MEASURE)",
            "CREATE INDEX IDX_ERP_MEASURE_TYPE ON ERP_MEASUREMENT(MEASURE_TYPE)",
            "CREATE INDEX IDX_ERP_MEASURE_ALLOW_UPD ON ERP_MEASUREMENT(ALLOW_UPD)"
        };
        
        try (Statement stmt = connection.createStatement()) {
            for (String index : indexes) {
                try {
                    stmt.execute(index);
                } catch (SQLException e) {
                    // تجاهل خطأ الفهرس الموجود
                    if (!e.getMessage().contains("already exists")) {
                        throw e;
                    }
                }
            }
            System.out.println("✅ تم إنشاء الفهارس بنجاح");
        }
    }
    
    private static void insertSampleData(Connection connection) throws SQLException {
        System.out.println("🔄 إدراج البيانات التجريبية...");
        
        String insertSQL = """
            INSERT INTO ERP_MEASUREMENT (
                MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB, 
                MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE, 
                ALLOW_UPD, UNT_SALE_TYP, AD_U_ID
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;
        
        Object[][] sampleData = {
            {"PIECE", "قطعة", "Piece", "PCE", 1, null, 0, null, 1, 3, 1},
            {"KG", "كيلوجرام", "Kilogram", "KGM", 2, 1, 0, 1.0, 1, 2, 1},
            {"GRAM", "جرام", "Gram", "GRM", 2, 1, 0, 0.001, 1, 2, 1},
            {"TON", "طن", "Ton", "TNE", 2, 1, 0, 1000.0, 1, 2, 1},
            {"LITER", "لتر", "Liter", "LTR", 3, null, 0, 1.0, 1, 3, 1},
            {"METER", "متر", "Meter", "MTR", 4, null, 0, 1.0, 1, 3, 1},
            {"CM", "سنتيمتر", "Centimeter", "CMT", 4, null, 0, 0.01, 1, 3, 1},
            {"SQM", "متر مربع", "Square Meter", "MTK", 5, null, 0, 1.0, 1, 3, 1},
            {"BOX", "صندوق", "Box", "BX", 1, null, 0, null, 1, 1, 1},
            {"DOZEN", "دزينة", "Dozen", "DZN", 1, null, 0, 12.0, 1, 1, 1},
            {"PACK", "عبوة", "Pack", "PK", 1, null, 0, null, 1, 1, 1},
            {"BOTTLE", "زجاجة", "Bottle", "BTL", 1, null, 0, null, 1, 1, 1},
            {"CAN", "علبة", "Can", "CA", 1, null, 0, null, 1, 1, 1},
            {"BAG", "كيس", "Bag", "BG", 1, null, 0, null, 1, 1, 1},
            {"ROLL", "لفة", "Roll", "RO", 1, null, 0, null, 1, 1, 1}
        };
        
        try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
            for (Object[] data : sampleData) {
                stmt.setString(1, (String) data[0]);
                stmt.setString(2, (String) data[1]);
                stmt.setString(3, (String) data[2]);
                stmt.setString(4, (String) data[3]);
                stmt.setObject(5, data[4]);
                stmt.setObject(6, data[5]);
                stmt.setObject(7, data[6]);
                stmt.setObject(8, data[7]);
                stmt.setObject(9, data[8]);
                stmt.setObject(10, data[9]);
                stmt.setObject(11, data[10]);
                
                stmt.executeUpdate();
            }
            
            connection.commit();
            System.out.println("✅ تم إدراج " + sampleData.length + " وحدة قياس تجريبية بنجاح");
        }
    }
}
