@echo off
chcp 65001 >nul
title تشغيل نظام إدارة وحدات القياس

echo.
echo ========================================
echo    🚀 نظام إدارة وحدات القياس
echo ========================================
echo.

cd /d "e:\ship_erp\java\src\main\java"

echo 📋 التحقق من المكتبات المطلوبة...
echo.

if not exist "lib\ojdbc11.jar" (
    echo ❌ ملف Oracle JDBC غير موجود: lib\ojdbc11.jar
    goto :error
)
echo ✅ Oracle JDBC Driver موجود

if not exist "lib\orai18n.jar" (
    echo ❌ ملف دعم العربية غير موجود: lib\orai18n.jar
    goto :error
)
echo ✅ ملف دعم الأحرف العربية موجود

if not exist "lib\h2-2.2.224.jar" (
    echo ❌ ملف H2 Database غير موجود: lib\h2-2.2.224.jar
    goto :error
)
echo ✅ H2 Database موجود

echo.
echo ========================================
echo.
echo اختر النظام المطلوب تشغيله:
echo.
echo 1. النظام الكامل (شجرة الأنظمة)
echo 2. نافذة وحدات القياس فقط
echo 3. إنشاء جدول قاعدة البيانات
echo 4. خروج
echo.
set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" goto :full_system
if "%choice%"=="2" goto :measurement_window
if "%choice%"=="3" goto :create_table
if "%choice%"=="4" goto :exit
goto :invalid_choice

:full_system
echo.
echo 🔄 تشغيل النظام الكامل...
java "-Duser.language=en" "-Duser.country=US" "-Duser.region=US" "-Dfile.encoding=UTF-8" "-Djava.locale.providers=COMPAT,SPI" -cp "lib/ojdbc11.jar;lib/orai18n.jar;lib/h2-2.2.224.jar;." CompleteSystemTest
goto :end

:measurement_window
echo.
echo 🔄 تشغيل نافذة وحدات القياس...
java "-Duser.language=en" "-Duser.country=US" "-Duser.region=US" "-Dfile.encoding=UTF-8" "-Djava.locale.providers=COMPAT,SPI" -cp "lib/ojdbc11.jar;lib/orai18n.jar;lib/h2-2.2.224.jar;." OriginalMeasurementUnitsWindow
goto :end

:create_table
echo.
echo 🔄 إنشاء جدول قاعدة البيانات...
java "-Duser.language=en" "-Duser.country=US" "-Dfile.encoding=UTF-8" -cp "lib/ojdbc11.jar;lib/orai18n.jar;lib/h2-2.2.224.jar;." CreateTableDirectly
goto :end

:invalid_choice
echo.
echo ❌ اختيار غير صحيح!
echo.
goto :start

:error
echo.
echo ❌ خطأ: بعض المكتبات مفقودة
echo تأكد من وجود جميع ملفات المكتبات في مجلد lib
goto :end

:exit
echo.
echo 👋 شكراً لاستخدام النظام
goto :end

:end
echo.
echo ========================================
echo انتهى التشغيل
pause
