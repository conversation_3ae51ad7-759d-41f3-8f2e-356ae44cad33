package com.shipment.erp.controller;

import com.shipment.erp.ShipERPApplication;
import com.shipment.erp.service.UserService;
import com.shipment.erp.util.DateUtil;
import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.util.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.time.LocalDateTime;
import java.util.ResourceBundle;

/**
 * Controller لشاشة تسجيل الدخول
 */
public class LoginController implements Initializable {

    private static final Logger logger = LoggerFactory.getLogger(LoginController.class);

    @FXML private TextField usernameField;
    @FXML private PasswordField passwordField;
    @FXML private Label errorLabel;
    @FXML private Button loginButton;
    @FXML private Button cancelButton;
    @FXML private Hyperlink forgotPasswordLink;
    @FXML private Label statusLabel;
    @FXML private Label dateTimeLabel;

    private UserService userService;
    private ResourceBundle resources;
    private Timeline clockTimeline;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        this.resources = resources;
        
        try {
            // الحصول على UserService من Spring Context
            userService = ShipERPApplication.getBean(UserService.class);
            
            setupUI();
            startClock();
            
            logger.info("تم تهيئة شاشة تسجيل الدخول بنجاح");
            
        } catch (Exception e) {
            logger.error("خطأ في تهيئة شاشة تسجيل الدخول", e);
            showError("خطأ في تهيئة الشاشة: " + e.getMessage());
        }
    }

    /**
     * إعداد واجهة المستخدم
     */
    private void setupUI() {
        // تعيين التركيز على حقل اسم المستخدم
        Platform.runLater(() -> usernameField.requestFocus());
        
        // ربط مفتاح Enter بتسجيل الدخول
        passwordField.setOnAction(event -> handleLogin());
        
        // إخفاء رسالة الخطأ في البداية
        errorLabel.setVisible(false);
        
        // تعيين النص الافتراضي لشريط الحالة
        statusLabel.setText(getMessage("status.ready", "جاهز"));
    }

    /**
     * بدء ساعة الوقت الحقيقي
     */
    private void startClock() {
        clockTimeline = new Timeline(new KeyFrame(Duration.seconds(1), e -> updateDateTime()));
        clockTimeline.setCycleCount(Timeline.INDEFINITE);
        clockTimeline.play();
        updateDateTime(); // تحديث فوري
    }

    /**
     * تحديث التاريخ والوقت
     */
    private void updateDateTime() {
        LocalDateTime now = DateUtil.getCurrentDateTime();
        String dateTimeText = DateUtil.formatDateTimeArabic(now);
        dateTimeLabel.setText(dateTimeText);
    }

    /**
     * معالج تسجيل الدخول
     */
    @FXML
    private void handleLogin() {
        String username = usernameField.getText().trim();
        String password = passwordField.getText();
        
        // التحقق من صحة البيانات
        if (username.isEmpty()) {
            showError(getMessage("error.username.required", "اسم المستخدم مطلوب"));
            usernameField.requestFocus();
            return;
        }
        
        if (password.isEmpty()) {
            showError(getMessage("error.password.required", "كلمة المرور مطلوبة"));
            passwordField.requestFocus();
            return;
        }
        
        // تعطيل الأزرار أثناء المعالجة
        setUIEnabled(false);
        statusLabel.setText(getMessage("status.logging.in", "جاري تسجيل الدخول..."));
        
        // تنفيذ تسجيل الدخول في خيط منفصل
        Task<UserService.LoginResult> loginTask = new Task<UserService.LoginResult>() {
            @Override
            protected UserService.LoginResult call() throws Exception {
                return userService.login(username, password, "127.0.0.1", "JavaFX Application");
            }
        };
        
        loginTask.setOnSucceeded(event -> {
            UserService.LoginResult result = loginTask.getValue();
            Platform.runLater(() -> {
                setUIEnabled(true);
                
                if (result.isSuccess()) {
                    logger.info("تم تسجيل دخول المستخدم: {}", username);
                    
                    // حفظ بيانات المستخدم في الجلسة
                    SessionManager.getInstance().setCurrentUser(result.getUser());
                    
                    // إغلاق الساعة
                    if (clockTimeline != null) {
                        clockTimeline.stop();
                    }
                    
                    // الانتقال للواجهة الرئيسية
                    ShipERPApplication app = (ShipERPApplication) usernameField.getScene().getWindow().getUserData();
                    if (app != null) {
                        app.showMainInterface();
                    }
                    
                } else {
                    logger.warn("فشل تسجيل الدخول للمستخدم: {} - {}", username, result.getMessage());
                    showError(result.getMessage());
                    passwordField.clear();
                    usernameField.requestFocus();
                }
                
                statusLabel.setText(getMessage("status.ready", "جاهز"));
            });
        });
        
        loginTask.setOnFailed(event -> {
            Throwable exception = loginTask.getException();
            logger.error("خطأ في تسجيل الدخول", exception);
            
            Platform.runLater(() -> {
                setUIEnabled(true);
                showError(getMessage("error.login.failed", "فشل في تسجيل الدخول: ") + exception.getMessage());
                statusLabel.setText(getMessage("status.ready", "جاهز"));
            });
        });
        
        // تشغيل المهمة
        Thread loginThread = new Thread(loginTask);
        loginThread.setDaemon(true);
        loginThread.start();
    }

    /**
     * معالج إلغاء تسجيل الدخول
     */
    @FXML
    private void handleCancel() {
        logger.info("تم إلغاء تسجيل الدخول");
        Platform.exit();
    }

    /**
     * معالج نسيان كلمة المرور
     */
    @FXML
    private void handleForgotPassword() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(getMessage("forgot.password.title", "نسيان كلمة المرور"));
        alert.setHeaderText(null);
        alert.setContentText(getMessage("forgot.password.message", 
            "يرجى الاتصال بمدير النظام لإعادة تعيين كلمة المرور"));
        alert.showAndWait();
    }

    /**
     * عرض رسالة خطأ
     */
    private void showError(String message) {
        errorLabel.setText(message);
        errorLabel.setVisible(true);
        
        // إخفاء الرسالة بعد 5 ثوان
        Timeline hideTimeline = new Timeline(new KeyFrame(Duration.seconds(5), e -> errorLabel.setVisible(false)));
        hideTimeline.play();
    }

    /**
     * تفعيل/تعطيل واجهة المستخدم
     */
    private void setUIEnabled(boolean enabled) {
        usernameField.setDisable(!enabled);
        passwordField.setDisable(!enabled);
        loginButton.setDisable(!enabled);
        cancelButton.setDisable(!enabled);
        forgotPasswordLink.setDisable(!enabled);
    }

    /**
     * الحصول على رسالة من ملف الموارد
     */
    private String getMessage(String key, String defaultValue) {
        if (resources != null && resources.containsKey(key)) {
            return resources.getString(key);
        }
        return defaultValue;
    }

    /**
     * تنظيف الموارد عند إغلاق الشاشة
     */
    public void cleanup() {
        if (clockTimeline != null) {
            clockTimeline.stop();
        }
    }

    /**
     * مدير الجلسة لحفظ بيانات المستخدم الحالي
     */
    public static class SessionManager {
        private static SessionManager instance;
        private com.shipment.erp.model.User currentUser;

        private SessionManager() {}

        public static SessionManager getInstance() {
            if (instance == null) {
                instance = new SessionManager();
            }
            return instance;
        }

        public com.shipment.erp.model.User getCurrentUser() {
            return currentUser;
        }

        public void setCurrentUser(com.shipment.erp.model.User currentUser) {
            this.currentUser = currentUser;
        }

        public boolean isLoggedIn() {
            return currentUser != null;
        }

        public void logout() {
            currentUser = null;
        }
    }
}
