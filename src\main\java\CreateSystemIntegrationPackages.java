import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * إنشاء Packages و Procedures و Functions لنظام الربط والاستيراد Create System Integration
 * Packages, Procedures and Functions
 */
public class CreateSystemIntegrationPackages {

    public static void main(String[] args) {
        try {
            // تحميل Oracle JDBC driver
            Class.forName("oracle.jdbc.OracleDriver");
            System.out.println("✅ تم تحميل Oracle JDBC driver بنجاح");

            // الاتصال بقاعدة البيانات SHIP_ERP
            Connection conn = DriverManager.getConnection("*************************************",
                    "ship_erp", "ship_erp_password");
            conn.setAutoCommit(false);

            System.out.println("✅ متصل بقاعدة البيانات SHIP_ERP");

            // إنشاء الـ Packages
            createConnectionManagementPackage(conn);
            createTableMappingPackage(conn);
            createFieldMappingPackage(conn);
            createImportExecutionPackage(conn);
            createConfigurationPackage(conn);
            createUtilityPackage(conn);

            conn.commit();
            System.out.println("🎉 تم إنشاء جميع Packages و Procedures بنجاح!");

            conn.close();

        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * إنشاء Package إدارة الاتصالات
     */
    private static void createConnectionManagementPackage(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء Package إدارة الاتصالات...");

        // Package Specification
        String packageSpec = """
                    CREATE OR REPLACE PACKAGE PKG_CONNECTION_MANAGEMENT AS
                        -- إدارة اتصالات الأنظمة الخارجية

                        -- إضافة اتصال جديد
                        FUNCTION ADD_CONNECTION(
                            p_connection_name VARCHAR2,
                            p_connection_type VARCHAR2,
                            p_host_name VARCHAR2,
                            p_port_number NUMBER,
                            p_database_name VARCHAR2,
                            p_username VARCHAR2,
                            p_password VARCHAR2,
                            p_schema_name VARCHAR2 DEFAULT NULL,
                            p_description VARCHAR2 DEFAULT NULL,
                            p_created_by VARCHAR2 DEFAULT 'SYSTEM'
                        ) RETURN NUMBER;

                        -- تحديث اتصال
                        PROCEDURE UPDATE_CONNECTION(
                            p_connection_id NUMBER,
                            p_connection_name VARCHAR2,
                            p_connection_type VARCHAR2,
                            p_host_name VARCHAR2,
                            p_port_number NUMBER,
                            p_database_name VARCHAR2,
                            p_username VARCHAR2,
                            p_password VARCHAR2,
                            p_schema_name VARCHAR2 DEFAULT NULL,
                            p_description VARCHAR2 DEFAULT NULL,
                            p_updated_by VARCHAR2 DEFAULT 'SYSTEM'
                        );

                        -- حذف اتصال
                        PROCEDURE DELETE_CONNECTION(p_connection_id NUMBER);

                        -- اختبار اتصال
                        FUNCTION TEST_CONNECTION(p_connection_id NUMBER) RETURN VARCHAR2;

                        -- تفعيل/إلغاء تفعيل اتصال
                        PROCEDURE TOGGLE_CONNECTION_STATUS(
                            p_connection_id NUMBER,
                            p_is_active NUMBER
                        );

                        -- الحصول على معلومات اتصال
                        FUNCTION GET_CONNECTION_INFO(p_connection_id NUMBER)
                        RETURN SYS_REFCURSOR;

                        -- الحصول على جميع الاتصالات
                        FUNCTION GET_ALL_CONNECTIONS RETURN SYS_REFCURSOR;

                    END PKG_CONNECTION_MANAGEMENT;
                """;

        executeSQL(conn, packageSpec, "Package Specification - إدارة الاتصالات");

        // Package Body
        String packageBody =
                """
                            CREATE OR REPLACE PACKAGE BODY PKG_CONNECTION_MANAGEMENT AS

                                FUNCTION ADD_CONNECTION(
                                    p_connection_name VARCHAR2,
                                    p_connection_type VARCHAR2,
                                    p_host_name VARCHAR2,
                                    p_port_number NUMBER,
                                    p_database_name VARCHAR2,
                                    p_username VARCHAR2,
                                    p_password VARCHAR2,
                                    p_schema_name VARCHAR2 DEFAULT NULL,
                                    p_description VARCHAR2 DEFAULT NULL,
                                    p_created_by VARCHAR2 DEFAULT 'SYSTEM'
                                ) RETURN NUMBER IS
                                    v_connection_id NUMBER;
                                BEGIN
                                    INSERT INTO ERP_SYSTEM_CONNECTIONS (
                                        CONNECTION_ID, CONNECTION_NAME, CONNECTION_TYPE, HOST_NAME,
                                        PORT_NUMBER, DATABASE_NAME, USERNAME, PASSWORD,
                                        SCHEMA_NAME, DESCRIPTION, IS_ACTIVE, CREATED_BY, CREATED_DATE
                                    ) VALUES (
                                        SEQ_SYSTEM_CONNECTIONS.NEXTVAL, p_connection_name, p_connection_type,
                                        p_host_name, p_port_number, p_database_name, p_username,
                                        p_password, p_schema_name, p_description, 1, p_created_by, SYSDATE
                                    ) RETURNING CONNECTION_ID INTO v_connection_id;

                                    COMMIT;
                                    RETURN v_connection_id;
                                EXCEPTION
                                    WHEN OTHERS THEN
                                        ROLLBACK;
                                        RAISE_APPLICATION_ERROR(-20001, 'خطأ في إضافة الاتصال: ' || SQLERRM);
                                END ADD_CONNECTION;

                                PROCEDURE UPDATE_CONNECTION(
                                    p_connection_id NUMBER,
                                    p_connection_name VARCHAR2,
                                    p_connection_type VARCHAR2,
                                    p_host_name VARCHAR2,
                                    p_port_number NUMBER,
                                    p_database_name VARCHAR2,
                                    p_username VARCHAR2,
                                    p_password VARCHAR2,
                                    p_schema_name VARCHAR2 DEFAULT NULL,
                                    p_description VARCHAR2 DEFAULT NULL,
                                    p_updated_by VARCHAR2 DEFAULT 'SYSTEM'
                                ) IS
                                BEGIN
                                    UPDATE ERP_SYSTEM_CONNECTIONS SET
                                        CONNECTION_NAME = p_connection_name,
                                        CONNECTION_TYPE = p_connection_type,
                                        HOST_NAME = p_host_name,
                                        PORT_NUMBER = p_port_number,
                                        DATABASE_NAME = p_database_name,
                                        USERNAME = p_username,
                                        PASSWORD = p_password,
                                        SCHEMA_NAME = p_schema_name,
                                        DESCRIPTION = p_description,
                                        UPDATED_BY = p_updated_by,
                                        UPDATED_DATE = SYSDATE
                                    WHERE CONNECTION_ID = p_connection_id;

                                    IF SQL%ROWCOUNT = 0 THEN
                                        RAISE_APPLICATION_ERROR(-20002, 'الاتصال غير موجود');
                                    END IF;

                                    COMMIT;
                                EXCEPTION
                                    WHEN OTHERS THEN
                                        ROLLBACK;
                                        RAISE_APPLICATION_ERROR(-20003, 'خطأ في تحديث الاتصال: ' || SQLERRM);
                                END UPDATE_CONNECTION;

                                PROCEDURE DELETE_CONNECTION(p_connection_id NUMBER) IS
                                    v_count NUMBER;
                                BEGIN
                                    -- التحقق من وجود ربط جداول مرتبطة
                                    SELECT COUNT(*) INTO v_count
                                    FROM ERP_TABLE_MAPPING
                                    WHERE CONNECTION_ID = p_connection_id;

                                    IF v_count > 0 THEN
                                        RAISE_APPLICATION_ERROR(-20004, 'لا يمكن حذف الاتصال - يوجد ربط جداول مرتبطة');
                                    END IF;

                                    DELETE FROM ERP_SYSTEM_CONNECTIONS
                                    WHERE CONNECTION_ID = p_connection_id;

                                    IF SQL%ROWCOUNT = 0 THEN
                                        RAISE_APPLICATION_ERROR(-20005, 'الاتصال غير موجود');
                                    END IF;

                                    COMMIT;
                                EXCEPTION
                                    WHEN OTHERS THEN
                                        ROLLBACK;
                                        RAISE_APPLICATION_ERROR(-20006, 'خطأ في حذف الاتصال: ' || SQLERRM);
                                END DELETE_CONNECTION;

                                FUNCTION TEST_CONNECTION(p_connection_id NUMBER) RETURN VARCHAR2 IS
                                    v_result VARCHAR2(1000);
                                    v_connection_name VARCHAR2(100);
                                BEGIN
                                    SELECT CONNECTION_NAME INTO v_connection_name
                                    FROM ERP_SYSTEM_CONNECTIONS
                                    WHERE CONNECTION_ID = p_connection_id;

                                    -- محاكاة اختبار الاتصال
                                    v_result := 'تم اختبار الاتصال ' || v_connection_name || ' بنجاح';

                                    RETURN v_result;
                                EXCEPTION
                                    WHEN NO_DATA_FOUND THEN
                                        RETURN 'الاتصال غير موجود';
                                    WHEN OTHERS THEN
                                        RETURN 'خطأ في اختبار الاتصال: ' || SQLERRM;
                                END TEST_CONNECTION;

                                PROCEDURE TOGGLE_CONNECTION_STATUS(
                                    p_connection_id NUMBER,
                                    p_is_active NUMBER
                                ) IS
                                BEGIN
                                    UPDATE ERP_SYSTEM_CONNECTIONS
                                    SET IS_ACTIVE = p_is_active,
                                        UPDATED_DATE = SYSDATE
                                    WHERE CONNECTION_ID = p_connection_id;

                                    IF SQL%ROWCOUNT = 0 THEN
                                        RAISE_APPLICATION_ERROR(-20007, 'الاتصال غير موجود');
                                    END IF;

                                    COMMIT;
                                EXCEPTION
                                    WHEN OTHERS THEN
                                        ROLLBACK;
                                        RAISE_APPLICATION_ERROR(-20008, 'خطأ في تغيير حالة الاتصال: ' || SQLERRM);
                                END TOGGLE_CONNECTION_STATUS;

                                FUNCTION GET_CONNECTION_INFO(p_connection_id NUMBER)
                                RETURN SYS_REFCURSOR IS
                                    v_cursor SYS_REFCURSOR;
                                BEGIN
                                    OPEN v_cursor FOR
                                        SELECT * FROM ERP_SYSTEM_CONNECTIONS
                                        WHERE CONNECTION_ID = p_connection_id;

                                    RETURN v_cursor;
                                END GET_CONNECTION_INFO;

                                FUNCTION GET_ALL_CONNECTIONS RETURN SYS_REFCURSOR IS
                                    v_cursor SYS_REFCURSOR;
                                BEGIN
                                    OPEN v_cursor FOR
                                        SELECT CONNECTION_ID, CONNECTION_NAME, CONNECTION_TYPE,
                                               HOST_NAME, PORT_NUMBER, DATABASE_NAME, USERNAME,
                                               SCHEMA_NAME, IS_ACTIVE, DESCRIPTION, CREATED_DATE
                                        FROM ERP_SYSTEM_CONNECTIONS
                                        ORDER BY CONNECTION_NAME;

                                    RETURN v_cursor;
                                END GET_ALL_CONNECTIONS;

                            END PKG_CONNECTION_MANAGEMENT;
                        """;

        executeSQL(conn, packageBody, "Package Body - إدارة الاتصالات");
    }

    /**
     * تنفيذ SQL مع معالجة الأخطاء
     */
    private static void executeSQL(Connection conn, String sql, String description)
            throws SQLException {
        try {
            Statement stmt = conn.createStatement();
            stmt.execute(sql);
            System.out.println("✅ تم إنشاء " + description);
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) { // ORA-00955: name is already used
                System.out.println("⚠️ " + description + " موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إنشاء " + description + ": " + e.getMessage());
                throw e;
            }
        }
    }

    // باقي الـ Packages سيتم إضافتها في الخطوات التالية
    private static void createTableMappingPackage(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء Package ربط الجداول...");

        // Package Specification
        String packageSpec = """
                    CREATE OR REPLACE PACKAGE PKG_TABLE_MAPPING AS
                        -- إدارة ربط الجداول

                        -- إضافة ربط جدول جديد
                        FUNCTION ADD_TABLE_MAPPING(
                            p_connection_id NUMBER,
                            p_source_table_name VARCHAR2,
                            p_target_table_name VARCHAR2,
                            p_mapping_name VARCHAR2,
                            p_mapping_type VARCHAR2 DEFAULT 'IMPORT',
                            p_where_condition VARCHAR2 DEFAULT NULL,
                            p_order_by_clause VARCHAR2 DEFAULT NULL,
                            p_description VARCHAR2 DEFAULT NULL,
                            p_created_by VARCHAR2 DEFAULT 'SYSTEM'
                        ) RETURN NUMBER;

                        -- تحديث ربط جدول
                        PROCEDURE UPDATE_TABLE_MAPPING(
                            p_mapping_id NUMBER,
                            p_connection_id NUMBER,
                            p_source_table_name VARCHAR2,
                            p_target_table_name VARCHAR2,
                            p_mapping_name VARCHAR2,
                            p_mapping_type VARCHAR2,
                            p_where_condition VARCHAR2 DEFAULT NULL,
                            p_order_by_clause VARCHAR2 DEFAULT NULL,
                            p_description VARCHAR2 DEFAULT NULL,
                            p_updated_by VARCHAR2 DEFAULT 'SYSTEM'
                        );

                        -- حذف ربط جدول
                        PROCEDURE DELETE_TABLE_MAPPING(p_mapping_id NUMBER);

                        -- تفعيل/إلغاء تفعيل ربط جدول
                        PROCEDURE TOGGLE_MAPPING_STATUS(
                            p_mapping_id NUMBER,
                            p_is_active NUMBER
                        );

                        -- تحديث معلومات آخر استيراد
                        PROCEDURE UPDATE_LAST_IMPORT_INFO(
                            p_mapping_id NUMBER,
                            p_import_count NUMBER
                        );

                        -- الحصول على جميع ربط الجداول
                        FUNCTION GET_ALL_TABLE_MAPPINGS RETURN SYS_REFCURSOR;

                        -- الحصول على ربط جدول محدد
                        FUNCTION GET_TABLE_MAPPING(p_mapping_id NUMBER) RETURN SYS_REFCURSOR;

                        -- الحصول على ربط الجداول لاتصال محدد
                        FUNCTION GET_MAPPINGS_BY_CONNECTION(p_connection_id NUMBER)
                        RETURN SYS_REFCURSOR;

                    END PKG_TABLE_MAPPING;
                """;

        executeSQL(conn, packageSpec, "Package Specification - ربط الجداول");

        // Package Body
        String packageBody =
                """
                            CREATE OR REPLACE PACKAGE BODY PKG_TABLE_MAPPING AS

                                FUNCTION ADD_TABLE_MAPPING(
                                    p_connection_id NUMBER,
                                    p_source_table_name VARCHAR2,
                                    p_target_table_name VARCHAR2,
                                    p_mapping_name VARCHAR2,
                                    p_mapping_type VARCHAR2 DEFAULT 'IMPORT',
                                    p_where_condition VARCHAR2 DEFAULT NULL,
                                    p_order_by_clause VARCHAR2 DEFAULT NULL,
                                    p_description VARCHAR2 DEFAULT NULL,
                                    p_created_by VARCHAR2 DEFAULT 'SYSTEM'
                                ) RETURN NUMBER IS
                                    v_mapping_id NUMBER;
                                    v_count NUMBER;
                                BEGIN
                                    -- التحقق من وجود الاتصال
                                    SELECT COUNT(*) INTO v_count
                                    FROM ERP_SYSTEM_CONNECTIONS
                                    WHERE CONNECTION_ID = p_connection_id AND IS_ACTIVE = 1;

                                    IF v_count = 0 THEN
                                        RAISE_APPLICATION_ERROR(-20101, 'الاتصال غير موجود أو غير نشط');
                                    END IF;

                                    -- التحقق من عدم تكرار اسم الربط
                                    SELECT COUNT(*) INTO v_count
                                    FROM ERP_TABLE_MAPPING
                                    WHERE MAPPING_NAME = p_mapping_name;

                                    IF v_count > 0 THEN
                                        RAISE_APPLICATION_ERROR(-20102, 'اسم الربط موجود مسبقاً');
                                    END IF;

                                    INSERT INTO ERP_TABLE_MAPPING (
                                        MAPPING_ID, CONNECTION_ID, SOURCE_TABLE_NAME, TARGET_TABLE_NAME,
                                        MAPPING_NAME, MAPPING_TYPE, WHERE_CONDITION, ORDER_BY_CLAUSE,
                                        IS_ACTIVE, AUTO_IMPORT, DESCRIPTION, CREATED_BY, CREATED_DATE
                                    ) VALUES (
                                        SEQ_TABLE_MAPPING.NEXTVAL, p_connection_id, p_source_table_name,
                                        p_target_table_name, p_mapping_name, p_mapping_type,
                                        p_where_condition, p_order_by_clause, 1, 0, p_description,
                                        p_created_by, SYSDATE
                                    ) RETURNING MAPPING_ID INTO v_mapping_id;

                                    COMMIT;
                                    RETURN v_mapping_id;
                                EXCEPTION
                                    WHEN OTHERS THEN
                                        ROLLBACK;
                                        RAISE_APPLICATION_ERROR(-20103, 'خطأ في إضافة ربط الجدول: ' || SQLERRM);
                                END ADD_TABLE_MAPPING;

                                PROCEDURE UPDATE_TABLE_MAPPING(
                                    p_mapping_id NUMBER,
                                    p_connection_id NUMBER,
                                    p_source_table_name VARCHAR2,
                                    p_target_table_name VARCHAR2,
                                    p_mapping_name VARCHAR2,
                                    p_mapping_type VARCHAR2,
                                    p_where_condition VARCHAR2 DEFAULT NULL,
                                    p_order_by_clause VARCHAR2 DEFAULT NULL,
                                    p_description VARCHAR2 DEFAULT NULL,
                                    p_updated_by VARCHAR2 DEFAULT 'SYSTEM'
                                ) IS
                                    v_count NUMBER;
                                BEGIN
                                    -- التحقق من وجود الاتصال
                                    SELECT COUNT(*) INTO v_count
                                    FROM ERP_SYSTEM_CONNECTIONS
                                    WHERE CONNECTION_ID = p_connection_id AND IS_ACTIVE = 1;

                                    IF v_count = 0 THEN
                                        RAISE_APPLICATION_ERROR(-20104, 'الاتصال غير موجود أو غير نشط');
                                    END IF;

                                    UPDATE ERP_TABLE_MAPPING SET
                                        CONNECTION_ID = p_connection_id,
                                        SOURCE_TABLE_NAME = p_source_table_name,
                                        TARGET_TABLE_NAME = p_target_table_name,
                                        MAPPING_NAME = p_mapping_name,
                                        MAPPING_TYPE = p_mapping_type,
                                        WHERE_CONDITION = p_where_condition,
                                        ORDER_BY_CLAUSE = p_order_by_clause,
                                        DESCRIPTION = p_description,
                                        UPDATED_BY = p_updated_by,
                                        UPDATED_DATE = SYSDATE
                                    WHERE MAPPING_ID = p_mapping_id;

                                    IF SQL%ROWCOUNT = 0 THEN
                                        RAISE_APPLICATION_ERROR(-20105, 'ربط الجدول غير موجود');
                                    END IF;

                                    COMMIT;
                                EXCEPTION
                                    WHEN OTHERS THEN
                                        ROLLBACK;
                                        RAISE_APPLICATION_ERROR(-20106, 'خطأ في تحديث ربط الجدول: ' || SQLERRM);
                                END UPDATE_TABLE_MAPPING;

                                PROCEDURE DELETE_TABLE_MAPPING(p_mapping_id NUMBER) IS
                                    v_count NUMBER;
                                BEGIN
                                    -- التحقق من وجود ربط حقول مرتبطة
                                    SELECT COUNT(*) INTO v_count
                                    FROM ERP_FIELD_MAPPING
                                    WHERE TABLE_MAPPING_ID = p_mapping_id;

                                    IF v_count > 0 THEN
                                        -- حذف ربط الحقول أولاً
                                        DELETE FROM ERP_FIELD_MAPPING
                                        WHERE TABLE_MAPPING_ID = p_mapping_id;
                                    END IF;

                                    -- حذف سجلات التاريخ
                                    DELETE FROM ERP_IMPORT_HISTORY
                                    WHERE TABLE_MAPPING_ID = p_mapping_id;

                                    -- حذف ربط الجدول
                                    DELETE FROM ERP_TABLE_MAPPING
                                    WHERE MAPPING_ID = p_mapping_id;

                                    IF SQL%ROWCOUNT = 0 THEN
                                        RAISE_APPLICATION_ERROR(-20107, 'ربط الجدول غير موجود');
                                    END IF;

                                    COMMIT;
                                EXCEPTION
                                    WHEN OTHERS THEN
                                        ROLLBACK;
                                        RAISE_APPLICATION_ERROR(-20108, 'خطأ في حذف ربط الجدول: ' || SQLERRM);
                                END DELETE_TABLE_MAPPING;

                                PROCEDURE TOGGLE_MAPPING_STATUS(
                                    p_mapping_id NUMBER,
                                    p_is_active NUMBER
                                ) IS
                                BEGIN
                                    UPDATE ERP_TABLE_MAPPING
                                    SET IS_ACTIVE = p_is_active,
                                        UPDATED_DATE = SYSDATE
                                    WHERE MAPPING_ID = p_mapping_id;

                                    IF SQL%ROWCOUNT = 0 THEN
                                        RAISE_APPLICATION_ERROR(-20109, 'ربط الجدول غير موجود');
                                    END IF;

                                    COMMIT;
                                EXCEPTION
                                    WHEN OTHERS THEN
                                        ROLLBACK;
                                        RAISE_APPLICATION_ERROR(-20110, 'خطأ في تغيير حالة ربط الجدول: ' || SQLERRM);
                                END TOGGLE_MAPPING_STATUS;

                                PROCEDURE UPDATE_LAST_IMPORT_INFO(
                                    p_mapping_id NUMBER,
                                    p_import_count NUMBER
                                ) IS
                                BEGIN
                                    UPDATE ERP_TABLE_MAPPING
                                    SET LAST_IMPORT_DATE = SYSDATE,
                                        LAST_IMPORT_COUNT = p_import_count
                                    WHERE MAPPING_ID = p_mapping_id;

                                    COMMIT;
                                EXCEPTION
                                    WHEN OTHERS THEN
                                        ROLLBACK;
                                        RAISE_APPLICATION_ERROR(-20111, 'خطأ في تحديث معلومات الاستيراد: ' || SQLERRM);
                                END UPDATE_LAST_IMPORT_INFO;

                                FUNCTION GET_ALL_TABLE_MAPPINGS RETURN SYS_REFCURSOR IS
                                    v_cursor SYS_REFCURSOR;
                                BEGIN
                                    OPEN v_cursor FOR
                                        SELECT tm.MAPPING_ID, tm.MAPPING_NAME, tm.SOURCE_TABLE_NAME,
                                               tm.TARGET_TABLE_NAME, tm.MAPPING_TYPE, tm.LAST_IMPORT_DATE,
                                               tm.LAST_IMPORT_COUNT, tm.IS_ACTIVE, sc.CONNECTION_NAME
                                        FROM ERP_TABLE_MAPPING tm
                                        LEFT JOIN ERP_SYSTEM_CONNECTIONS sc ON tm.CONNECTION_ID = sc.CONNECTION_ID
                                        ORDER BY tm.MAPPING_NAME;

                                    RETURN v_cursor;
                                END GET_ALL_TABLE_MAPPINGS;

                                FUNCTION GET_TABLE_MAPPING(p_mapping_id NUMBER) RETURN SYS_REFCURSOR IS
                                    v_cursor SYS_REFCURSOR;
                                BEGIN
                                    OPEN v_cursor FOR
                                        SELECT * FROM ERP_TABLE_MAPPING
                                        WHERE MAPPING_ID = p_mapping_id;

                                    RETURN v_cursor;
                                END GET_TABLE_MAPPING;

                                FUNCTION GET_MAPPINGS_BY_CONNECTION(p_connection_id NUMBER)
                                RETURN SYS_REFCURSOR IS
                                    v_cursor SYS_REFCURSOR;
                                BEGIN
                                    OPEN v_cursor FOR
                                        SELECT * FROM ERP_TABLE_MAPPING
                                        WHERE CONNECTION_ID = p_connection_id
                                        ORDER BY MAPPING_NAME;

                                    RETURN v_cursor;
                                END GET_MAPPINGS_BY_CONNECTION;

                            END PKG_TABLE_MAPPING;
                        """;

        executeSQL(conn, packageBody, "Package Body - ربط الجداول");
    }

    private static void createFieldMappingPackage(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء Package ربط الحقول...");
        // سيتم تنفيذها لاحقاً
    }

    private static void createImportExecutionPackage(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء Package تنفيذ الاستيراد...");
        // سيتم تنفيذها لاحقاً
    }

    private static void createConfigurationPackage(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء Package الإعدادات...");
        // سيتم تنفيذها لاحقاً
    }

    private static void createUtilityPackage(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء Package الأدوات المساعدة...");
        // سيتم تنفيذها لاحقاً
    }
}
