@echo off
chcp 65001 > nul
echo ========================================
echo    نظام إدارة الشحنات المحسن
echo    Enhanced Ship ERP System
echo ========================================
echo.

echo [INFO] بدء تشغيل النظام المحسن...
echo.

REM التحقق من وجود Java
echo [1/4] التحقق من Java...
java -version > nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java غير مثبت أو غير موجود في PATH
    echo [INFO] يرجى تثبيت Java 17 أو أحدث
    pause
    exit /b 1
)
echo [OK] Java متوفر

REM التحقق من وجود الملفات المطلوبة
echo [2/4] التحقق من الملفات...
if not exist "src\main\java\EnhancedShipERP.java" (
    echo [ERROR] ملف التطبيق المحسن غير موجود
    pause
    exit /b 1
)
echo [OK] الملفات متوفرة

REM تجميع التطبيق
echo [3/4] تجميع التطبيق المحسن...
cd src\main\java

REM محاولة التجميع مع المكتبات المحسنة
echo [INFO] محاولة التجميع مع المكتبات المحسنة...
javac -encoding UTF-8 -cp ".;*" EnhancedShipERP.java > nul 2>&1

if %errorlevel% neq 0 (
    echo [WARNING] فشل التجميع مع المكتبات المحسنة
    echo [INFO] التجميع بدون المكتبات الخارجية...
    
    REM إنشاء نسخة مبسطة بدون المكتبات الخارجية
    echo [INFO] إنشاء نسخة مبسطة...
    copy EnhancedShipERP.java SimpleEnhancedERP.java > nul
    
    REM إزالة الاستيرادات الخارجية
    powershell -Command "(Get-Content SimpleEnhancedERP.java) -replace 'import com\.formdev\.flatlaf\..*', '// FlatLaf not available' | Set-Content SimpleEnhancedERP.java"
    powershell -Command "(Get-Content SimpleEnhancedERP.java) -replace 'import org\.jfree\..*', '// JFreeChart not available' | Set-Content SimpleEnhancedERP.java"
    powershell -Command "(Get-Content SimpleEnhancedERP.java) -replace 'FlatLightLaf\.setup\(\);', 'UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());' | Set-Content SimpleEnhancedERP.java"
    powershell -Command "(Get-Content SimpleEnhancedERP.java) -replace 'FlatAnimatedLafChange\..*', '// Animation not available' | Set-Content SimpleEnhancedERP.java"
    powershell -Command "(Get-Content SimpleEnhancedERP.java) -replace 'createLineChart\(\)', 'createSimplePanel(\"Line Chart\")' | Set-Content SimpleEnhancedERP.java"
    powershell -Command "(Get-Content SimpleEnhancedERP.java) -replace 'createPieChart\(\)', 'createSimplePanel(\"Pie Chart\")' | Set-Content SimpleEnhancedERP.java"
    
    REM إضافة دالة بديلة للرسوم البيانية
    echo. >> SimpleEnhancedERP.java
    echo     private JPanel createSimplePanel(String title) { >> SimpleEnhancedERP.java
    echo         JPanel panel = new JPanel(new BorderLayout()); >> SimpleEnhancedERP.java
    echo         panel.setBorder(BorderFactory.createTitledBorder(title)); >> SimpleEnhancedERP.java
    echo         JLabel label = new JLabel(title + " - قيد التطوير", SwingConstants.CENTER); >> SimpleEnhancedERP.java
    echo         label.setFont(new Font("Segoe UI", Font.BOLD, 16)); >> SimpleEnhancedERP.java
    echo         panel.add(label, BorderLayout.CENTER); >> SimpleEnhancedERP.java
    echo         return panel; >> SimpleEnhancedERP.java
    echo     } >> SimpleEnhancedERP.java
    
    REM تجميع النسخة المبسطة
    javac -encoding UTF-8 SimpleEnhancedERP.java
    if %errorlevel% neq 0 (
        echo [ERROR] فشل في تجميع التطبيق
        cd ..\..\..
        pause
        exit /b 1
    )
    
    set MAIN_CLASS=SimpleEnhancedERP
    echo [OK] تم تجميع النسخة المبسطة
) else (
    set MAIN_CLASS=EnhancedShipERP
    echo [OK] تم تجميع النسخة المحسنة
)

REM تشغيل التطبيق
echo [4/4] تشغيل التطبيق...
echo.
echo ========================================
echo    تم بدء تشغيل النظام بنجاح!
echo ========================================
echo.

echo [INFO] النظام يعمل الآن...
echo [INFO] للخروج، أغلق نافذة التطبيق أو اضغط Ctrl+C هنا

REM تشغيل التطبيق مع معاملات محسنة
java -Dfile.encoding=UTF-8 -Duser.language=ar -Duser.country=SA -Xmx1024m %MAIN_CLASS%

echo.
echo [INFO] تم إغلاق التطبيق
cd ..\..\..

pause
