import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * مستورد الأصناف من قاعدة بيانات Oracle Oracle Items Importer
 */
public class OracleItemImporter {

    private DatabaseConfig dbConfig;
    private Connection connection;

    public OracleItemImporter(DatabaseConfig dbConfig) {
        this.dbConfig = dbConfig;
    }

    /**
     * الاتصال بقاعدة البيانات
     */
    public boolean connect() {
        try {
            connection = dbConfig.createConnection();
            return connection != null && !connection.isClosed();
        } catch (SQLException e) {
            System.err.println("خطأ في الاتصال: " + e.getMessage());
            return false;
        }
    }

    /**
     * قطع الاتصال
     */
    public void disconnect() {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                System.err.println("خطأ في قطع الاتصال: " + e.getMessage());
            }
        }
    }

    /**
     * الحصول على قائمة الجداول المتاحة
     */
    public List<String> getAvailableTables() throws SQLException {
        List<String> tables = new ArrayList<>();

        String query =
                "SELECT table_name FROM user_tables WHERE table_name LIKE '%ITEM%' OR table_name LIKE '%PRODUCT%' ORDER BY table_name";

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                tables.add(rs.getString("table_name"));
            }
        }

        return tables;
    }

    /**
     * الحصول على هيكل الجدول
     */
    public List<ColumnInfo> getTableStructure(String tableName) throws SQLException {
        List<ColumnInfo> columns = new ArrayList<>();

        String query =
                "SELECT column_name, data_type, data_length, nullable FROM user_tab_columns WHERE table_name = ? ORDER BY column_id";

        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, tableName.toUpperCase());

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    ColumnInfo column = new ColumnInfo();
                    column.setName(rs.getString("column_name"));
                    column.setDataType(rs.getString("data_type"));
                    column.setLength(rs.getInt("data_length"));
                    column.setNullable("Y".equals(rs.getString("nullable")));
                    columns.add(column);
                }
            }
        }

        return columns;
    }

    /**
     * استيراد الأصناف من جدول محدد
     */
    public List<ImportedItem> importItemsFromTable(String tableName,
            Map<String, String> columnMapping, String whereClause, int maxRecords)
            throws SQLException {
        List<ImportedItem> items = new ArrayList<>();

        // بناء الاستعلام
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("SELECT ");

        // إضافة الأعمدة المطلوبة
        List<String> selectedColumns = new ArrayList<>();
        for (String sourceColumn : columnMapping.keySet()) {
            selectedColumns.add(sourceColumn);
        }
        queryBuilder.append(String.join(", ", selectedColumns));

        queryBuilder.append(" FROM ").append(tableName);

        if (whereClause != null && !whereClause.trim().isEmpty()) {
            queryBuilder.append(" WHERE ").append(whereClause);
        }

        if (maxRecords > 0) {
            queryBuilder.append(" AND ROWNUM <= ").append(maxRecords);
        }

        try (PreparedStatement stmt = connection.prepareStatement(queryBuilder.toString());
                ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                ImportedItem item = new ImportedItem();

                // تعيين القيم بناءً على التطابق
                for (Map.Entry<String, String> mapping : columnMapping.entrySet()) {
                    String sourceColumn = mapping.getKey();
                    String targetField = mapping.getValue();

                    Object value = rs.getObject(sourceColumn);
                    setItemField(item, targetField, value);
                }

                items.add(item);
            }
        }

        return items;
    }

    /**
     * تعيين قيمة حقل في الصنف المستورد
     */
    private void setItemField(ImportedItem item, String fieldName, Object value) {
        if (value == null)
            return;

        String stringValue = value.toString().trim();

        switch (fieldName.toLowerCase()) {
            case "code":
                item.setCode(stringValue);
                break;
            case "name_ar":
                item.setNameAr(stringValue);
                break;
            case "name_en":
                item.setNameEn(stringValue);
                break;
            case "description":
                item.setDescription(stringValue);
                break;
            case "category_code":
                item.setCategoryCode(stringValue);
                break;
            case "unit_code":
                item.setUnitCode(stringValue);
                break;
            case "sales_price":
                try {
                    item.setSalesPrice(Double.parseDouble(stringValue));
                } catch (NumberFormatException e) {
                    item.setSalesPrice(0.0);
                }
                break;
            case "cost_price":
                try {
                    item.setCostPrice(Double.parseDouble(stringValue));
                } catch (NumberFormatException e) {
                    item.setCostPrice(0.0);
                }
                break;
            case "current_stock":
                try {
                    item.setCurrentStock(Double.parseDouble(stringValue));
                } catch (NumberFormatException e) {
                    item.setCurrentStock(0.0);
                }
                break;
            case "min_stock_level":
                try {
                    item.setMinStockLevel(Double.parseDouble(stringValue));
                } catch (NumberFormatException e) {
                    item.setMinStockLevel(0.0);
                }
                break;
            case "is_active":
                item.setActive("1".equals(stringValue) || "Y".equalsIgnoreCase(stringValue)
                        || "true".equalsIgnoreCase(stringValue));
                break;
            case "barcode":
                item.setBarcode(stringValue);
                break;
            case "supplier":
                item.setSupplier(stringValue);
                break;
            case "manufacturer":
                item.setManufacturer(stringValue);
                break;
        }
    }

    /**
     * الحصول على عدد السجلات في الجدول
     */
    public int getRecordCount(String tableName, String whereClause) throws SQLException {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("SELECT COUNT(*) FROM ").append(tableName);

        if (whereClause != null && !whereClause.trim().isEmpty()) {
            queryBuilder.append(" WHERE ").append(whereClause);
        }

        try (PreparedStatement stmt = connection.prepareStatement(queryBuilder.toString());
                ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                return rs.getInt(1);
            }
        }

        return 0;
    }

    /**
     * معاينة البيانات من الجدول
     */
    public List<Map<String, Object>> previewTableData(String tableName, int maxRecords)
            throws SQLException {
        List<Map<String, Object>> preview = new ArrayList<>();

        String query = "SELECT * FROM " + tableName + " WHERE ROWNUM <= " + maxRecords;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                Map<String, Object> row = new LinkedHashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = rs.getObject(i);
                    row.put(columnName, value);
                }
                preview.add(row);
            }
        }

        return preview;
    }

    /**
     * فئة معلومات العمود
     */
    public static class ColumnInfo {
        private String name;
        private String dataType;
        private int length;
        private boolean nullable;

        // Getters and Setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDataType() {
            return dataType;
        }

        public void setDataType(String dataType) {
            this.dataType = dataType;
        }

        public int getLength() {
            return length;
        }

        public void setLength(int length) {
            this.length = length;
        }

        public boolean isNullable() {
            return nullable;
        }

        public void setNullable(boolean nullable) {
            this.nullable = nullable;
        }

        @Override
        public String toString() {
            return String.format("%s (%s%s)%s", name, dataType,
                    length > 0 ? "(" + length + ")" : "", nullable ? "" : " NOT NULL");
        }
    }

    /**
     * فئة الصنف المستورد
     */
    public static class ImportedItem {
        private String code;
        private String nameAr;
        private String nameEn;
        private String description;
        private String categoryCode;
        private String unitCode;
        private double salesPrice;
        private double costPrice;
        private double currentStock;
        private double minStockLevel;
        private boolean isActive;
        private String barcode;
        private String supplier;
        private String manufacturer;
        private Date importDate;

        // خريطة لحفظ جميع الحقول من قاعدة البيانات
        private Map<String, Object> fields = new HashMap<>();

        public ImportedItem() {
            this.importDate = new Date();
            this.isActive = true;
        }

        // Getters and Setters
        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getCategoryCode() {
            return categoryCode;
        }

        public void setCategoryCode(String categoryCode) {
            this.categoryCode = categoryCode;
        }

        public String getUnitCode() {
            return unitCode;
        }

        public void setUnitCode(String unitCode) {
            this.unitCode = unitCode;
        }

        public double getSalesPrice() {
            return salesPrice;
        }

        public void setSalesPrice(double salesPrice) {
            this.salesPrice = salesPrice;
        }

        public double getCostPrice() {
            return costPrice;
        }

        public void setCostPrice(double costPrice) {
            this.costPrice = costPrice;
        }

        public double getCurrentStock() {
            return currentStock;
        }

        public void setCurrentStock(double currentStock) {
            this.currentStock = currentStock;
        }

        public double getMinStockLevel() {
            return minStockLevel;
        }

        public void setMinStockLevel(double minStockLevel) {
            this.minStockLevel = minStockLevel;
        }

        public boolean isActive() {
            return isActive;
        }

        public void setActive(boolean active) {
            isActive = active;
        }

        public String getBarcode() {
            return barcode;
        }

        public void setBarcode(String barcode) {
            this.barcode = barcode;
        }

        public String getSupplier() {
            return supplier;
        }

        public void setSupplier(String supplier) {
            this.supplier = supplier;
        }

        public String getManufacturer() {
            return manufacturer;
        }

        public void setManufacturer(String manufacturer) {
            this.manufacturer = manufacturer;
        }

        public Date getImportDate() {
            return importDate;
        }

        public void setImportDate(Date importDate) {
            this.importDate = importDate;
        }

        /**
         * إضافة حقل إلى الخريطة
         */
        public void addField(String fieldName, Object value) {
            fields.put(fieldName, value);

            // تحديث الحقول الأساسية حسب اسم الحقل
            switch (fieldName) {
                case "ITM_CODE":
                    this.code = (String) value;
                    break;
                case "ITM_NAME":
                    this.nameAr = (String) value;
                    this.nameEn = (String) value; // نفس الاسم للعربي والإنجليزي
                    break;
                case "ITM_DESC":
                    this.description = (String) value;
                    break;
                case "CAT_ID":
                    this.categoryCode = value != null ? value.toString() : null;
                    break;
                case "UNIT_ID":
                    this.unitCode = value != null ? value.toString() : null;
                    break;
                case "COST_PRICE":
                    this.costPrice = value != null ? ((Number) value).doubleValue() : 0.0;
                    break;
                case "SELL_PRICE":
                    this.salesPrice = value != null ? ((Number) value).doubleValue() : 0.0;
                    break;
                case "STOCK_QTY":
                    this.currentStock = value != null ? ((Number) value).doubleValue() : 0.0;
                    break;
                case "MIN_STOCK":
                    this.minStockLevel = value != null ? ((Number) value).doubleValue() : 0.0;
                    break;
                case "IS_ACTIVE":
                    this.isActive = value != null && ((Number) value).intValue() == 1;
                    break;
            }
        }

        /**
         * الحصول على قيمة حقل
         */
        public Object getField(String fieldName) {
            return fields.get(fieldName);
        }

        /**
         * الحصول على جميع الحقول
         */
        public Map<String, Object> getAllFields() {
            return new HashMap<>(fields);
        }

        /**
         * تحويل إلى ItemData
         */
        public ItemData toItemData() {
            return new ItemData(code, nameAr, nameEn, description, categoryCode, unitCode,
                    salesPrice, costPrice, currentStock, minStockLevel, isActive, true, true, true);
        }
    }

    /**
     * استيراد الأصناف من الجداول الفعلية IAS_ITM_MST و IAS_ITM_DTL
     */
    public List<ImportedItem> importFromIASItemTables() throws SQLException {
        List<ImportedItem> items = new ArrayList<>();

        // استخدام الاستعلام الموحد
        String query = UnifiedTableStructure.createUnifiedQuery();

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                ImportedItem item = new ImportedItem();

                // استخدام أسماء الأعمدة الموحدة
                item.addField("ITM_CODE", rs.getString("itemCode"));
                item.addField("ITM_NAME", rs.getString("itemName"));
                item.addField("ITM_DESC", rs.getString("itemDesc"));
                item.addField("CAT_ID", rs.getString("categoryCode"));
                item.addField("UNIT_ID", rs.getString("unitCode"));
                item.addField("IS_ACTIVE", rs.getInt("isActive"));
                item.addField("CREATED_DATE", rs.getDate("createdDate"));
                item.addField("LAST_MODIFIED", rs.getDate("modifiedDate"));

                // الحقول المالية والتفصيلية
                item.addField("COST_PRICE", rs.getBigDecimal("costPrice"));
                item.addField("SELL_PRICE", rs.getBigDecimal("sellPrice"));
                item.addField("STOCK_QTY", rs.getBigDecimal("stockQty"));
                item.addField("MIN_STOCK", rs.getBigDecimal("minStock"));
                item.addField("MAX_STOCK", rs.getBigDecimal("maxStock"));
                item.addField("REORDER_LEVEL", rs.getBigDecimal("reorderLevel"));
                item.addField("LOCATION_CODE", rs.getString("locationCode"));
                item.addField("SUPPLIER_ID", rs.getString("supplierCode"));
                item.addField("LAST_PURCHASE_DATE", rs.getDate("lastPurchaseDate"));
                item.addField("LAST_SALE_DATE", rs.getDate("lastSaleDate"));

                items.add(item);
            }
        }

        return items;
    }

    /**
     * فحص وجود الجداول المطلوبة
     */
    public boolean checkIASTablesExist() throws SQLException {
        String query = """
                    SELECT COUNT(*) as table_count
                    FROM all_tables
                    WHERE owner = 'IAS20251' AND table_name IN ('IAS_ITM_MST', 'IAS_ITM_DTL')
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                int count = rs.getInt("table_count");
                return count == 2; // يجب أن يكون كلا الجدولين موجودين
            }
        }

        return false;
    }

    /**
     * الحصول على إحصائيات الجداول
     */
    public String getIASTablesStatistics() throws SQLException {
        StringBuilder stats = new StringBuilder();

        // إحصائيات IAS_ITM_MST
        String mstQuery =
                "SELECT COUNT(*) as total_items, COUNT(CASE WHEN INACTIVE = 0 THEN 1 END) as active_items FROM IAS20251.IAS_ITM_MST";
        try (PreparedStatement stmt = connection.prepareStatement(mstQuery);
                ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                stats.append("جدول IAS_ITM_MST:\n");
                stats.append("  إجمالي الأصناف: ").append(rs.getInt("total_items")).append("\n");
                stats.append("  الأصناف النشطة: ").append(rs.getInt("active_items")).append("\n");
            }
        }

        // إحصائيات IAS_ITM_DTL
        String dtlQuery =
                "SELECT COUNT(*) as total_details, COUNT(CASE WHEN P_SIZE > 0 THEN 1 END) as items_with_cost FROM IAS20251.IAS_ITM_DTL";
        try (PreparedStatement stmt = connection.prepareStatement(dtlQuery);
                ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                stats.append("\nجدول IAS_ITM_DTL:\n");
                stats.append("  إجمالي التفاصيل: ").append(rs.getInt("total_details")).append("\n");
                stats.append("  أصناف بأسعار: ").append(rs.getInt("items_with_cost")).append("\n");
            }
        }

        return stats.toString();
    }
}
