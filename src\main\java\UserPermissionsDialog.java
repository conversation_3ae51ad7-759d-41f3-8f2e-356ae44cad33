import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.util.*;
import java.util.List;

/**
 * نافذة إدارة صلاحيات المستخدم
 * User Permissions Management Dialog
 */
public class UserPermissionsDialog extends JDialog {
    
    private Font arabicFont;
    private String userName;
    private String userId;
    private Map<String, JCheckBox> permissionCheckBoxes;
    private List<String> availablePermissions;
    
    public UserPermissionsDialog(JDialog parent, String userName, String userId) {
        super(parent, "إدارة صلاحيات المستخدم: " + userName, true);
        
        this.userName = userName;
        this.userId = userId;
        this.arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        this.permissionCheckBoxes = new HashMap<>();
        
        initializePermissions();
        initializeComponents();
        setupLayout();
        loadUserPermissions();
        
        setSize(700, 600);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(false);
    }
    
    /**
     * تهيئة قائمة الصلاحيات المتاحة
     */
    private void initializePermissions() {
        availablePermissions = Arrays.asList(
            // صلاحيات النظام العامة
            "عرض لوحة التحكم",
            "الوصول للإعدادات العامة",
            "عرض التقارير",
            "تصدير البيانات",
            
            // صلاحيات إدارة المستخدمين
            "عرض قائمة المستخدمين",
            "إضافة مستخدم جديد",
            "تعديل بيانات المستخدمين",
            "حذف المستخدمين",
            "إدارة صلاحيات المستخدمين",
            "إعادة تعيين كلمات المرور",
            
            // صلاحيات إدارة الشحنات
            "عرض قائمة الشحنات",
            "إضافة شحنة جديدة",
            "تعديل بيانات الشحنات",
            "حذف الشحنات",
            "تتبع الشحنات",
            "تحديث حالة الشحنة",
            "طباعة بوليصة الشحن",
            
            // صلاحيات المحاسبة
            "عرض الفواتير",
            "إنشاء فاتورة جديدة",
            "تعديل الفواتير",
            "حذف الفواتير",
            "عرض التقارير المالية",
            "إدارة المدفوعات",
            "إدارة الحسابات",
            
            // صلاحيات المخزون
            "عرض المخزون",
            "إضافة عناصر للمخزون",
            "تعديل بيانات المخزون",
            "حذف عناصر المخزون",
            "جرد المخزون",
            "تقارير المخزون",
            
            // صلاحيات العملاء
            "عرض قائمة العملاء",
            "إضافة عميل جديد",
            "تعديل بيانات العملاء",
            "حذف العملاء",
            "عرض تاريخ العميل",
            
            // صلاحيات النسخ الاحتياطي
            "إنشاء نسخة احتياطية",
            "استعادة النسخ الاحتياطية",
            "إدارة النسخ الاحتياطية",
            
            // صلاحيات الإدارة المتقدمة
            "إدارة قاعدة البيانات",
            "عرض سجلات النظام",
            "إدارة الأمان",
            "تكوين النظام"
        );
    }
    
    /**
     * تهيئة المكونات
     */
    private void initializeComponents() {
        // إنشاء checkboxes للصلاحيات
        for (String permission : availablePermissions) {
            JCheckBox checkBox = new JCheckBox(permission);
            checkBox.setFont(arabicFont);
            checkBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            permissionCheckBoxes.put(permission, checkBox);
        }
    }
    
    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // الرأس
        add(createHeaderPanel(), BorderLayout.NORTH);
        
        // المحتوى الرئيسي
        add(createMainPanel(), BorderLayout.CENTER);
        
        // الأزرار
        add(createButtonPanel(), BorderLayout.SOUTH);
    }
    
    /**
     * إنشاء لوحة الرأس
     */
    private JPanel createHeaderPanel() {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(111, 66, 193));
        headerPanel.setBorder(BorderFactory.createEmptyBorder(15, 20, 15, 20));
        headerPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel titleLabel = new JLabel("إدارة صلاحيات المستخدم");
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 18));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel userLabel = new JLabel("المستخدم: " + userName + " (" + userId + ")");
        userLabel.setFont(new Font("Tahoma", Font.PLAIN, 14));
        userLabel.setForeground(new Color(236, 240, 241));
        userLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JPanel textPanel = new JPanel(new GridLayout(2, 1, 0, 5));
        textPanel.setOpaque(false);
        textPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        textPanel.add(titleLabel);
        textPanel.add(userLabel);
        
        headerPanel.add(textPanel, BorderLayout.CENTER);
        
        return headerPanel;
    }
    
    /**
     * إنشاء اللوحة الرئيسية
     */
    private JPanel createMainPanel() {
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // أزرار التحكم السريع
        JPanel quickControlPanel = createQuickControlPanel();
        mainPanel.add(quickControlPanel, BorderLayout.NORTH);
        
        // لوحة الصلاحيات
        JPanel permissionsPanel = createPermissionsPanel();
        JScrollPane scrollPane = new JScrollPane(permissionsPanel);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(BorderFactory.createTitledBorder("الصلاحيات المتاحة"));
        
        mainPanel.add(scrollPane, BorderLayout.CENTER);
        
        return mainPanel;
    }
    
    /**
     * إنشاء لوحة التحكم السريع
     */
    private JPanel createQuickControlPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 10));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        JButton selectAllBtn = createStyledButton("تحديد الكل", new Color(40, 167, 69), Color.WHITE);
        selectAllBtn.addActionListener(e -> selectAllPermissions(true));
        
        JButton deselectAllBtn = createStyledButton("إلغاء تحديد الكل", new Color(220, 53, 69), Color.WHITE);
        deselectAllBtn.addActionListener(e -> selectAllPermissions(false));
        
        JButton selectByRoleBtn = createStyledButton("تحديد حسب الدور", new Color(0, 123, 255), Color.WHITE);
        selectByRoleBtn.addActionListener(e -> selectPermissionsByRole());
        
        panel.add(selectAllBtn);
        panel.add(deselectAllBtn);
        panel.add(selectByRoleBtn);
        
        return panel;
    }
    
    /**
     * إنشاء لوحة الصلاحيات
     */
    private JPanel createPermissionsPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.anchor = GridBagConstraints.EAST;
        gbc.insets = new Insets(3, 3, 3, 3);
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        
        // تجميع الصلاحيات حسب الفئة
        Map<String, java.util.List<String>> categorizedPermissions = categorizePermissions();
        
        int row = 0;
        for (Map.Entry<String, java.util.List<String>> entry : categorizedPermissions.entrySet()) {
            // عنوان الفئة
            gbc.gridx = 0; gbc.gridy = row++; gbc.gridwidth = 1;
            JLabel categoryLabel = new JLabel(entry.getKey());
            categoryLabel.setFont(new Font("Tahoma", Font.BOLD, 13));
            categoryLabel.setForeground(new Color(52, 152, 219));
            categoryLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            panel.add(categoryLabel, gbc);
            
            // صلاحيات الفئة
            for (String permission : entry.getValue()) {
                gbc.gridx = 0; gbc.gridy = row++; gbc.gridwidth = 1;
                JCheckBox checkBox = permissionCheckBoxes.get(permission);
                checkBox.setBorder(BorderFactory.createEmptyBorder(0, 20, 0, 0)); // مسافة بادئة
                panel.add(checkBox, gbc);
            }
            
            // فاصل بين الفئات
            gbc.gridx = 0; gbc.gridy = row++; gbc.gridwidth = 1;
            panel.add(Box.createVerticalStrut(10), gbc);
        }
        
        return panel;
    }
    
    /**
     * تصنيف الصلاحيات حسب الفئة
     */
    private Map<String, java.util.List<String>> categorizePermissions() {
        Map<String, java.util.List<String>> categories = new LinkedHashMap<>();
        
        categories.put("صلاحيات النظام العامة", new ArrayList<>());
        categories.put("إدارة المستخدمين", new ArrayList<>());
        categories.put("إدارة الشحنات", new ArrayList<>());
        categories.put("المحاسبة والفواتير", new ArrayList<>());
        categories.put("إدارة المخزون", new ArrayList<>());
        categories.put("إدارة العملاء", new ArrayList<>());
        categories.put("النسخ الاحتياطية", new ArrayList<>());
        categories.put("الإدارة المتقدمة", new ArrayList<>());
        
        for (String permission : availablePermissions) {
            if (permission.contains("لوحة التحكم") || permission.contains("الإعدادات") || 
                permission.contains("التقارير") || permission.contains("تصدير")) {
                categories.get("صلاحيات النظام العامة").add(permission);
            } else if (permission.contains("المستخدمين") || permission.contains("كلمات المرور")) {
                categories.get("إدارة المستخدمين").add(permission);
            } else if (permission.contains("الشحنات") || permission.contains("الشحنة") || permission.contains("بوليصة")) {
                categories.get("إدارة الشحنات").add(permission);
            } else if (permission.contains("الفواتير") || permission.contains("المالية") || 
                      permission.contains("المدفوعات") || permission.contains("الحسابات")) {
                categories.get("المحاسبة والفواتير").add(permission);
            } else if (permission.contains("المخزون") || permission.contains("جرد")) {
                categories.get("إدارة المخزون").add(permission);
            } else if (permission.contains("العملاء") || permission.contains("العميل")) {
                categories.get("إدارة العملاء").add(permission);
            } else if (permission.contains("احتياطية") || permission.contains("استعادة")) {
                categories.get("النسخ الاحتياطية").add(permission);
            } else {
                categories.get("الإدارة المتقدمة").add(permission);
            }
        }

        return categories;
    }

    /**
     * إنشاء لوحة الأزرار
     */
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 15));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(new Color(248, 249, 250));
        panel.setBorder(BorderFactory.createMatteBorder(1, 0, 0, 0, new Color(222, 226, 230)));

        JButton saveButton = createStyledButton("حفظ الصلاحيات", new Color(40, 167, 69), Color.WHITE);
        saveButton.addActionListener(e -> savePermissions());

        JButton cancelButton = createStyledButton("إلغاء", new Color(108, 117, 125), Color.WHITE);
        cancelButton.addActionListener(e -> dispose());

        JButton previewButton = createStyledButton("معاينة الصلاحيات", new Color(23, 162, 184), Color.WHITE);
        previewButton.addActionListener(e -> previewPermissions());

        panel.add(saveButton);
        panel.add(cancelButton);
        panel.add(previewButton);

        return panel;
    }

    /**
     * إنشاء زر مُنسق
     */
    private JButton createStyledButton(String text, Color bgColor, Color fgColor) {
        JButton button = new JButton(text);
        button.setFont(arabicFont);
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        button.setBackground(bgColor);
        button.setForeground(fgColor);
        button.setPreferredSize(new Dimension(140, 35));
        button.setFocusPainted(false);
        button.setBorderPainted(false);

        return button;
    }

    /**
     * تحميل صلاحيات المستخدم الحالية
     */
    private void loadUserPermissions() {
        // في التطبيق الحقيقي، سيتم تحميل الصلاحيات من قاعدة البيانات
        // هنا سنضع بعض الصلاحيات الافتراضية للتجربة

        java.util.List<String> userPermissions = Arrays.asList(
            "عرض لوحة التحكم",
            "عرض قائمة الشحنات",
            "إضافة شحنة جديدة",
            "تتبع الشحنات",
            "عرض قائمة العملاء",
            "إضافة عميل جديد"
        );

        // تحديد الصلاحيات المحملة
        for (String permission : userPermissions) {
            JCheckBox checkBox = permissionCheckBoxes.get(permission);
            if (checkBox != null) {
                checkBox.setSelected(true);
            }
        }
    }

    /**
     * تحديد/إلغاء تحديد جميع الصلاحيات
     */
    private void selectAllPermissions(boolean select) {
        for (JCheckBox checkBox : permissionCheckBoxes.values()) {
            checkBox.setSelected(select);
        }
    }

    /**
     * تحديد الصلاحيات حسب الدور
     */
    private void selectPermissionsByRole() {
        String[] roles = {
            "مدير النظام", "محاسب", "مشرف الشحن", "موظف إدخال بيانات",
            "مدير المبيعات", "موظف خدمة العملاء"
        };

        String selectedRole = (String) JOptionPane.showInputDialog(
            this,
            "اختر الدور لتحديد الصلاحيات المناسبة:",
            "تحديد حسب الدور",
            JOptionPane.QUESTION_MESSAGE,
            null,
            roles,
            roles[0]
        );

        if (selectedRole != null) {
            // مسح التحديد الحالي
            selectAllPermissions(false);

            // تحديد الصلاحيات حسب الدور
            java.util.List<String> rolePermissions = getPermissionsForRole(selectedRole);
            for (String permission : rolePermissions) {
                JCheckBox checkBox = permissionCheckBoxes.get(permission);
                if (checkBox != null) {
                    checkBox.setSelected(true);
                }
            }
        }
    }

    /**
     * الحصول على الصلاحيات المناسبة للدور
     */
    private java.util.List<String> getPermissionsForRole(String role) {
        java.util.List<String> permissions = new ArrayList<>();

        // صلاحيات أساسية لجميع الأدوار
        permissions.add("عرض لوحة التحكم");

        switch (role) {
            case "مدير النظام":
                // جميع الصلاحيات
                permissions.addAll(availablePermissions);
                break;

            case "محاسب":
                permissions.addAll(Arrays.asList(
                    "عرض الفواتير", "إنشاء فاتورة جديدة", "تعديل الفواتير",
                    "عرض التقارير المالية", "إدارة المدفوعات", "إدارة الحسابات",
                    "عرض قائمة العملاء", "تعديل بيانات العملاء", "عرض تاريخ العميل"
                ));
                break;

            case "مشرف الشحن":
                permissions.addAll(Arrays.asList(
                    "عرض قائمة الشحنات", "إضافة شحنة جديدة", "تعديل بيانات الشحنات",
                    "تتبع الشحنات", "تحديث حالة الشحنة", "طباعة بوليصة الشحن",
                    "عرض قائمة العملاء", "إضافة عميل جديد", "تعديل بيانات العملاء"
                ));
                break;

            case "موظف إدخال بيانات":
                permissions.addAll(Arrays.asList(
                    "عرض قائمة الشحنات", "إضافة شحنة جديدة",
                    "عرض قائمة العملاء", "إضافة عميل جديد"
                ));
                break;

            case "مدير المبيعات":
                permissions.addAll(Arrays.asList(
                    "عرض قائمة الشحنات", "إضافة شحنة جديدة", "تتبع الشحنات",
                    "عرض قائمة العملاء", "إضافة عميل جديد", "تعديل بيانات العملاء",
                    "عرض تاريخ العميل", "عرض التقارير"
                ));
                break;

            case "موظف خدمة العملاء":
                permissions.addAll(Arrays.asList(
                    "عرض قائمة الشحنات", "تتبع الشحنات",
                    "عرض قائمة العملاء", "تعديل بيانات العملاء", "عرض تاريخ العميل"
                ));
                break;
        }

        return permissions;
    }

    /**
     * حفظ الصلاحيات
     */
    private void savePermissions() {
        java.util.List<String> selectedPermissions = new ArrayList<>();

        for (Map.Entry<String, JCheckBox> entry : permissionCheckBoxes.entrySet()) {
            if (entry.getValue().isSelected()) {
                selectedPermissions.add(entry.getKey());
            }
        }

        // في التطبيق الحقيقي، سيتم حفظ الصلاحيات في قاعدة البيانات
        System.out.println("حفظ صلاحيات المستخدم " + userId + ":");
        for (String permission : selectedPermissions) {
            System.out.println("- " + permission);
        }

        JOptionPane.showMessageDialog(this,
            "تم حفظ صلاحيات المستخدم بنجاح!\nعدد الصلاحيات المحددة: " + selectedPermissions.size(),
            "حفظ ناجح",
            JOptionPane.INFORMATION_MESSAGE);

        dispose();
    }

    /**
     * معاينة الصلاحيات المحددة
     */
    private void previewPermissions() {
        java.util.List<String> selectedPermissions = new ArrayList<>();

        for (Map.Entry<String, JCheckBox> entry : permissionCheckBoxes.entrySet()) {
            if (entry.getValue().isSelected()) {
                selectedPermissions.add(entry.getKey());
            }
        }

        if (selectedPermissions.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "لم يتم تحديد أي صلاحيات",
                "تنبيه",
                JOptionPane.WARNING_MESSAGE);
            return;
        }

        StringBuilder preview = new StringBuilder();
        preview.append("الصلاحيات المحددة للمستخدم: ").append(userName).append("\n\n");

        Map<String, java.util.List<String>> categorizedSelected = new LinkedHashMap<>();
        Map<String, java.util.List<String>> allCategories = categorizePermissions();

        for (Map.Entry<String, java.util.List<String>> entry : allCategories.entrySet()) {
            java.util.List<String> categoryPermissions = new ArrayList<>();
            for (String permission : entry.getValue()) {
                if (selectedPermissions.contains(permission)) {
                    categoryPermissions.add(permission);
                }
            }
            if (!categoryPermissions.isEmpty()) {
                categorizedSelected.put(entry.getKey(), categoryPermissions);
            }
        }

        for (Map.Entry<String, java.util.List<String>> entry : categorizedSelected.entrySet()) {
            preview.append("▶ ").append(entry.getKey()).append(":\n");
            for (String permission : entry.getValue()) {
                preview.append("  • ").append(permission).append("\n");
            }
            preview.append("\n");
        }

        JTextArea textArea = new JTextArea(preview.toString());
        textArea.setFont(arabicFont);
        textArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        textArea.setEditable(false);
        textArea.setRows(20);
        textArea.setColumns(50);

        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JOptionPane.showMessageDialog(this,
            scrollPane,
            "معاينة الصلاحيات المحددة",
            JOptionPane.INFORMATION_MESSAGE);
    }
}
