import java.sql.*;
import javax.swing.*;
import java.awt.*;

/**
 * الحل الصادق - بدون كذب أو خداع
 * يوضح ما يعمل فعلاً وما لا يعمل
 */
public class HonestSolution extends JFrame {
    
    private Connection shipErpConnection;
    private Connection ias20251Connection;
    private JTextArea logArea;
    private JTextField groupCodeField, groupNameField;
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new HonestSolution().setVisible(true);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, "خطأ في بدء التطبيق: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    public HonestSolution() throws Exception {
        initializeConnections();
        createRealPackage();
        initializeGUI();
    }
    
    /**
     * إنشاء اتصالات حقيقية
     */
    private void initializeConnections() throws Exception {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        
        // اتصال SHIP_ERP
        try {
            shipErpConnection = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            log("✅ تم الاتصال بـ SHIP_ERP");
        } catch (SQLException e) {
            log("❌ فشل الاتصال بـ SHIP_ERP: " + e.getMessage());
            throw e;
        }
        
        // اتصال IAS20251
        try {
            ias20251Connection = DriverManager.getConnection(
                "*************************************", 
                "ias20251", 
                "ys123"
            );
            log("✅ تم الاتصال بـ IAS20251");
            
            // فحص الجداول الموجودة فعلاً
            checkRealTables();
            
        } catch (SQLException e) {
            log("❌ فشل الاتصال بـ IAS20251: " + e.getMessage());
            log("⚠️ سيتم العمل بدون استيراد من IAS20251");
            ias20251Connection = null;
        }
    }
    
    /**
     * فحص الجداول الموجودة فعلاً في IAS20251
     */
    private void checkRealTables() throws SQLException {
        log("🔍 فحص الجداول الموجودة في IAS20251...");
        
        DatabaseMetaData metaData = ias20251Connection.getMetaData();
        ResultSet tables = metaData.getTables(null, "IAS20251", "%", new String[]{"TABLE"});
        
        boolean foundGroupTables = false;
        while (tables.next()) {
            String tableName = tables.getString("TABLE_NAME");
            log("📋 جدول موجود: " + tableName);
            
            if (tableName.contains("GROUP") || tableName.contains("GRP")) {
                foundGroupTables = true;
                
                // فحص بنية الجدول
                ResultSet columns = metaData.getColumns(null, "IAS20251", tableName, null);
                log("   📋 أعمدة الجدول " + tableName + ":");
                while (columns.next()) {
                    String columnName = columns.getString("COLUMN_NAME");
                    String dataType = columns.getString("TYPE_NAME");
                    log("      - " + columnName + " (" + dataType + ")");
                }
                columns.close();
            }
        }
        tables.close();
        
        if (!foundGroupTables) {
            log("⚠️ لم يتم العثور على جداول المجموعات في IAS20251");
        }
    }
    
    /**
     * إنشاء Package حقيقي يعمل فعلاً
     */
    private void createRealPackage() throws SQLException {
        log("📦 إنشاء Package حقيقي...");
        
        Statement stmt = shipErpConnection.createStatement();
        
        // حذف Package القديم
        try {
            stmt.execute("DROP PACKAGE ERP_ITEM_GROUPS");
            log("🗑️ تم حذف Package القديم");
        } catch (SQLException e) {
            log("ℹ️ Package غير موجود مسبقاً");
        }
        
        // Package Specification
        String packageSpec = """
            CREATE OR REPLACE PACKAGE ERP_ITEM_GROUPS AS
                FUNCTION get_groups_count RETURN NUMBER;
                FUNCTION get_real_table_info RETURN VARCHAR2;
                FUNCTION add_main_group(p_g_code VARCHAR2, p_g_a_name VARCHAR2, p_g_e_name VARCHAR2) RETURN VARCHAR2;
                FUNCTION import_real_data RETURN VARCHAR2;
                PROCEDURE log_operation(p_operation VARCHAR2, p_message VARCHAR2);
            END ERP_ITEM_GROUPS;
        """;
        
        stmt.execute(packageSpec);
        log("✅ تم إنشاء Package Specification");
        
        // Package Body
        String packageBody = """
            CREATE OR REPLACE PACKAGE BODY ERP_ITEM_GROUPS AS
                
                FUNCTION get_groups_count RETURN NUMBER IS
                    l_count NUMBER;
                BEGIN
                    SELECT COUNT(*) INTO l_count FROM ERP_GROUP_DETAILS;
                    RETURN l_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        RETURN -1;
                END get_groups_count;
                
                FUNCTION get_real_table_info RETURN VARCHAR2 IS
                    l_info VARCHAR2(4000);
                    l_count NUMBER;
                BEGIN
                    SELECT COUNT(*) INTO l_count FROM ERP_GROUP_DETAILS;
                    l_info := 'ERP_GROUP_DETAILS: ' || l_count || ' سجل';
                    
                    BEGIN
                        SELECT COUNT(*) INTO l_count FROM ERP_MAINSUB_GRP_DTL;
                        l_info := l_info || ', ERP_MAINSUB_GRP_DTL: ' || l_count || ' سجل';
                    EXCEPTION
                        WHEN OTHERS THEN
                            l_info := l_info || ', ERP_MAINSUB_GRP_DTL: غير موجود';
                    END;
                    
                    RETURN l_info;
                END get_real_table_info;
                
                FUNCTION add_main_group(p_g_code VARCHAR2, p_g_a_name VARCHAR2, p_g_e_name VARCHAR2) RETURN VARCHAR2 IS
                    l_count NUMBER;
                BEGIN
                    SELECT COUNT(*) INTO l_count FROM ERP_GROUP_DETAILS WHERE G_CODE = p_g_code;
                    IF l_count > 0 THEN
                        RETURN 'ERROR: كود المجموعة موجود مسبقاً';
                    END IF;
                    
                    INSERT INTO ERP_GROUP_DETAILS (G_CODE, G_A_NAME, G_E_NAME)
                    VALUES (p_g_code, p_g_a_name, p_g_e_name);
                    
                    log_operation('INSERT', 'تم إضافة المجموعة: ' || p_g_code);
                    COMMIT;
                    RETURN 'SUCCESS: تم إضافة المجموعة بنجاح';
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RETURN 'ERROR: ' || SQLERRM;
                END add_main_group;
                
                FUNCTION import_real_data RETURN VARCHAR2 IS
                BEGIN
                    -- هذه الوظيفة تحتاج إلى معرفة الجداول الحقيقية في IAS20251
                    -- حالياً ترجع رسالة صادقة
                    RETURN 'INFO: الاستيراد يحتاج إلى تحديد الجداول الحقيقية في IAS20251';
                END import_real_data;
                
                PROCEDURE log_operation(p_operation VARCHAR2, p_message VARCHAR2) IS
                    PRAGMA AUTONOMOUS_TRANSACTION;
                BEGIN
                    INSERT INTO ERP_OPERATION_LOG (
                        log_id, operation_type, table_name, status,
                        message, records_count, operation_date, username
                    ) VALUES (
                        ERP_LOG_SEQ.NEXTVAL, p_operation, 'ERP_ITEM_GROUPS', 'SUCCESS',
                        p_message, 0, SYSDATE, USER
                    );
                    COMMIT;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        NULL;
                END log_operation;
                
            END ERP_ITEM_GROUPS;
        """;
        
        stmt.execute(packageBody);
        log("✅ تم إنشاء Package Body");
        
        // اختبار Package
        try (CallableStatement cs = shipErpConnection.prepareCall("{ ? = call ERP_ITEM_GROUPS.get_groups_count }")) {
            cs.registerOutParameter(1, Types.NUMERIC);
            cs.execute();
            int count = cs.getInt(1);
            log("📊 عدد المجموعات الحالي: " + count);
            log("✅ Package يعمل فعلاً!");
        }
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    private void initializeGUI() {
        setTitle("نظام إدارة مجموعات الأصناف - الحل الصادق");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(800, 600);
        setLocationRelativeTo(null);
        
        setLayout(new BorderLayout());
        
        // لوحة الإدخال
        JPanel inputPanel = new JPanel(new GridBagLayout());
        inputPanel.setBorder(BorderFactory.createTitledBorder("إدارة المجموعات"));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // حقول الإدخال
        gbc.gridx = 0; gbc.gridy = 0;
        inputPanel.add(new JLabel("كود المجموعة:"), gbc);
        gbc.gridx = 1;
        groupCodeField = new JTextField(15);
        inputPanel.add(groupCodeField, gbc);
        
        gbc.gridx = 0; gbc.gridy = 1;
        inputPanel.add(new JLabel("اسم المجموعة:"), gbc);
        gbc.gridx = 1;
        groupNameField = new JTextField(15);
        inputPanel.add(groupNameField, gbc);
        
        // الأزرار
        JPanel buttonPanel = new JPanel(new FlowLayout());
        
        JButton addButton = new JButton("إضافة مجموعة");
        addButton.addActionListener(e -> addGroup());
        buttonPanel.add(addButton);
        
        JButton countButton = new JButton("عدد المجموعات");
        countButton.addActionListener(e -> getGroupsCount());
        buttonPanel.add(countButton);
        
        JButton infoButton = new JButton("معلومات الجداول");
        infoButton.addActionListener(e -> getTableInfo());
        buttonPanel.add(infoButton);
        
        JButton realImportButton = new JButton("استيراد حقيقي");
        realImportButton.addActionListener(e -> realImport());
        buttonPanel.add(realImportButton);
        
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 2;
        inputPanel.add(buttonPanel, gbc);
        
        add(inputPanel, BorderLayout.NORTH);
        
        // منطقة السجل
        logArea = new JTextArea(20, 60);
        logArea.setEditable(false);
        logArea.setFont(new Font("Arial", Font.PLAIN, 12));
        JScrollPane scrollPane = new JScrollPane(logArea);
        scrollPane.setBorder(BorderFactory.createTitledBorder("سجل العمليات الحقيقي"));
        add(scrollPane, BorderLayout.CENTER);
        
        log("🎉 تم تهيئة النظام الصادق!");
        log("⚠️ هذا النظام يوضح ما يعمل فعلاً وما لا يعمل");
    }
    
    /**
     * إضافة مجموعة (يعمل فعلاً)
     */
    private void addGroup() {
        try {
            String code = groupCodeField.getText().trim();
            String name = groupNameField.getText().trim();
            
            if (code.isEmpty() || name.isEmpty()) {
                JOptionPane.showMessageDialog(this, "يرجى إدخال كود واسم المجموعة");
                return;
            }
            
            CallableStatement cs = shipErpConnection.prepareCall("{ ? = call ERP_ITEM_GROUPS.add_main_group(?, ?, ?) }");
            cs.registerOutParameter(1, Types.VARCHAR);
            cs.setString(2, code);
            cs.setString(3, name);
            cs.setString(4, name + " (English)");
            cs.execute();
            
            String result = cs.getString(1);
            log("📋 " + result);
            
            if (result.startsWith("SUCCESS")) {
                groupCodeField.setText("");
                groupNameField.setText("");
                JOptionPane.showMessageDialog(this, "تم إضافة المجموعة بنجاح");
            } else {
                JOptionPane.showMessageDialog(this, result, "خطأ", JOptionPane.ERROR_MESSAGE);
            }
            
        } catch (Exception e) {
            log("❌ خطأ في إضافة المجموعة: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * الحصول على عدد المجموعات (يعمل فعلاً)
     */
    private void getGroupsCount() {
        try {
            CallableStatement cs = shipErpConnection.prepareCall("{ ? = call ERP_ITEM_GROUPS.get_groups_count }");
            cs.registerOutParameter(1, Types.NUMERIC);
            cs.execute();
            
            int count = cs.getInt(1);
            log("📊 عدد المجموعات الحالي: " + count);
            JOptionPane.showMessageDialog(this, "عدد المجموعات: " + count);
            
        } catch (Exception e) {
            log("❌ خطأ في الحصول على عدد المجموعات: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * الحصول على معلومات الجداول الحقيقية
     */
    private void getTableInfo() {
        try {
            CallableStatement cs = shipErpConnection.prepareCall("{ ? = call ERP_ITEM_GROUPS.get_real_table_info }");
            cs.registerOutParameter(1, Types.VARCHAR);
            cs.execute();
            
            String info = cs.getString(1);
            log("📋 معلومات الجداول: " + info);
            JOptionPane.showMessageDialog(this, info);
            
        } catch (Exception e) {
            log("❌ خطأ في الحصول على معلومات الجداول: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * استيراد حقيقي (يوضح الحقيقة)
     */
    private void realImport() {
        if (ias20251Connection == null) {
            log("❌ لا يوجد اتصال بـ IAS20251");
            JOptionPane.showMessageDialog(this, "لا يوجد اتصال بـ IAS20251", "خطأ", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        try {
            log("🔄 محاولة الاستيراد الحقيقي...");
            
            // هنا نحتاج لمعرفة أسماء الجداول الحقيقية
            CallableStatement cs = shipErpConnection.prepareCall("{ ? = call ERP_ITEM_GROUPS.import_real_data }");
            cs.registerOutParameter(1, Types.VARCHAR);
            cs.execute();
            
            String result = cs.getString(1);
            log("📋 " + result);
            JOptionPane.showMessageDialog(this, result);
            
        } catch (Exception e) {
            log("❌ خطأ في الاستيراد: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * تسجيل رسالة في السجل
     */
    private void log(String message) {
        SwingUtilities.invokeLater(() -> {
            logArea.append(java.time.LocalTime.now() + " - " + message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
        System.out.println(message);
    }
}
