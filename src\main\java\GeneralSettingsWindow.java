import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.GridLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JComponent;
import javax.swing.JDialog;
import javax.swing.JFileChooser;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPasswordField;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JSpinner;
import javax.swing.JTabbedPane;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.SpinnerNumberModel;
import javax.swing.SwingConstants;
import javax.swing.SwingUtilities;
import javax.swing.SwingWorker;
import javax.swing.UIManager;

/**
 * نظام الإعدادات العامة المتقدم Advanced General Settings System
 */
public class GeneralSettingsWindow extends JDialog {

    // بيانات الشركة
    private JTextField companyNameField;
    private JTextArea companyAddressArea;
    private JTextField companyPhoneField;
    private JTextField companyEmailField;
    private JTextField companyWebsiteField;
    private JTextField taxNumberField;
    private JTextField commercialRegisterField;
    private JTextField companyLogoPathField;
    private JComboBox<String> companyTypeCombo;
    private JComboBox<String> businessSectorCombo;

    // إعدادات النظام
    private JComboBox<String> defaultCurrencyCombo;
    private JSpinner decimalPlacesSpinner;
    private JComboBox<String> dateFormatCombo;
    private JComboBox<String> timeFormatCombo;
    private JComboBox<String> languageCombo;
    private JComboBox<String> themeCombo;
    private JCheckBox enableNotificationsCheck;
    private JCheckBox enableSoundsCheck;
    private JCheckBox enableAnimationsCheck;

    // إعدادات الأمان
    private JSpinner sessionTimeoutSpinner;
    private JCheckBox enableTwoFactorCheck;
    private JCheckBox enablePasswordExpiryCheck;
    private JSpinner passwordExpiryDaysSpinner;
    private JSpinner maxLoginAttemptsSpinner;
    private JCheckBox enableAuditLogCheck;
    private JCheckBox enableEncryptionCheck;

    // إعدادات النسخ الاحتياطي
    private JCheckBox backupEnabledCheck;
    private JSpinner backupIntervalSpinner;
    private JTextField backupDirectoryField;
    private JComboBox<String> backupTypeCombo;
    private JSpinner backupRetentionSpinner;
    private JCheckBox enableCloudBackupCheck;
    private JTextField cloudBackupPathField;

    // إعدادات البريد الإلكتروني
    private JTextField smtpServerField;
    private JSpinner smtpPortSpinner;
    private JTextField smtpUsernameField;
    private JPasswordField smtpPasswordField;
    private JCheckBox enableSSLCheck;
    private JTextField fromEmailField;
    private JTextField fromNameField;

    // إعدادات قاعدة البيانات
    private JTextField dbHostField;
    private JSpinner dbPortSpinner;
    private JTextField dbNameField;
    private JTextField dbUsernameField;
    private JPasswordField dbPasswordField;
    private JComboBox<String> dbTypeCombo;
    private JSpinner connectionPoolSizeSpinner;
    private JSpinner connectionTimeoutSpinner;

    // إعدادات التقارير
    private JTextField reportTemplatePathField;
    private JComboBox<String> defaultReportFormatCombo;
    private JCheckBox enableReportCacheCheck;
    private JSpinner reportCacheTimeSpinner;
    private JTextField reportOutputPathField;

    // إعدادات الشبكة
    private JTextField proxyHostField;
    private JSpinner proxyPortSpinner;
    private JTextField proxyUsernameField;
    private JPasswordField proxyPasswordField;
    private JCheckBox enableProxyCheck;
    private JSpinner networkTimeoutSpinner;

    private Font arabicFont;
    private JTabbedPane tabbedPane;
    private JFrame parentFrame;

    public GeneralSettingsWindow(JFrame parent) {
        super(parent,
                "\u0646\u0638\u0627\u0645 \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0639\u0627\u0645\u0629 \u0627\u0644\u0645\u062A\u0642\u062F\u0645",
                true); // نظام الإعدادات العامة المتقدم

        this.parentFrame = parent;
        arabicFont = new Font("Tahoma", Font.PLAIN, 12);

        initializeAllComponents();
        setupAdvancedLayout();
        loadAllSettings();
        setupEventHandlers();

        setSize(1200, 800);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(true);
        setMinimumSize(new Dimension(1000, 700));
    }

    /**
     * تهيئة جميع المكونات المتقدمة
     */
    private void initializeAllComponents() {
        initializeCompanyComponents();
        initializeSystemComponents();
        initializeSecurityComponents();
        initializeBackupComponents();
        initializeEmailComponents();
        initializeDatabaseComponents();
        initializeReportComponents();
        initializeNetworkComponents();
    }

    /**
     * تهيئة مكونات بيانات الشركة
     */
    private void initializeCompanyComponents() {
        companyNameField = createTextField(25);
        companyAddressArea = createTextArea(4, 25);
        companyPhoneField = createTextField(20);
        companyEmailField = createTextField(25);
        companyWebsiteField = createTextField(25);
        taxNumberField = createTextField(20);
        commercialRegisterField = createTextField(20);
        companyLogoPathField = createTextField(30);

        companyTypeCombo = new JComboBox<>(
                new String[] {"\u0634\u0631\u0643\u0629 \u0645\u0633\u0627\u0647\u0645\u0629", // شركة
                                                                                               // مساهمة
                        "\u0634\u0631\u0643\u0629 \u0630\u0627\u062A \u0645\u0633\u0624\u0648\u0644\u064A\u0629 \u0645\u062D\u062F\u0648\u062F\u0629", // شركة
                                                                                                                                                       // ذات
                                                                                                                                                       // مسؤولية
                                                                                                                                                       // محدودة
                        "\u0645\u0624\u0633\u0633\u0629 \u0641\u0631\u062F\u064A\u0629", // مؤسسة
                                                                                         // فردية
                        "\u0634\u0631\u0643\u0629 \u062A\u0636\u0627\u0645\u0646", // شركة تضامن
                        "\u0634\u0631\u0643\u0629 \u062A\u0648\u0635\u064A\u0629" // شركة توصية
                });
        setupComboBox(companyTypeCombo);

        businessSectorCombo = new JComboBox<>(new String[] {
                "\u0627\u0644\u0634\u062D\u0646 \u0648\u0627\u0644\u0644\u0648\u062C\u0633\u062A\u064A\u0627\u062A", // الشحن
                                                                                                                     // واللوجستيات
                "\u0627\u0644\u062A\u062C\u0627\u0631\u0629 \u0648\u0627\u0644\u0627\u0633\u062A\u064A\u0631\u0627\u062F", // التجارة
                                                                                                                           // والاستيراد
                "\u0627\u0644\u062A\u0635\u0646\u064A\u0639", // التصنيع
                "\u0627\u0644\u062E\u062F\u0645\u0627\u062A", // الخدمات
                "\u0627\u0644\u062A\u0642\u0646\u064A\u0629", // التقنية
                "\u0623\u062E\u0631\u0649" // أخرى
        });
        setupComboBox(businessSectorCombo);
    }

    /**
     * تهيئة مكونات إعدادات النظام
     */
    private void initializeSystemComponents() {
        defaultCurrencyCombo = new JComboBox<>(new String[] {
                "SAR - \u0631\u064A\u0627\u0644 \u0633\u0639\u0648\u062F\u064A",
                "USD - \u062F\u0648\u0644\u0627\u0631 \u0623\u0645\u0631\u064A\u0643\u064A",
                "EUR - \u064A\u0648\u0631\u0648",
                "GBP - \u062C\u0646\u064A\u0647 \u0625\u0633\u062A\u0631\u0644\u064A\u0646\u064A",
                "AED - \u062F\u0631\u0647\u0645 \u0625\u0645\u0627\u0631\u0627\u062A\u064A"});
        setupComboBox(defaultCurrencyCombo);

        decimalPlacesSpinner = new JSpinner(new SpinnerNumberModel(2, 0, 6, 1));
        setupSpinner(decimalPlacesSpinner);

        dateFormatCombo = new JComboBox<>(
                new String[] {"dd/MM/yyyy", "MM/dd/yyyy", "yyyy-MM-dd", "dd-MM-yyyy"});
        setupComboBox(dateFormatCombo);

        timeFormatCombo =
                new JComboBox<>(new String[] {"HH:mm:ss", "hh:mm:ss a", "HH:mm", "hh:mm a"});
        setupComboBox(timeFormatCombo);

        languageCombo = new JComboBox<>(new String[] {"\u0627\u0644\u0639\u0631\u0628\u064A\u0629", // العربية
                "English", "\u0641\u0631\u0646\u0633\u064A", // فرنسي
                "\u0623\u0644\u0645\u0627\u0646\u064A" // ألماني
        });
        setupComboBox(languageCombo);

        // إضافة مستمع لتغيير اللغة (بدون رسائل مزعجة)
        languageCombo.addActionListener(e -> {
            // تسجيل تغيير اللغة فقط بدون رسائل
            String selectedLanguage = (String) languageCombo.getSelectedItem();
            System.out.println("تم اختيار اللغة: " + selectedLanguage);
        });

        themeCombo = new JComboBox<>(new String[] {"\u0641\u0627\u062A\u062D", // فاتح
                "\u0645\u0638\u0644\u0645", // مظلم
                "\u062A\u0644\u0642\u0627\u0626\u064A", // تلقائي
                "\u0645\u062E\u0635\u0635" // مخصص
        });
        setupComboBox(themeCombo);

        // إضافة مستمع لتغيير المظهر فوراً (بدون رسائل)
        themeCombo.addActionListener(e -> applyThemeChangeSilently());

        enableNotificationsCheck = createCheckBox(
                "\u062A\u0641\u0639\u064A\u0644 \u0627\u0644\u0625\u0634\u0639\u0627\u0631\u0627\u062A",
                true); // تفعيل الإشعارات
        enableSoundsCheck = createCheckBox(
                "\u062A\u0641\u0639\u064A\u0644 \u0627\u0644\u0623\u0635\u0648\u0627\u062A", true); // تفعيل
                                                                                                    // الأصوات
        enableAnimationsCheck = createCheckBox(
                "\u062A\u0641\u0639\u064A\u0644 \u0627\u0644\u0631\u0633\u0648\u0645 \u0627\u0644\u0645\u062A\u062D\u0631\u0643\u0629",
                true); // تفعيل الرسوم المتحركة
    }

    /**
     * تهيئة مكونات الأمان
     */
    private void initializeSecurityComponents() {
        sessionTimeoutSpinner = new JSpinner(new SpinnerNumberModel(30, 5, 480, 5));
        setupSpinner(sessionTimeoutSpinner);

        enableTwoFactorCheck = createCheckBox(
                "\u062A\u0641\u0639\u064A\u0644 \u0627\u0644\u0645\u0635\u0627\u062F\u0642\u0629 \u0627\u0644\u062B\u0646\u0627\u0626\u064A\u0629",
                false); // تفعيل المصادقة الثنائية
        enablePasswordExpiryCheck = createCheckBox(
                "\u062A\u0641\u0639\u064A\u0644 \u0627\u0646\u062A\u0647\u0627\u0621 \u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631",
                true); // تفعيل انتهاء كلمة المرور

        passwordExpiryDaysSpinner = new JSpinner(new SpinnerNumberModel(90, 30, 365, 10));
        setupSpinner(passwordExpiryDaysSpinner);

        maxLoginAttemptsSpinner = new JSpinner(new SpinnerNumberModel(3, 1, 10, 1));
        setupSpinner(maxLoginAttemptsSpinner);

        enableAuditLogCheck = createCheckBox(
                "\u062A\u0641\u0639\u064A\u0644 \u0633\u062C\u0644 \u0627\u0644\u0645\u0631\u0627\u062C\u0639\u0629",
                true); // تفعيل سجل المراجعة
        enableEncryptionCheck = createCheckBox(
                "\u062A\u0641\u0639\u064A\u0644 \u0627\u0644\u062A\u0634\u0641\u064A\u0631", true); // تفعيل
                                                                                                    // التشفير
    }

    /**
     * تهيئة مكونات النسخ الاحتياطي
     */
    private void initializeBackupComponents() {
        backupEnabledCheck = createCheckBox(
                "\u062A\u0641\u0639\u064A\u0644 \u0627\u0644\u0646\u0633\u062E \u0627\u0644\u0627\u062D\u062A\u064A\u0627\u0637\u064A \u0627\u0644\u062A\u0644\u0642\u0627\u0626\u064A",
                true); // تفعيل النسخ الاحتياطي التلقائي

        backupIntervalSpinner = new JSpinner(new SpinnerNumberModel(24, 1, 168, 1));
        setupSpinner(backupIntervalSpinner);

        backupDirectoryField = createTextField(30);
        backupDirectoryField.setText("backup");

        backupTypeCombo =
                new JComboBox<>(new String[] {"\u0646\u0633\u062E \u0643\u0627\u0645\u0644", // نسخ
                                                                                             // كامل
                        "\u0646\u0633\u062E \u062A\u0632\u0627\u064A\u062F\u064A", // نسخ تزايدي
                        "\u0646\u0633\u062E \u062A\u0641\u0627\u0636\u0644\u064A" // نسخ تفاضلي
                });
        setupComboBox(backupTypeCombo);

        backupRetentionSpinner = new JSpinner(new SpinnerNumberModel(30, 7, 365, 7));
        setupSpinner(backupRetentionSpinner);

        enableCloudBackupCheck = createCheckBox(
                "\u062A\u0641\u0639\u064A\u0644 \u0627\u0644\u0646\u0633\u062E \u0627\u0644\u0633\u062D\u0627\u0628\u064A",
                false); // تفعيل النسخ السحابي
        cloudBackupPathField = createTextField(30);
    }

    /**
     * تهيئة مكونات البريد الإلكتروني
     */
    private void initializeEmailComponents() {
        smtpServerField = createTextField(25);
        smtpServerField.setText("smtp.gmail.com");

        smtpPortSpinner = new JSpinner(new SpinnerNumberModel(587, 25, 65535, 1));
        setupSpinner(smtpPortSpinner);

        smtpUsernameField = createTextField(25);
        smtpPasswordField = createPasswordField(25);

        enableSSLCheck = createCheckBox("\u062A\u0641\u0639\u064A\u0644 SSL/TLS", true); // تفعيل
                                                                                         // SSL/TLS

        fromEmailField = createTextField(25);
        fromNameField = createTextField(25);
        fromNameField.setText(
                "\u0646\u0638\u0627\u0645 \u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0634\u062D\u0646\u0627\u062A"); // نظام
                                                                                                                       // إدارة
                                                                                                                       // الشحنات
    }

    /**
     * تهيئة مكونات قاعدة البيانات
     */
    private void initializeDatabaseComponents() {
        dbHostField = createTextField(20);
        dbHostField.setText("localhost");

        dbPortSpinner = new JSpinner(new SpinnerNumberModel(1521, 1, 65535, 1));
        setupSpinner(dbPortSpinner);

        dbNameField = createTextField(20);
        dbNameField.setText("orcl");

        dbUsernameField = createTextField(20);
        dbPasswordField = createPasswordField(20);

        dbTypeCombo = new JComboBox<>(
                new String[] {"Oracle Database", "MySQL", "PostgreSQL", "SQL Server", "SQLite"});
        setupComboBox(dbTypeCombo);

        connectionPoolSizeSpinner = new JSpinner(new SpinnerNumberModel(10, 1, 100, 5));
        setupSpinner(connectionPoolSizeSpinner);

        connectionTimeoutSpinner = new JSpinner(new SpinnerNumberModel(30, 5, 300, 5));
        setupSpinner(connectionTimeoutSpinner);
    }

    /**
     * تهيئة مكونات التقارير
     */
    private void initializeReportComponents() {
        reportTemplatePathField = createTextField(30);
        reportTemplatePathField.setText("templates/reports");

        defaultReportFormatCombo =
                new JComboBox<>(new String[] {"PDF", "Excel", "Word", "HTML", "CSV"});
        setupComboBox(defaultReportFormatCombo);

        enableReportCacheCheck = createCheckBox(
                "\u062A\u0641\u0639\u064A\u0644 \u062A\u062E\u0632\u064A\u0646 \u0627\u0644\u062A\u0642\u0627\u0631\u064A\u0631 \u0645\u0624\u0642\u062A\u0627\u064B",
                true); // تفعيل تخزين التقارير مؤقتاً

        reportCacheTimeSpinner = new JSpinner(new SpinnerNumberModel(60, 5, 1440, 5));
        setupSpinner(reportCacheTimeSpinner);

        reportOutputPathField = createTextField(30);
        reportOutputPathField.setText("output/reports");
    }

    /**
     * تهيئة مكونات الشبكة
     */
    private void initializeNetworkComponents() {
        enableProxyCheck = createCheckBox(
                "\u062A\u0641\u0639\u064A\u0644 \u0627\u0644\u0628\u0631\u0648\u0643\u0633\u064A",
                false); // تفعيل البروكسي

        proxyHostField = createTextField(20);
        proxyPortSpinner = new JSpinner(new SpinnerNumberModel(8080, 1, 65535, 1));
        setupSpinner(proxyPortSpinner);

        proxyUsernameField = createTextField(20);
        proxyPasswordField = createPasswordField(20);

        networkTimeoutSpinner = new JSpinner(new SpinnerNumberModel(30, 5, 300, 5));
        setupSpinner(networkTimeoutSpinner);
    }

    /**
     * دوال مساعدة لإنشاء المكونات
     */
    private JTextField createTextField(int columns) {
        JTextField field = new JTextField(columns);
        field.setFont(arabicFont);
        field.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return field;
    }

    private JTextArea createTextArea(int rows, int columns) {
        JTextArea area = new JTextArea(rows, columns);
        area.setFont(arabicFont);
        area.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        area.setLineWrap(true);
        area.setWrapStyleWord(true);
        return area;
    }

    private JPasswordField createPasswordField(int columns) {
        JPasswordField field = new JPasswordField(columns);
        field.setFont(arabicFont);
        field.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return field;
    }

    private JCheckBox createCheckBox(String text, boolean selected) {
        JCheckBox checkBox = new JCheckBox(text, selected);
        checkBox.setFont(arabicFont);
        checkBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return checkBox;
    }

    private void setupComboBox(JComboBox<String> comboBox) {
        comboBox.setFont(arabicFont);
        comboBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }

    private void setupSpinner(JSpinner spinner) {
        spinner.setFont(arabicFont);
        JSpinner.DefaultEditor editor = (JSpinner.DefaultEditor) spinner.getEditor();
        editor.getTextField().setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }

    /**
     * إعداد التخطيط المتقدم مع التبويبات
     */
    private void setupAdvancedLayout() {
        setLayout(new BorderLayout());
        getContentPane().setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // العنوان الرئيسي
        JPanel headerPanel = createHeaderPanel();
        add(headerPanel, BorderLayout.NORTH);

        // التبويبات الرئيسية
        tabbedPane = new JTabbedPane();
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        tabbedPane.setFont(arabicFont);
        tabbedPane.setTabPlacement(JTabbedPane.TOP);

        // إضافة التبويبات
        tabbedPane.addTab(
                "\u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u0634\u0631\u0643\u0629",
                createCompanyInfoPanel()); // بيانات الشركة
        tabbedPane.addTab(
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0646\u0638\u0627\u0645",
                createSystemSettingsPanel()); // إعدادات النظام
        tabbedPane.addTab("\u0627\u0644\u0623\u0645\u0627\u0646", createSecurityPanel()); // الأمان
        tabbedPane.addTab(
                "\u0627\u0644\u0646\u0633\u062E \u0627\u0644\u0627\u062D\u062A\u064A\u0627\u0637\u064A",
                createBackupSettingsPanel()); // النسخ الاحتياطي
        tabbedPane.addTab(
                "\u0627\u0644\u0628\u0631\u064A\u062F \u0627\u0644\u0625\u0644\u0643\u062A\u0631\u0648\u0646\u064A",
                createEmailPanel()); // البريد الإلكتروني
        tabbedPane.addTab(
                "\u0642\u0627\u0639\u062F\u0629 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A",
                createDatabasePanel()); // قاعدة البيانات
        tabbedPane.addTab("\u0627\u0644\u062A\u0642\u0627\u0631\u064A\u0631", createReportsPanel()); // التقارير
        tabbedPane.addTab("\u0627\u0644\u0634\u0628\u0643\u0629", createNetworkPanel()); // الشبكة

        add(tabbedPane, BorderLayout.CENTER);

        // أزرار التحكم
        add(createAdvancedButtonPanel(), BorderLayout.SOUTH);
    }

    /**
     * إنشاء لوحة العنوان
     */
    private JPanel createHeaderPanel() {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        headerPanel.setBackground(new Color(0, 123, 255));
        headerPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // العنوان الرئيسي
        JLabel titleLabel = new JLabel(
                "\u0646\u0638\u0627\u0645 \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0639\u0627\u0645\u0629 \u0627\u0644\u0645\u062A\u0642\u062F\u0645"); // نظام
                                                                                                                                                                                    // الإعدادات
                                                                                                                                                                                    // العامة
                                                                                                                                                                                    // المتقدم
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 24));
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);

        // العنوان الفرعي
        JLabel subtitleLabel = new JLabel(
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0634\u0627\u0645\u0644\u0629 \u0648\u0645\u062A\u0642\u062F\u0645\u0629 \u0644\u0646\u0638\u0627\u0645 \u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0634\u062D\u0646\u0627\u062A"); // إعدادات
                                                                                                                                                                                                                                                  // شاملة
                                                                                                                                                                                                                                                  // ومتقدمة
                                                                                                                                                                                                                                                  // لنظام
                                                                                                                                                                                                                                                  // إدارة
                                                                                                                                                                                                                                                  // الشحنات
        subtitleLabel.setFont(new Font("Tahoma", Font.PLAIN, 14));
        subtitleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        subtitleLabel.setForeground(new Color(220, 220, 220));
        subtitleLabel.setHorizontalAlignment(SwingConstants.CENTER);

        headerPanel.add(titleLabel, BorderLayout.CENTER);
        headerPanel.add(subtitleLabel, BorderLayout.SOUTH);

        return headerPanel;
    }

    /**
     * إنشاء لوحة الأمان
     */
    private JPanel createSecurityPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // إعدادات الجلسة
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u062C\u0644\u0633\u0629",
                gbc, row++); // إعدادات الجلسة

        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel(
                "\u0645\u0647\u0644\u0629 \u0627\u0646\u062A\u0647\u0627\u0621 \u0627\u0644\u062C\u0644\u0633\u0629 (\u062F\u0642\u064A\u0642\u0629):"),
                gbc); // مهلة انتهاء الجلسة (دقيقة):
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        mainPanel.add(sessionTimeoutSpinner, gbc);
        row++;

        // المصادقة الثنائية
        addSectionTitle(mainPanel,
                "\u0627\u0644\u0645\u0635\u0627\u062F\u0642\u0629 \u0648\u0627\u0644\u062A\u062D\u0642\u0642",
                gbc, row++); // المصادقة والتحقق

        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(enableTwoFactorCheck, gbc);
        row++;

        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        mainPanel.add(createLabel(
                "\u0639\u062F\u062F \u0645\u062D\u0627\u0648\u0644\u0627\u062A \u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644:"),
                gbc); // عدد محاولات تسجيل الدخول:
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        mainPanel.add(maxLoginAttemptsSpinner, gbc);
        row++;

        // إعدادات كلمة المرور
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631",
                gbc, row++); // إعدادات كلمة المرور

        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(enablePasswordExpiryCheck, gbc);
        row++;

        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        mainPanel.add(createLabel(
                "\u0641\u062A\u0631\u0629 \u0627\u0646\u062A\u0647\u0627\u0621 \u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631 (\u064A\u0648\u0645):"),
                gbc); // فترة انتهاء كلمة المرور (يوم):
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        mainPanel.add(passwordExpiryDaysSpinner, gbc);
        row++;

        // إعدادات المراجعة والتشفير
        addSectionTitle(mainPanel,
                "\u0627\u0644\u0645\u0631\u0627\u062C\u0639\u0629 \u0648\u0627\u0644\u062A\u0634\u0641\u064A\u0631",
                gbc, row++); // المراجعة والتشفير

        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(enableAuditLogCheck, gbc);
        row++;

        gbc.gridy = row;
        mainPanel.add(enableEncryptionCheck, gbc);

        JScrollPane scrollPane = new JScrollPane(mainPanel);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(null);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إضافة عنوان قسم
     */
    private void addSectionTitle(JPanel panel, String title, GridBagConstraints gbc, int row) {
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;

        JLabel sectionLabel = new JLabel(title);
        sectionLabel.setFont(new Font("Tahoma", Font.BOLD, 14));
        sectionLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        sectionLabel.setForeground(new Color(0, 123, 255));
        sectionLabel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createMatteBorder(0, 0, 1, 0, new Color(0, 123, 255)),
                BorderFactory.createEmptyBorder(10, 0, 10, 0)));

        panel.add(sectionLabel, gbc);

        // إعادة تعيين القيم الافتراضية
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
    }

    /**
     * إنشاء لوحة البريد الإلكتروني
     */
    private JPanel createEmailPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // إعدادات خادم SMTP
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u062E\u0627\u062F\u0645 SMTP", gbc,
                row++); // إعدادات خادم SMTP

        addFieldRow(mainPanel, "\u062E\u0627\u062F\u0645 SMTP:", smtpServerField, gbc, row++); // خادم
                                                                                               // SMTP:
        addFieldRow(mainPanel, "\u0645\u0646\u0641\u0630 SMTP:", smtpPortSpinner, gbc, row++); // منفذ
                                                                                               // SMTP:
        addFieldRow(mainPanel,
                "\u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645:",
                smtpUsernameField, gbc, row++); // اسم المستخدم:
        addFieldRow(mainPanel, "\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631:",
                smtpPasswordField, gbc, row++); // كلمة المرور:

        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(enableSSLCheck, gbc);
        row++;

        // إعدادات المرسل
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0645\u0631\u0633\u0644",
                gbc, row++); // إعدادات المرسل

        addFieldRow(mainPanel, "\u0628\u0631\u064A\u062F \u0627\u0644\u0645\u0631\u0633\u0644:",
                fromEmailField, gbc, row++); // بريد المرسل:
        addFieldRow(mainPanel, "\u0627\u0633\u0645 \u0627\u0644\u0645\u0631\u0633\u0644:",
                fromNameField, gbc, row++); // اسم المرسل:

        JScrollPane scrollPane = new JScrollPane(mainPanel);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(null);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء لوحة قاعدة البيانات
     */
    private JPanel createDatabasePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // إعدادات الاتصال
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0627\u062A\u0635\u0627\u0644",
                gbc, row++); // إعدادات الاتصال

        addFieldRow(mainPanel,
                "\u0646\u0648\u0639 \u0642\u0627\u0639\u062F\u0629 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A:",
                dbTypeCombo, gbc, row++); // نوع قاعدة البيانات:
        addFieldRow(mainPanel,
                "\u0639\u0646\u0648\u0627\u0646 \u0627\u0644\u062E\u0627\u062F\u0645:", dbHostField,
                gbc, row++); // عنوان الخادم:
        addFieldRow(mainPanel, "\u0631\u0642\u0645 \u0627\u0644\u0645\u0646\u0641\u0630:",
                dbPortSpinner, gbc, row++); // رقم المنفذ:
        addFieldRow(mainPanel,
                "\u0627\u0633\u0645 \u0642\u0627\u0639\u062F\u0629 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A:",
                dbNameField, gbc, row++); // اسم قاعدة البيانات:
        addFieldRow(mainPanel,
                "\u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645:",
                dbUsernameField, gbc, row++); // اسم المستخدم:
        addFieldRow(mainPanel, "\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631:",
                dbPasswordField, gbc, row++); // كلمة المرور:

        // إعدادات الأداء
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0623\u062F\u0627\u0621",
                gbc, row++); // إعدادات الأداء

        addFieldRow(mainPanel,
                "\u062D\u062C\u0645 \u0645\u062C\u0645\u0648\u0639\u0629 \u0627\u0644\u0627\u062A\u0635\u0627\u0644\u0627\u062A:",
                connectionPoolSizeSpinner, gbc, row++); // حجم مجموعة الاتصالات:
        addFieldRow(mainPanel,
                "\u0645\u0647\u0644\u0629 \u0627\u0646\u062A\u0647\u0627\u0621 \u0627\u0644\u0627\u062A\u0635\u0627\u0644 (\u062B\u0627\u0646\u064A\u0629):",
                connectionTimeoutSpinner, gbc, row++); // مهلة انتهاء الاتصال (ثانية):

        // أزرار اختبار الاتصال
        JPanel testPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        testPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton testConnectionBtn = new JButton(
                "\u0627\u062E\u062A\u0628\u0627\u0631 \u0627\u0644\u0627\u062A\u0635\u0627\u0644"); // اختبار
                                                                                                    // الاتصال
        testConnectionBtn.setFont(arabicFont);
        testConnectionBtn.setBackground(new Color(40, 167, 69));
        testConnectionBtn.setForeground(Color.WHITE);
        testConnectionBtn.addActionListener(e -> testDatabaseConnection());

        testPanel.add(testConnectionBtn);

        gbc.gridx = 0;
        gbc.gridy = row++;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(testPanel, gbc);

        // قسم إدارة قاعدة البيانات
        addSectionTitle(mainPanel, "إدارة قاعدة البيانات", gbc, row++);

        // أزرار إدارة قاعدة البيانات
        JPanel managementPanel = new JPanel(new GridLayout(2, 2, 10, 10));
        managementPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // زر إنشاء قاعدة البيانات
        JButton createDBBtn = new JButton("🏗️ إنشاء بنية قاعدة البيانات");
        createDBBtn.setFont(arabicFont);
        createDBBtn.setBackground(new Color(40, 167, 69));
        createDBBtn.setForeground(Color.WHITE);
        createDBBtn.addActionListener(e -> createDatabaseStructure());

        // زر فحص قاعدة البيانات
        JButton checkDBBtn = new JButton("🔍 فحص بنية قاعدة البيانات");
        checkDBBtn.setFont(arabicFont);
        checkDBBtn.setBackground(new Color(23, 162, 184));
        checkDBBtn.setForeground(Color.WHITE);
        checkDBBtn.addActionListener(e -> checkDatabaseStructure());

        // زر إعادة تعيين قاعدة البيانات
        JButton resetDBBtn = new JButton("🔄 إعادة تعيين قاعدة البيانات");
        resetDBBtn.setFont(arabicFont);
        resetDBBtn.setBackground(new Color(255, 193, 7));
        resetDBBtn.setForeground(Color.BLACK);
        resetDBBtn.addActionListener(e -> resetDatabaseStructure());

        // زر حذف قاعدة البيانات
        JButton deleteDBBtn = new JButton("🗑️ حذف قاعدة البيانات");
        deleteDBBtn.setFont(arabicFont);
        deleteDBBtn.setBackground(new Color(220, 53, 69));
        deleteDBBtn.setForeground(Color.WHITE);
        deleteDBBtn.addActionListener(e -> deleteDatabaseStructure());

        managementPanel.add(createDBBtn);
        managementPanel.add(checkDBBtn);
        managementPanel.add(resetDBBtn);
        managementPanel.add(deleteDBBtn);

        gbc.gridx = 0;
        gbc.gridy = row++;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.insets = new Insets(15, 8, 8, 8);
        mainPanel.add(managementPanel, gbc);

        JScrollPane scrollPane = new JScrollPane(mainPanel);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(null);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إضافة صف حقل
     */
    private void addFieldRow(JPanel panel, String labelText, JComponent field,
            GridBagConstraints gbc, int row) {
        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        panel.add(createLabel(labelText), gbc);

        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        panel.add(field, gbc);
    }

    /**
     * اختبار اتصال قاعدة البيانات
     */
    private void testDatabaseConnection() {
        // محاكاة اختبار الاتصال
        JOptionPane.showMessageDialog(this,
                "\u062C\u0627\u0631\u064A \u0627\u062E\u062A\u0628\u0627\u0631 \u0627\u0644\u0627\u062A\u0635\u0627\u0644...\n\u062A\u0645 \u0627\u0644\u0627\u062A\u0635\u0627\u0644 \u0628\u0646\u062C\u0627\u062D!", // جاري
                                                                                                                                                                                                                        // اختبار
                                                                                                                                                                                                                        // الاتصال...
                                                                                                                                                                                                                        // تم
                                                                                                                                                                                                                        // الاتصال
                                                                                                                                                                                                                        // بنجاح!
                "\u0627\u062E\u062A\u0628\u0627\u0631 \u0627\u0644\u0627\u062A\u0635\u0627\u0644", // اختبار
                                                                                                   // الاتصال
                JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * إنشاء لوحة التقارير
     */
    private JPanel createReportsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // إعدادات القوالب
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0642\u0648\u0627\u0644\u0628 \u0627\u0644\u062A\u0642\u0627\u0631\u064A\u0631",
                gbc, row++); // إعدادات قوالب التقارير

        addFieldRow(mainPanel,
                "\u0645\u0633\u0627\u0631 \u0642\u0648\u0627\u0644\u0628 \u0627\u0644\u062A\u0642\u0627\u0631\u064A\u0631:",
                reportTemplatePathField, gbc, row++); // مسار قوالب التقارير:
        addFieldRow(mainPanel,
                "\u062A\u0646\u0633\u064A\u0642 \u0627\u0644\u062A\u0642\u0631\u064A\u0631 \u0627\u0644\u0627\u0641\u062A\u0631\u0627\u0636\u064A:",
                defaultReportFormatCombo, gbc, row++); // تنسيق التقرير الافتراضي:
        addFieldRow(mainPanel,
                "\u0645\u0633\u0627\u0631 \u0645\u062E\u0631\u062C\u0627\u062A \u0627\u0644\u062A\u0642\u0627\u0631\u064A\u0631:",
                reportOutputPathField, gbc, row++); // مسار مخرجات التقارير:

        // إعدادات التخزين المؤقت
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u062A\u062E\u0632\u064A\u0646 \u0627\u0644\u0645\u0624\u0642\u062A",
                gbc, row++); // إعدادات التخزين المؤقت

        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(enableReportCacheCheck, gbc);
        row++;

        addFieldRow(mainPanel,
                "\u0645\u062F\u0629 \u0627\u0644\u062A\u062E\u0632\u064A\u0646 \u0627\u0644\u0645\u0624\u0642\u062A (\u062F\u0642\u064A\u0642\u0629):",
                reportCacheTimeSpinner, gbc, row++); // مدة التخزين المؤقت (دقيقة):

        JScrollPane scrollPane = new JScrollPane(mainPanel);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(null);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء لوحة الشبكة
     */
    private JPanel createNetworkPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // إعدادات البروكسي
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0628\u0631\u0648\u0643\u0633\u064A",
                gbc, row++); // إعدادات البروكسي

        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(enableProxyCheck, gbc);
        row++;

        addFieldRow(mainPanel,
                "\u0639\u0646\u0648\u0627\u0646 \u0627\u0644\u0628\u0631\u0648\u0643\u0633\u064A:",
                proxyHostField, gbc, row++); // عنوان البروكسي:
        addFieldRow(mainPanel,
                "\u0645\u0646\u0641\u0630 \u0627\u0644\u0628\u0631\u0648\u0643\u0633\u064A:",
                proxyPortSpinner, gbc, row++); // منفذ البروكسي:
        addFieldRow(mainPanel,
                "\u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645:",
                proxyUsernameField, gbc, row++); // اسم المستخدم:
        addFieldRow(mainPanel, "\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631:",
                proxyPasswordField, gbc, row++); // كلمة المرور:

        // إعدادات الشبكة العامة
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0639\u0627\u0645\u0629", gbc, row++); // إعدادات
                                                                                                    // عامة

        addFieldRow(mainPanel,
                "\u0645\u0647\u0644\u0629 \u0627\u0646\u062A\u0647\u0627\u0621 \u0627\u0644\u0634\u0628\u0643\u0629 (\u062B\u0627\u0646\u064A\u0629):",
                networkTimeoutSpinner, gbc, row++); // مهلة انتهاء الشبكة (ثانية):

        JScrollPane scrollPane = new JScrollPane(mainPanel);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(null);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء لوحة الأزرار المتقدمة
     */
    private JPanel createAdvancedButtonPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(new Color(248, 249, 250));
        panel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createMatteBorder(1, 0, 0, 0, new Color(222, 226, 230)),
                BorderFactory.createEmptyBorder(15, 20, 15, 20)));

        // الأزرار الرئيسية
        JPanel mainButtonsPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 0));
        mainButtonsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainButtonsPanel.setOpaque(false);

        JButton saveButton =
                createStyledButton("\u062D\u0641\u0638", new Color(40, 167, 69), Color.WHITE);
        saveButton.addActionListener(e -> saveAllSettings());

        JButton applyButton = createStyledButton("\u062A\u0637\u0628\u064A\u0642",
                new Color(0, 123, 255), Color.WHITE); // تطبيق
        applyButton.addActionListener(e -> applySettings());

        JButton resetButton =
                createStyledButton("\u0625\u0639\u0627\u062F\u0629 \u062A\u0639\u064A\u064A\u0646",
                        new Color(255, 193, 7), Color.BLACK); // إعادة تعيين
        resetButton.addActionListener(e -> resetAllToDefaults());

        JButton cancelButton = createStyledButton("\u0625\u0644\u063A\u0627\u0621",
                new Color(108, 117, 125), Color.WHITE); // إلغاء
        cancelButton.addActionListener(e -> dispose());

        mainButtonsPanel.add(saveButton);
        mainButtonsPanel.add(applyButton);
        mainButtonsPanel.add(resetButton);
        mainButtonsPanel.add(cancelButton);

        // الأزرار الإضافية
        JPanel extraButtonsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 0));
        extraButtonsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        extraButtonsPanel.setOpaque(false);

        JButton importButton = createStyledButton("\u0627\u0633\u062A\u064A\u0631\u0627\u062F",
                new Color(23, 162, 184), Color.WHITE); // استيراد
        importButton.addActionListener(e -> importSettings());

        JButton exportButton = createStyledButton("\u062A\u0635\u062F\u064A\u0631",
                new Color(111, 66, 193), Color.WHITE); // تصدير
        exportButton.addActionListener(e -> exportSettings());

        extraButtonsPanel.add(importButton);
        extraButtonsPanel.add(exportButton);

        panel.add(mainButtonsPanel, BorderLayout.CENTER);
        panel.add(extraButtonsPanel, BorderLayout.WEST);

        return panel;
    }

    /**
     * إنشاء زر مُنسق
     */
    private JButton createStyledButton(String text, Color bgColor, Color fgColor) {
        JButton button = new JButton(text);
        button.setFont(arabicFont);
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        button.setBackground(bgColor);
        button.setForeground(fgColor);
        button.setPreferredSize(new Dimension(120, 40));
        button.setFocusPainted(false);
        button.setBorderPainted(false);

        // تأثيرات التفاعل
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            Color originalBg = bgColor;

            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(bgColor.darker());
            }

            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(originalBg);
            }
        });

        return button;
    }

    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        // ربط تفعيل النسخ الاحتياطي بالحقول المرتبطة
        backupEnabledCheck.addActionListener(e -> {
            boolean enabled = backupEnabledCheck.isSelected();
            backupIntervalSpinner.setEnabled(enabled);
            backupDirectoryField.setEnabled(enabled);
            backupTypeCombo.setEnabled(enabled);
            backupRetentionSpinner.setEnabled(enabled);
        });

        // ربط تفعيل البروكسي بالحقول المرتبطة
        enableProxyCheck.addActionListener(e -> {
            boolean enabled = enableProxyCheck.isSelected();
            proxyHostField.setEnabled(enabled);
            proxyPortSpinner.setEnabled(enabled);
            proxyUsernameField.setEnabled(enabled);
            proxyPasswordField.setEnabled(enabled);
        });

        // ربط تفعيل انتهاء كلمة المرور
        enablePasswordExpiryCheck.addActionListener(e -> {
            passwordExpiryDaysSpinner.setEnabled(enablePasswordExpiryCheck.isSelected());
        });

        // ربط تفعيل التخزين المؤقت للتقارير
        enableReportCacheCheck.addActionListener(e -> {
            reportCacheTimeSpinner.setEnabled(enableReportCacheCheck.isSelected());
        });

        // ربط تفعيل النسخ السحابي
        enableCloudBackupCheck.addActionListener(e -> {
            cloudBackupPathField.setEnabled(enableCloudBackupCheck.isSelected());
        });
    }

    /**
     * تحميل جميع الإعدادات
     */
    private void loadAllSettings() {
        loadCompanySettings();
        loadSystemSettings();
        loadSecuritySettings();
        loadBackupSettings();
        loadEmailSettings();
        loadDatabaseSettings();
        loadReportSettings();
        loadNetworkSettings();
    }

    /**
     * تحميل إعدادات الشركة
     */
    private void loadCompanySettings() {
        companyNameField.setText(
                "\u0634\u0631\u0643\u0629 \u0627\u0644\u0634\u062D\u0646 \u0627\u0644\u0645\u062A\u0642\u062F\u0645\u0629"); // شركة
                                                                                                                             // الشحن
                                                                                                                             // المتقدمة
        companyAddressArea.setText(
                "\u0627\u0644\u0631\u064A\u0627\u0636\u060C \u0627\u0644\u0645\u0645\u0644\u0643\u0629 \u0627\u0644\u0639\u0631\u0628\u064A\u0629 \u0627\u0644\u0633\u0639\u0648\u062F\u064A\u0629\n\u0635\u0646\u062F\u0648\u0642 \u0628\u0631\u064A\u062F: 12345\n\u0627\u0644\u0631\u0645\u0632 \u0627\u0644\u0628\u0631\u064A\u062F\u064A: 11564"); // الرياض،
                                                                                                                                                                                                                                                                                                                                                        // المملكة
                                                                                                                                                                                                                                                                                                                                                        // العربية
                                                                                                                                                                                                                                                                                                                                                        // السعودية
        companyPhoneField.setText("+966 11 123 4567");
        companyEmailField.setText("<EMAIL>");
        companyWebsiteField.setText("www.advancedshipping.com");
        taxNumberField.setText("123456789012345");
        commercialRegisterField.setText("1010123456");
        companyLogoPathField.setText("assets/logo.png");
        companyTypeCombo.setSelectedIndex(0);
        businessSectorCombo.setSelectedIndex(0);
    }

    /**
     * تحميل إعدادات النظام
     */
    private void loadSystemSettings() {
        defaultCurrencyCombo.setSelectedIndex(0); // SAR
        decimalPlacesSpinner.setValue(2);
        dateFormatCombo.setSelectedIndex(0);
        timeFormatCombo.setSelectedIndex(0);

        // تحميل اللغة والمظهر المحفوظين
        String savedLanguage = SettingsManager.getSavedLanguage();
        String savedTheme = SettingsManager.getSavedTheme();

        // تعيين اللغة المحفوظة
        for (int i = 0; i < languageCombo.getItemCount(); i++) {
            if (languageCombo.getItemAt(i).equals(savedLanguage)) {
                languageCombo.setSelectedIndex(i);
                break;
            }
        }

        // تعيين المظهر المحفوظ
        for (int i = 0; i < themeCombo.getItemCount(); i++) {
            if (themeCombo.getItemAt(i).equals(savedTheme)) {
                themeCombo.setSelectedIndex(i);
                break;
            }
        }

        enableNotificationsCheck.setSelected(true);
        enableSoundsCheck.setSelected(true);
        enableAnimationsCheck.setSelected(true);

        System.out.println("تم تحميل الإعدادات: المظهر=" + savedTheme + ", اللغة=" + savedLanguage);
    }

    /**
     * تحميل إعدادات الأمان
     */
    private void loadSecuritySettings() {
        sessionTimeoutSpinner.setValue(30);
        enableTwoFactorCheck.setSelected(false);
        enablePasswordExpiryCheck.setSelected(true);
        passwordExpiryDaysSpinner.setValue(90);
        maxLoginAttemptsSpinner.setValue(3);
        enableAuditLogCheck.setSelected(true);
        enableEncryptionCheck.setSelected(true);
    }

    /**
     * تحميل إعدادات النسخ الاحتياطي
     */
    private void loadBackupSettings() {
        backupEnabledCheck.setSelected(true);
        backupIntervalSpinner.setValue(24);
        backupDirectoryField.setText("backup/daily");
        backupTypeCombo.setSelectedIndex(0); // نسخ كامل
        backupRetentionSpinner.setValue(30);
        enableCloudBackupCheck.setSelected(false);
        cloudBackupPathField.setText("");
    }

    /**
     * تحميل إعدادات البريد الإلكتروني
     */
    private void loadEmailSettings() {
        smtpServerField.setText("smtp.gmail.com");
        smtpPortSpinner.setValue(587);
        smtpUsernameField.setText("");
        smtpPasswordField.setText("");
        enableSSLCheck.setSelected(true);
        fromEmailField.setText("<EMAIL>");
        fromNameField.setText(
                "\u0646\u0638\u0627\u0645 \u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0634\u062D\u0646\u0627\u062A"); // نظام
                                                                                                                       // إدارة
                                                                                                                       // الشحنات
    }

    /**
     * تحميل إعدادات قاعدة البيانات
     */
    private void loadDatabaseSettings() {
        dbTypeCombo.setSelectedIndex(0); // Oracle
        dbHostField.setText("localhost");
        dbPortSpinner.setValue(1521);
        dbNameField.setText("orcl");
        dbUsernameField.setText("ship_erp");
        dbPasswordField.setText("ship_erp_password");
        connectionPoolSizeSpinner.setValue(10);
        connectionTimeoutSpinner.setValue(30);
    }

    /**
     * تحميل إعدادات التقارير
     */
    private void loadReportSettings() {
        reportTemplatePathField.setText("templates/reports");
        defaultReportFormatCombo.setSelectedIndex(0); // PDF
        enableReportCacheCheck.setSelected(true);
        reportCacheTimeSpinner.setValue(60);
        reportOutputPathField.setText("output/reports");
    }

    /**
     * تحميل إعدادات الشبكة
     */
    private void loadNetworkSettings() {
        enableProxyCheck.setSelected(false);
        proxyHostField.setText("");
        proxyPortSpinner.setValue(8080);
        proxyUsernameField.setText("");
        proxyPasswordField.setText("");
        networkTimeoutSpinner.setValue(30);

        // تطبيق حالة التفعيل
        boolean proxyEnabled = enableProxyCheck.isSelected();
        proxyHostField.setEnabled(proxyEnabled);
        proxyPortSpinner.setEnabled(proxyEnabled);
        proxyUsernameField.setEnabled(proxyEnabled);
        proxyPasswordField.setEnabled(proxyEnabled);
    }

    /**
     * حفظ جميع الإعدادات
     */
    private void saveAllSettings() {
        if (validateAllSettings()) {
            try {
                // حفظ المظهر المختار
                saveThemeSettings();

                // تطبيق تغيير اللغة إذا لزم الأمر
                applyLanguageChange();

                // محاكاة حفظ الإعدادات
                Thread.sleep(500); // تقليل وقت المعالجة

                JOptionPane.showMessageDialog(this,
                        "\u062A\u0645 \u062D\u0641\u0638 \u062C\u0645\u064A\u0639 \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0628\u0646\u062C\u0627\u062D!", // تم
                                                                                                                                                                           // حفظ
                                                                                                                                                                           // جميع
                                                                                                                                                                           // الإعدادات
                                                                                                                                                                           // بنجاح!
                        "\u062D\u0641\u0638 \u0646\u0627\u062C\u062D", // حفظ ناجح
                        JOptionPane.INFORMATION_MESSAGE);

                dispose();

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * حفظ إعدادات المظهر
     */
    private void saveThemeSettings() {
        try {
            String selectedTheme = (String) themeCombo.getSelectedItem();
            String selectedLanguage = (String) languageCombo.getSelectedItem();

            // حفظ الإعدادات باستخدام SettingsManager
            SettingsManager.saveSettings(selectedTheme, selectedLanguage);

        } catch (Exception e) {
            System.err.println("خطأ في حفظ إعدادات المظهر: " + e.getMessage());
        }
    }

    /**
     * تطبيق الإعدادات بدون إغلاق النافذة
     */
    private void applySettings() {
        if (validateAllSettings()) {
            JOptionPane.showMessageDialog(this,
                    "\u062A\u0645 \u062A\u0637\u0628\u064A\u0642 \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0628\u0646\u062C\u0627\u062D!", // تم
                                                                                                                                                          // تطبيق
                                                                                                                                                          // الإعدادات
                                                                                                                                                          // بنجاح!
                    "\u062A\u0637\u0628\u064A\u0642 \u0646\u0627\u062C\u062D", // تطبيق ناجح
                    JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * إعادة تعيين جميع الإعدادات للقيم الافتراضية
     */
    private void resetAllToDefaults() {
        int result = JOptionPane.showConfirmDialog(this,
                "\u0647\u0644 \u062A\u0631\u064A\u062F \u0625\u0639\u0627\u062F\u0629 \u062A\u0639\u064A\u064A\u0646 \u062C\u0645\u064A\u0639 \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0644\u0644\u0642\u064A\u0645 \u0627\u0644\u0627\u0641\u062A\u0631\u0627\u0636\u064A\u0629\u061F\n\u0647\u0630\u0647 \u0627\u0644\u0639\u0645\u0644\u064A\u0629 \u0644\u0627 \u064A\u0645\u0643\u0646 \u0627\u0644\u062A\u0631\u0627\u062C\u0639 \u0639\u0646\u0647\u0627.", // هل
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   // تريد
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   // إعادة
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   // تعيين
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   // جميع
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   // الإعدادات
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   // للقيم
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   // الافتراضية؟
                "\u062A\u0623\u0643\u064A\u062F \u0625\u0639\u0627\u062F\u0629 \u0627\u0644\u062A\u0639\u064A\u064A\u0646", // تأكيد
                                                                                                                            // إعادة
                                                                                                                            // التعيين
                JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            resetToDefaults();
            JOptionPane.showMessageDialog(this,
                    "\u062A\u0645 \u0625\u0639\u0627\u062F\u0629 \u062A\u0639\u064A\u064A\u0646 \u062C\u0645\u064A\u0639 \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0628\u0646\u062C\u0627\u062D!", // تم
                                                                                                                                                                                                                  // إعادة
                                                                                                                                                                                                                  // تعيين
                                                                                                                                                                                                                  // جميع
                                                                                                                                                                                                                  // الإعدادات
                                                                                                                                                                                                                  // بنجاح!
                    "\u0625\u0639\u0627\u062F\u0629 \u062A\u0639\u064A\u064A\u0646 \u0646\u0627\u062C\u062D\u0629", // إعادة
                                                                                                                    // تعيين
                                                                                                                    // ناجحة
                    JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * تصدير الإعدادات
     */
    private void exportSettings() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle(
                "\u062A\u0635\u062F\u064A\u0631 \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A"); // تصدير
                                                                                                          // الإعدادات
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter(
                "Settings Files (*.settings)", "settings"));
        fileChooser.setSelectedFile(new java.io.File("ship_erp_settings.settings"));

        if (fileChooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
            try {
                // محاكاة تصدير الإعدادات
                Thread.sleep(500);

                JOptionPane.showMessageDialog(this,
                        "\u062A\u0645 \u062A\u0635\u062F\u064A\u0631 \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0628\u0646\u062C\u0627\u062D \u0625\u0644\u0649:\n"
                                + fileChooser.getSelectedFile().getAbsolutePath(), // تم تصدير
                                                                                   // الإعدادات
                                                                                   // بنجاح إلى:
                        "\u062A\u0635\u062F\u064A\u0631 \u0646\u0627\u062C\u062D", // تصدير ناجح
                        JOptionPane.INFORMATION_MESSAGE);

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * استيراد الإعدادات
     */
    private void importSettings() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle(
                "\u0627\u0633\u062A\u064A\u0631\u0627\u062F \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A"); // استيراد
                                                                                                                      // الإعدادات
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter(
                "Settings Files (*.settings)", "settings"));

        if (fileChooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            int result = JOptionPane.showConfirmDialog(this,
                    "\u0647\u0644 \u062A\u0631\u064A\u062F \u0627\u0633\u062A\u064A\u0631\u0627\u062F \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0645\u0646 \u0627\u0644\u0645\u0644\u0641 \u0627\u0644\u0645\u062D\u062F\u062F\u061F\n\u0633\u064A\u062A\u0645 \u0627\u0633\u062A\u0628\u062F\u0627\u0644 \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u062D\u0627\u0644\u064A\u0629.", // هل
                                                                                                                                                                                                                                                                                                                                                                                                                              // تريد
                                                                                                                                                                                                                                                                                                                                                                                                                              // استيراد
                                                                                                                                                                                                                                                                                                                                                                                                                              // الإعدادات
                                                                                                                                                                                                                                                                                                                                                                                                                              // من
                                                                                                                                                                                                                                                                                                                                                                                                                              // الملف
                                                                                                                                                                                                                                                                                                                                                                                                                              // المحدد؟
                    "\u062A\u0623\u0643\u064A\u062F \u0627\u0644\u0627\u0633\u062A\u064A\u0631\u0627\u062F", // تأكيد
                                                                                                             // الاستيراد
                    JOptionPane.YES_NO_OPTION);

            if (result == JOptionPane.YES_OPTION) {
                try {
                    // محاكاة استيراد الإعدادات
                    Thread.sleep(500);
                    loadAllSettings(); // إعادة تحميل الإعدادات

                    JOptionPane.showMessageDialog(this,
                            "\u062A\u0645 \u0627\u0633\u062A\u064A\u0631\u0627\u062F \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0628\u0646\u062C\u0627\u062D!", // تم
                                                                                                                                                                              // استيراد
                                                                                                                                                                              // الإعدادات
                                                                                                                                                                              // بنجاح!
                            "\u0627\u0633\u062A\u064A\u0631\u0627\u062F \u0646\u0627\u062C\u062D", // استيراد
                                                                                                   // ناجح
                            JOptionPane.INFORMATION_MESSAGE);

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    /**
     * التحقق من صحة جميع الإعدادات
     */
    private boolean validateAllSettings() {
        // التحقق من بيانات الشركة
        if (companyNameField.getText().trim().isEmpty()) {
            showValidationError(
                    "\u0627\u0633\u0645 \u0627\u0644\u0634\u0631\u0643\u0629 \u0645\u0637\u0644\u0648\u0628",
                    0); // اسم الشركة مطلوب
            return false;
        }

        if (companyEmailField.getText().trim().isEmpty()
                || !isValidEmail(companyEmailField.getText())) {
            showValidationError(
                    "\u064A\u0631\u062C\u0649 \u0625\u062F\u062E\u0627\u0644 \u0628\u0631\u064A\u062F \u0625\u0644\u0643\u062A\u0631\u0648\u0646\u064A \u0635\u062D\u064A\u062D \u0644\u0644\u0634\u0631\u0643\u0629",
                    0); // يرجى إدخال بريد إلكتروني صحيح للشركة
            return false;
        }

        // التحقق من إعدادات البريد الإلكتروني
        if (!smtpUsernameField.getText().trim().isEmpty()
                && !isValidEmail(smtpUsernameField.getText())) {
            showValidationError(
                    "\u064A\u0631\u062C\u0649 \u0625\u062F\u062E\u0627\u0644 \u0628\u0631\u064A\u062F \u0625\u0644\u0643\u062A\u0631\u0648\u0646\u064A \u0635\u062D\u064A\u062D \u0644\u062E\u0627\u062F\u0645 SMTP",
                    4); // يرجى إدخال بريد إلكتروني صحيح لخادم SMTP
            return false;
        }

        // التحقق من إعدادات قاعدة البيانات
        if (dbHostField.getText().trim().isEmpty()) {
            showValidationError(
                    "\u0639\u0646\u0648\u0627\u0646 \u062E\u0627\u062F\u0645 \u0642\u0627\u0639\u062F\u0629 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A \u0645\u0637\u0644\u0648\u0628",
                    5); // عنوان خادم قاعدة البيانات مطلوب
            return false;
        }

        if (dbNameField.getText().trim().isEmpty()) {
            showValidationError(
                    "\u0627\u0633\u0645 \u0642\u0627\u0639\u062F\u0629 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A \u0645\u0637\u0644\u0648\u0628",
                    5); // اسم قاعدة البيانات مطلوب
            return false;
        }

        return true;
    }

    /**
     * عرض رسالة خطأ التحقق
     */
    private void showValidationError(String message, int tabIndex) {
        tabbedPane.setSelectedIndex(tabIndex);
        JOptionPane.showMessageDialog(this, message,
                "\u062E\u0637\u0623 \u0641\u064A \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A",
                JOptionPane.ERROR_MESSAGE); // خطأ في البيانات
    }

    /**
     * التحقق من صحة البريد الإلكتروني
     */
    private boolean isValidEmail(String email) {
        return email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }

    /**
     * إعادة تعيين القيم الافتراضية
     */
    private void resetToDefaults() {
        // إعادة تعيين بيانات الشركة
        companyNameField.setText("");
        companyAddressArea.setText("");
        companyPhoneField.setText("");
        companyEmailField.setText("");
        companyWebsiteField.setText("");
        taxNumberField.setText("");
        commercialRegisterField.setText("");
        companyLogoPathField.setText("");
        companyTypeCombo.setSelectedIndex(0);
        businessSectorCombo.setSelectedIndex(0);

        // إعادة تعيين إعدادات النظام
        defaultCurrencyCombo.setSelectedIndex(0);
        decimalPlacesSpinner.setValue(2);
        dateFormatCombo.setSelectedIndex(0);
        timeFormatCombo.setSelectedIndex(0);
        languageCombo.setSelectedIndex(0);
        themeCombo.setSelectedIndex(0);
        enableNotificationsCheck.setSelected(true);
        enableSoundsCheck.setSelected(true);
        enableAnimationsCheck.setSelected(true);

        // إعادة تعيين إعدادات الأمان
        sessionTimeoutSpinner.setValue(30);
        enableTwoFactorCheck.setSelected(false);
        enablePasswordExpiryCheck.setSelected(true);
        passwordExpiryDaysSpinner.setValue(90);
        maxLoginAttemptsSpinner.setValue(3);
        enableAuditLogCheck.setSelected(true);
        enableEncryptionCheck.setSelected(true);

        // إعادة تعيين إعدادات النسخ الاحتياطي
        backupEnabledCheck.setSelected(true);
        backupIntervalSpinner.setValue(24);
        backupDirectoryField.setText("backup");
        backupTypeCombo.setSelectedIndex(0);
        backupRetentionSpinner.setValue(30);
        enableCloudBackupCheck.setSelected(false);
        cloudBackupPathField.setText("");

        // إعادة تعيين إعدادات البريد الإلكتروني
        smtpServerField.setText("smtp.gmail.com");
        smtpPortSpinner.setValue(587);
        smtpUsernameField.setText("");
        smtpPasswordField.setText("");
        enableSSLCheck.setSelected(true);
        fromEmailField.setText("");
        fromNameField.setText("");

        // إعادة تعيين إعدادات قاعدة البيانات
        dbTypeCombo.setSelectedIndex(0);
        dbHostField.setText("localhost");
        dbPortSpinner.setValue(1521);
        dbNameField.setText("");
        dbUsernameField.setText("");
        dbPasswordField.setText("");
        connectionPoolSizeSpinner.setValue(10);
        connectionTimeoutSpinner.setValue(30);

        // إعادة تعيين إعدادات التقارير
        reportTemplatePathField.setText("templates/reports");
        defaultReportFormatCombo.setSelectedIndex(0);
        enableReportCacheCheck.setSelected(true);
        reportCacheTimeSpinner.setValue(60);
        reportOutputPathField.setText("output/reports");

        // إعادة تعيين إعدادات الشبكة
        enableProxyCheck.setSelected(false);
        proxyHostField.setText("");
        proxyPortSpinner.setValue(8080);
        proxyUsernameField.setText("");
        proxyPasswordField.setText("");
        networkTimeoutSpinner.setValue(30);

        // تطبيق حالات التفعيل
        setupEventHandlers();
    }

    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }

    private void browseBackupDirectory() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
        fileChooser.setDialogTitle(
                "\u0627\u062E\u062A\u064A\u0627\u0631 \u0645\u062C\u0644\u062F \u0627\u0644\u0646\u0633\u062E \u0627\u0644\u0627\u062D\u062A\u064A\u0627\u0637\u064A"); // اختيار
                                                                                                                                                                        // مجلد
                                                                                                                                                                        // النسخ
                                                                                                                                                                        // الاحتياطي
        fileChooser.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        if (fileChooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            backupDirectoryField.setText(fileChooser.getSelectedFile().getAbsolutePath());
        }
    }

    /**
     * تطبيق تغيير المظهر فوراً بدون رسائل
     */
    private void applyThemeChangeSilently() {
        try {
            String selectedTheme = (String) themeCombo.getSelectedItem();
            String selectedLanguage = (String) languageCombo.getSelectedItem();

            if (selectedTheme.equals("\u0641\u0627\u062A\u062D")) { // فاتح
                applyLightTheme();
            } else if (selectedTheme.equals("\u0645\u0638\u0644\u0645")) { // مظلم
                applyDarkTheme();
            } else if (selectedTheme.equals("\u062A\u0644\u0642\u0627\u0626\u064A")) { // تلقائي
                applySystemTheme();
            } else if (selectedTheme.equals("\u0645\u062E\u0635\u0635")) { // مخصص
                applyCustomTheme();
            }

            // حفظ المظهر الجديد فوراً
            SettingsManager.saveSettings(selectedTheme, selectedLanguage);

            // تحديث جميع النوافذ المفتوحة
            updateAllWindows();

        } catch (Exception e) {
            // تسجيل الخطأ فقط بدون إظهار رسالة للمستخدم
            System.err.println("خطأ في تغيير المظهر: " + e.getMessage());
        }
    }

    /**
     * تطبيق تغيير المظهر مع رسالة تأكيد (للزر معاينة فقط)
     */
    private void applyThemeChange() {
        try {
            applyThemeChangeSilently();

            String selectedTheme = (String) themeCombo.getSelectedItem();

            // إظهار رسالة تأكيد مختصرة فقط عند الضغط على زر المعاينة
            JOptionPane.showMessageDialog(this,
                    "\u062A\u0645 \u062A\u0637\u0628\u064A\u0642 \u0645\u0638\u0647\u0631: "
                            + selectedTheme, // تم تطبيق مظهر:
                    "\u0645\u0639\u0627\u064A\u0646\u0629 \u0627\u0644\u0645\u0638\u0647\u0631", // معاينة
                                                                                                 // المظهر
                    JOptionPane.INFORMATION_MESSAGE);

        } catch (Exception e) {
            JOptionPane.showMessageDialog(this,
                    "\u062D\u062F\u062B \u062E\u0637\u0623 \u0641\u064A \u062A\u063A\u064A\u064A\u0631 \u0627\u0644\u0645\u0638\u0647\u0631: "
                            + e.getMessage(), // حدث خطأ في تغيير المظهر
                    "\u062E\u0637\u0623", // خطأ
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * تطبيق المظهر الفاتح
     */
    private void applyLightTheme() throws Exception {
        // تطبيق المظهر الفاتح
        for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
            if ("Nimbus".equals(info.getName()) || "Windows".equals(info.getName())
                    || "Metal".equals(info.getName())) {
                UIManager.setLookAndFeel(info.getClassName());
                break;
            }
        }

        // تخصيص الألوان للمظهر الفاتح
        UIManager.put("Panel.background", new Color(255, 255, 255));
        UIManager.put("Button.background", new Color(248, 249, 250));
        UIManager.put("TextField.background", Color.WHITE);
        UIManager.put("ComboBox.background", Color.WHITE);
        UIManager.put("TabbedPane.background", new Color(248, 249, 250));
        UIManager.put("TabbedPane.selected", Color.WHITE);
        UIManager.put("Label.foreground", new Color(33, 37, 41));
        UIManager.put("Button.foreground", new Color(33, 37, 41));
    }

    /**
     * تطبيق المظهر المظلم
     */
    private void applyDarkTheme() throws Exception {
        // تطبيق المظهر المظلم
        for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
            if ("Metal".equals(info.getName())) {
                UIManager.setLookAndFeel(info.getClassName());
                break;
            }
        }

        // تخصيص الألوان للمظهر المظلم
        UIManager.put("Panel.background", new Color(33, 37, 41));
        UIManager.put("Button.background", new Color(52, 58, 64));
        UIManager.put("TextField.background", new Color(52, 58, 64));
        UIManager.put("ComboBox.background", new Color(52, 58, 64));
        UIManager.put("TabbedPane.background", new Color(33, 37, 41));
        UIManager.put("TabbedPane.selected", new Color(52, 58, 64));
        UIManager.put("Label.foreground", new Color(248, 249, 250));
        UIManager.put("Button.foreground", new Color(248, 249, 250));
        UIManager.put("TextField.foreground", new Color(248, 249, 250));
        UIManager.put("ComboBox.foreground", new Color(248, 249, 250));
        UIManager.put("TabbedPane.foreground", new Color(248, 249, 250));
    }

    /**
     * تطبيق المظهر التلقائي (حسب نظام التشغيل)
     */
    private void applySystemTheme() throws Exception {
        // استخدام مظهر نظام التشغيل
        for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
            if ("Windows".equals(info.getName()) || "Aqua".equals(info.getName())
                    || "GTK+".equals(info.getName())) {
                UIManager.setLookAndFeel(info.getClassName());
                return;
            }
        }
        // إذا لم يتم العثور على مظهر النظام، استخدم Nimbus
        for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
            if ("Nimbus".equals(info.getName())) {
                UIManager.setLookAndFeel(info.getClassName());
                break;
            }
        }
    }

    /**
     * تطبيق المظهر المخصص
     */
    private void applyCustomTheme() throws Exception {
        // تطبيق مظهر مخصص مع ألوان خاصة
        for (UIManager.LookAndFeelInfo info : UIManager.getInstalledLookAndFeels()) {
            if ("Nimbus".equals(info.getName())) {
                UIManager.setLookAndFeel(info.getClassName());
                break;
            }
        }

        // ألوان مخصصة (أزرق وذهبي)
        UIManager.put("Panel.background", new Color(240, 248, 255));
        UIManager.put("Button.background", new Color(0, 123, 255));
        UIManager.put("TextField.background", Color.WHITE);
        UIManager.put("ComboBox.background", Color.WHITE);
        UIManager.put("TabbedPane.background", new Color(240, 248, 255));
        UIManager.put("TabbedPane.selected", Color.WHITE);
        UIManager.put("Label.foreground", new Color(0, 86, 179));
        UIManager.put("Button.foreground", Color.WHITE);
        UIManager.put("TabbedPane.foreground", new Color(0, 86, 179));
    }

    /**
     * تحديث جميع النوافذ المفتوحة
     */
    private void updateAllWindows() {
        // تحديث نافذة الإعدادات الحالية
        SwingUtilities.updateComponentTreeUI(this);

        // تحديث النافذة الرئيسية
        if (parentFrame != null) {
            SwingUtilities.updateComponentTreeUI(parentFrame);
        }

        // تحديث جميع النوافذ المفتوحة في التطبيق
        for (java.awt.Window window : java.awt.Window.getWindows()) {
            SwingUtilities.updateComponentTreeUI(window);
        }

        // إعادة رسم جميع المكونات
        repaint();
        if (parentFrame != null) {
            parentFrame.repaint();
        }
    }

    /**
     * تطبيق تغيير اللغة (يتم استدعاؤها عند الحفظ فقط)
     */
    private void applyLanguageChange() {
        String selectedLanguage = (String) languageCombo.getSelectedItem();

        // إظهار رسالة فقط إذا تم تغيير اللغة فعلاً
        if (!selectedLanguage.equals("\u0627\u0644\u0639\u0631\u0628\u064A\u0629")) { // إذا لم تكن
                                                                                      // العربية
            JOptionPane.showMessageDialog(this,
                    "\u062A\u0645 \u062D\u0641\u0638 \u0627\u0644\u0644\u063A\u0629: "
                            + selectedLanguage
                            + "\n\u0633\u064A\u062A\u0645 \u062A\u0637\u0628\u064A\u0642\u0647\u0627 \u0639\u0646\u062F \u0625\u0639\u0627\u062F\u0629 \u062A\u0634\u063A\u064A\u0644 \u0627\u0644\u0646\u0638\u0627\u0645.", // تم
                                                                                                                                                                                                                              // حفظ
                                                                                                                                                                                                                              // اللغة
                    "\u062D\u0641\u0638 \u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0644\u063A\u0629", // حفظ
                                                                                                                    // إعدادات
                                                                                                                    // اللغة
                    JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * إنشاء لوحة بيانات الشركة المحسنة
     */
    private JPanel createCompanyInfoPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // المعلومات الأساسية
        addSectionTitle(mainPanel,
                "\u0627\u0644\u0645\u0639\u0644\u0648\u0645\u0627\u062A \u0627\u0644\u0623\u0633\u0627\u0633\u064A\u0629",
                gbc, row++); // المعلومات الأساسية

        addFieldRow(mainPanel, "\u0627\u0633\u0645 \u0627\u0644\u0634\u0631\u0643\u0629:",
                companyNameField, gbc, row++); // اسم الشركة:
        addFieldRow(mainPanel, "\u0646\u0648\u0639 \u0627\u0644\u0634\u0631\u0643\u0629:",
                companyTypeCombo, gbc, row++); // نوع الشركة:
        addFieldRow(mainPanel, "\u0642\u0637\u0627\u0639 \u0627\u0644\u0639\u0645\u0644:",
                businessSectorCombo, gbc, row++); // قطاع العمل:

        // العنوان ومعلومات الاتصال
        addSectionTitle(mainPanel,
                "\u0645\u0639\u0644\u0648\u0645\u0627\u062A \u0627\u0644\u0627\u062A\u0635\u0627\u0644",
                gbc, row++); // معلومات الاتصال

        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.NONE;
        gbc.weightx = 0;
        mainPanel.add(
                createLabel("\u0639\u0646\u0648\u0627\u0646 \u0627\u0644\u0634\u0631\u0643\u0629:"),
                gbc); // عنوان الشركة:
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 0.3;
        mainPanel.add(new JScrollPane(companyAddressArea), gbc);
        row++;
        gbc.weighty = 0;

        addFieldRow(mainPanel, "\u0631\u0642\u0645 \u0627\u0644\u0647\u0627\u062A\u0641:",
                companyPhoneField, gbc, row++); // رقم الهاتف:
        addFieldRow(mainPanel,
                "\u0627\u0644\u0628\u0631\u064A\u062F \u0627\u0644\u0625\u0644\u0643\u062A\u0631\u0648\u0646\u064A:",
                companyEmailField, gbc, row++); // البريد الإلكتروني:
        addFieldRow(mainPanel,
                "\u0627\u0644\u0645\u0648\u0642\u0639 \u0627\u0644\u0625\u0644\u0643\u062A\u0631\u0648\u0646\u064A:",
                companyWebsiteField, gbc, row++); // الموقع الإلكتروني:

        // المعلومات القانونية
        addSectionTitle(mainPanel,
                "\u0627\u0644\u0645\u0639\u0644\u0648\u0645\u0627\u062A \u0627\u0644\u0642\u0627\u0646\u0648\u0646\u064A\u0629",
                gbc, row++); // المعلومات القانونية

        addFieldRow(mainPanel,
                "\u0627\u0644\u0631\u0642\u0645 \u0627\u0644\u0636\u0631\u064A\u0628\u064A:",
                taxNumberField, gbc, row++); // الرقم الضريبي:
        addFieldRow(mainPanel,
                "\u0627\u0644\u0633\u062C\u0644 \u0627\u0644\u062A\u062C\u0627\u0631\u064A:",
                commercialRegisterField, gbc, row++); // السجل التجاري:

        // الشعار والهوية
        addSectionTitle(mainPanel,
                "\u0627\u0644\u0647\u0648\u064A\u0629 \u0627\u0644\u0628\u0635\u0631\u064A\u0629",
                gbc, row++); // الهوية البصرية

        JPanel logoPanel = new JPanel(new BorderLayout(5, 0));
        logoPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        logoPanel.add(companyLogoPathField, BorderLayout.CENTER);

        JButton browseLogoBtn = new JButton("\u062A\u0635\u0641\u062D"); // تصفح
        browseLogoBtn.setFont(arabicFont);
        browseLogoBtn.addActionListener(e -> browseCompanyLogo());
        logoPanel.add(browseLogoBtn, BorderLayout.EAST);

        addFieldRow(mainPanel,
                "\u0645\u0633\u0627\u0631 \u0634\u0639\u0627\u0631 \u0627\u0644\u0634\u0631\u0643\u0629:",
                logoPanel, gbc, row++); // مسار شعار الشركة:

        JScrollPane scrollPane = new JScrollPane(mainPanel);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(null);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * تصفح شعار الشركة
     */
    private void browseCompanyLogo() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle(
                "\u0627\u062E\u062A\u064A\u0627\u0631 \u0634\u0639\u0627\u0631 \u0627\u0644\u0634\u0631\u0643\u0629"); // اختيار
                                                                                                                       // شعار
                                                                                                                       // الشركة
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("Image Files",
                "jpg", "jpeg", "png", "gif", "bmp"));

        if (fileChooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            companyLogoPathField.setText(fileChooser.getSelectedFile().getAbsolutePath());
        }
    }

    /**
     * إنشاء لوحة إعدادات النظام المحسنة
     */
    private JPanel createSystemSettingsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // إعدادات العملة والأرقام
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0639\u0645\u0644\u0629 \u0648\u0627\u0644\u0623\u0631\u0642\u0627\u0645",
                gbc, row++); // إعدادات العملة والأرقام

        addFieldRow(mainPanel,
                "\u0627\u0644\u0639\u0645\u0644\u0629 \u0627\u0644\u0627\u0641\u062A\u0631\u0627\u0636\u064A\u0629:",
                defaultCurrencyCombo, gbc, row++); // العملة الافتراضية:
        addFieldRow(mainPanel,
                "\u0639\u062F\u062F \u0627\u0644\u0645\u0646\u0627\u0632\u0644 \u0627\u0644\u0639\u0634\u0631\u064A\u0629:",
                decimalPlacesSpinner, gbc, row++); // عدد المنازل العشرية:

        // إعدادات التاريخ والوقت
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u062A\u0627\u0631\u064A\u062E \u0648\u0627\u0644\u0648\u0642\u062A",
                gbc, row++); // إعدادات التاريخ والوقت

        addFieldRow(mainPanel,
                "\u062A\u0646\u0633\u064A\u0642 \u0627\u0644\u062A\u0627\u0631\u064A\u062E:",
                dateFormatCombo, gbc, row++); // تنسيق التاريخ:
        addFieldRow(mainPanel, "\u062A\u0646\u0633\u064A\u0642 \u0627\u0644\u0648\u0642\u062A:",
                timeFormatCombo, gbc, row++); // تنسيق الوقت:

        // إعدادات اللغة والمظهر
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0644\u063A\u0629 \u0648\u0627\u0644\u0645\u0638\u0647\u0631",
                gbc, row++); // إعدادات اللغة والمظهر

        addFieldRow(mainPanel, "\u0644\u063A\u0629 \u0627\u0644\u0648\u0627\u062C\u0647\u0629:",
                languageCombo, gbc, row++); // لغة الواجهة:

        // مظهر الواجهة مع زر معاينة
        JPanel themePanel = new JPanel(new BorderLayout(5, 0));
        themePanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        themePanel.add(themeCombo, BorderLayout.CENTER);

        JButton previewButton = new JButton("\u0645\u0639\u0627\u064A\u0646\u0629"); // معاينة
        previewButton.setFont(arabicFont);
        previewButton.setBackground(new Color(111, 66, 193));
        previewButton.setForeground(Color.WHITE);
        previewButton.setPreferredSize(new Dimension(80, 25));
        previewButton.addActionListener(e -> applyThemeChange());
        themePanel.add(previewButton, BorderLayout.EAST);

        addFieldRow(mainPanel,
                "\u0645\u0638\u0647\u0631 \u0627\u0644\u0648\u0627\u062C\u0647\u0629:", themePanel,
                gbc, row++); // مظهر الواجهة:

        // إعدادات التفاعل
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u062A\u0641\u0627\u0639\u0644",
                gbc, row++); // إعدادات التفاعل

        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(enableNotificationsCheck, gbc);
        row++;

        gbc.gridy = row;
        mainPanel.add(enableSoundsCheck, gbc);
        row++;

        gbc.gridy = row;
        mainPanel.add(enableAnimationsCheck, gbc);

        JScrollPane scrollPane = new JScrollPane(mainPanel);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(null);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء لوحة النسخ الاحتياطي المحسنة
     */
    private JPanel createBackupSettingsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // إعدادات النسخ المحلي
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0646\u0633\u062E \u0627\u0644\u0645\u062D\u0644\u064A",
                gbc, row++); // إعدادات النسخ المحلي

        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(backupEnabledCheck, gbc);
        row++;

        addFieldRow(mainPanel, "\u0646\u0648\u0639 \u0627\u0644\u0646\u0633\u062E:",
                backupTypeCombo, gbc, row++); // نوع النسخ:
        addFieldRow(mainPanel,
                "\u0641\u062A\u0631\u0629 \u0627\u0644\u0646\u0633\u062E (\u0633\u0627\u0639\u0629):",
                backupIntervalSpinner, gbc, row++); // فترة النسخ (ساعة):
        addFieldRow(mainPanel,
                "\u0641\u062A\u0631\u0629 \u0627\u0644\u0627\u062D\u062A\u0641\u0627\u0638 (\u064A\u0648\u0645):",
                backupRetentionSpinner, gbc, row++); // فترة الاحتفاظ (يوم):

        // مجلد النسخ الاحتياطي
        JPanel backupDirPanel = new JPanel(new BorderLayout(5, 0));
        backupDirPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        backupDirPanel.add(backupDirectoryField, BorderLayout.CENTER);

        JButton browseButton = new JButton("\u062A\u0635\u0641\u062D"); // تصفح
        browseButton.setFont(arabicFont);
        browseButton.setBackground(new Color(0, 123, 255));
        browseButton.setForeground(Color.WHITE);
        browseButton.addActionListener(e -> browseBackupDirectory());
        backupDirPanel.add(browseButton, BorderLayout.EAST);

        addFieldRow(mainPanel, "\u0645\u062C\u0644\u062F \u0627\u0644\u0646\u0633\u062E:",
                backupDirPanel, gbc, row++); // مجلد النسخ:

        // إعدادات النسخ السحابي
        addSectionTitle(mainPanel,
                "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0646\u0633\u062E \u0627\u0644\u0633\u062D\u0627\u0628\u064A",
                gbc, row++); // إعدادات النسخ السحابي

        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(enableCloudBackupCheck, gbc);
        row++;

        addFieldRow(mainPanel,
                "\u0645\u0633\u0627\u0631 \u0627\u0644\u0646\u0633\u062E \u0627\u0644\u0633\u062D\u0627\u0628\u064A:",
                cloudBackupPathField, gbc, row++); // مسار النسخ السحابي:

        // أزرار العمليات
        JPanel operationsPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 10));
        operationsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton backupNowBtn = new JButton(
                "\u0646\u0633\u062E \u0627\u062D\u062A\u064A\u0627\u0637\u064A \u0641\u0648\u0631\u064A"); // نسخ
                                                                                                           // احتياطي
                                                                                                           // فوري
        backupNowBtn.setFont(arabicFont);
        backupNowBtn.setBackground(new Color(40, 167, 69));
        backupNowBtn.setForeground(Color.WHITE);
        backupNowBtn.addActionListener(e -> performBackupNow());

        JButton restoreBtn =
                new JButton("\u0627\u0633\u062A\u0639\u0627\u062F\u0629 \u0646\u0633\u062E\u0629"); // استعادة
                                                                                                    // نسخة
        restoreBtn.setFont(arabicFont);
        restoreBtn.setBackground(new Color(255, 193, 7));
        restoreBtn.setForeground(Color.BLACK);
        restoreBtn.addActionListener(e -> restoreBackup());

        operationsPanel.add(backupNowBtn);
        operationsPanel.add(restoreBtn);

        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(operationsPanel, gbc);

        JScrollPane scrollPane = new JScrollPane(mainPanel);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(null);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * تنفيذ نسخ احتياطي فوري
     */
    private void performBackupNow() {
        JOptionPane.showMessageDialog(this,
                "\u062C\u0627\u0631\u064A \u0625\u0646\u0634\u0627\u0621 \u0646\u0633\u062E\u0629 \u0627\u062D\u062A\u064A\u0627\u0637\u064A\u0629...\n\u062A\u0645 \u0625\u0646\u0634\u0627\u0621 \u0627\u0644\u0646\u0633\u062E\u0629 \u0628\u0646\u062C\u0627\u062D!", // جاري
                                                                                                                                                                                                                                                                          // إنشاء
                                                                                                                                                                                                                                                                          // نسخة
                                                                                                                                                                                                                                                                          // احتياطية...
                                                                                                                                                                                                                                                                          // تم
                                                                                                                                                                                                                                                                          // إنشاء
                                                                                                                                                                                                                                                                          // النسخة
                                                                                                                                                                                                                                                                          // بنجاح!
                "\u0646\u0633\u062E \u0627\u062D\u062A\u064A\u0627\u0637\u064A", // نسخ احتياطي
                JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * استعادة نسخة احتياطية
     */
    private void restoreBackup() {
        int result = JOptionPane.showConfirmDialog(this,
                "\u0647\u0644 \u062A\u0631\u064A\u062F \u0627\u0633\u062A\u0639\u0627\u062F\u0629 \u0646\u0633\u062E\u0629 \u0627\u062D\u062A\u064A\u0627\u0637\u064A\u0629\u061F\n\u0633\u064A\u062A\u0645 \u0627\u0633\u062A\u0628\u062F\u0627\u0644 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u062D\u0627\u0644\u064A\u0629.", // هل
                                                                                                                                                                                                                                                                                                                                                      // تريد
                                                                                                                                                                                                                                                                                                                                                      // استعادة
                                                                                                                                                                                                                                                                                                                                                      // نسخة
                                                                                                                                                                                                                                                                                                                                                      // احتياطية؟
                "\u062A\u0623\u0643\u064A\u062F \u0627\u0644\u0627\u0633\u062A\u0639\u0627\u062F\u0629", // تأكيد
                                                                                                         // الاستعادة
                JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            JOptionPane.showMessageDialog(this,
                    "\u062A\u0645 \u0627\u0633\u062A\u0639\u0627\u062F\u0629 \u0627\u0644\u0646\u0633\u062E\u0629 \u0628\u0646\u062C\u0627\u062D!", // تم
                                                                                                                                                    // استعادة
                                                                                                                                                    // النسخة
                                                                                                                                                    // بنجاح!
                    "\u0627\u0633\u062A\u0639\u0627\u062F\u0629 \u0646\u0627\u062C\u062D\u0629", // استعادة
                                                                                                 // ناجحة
                    JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * إنشاء بنية قاعدة البيانات
     */
    private void createDatabaseStructure() {
        int result = JOptionPane.showConfirmDialog(this,
                "هل تريد إنشاء بنية قاعدة البيانات الكاملة؟\n"
                        + "سيتم إنشاء جميع الجداول والبيانات الأساسية.\n\n"
                        + "تأكد من صحة إعدادات الاتصال أولاً.",
                "تأكيد إنشاء قاعدة البيانات", JOptionPane.YES_NO_OPTION,
                JOptionPane.QUESTION_MESSAGE);

        if (result != JOptionPane.YES_OPTION) {
            return;
        }

        // إنشاء نافذة تقدم العملية
        JDialog progressDialog = new JDialog(this, "إنشاء قاعدة البيانات", true);
        progressDialog.setSize(500, 300);
        progressDialog.setLocationRelativeTo(this);

        JTextArea logArea = new JTextArea(15, 40);
        logArea.setFont(arabicFont);
        logArea.setEditable(false);
        logArea.setBackground(Color.BLACK);
        logArea.setForeground(Color.GREEN);

        JScrollPane scrollPane = new JScrollPane(logArea);
        progressDialog.add(scrollPane, BorderLayout.CENTER);

        JProgressBar progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setFont(arabicFont);
        progressDialog.add(progressBar, BorderLayout.SOUTH);

        // تشغيل العملية في خيط منفصل
        SwingWorker<Boolean, String> worker = new SwingWorker<Boolean, String>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                try {
                    publish("🔄 بدء إنشاء بنية قاعدة البيانات...");
                    progressBar.setValue(10);

                    // إنشاء الاتصال
                    String url = "jdbc:oracle:thin:@" + dbHostField.getText() + ":"
                            + dbPortSpinner.getValue() + ":" + dbNameField.getText();

                    publish("🔗 الاتصال بقاعدة البيانات...");
                    Connection conn = DriverManager.getConnection(url, dbUsernameField.getText(),
                            new String(dbPasswordField.getPassword()));

                    progressBar.setValue(20);
                    publish("✅ تم الاتصال بنجاح!");

                    // إنشاء الجداول
                    publish("🏗️ إنشاء الجداول الأساسية...");
                    createBasicTables(conn);
                    progressBar.setValue(60);

                    // إدراج البيانات الأساسية
                    publish("📝 إدراج البيانات الأساسية...");
                    insertBasicData(conn);
                    progressBar.setValue(90);

                    conn.close();
                    progressBar.setValue(100);
                    publish("🎉 تم إنشاء بنية قاعدة البيانات بنجاح!");

                    return true;

                } catch (Exception e) {
                    publish("❌ خطأ: " + e.getMessage());
                    return false;
                }
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String message : chunks) {
                    logArea.append(message + "\n");
                    logArea.setCaretPosition(logArea.getDocument().getLength());
                }
            }

            @Override
            protected void done() {
                try {
                    boolean success = get();
                    if (success) {
                        JOptionPane.showMessageDialog(progressDialog,
                                "تم إنشاء بنية قاعدة البيانات بنجاح!", "نجح الإنشاء",
                                JOptionPane.INFORMATION_MESSAGE);
                    } else {
                        JOptionPane.showMessageDialog(progressDialog,
                                "فشل في إنشاء بنية قاعدة البيانات!", "خطأ",
                                JOptionPane.ERROR_MESSAGE);
                    }
                    progressDialog.dispose();
                } catch (Exception e) {
                    logArea.append("❌ خطأ: " + e.getMessage() + "\n");
                }
            }
        };

        progressDialog.setVisible(true);
        worker.execute();
    }

    /**
     * إنشاء الجداول الأساسية
     */
    private void createBasicTables(Connection conn) throws Exception {
        String[] createStatements = {
                // جدول وحدات القياس
                """
                        CREATE TABLE UNITS_OF_MEASURE (
                            UNIT_ID NUMBER(10) PRIMARY KEY,
                            UNIT_CODE VARCHAR2(10) UNIQUE NOT NULL,
                            UNIT_NAME VARCHAR2(50) NOT NULL,
                            UNIT_DESC VARCHAR2(200),
                            IS_ACTIVE NUMBER(1) DEFAULT 1,
                            CREATED_DATE DATE DEFAULT SYSDATE,
                            CREATED_BY VARCHAR2(50)
                        )
                        """,

                // جدول فئات الأصناف
                """
                        CREATE TABLE ITEM_CATEGORIES (
                            CAT_ID NUMBER(10) PRIMARY KEY,
                            CAT_CODE VARCHAR2(20) UNIQUE NOT NULL,
                            CAT_NAME VARCHAR2(100) NOT NULL,
                            CAT_DESC VARCHAR2(500),
                            IS_ACTIVE NUMBER(1) DEFAULT 1,
                            CREATED_DATE DATE DEFAULT SYSDATE,
                            CREATED_BY VARCHAR2(50)
                        )
                        """,

                // جدول الأصناف الرئيسي
                """
                        CREATE TABLE ITEMS (
                            ITM_ID NUMBER(10) PRIMARY KEY,
                            ITM_CODE VARCHAR2(30) UNIQUE NOT NULL,
                            ITM_NAME VARCHAR2(100) NOT NULL,
                            ITM_DESC VARCHAR2(2000),
                            CAT_ID NUMBER(10),
                            UNIT_ID NUMBER(10),
                            IS_ACTIVE NUMBER(1) DEFAULT 1,
                            CREATED_DATE DATE DEFAULT SYSDATE,
                            CREATED_BY VARCHAR2(50),
                            LAST_MODIFIED DATE DEFAULT SYSDATE,
                            COST_PRICE NUMBER(15,3) DEFAULT 0,
                            SELL_PRICE NUMBER(15,3) DEFAULT 0,
                            MIN_STOCK NUMBER(15,3) DEFAULT 0,
                            MAX_STOCK NUMBER(15,3) DEFAULT 999999,
                            CURRENT_STOCK NUMBER(15,3) DEFAULT 0,
                            LOCATION_CODE VARCHAR2(20),
                            IMPORT_SOURCE VARCHAR2(20),
                            IMPORT_DATE DATE,
                            EXTERNAL_CODE VARCHAR2(50)
                        )
                        """,

                // المتسلسلات
                "CREATE SEQUENCE SEQ_UNITS_OF_MEASURE START WITH 1 INCREMENT BY 1",
                "CREATE SEQUENCE SEQ_ITEM_CATEGORIES START WITH 1 INCREMENT BY 1",
                "CREATE SEQUENCE SEQ_ITEMS START WITH 1 INCREMENT BY 1"};

        for (String sql : createStatements) {
            try {
                Statement stmt = conn.createStatement();
                stmt.execute(sql);
                stmt.close();
            } catch (SQLException e) {
                if (e.getErrorCode() != 955) { // Object already exists
                    throw e;
                }
            }
        }
    }

    /**
     * إدراج البيانات الأساسية
     */
    private void insertBasicData(Connection conn) throws Exception {
        String[] insertStatements = {
                // وحدات القياس
                "INSERT INTO UNITS_OF_MEASURE (UNIT_ID, UNIT_CODE, UNIT_NAME, UNIT_DESC, CREATED_BY) VALUES (SEQ_UNITS_OF_MEASURE.NEXTVAL, 'PCS', 'قطعة', 'وحدة العد بالقطع', 'SYSTEM')",
                "INSERT INTO UNITS_OF_MEASURE (UNIT_ID, UNIT_CODE, UNIT_NAME, UNIT_DESC, CREATED_BY) VALUES (SEQ_UNITS_OF_MEASURE.NEXTVAL, 'KG', 'كيلوجرام', 'وحدة الوزن', 'SYSTEM')",
                "INSERT INTO UNITS_OF_MEASURE (UNIT_ID, UNIT_CODE, UNIT_NAME, UNIT_DESC, CREATED_BY) VALUES (SEQ_UNITS_OF_MEASURE.NEXTVAL, 'LTR', 'لتر', 'وحدة الحجم', 'SYSTEM')",

                // فئات الأصناف
                "INSERT INTO ITEM_CATEGORIES (CAT_ID, CAT_CODE, CAT_NAME, CAT_DESC, CREATED_BY) VALUES (SEQ_ITEM_CATEGORIES.NEXTVAL, '001', 'مواد غذائية', 'المواد الغذائية والمشروبات', 'SYSTEM')",
                "INSERT INTO ITEM_CATEGORIES (CAT_ID, CAT_CODE, CAT_NAME, CAT_DESC, CREATED_BY) VALUES (SEQ_ITEM_CATEGORIES.NEXTVAL, '002', 'مواد تنظيف', 'مواد التنظيف والمطهرات', 'SYSTEM')",
                "INSERT INTO ITEM_CATEGORIES (CAT_ID, CAT_CODE, CAT_NAME, CAT_DESC, CREATED_BY) VALUES (SEQ_ITEM_CATEGORIES.NEXTVAL, '003', 'أدوات مكتبية', 'الأدوات المكتبية', 'SYSTEM')",

                // أصناف تجريبية
                "INSERT INTO ITEMS (ITM_ID, ITM_CODE, ITM_NAME, ITM_DESC, CAT_ID, UNIT_ID, COST_PRICE, SELL_PRICE, CREATED_BY) VALUES (SEQ_ITEMS.NEXTVAL, 'TEST-001', 'صنف تجريبي 1', 'صنف تجريبي للاختبار', 1, 1, 10.50, 15.75, 'SYSTEM')",
                "INSERT INTO ITEMS (ITM_ID, ITM_CODE, ITM_NAME, ITM_DESC, CAT_ID, UNIT_ID, COST_PRICE, SELL_PRICE, CREATED_BY) VALUES (SEQ_ITEMS.NEXTVAL, 'TEST-002', 'صنف تجريبي 2', 'صنف تجريبي آخر', 2, 2, 25.00, 35.00, 'SYSTEM')"};

        for (String sql : insertStatements) {
            try {
                Statement stmt = conn.createStatement();
                stmt.execute(sql);
                stmt.close();
            } catch (SQLException e) {
                // تجاهل أخطاء البيانات المكررة
                if (e.getErrorCode() != 1) { // Unique constraint violation
                    throw e;
                }
            }
        }

        // تأكيد التغييرات
        conn.commit();
    }

    /**
     * فحص بنية قاعدة البيانات
     */
    private void checkDatabaseStructure() {
        JOptionPane.showMessageDialog(this,
                "سيتم فحص بنية قاعدة البيانات والتحقق من وجود الجداول المطلوبة.",
                "فحص قاعدة البيانات", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * إعادة تعيين بنية قاعدة البيانات
     */
    private void resetDatabaseStructure() {
        int result = JOptionPane.showConfirmDialog(this,
                "تحذير: سيتم حذف جميع البيانات وإعادة إنشاء قاعدة البيانات!\n"
                        + "هذه العملية لا يمكن التراجع عنها.\n\n" + "هل أنت متأكد من المتابعة؟",
                "تأكيد إعادة التعيين", JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            JOptionPane.showMessageDialog(this, "سيتم إعادة تعيين قاعدة البيانات.", "إعادة تعيين",
                    JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * حذف بنية قاعدة البيانات
     */
    private void deleteDatabaseStructure() {
        int result = JOptionPane.showConfirmDialog(this,
                "تحذير خطير: سيتم حذف جميع الجداول والبيانات نهائياً!\n"
                        + "هذه العملية لا يمكن التراجع عنها أبداً.\n\n" + "اكتب 'DELETE' للتأكيد:",
                "تأكيد الحذف النهائي", JOptionPane.YES_NO_OPTION, JOptionPane.ERROR_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            String confirmation =
                    JOptionPane.showInputDialog(this, "اكتب 'DELETE' بالأحرف الكبيرة للتأكيد:",
                            "تأكيد نهائي", JOptionPane.WARNING_MESSAGE);

            if ("DELETE".equals(confirmation)) {
                JOptionPane.showMessageDialog(this, "سيتم حذف قاعدة البيانات.", "حذف مؤكد",
                        JOptionPane.INFORMATION_MESSAGE);
            } else {
                JOptionPane.showMessageDialog(this, "تم إلغاء عملية الحذف.", "تم الإلغاء",
                        JOptionPane.INFORMATION_MESSAGE);
            }
        }
    }
}
