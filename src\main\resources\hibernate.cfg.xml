<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-configuration PUBLIC
        "-//Hibernate/Hibernate Configuration DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-configuration-3.0.dtd">

<hibernate-configuration>
    <session-factory>
        <!-- إعدادات قاعدة البيانات -->
        <property name="hibernate.connection.driver_class">oracle.jdbc.OracleDriver</property>
        <property name="hibernate.connection.url">***********************************</property>
        <property name="hibernate.connection.username">ship_erp</property>
        <property name="hibernate.connection.password">ship_erp_password</property>
        
        <!-- إعدادات Oracle Dialect -->
        <property name="hibernate.dialect">org.hibernate.dialect.OracleDialect</property>
        
        <!-- إعدادات الاتصال -->
        <property name="hibernate.connection.pool_size">20</property>
        <property name="hibernate.connection.autocommit">false</property>
        
        <!-- إعدادات DDL -->
        <property name="hibernate.hbm2ddl.auto">update</property>
        
        <!-- إعدادات العرض -->
        <property name="hibernate.show_sql">false</property>
        <property name="hibernate.format_sql">true</property>
        <property name="hibernate.use_sql_comments">true</property>
        
        <!-- إعدادات الأداء -->
        <property name="hibernate.jdbc.batch_size">20</property>
        <property name="hibernate.order_inserts">true</property>
        <property name="hibernate.order_updates">true</property>
        <property name="hibernate.jdbc.batch_versioned_data">true</property>
        
        <!-- إعدادات Cache -->
        <property name="hibernate.cache.use_second_level_cache">true</property>
        <property name="hibernate.cache.use_query_cache">true</property>
        <property name="hibernate.cache.region.factory_class">org.hibernate.cache.jcache.JCacheRegionFactory</property>
        
        <!-- إعدادات الترميز -->
        <property name="hibernate.connection.characterEncoding">UTF-8</property>
        <property name="hibernate.connection.useUnicode">true</property>
        
        <!-- تسجيل الكيانات -->
        <mapping class="com.shipment.erp.model.Company"/>
        <mapping class="com.shipment.erp.model.Branch"/>
        <mapping class="com.shipment.erp.model.User"/>
        <mapping class="com.shipment.erp.model.Role"/>
        <mapping class="com.shipment.erp.model.Permission"/>
        <mapping class="com.shipment.erp.model.Currency"/>
        <mapping class="com.shipment.erp.model.FiscalYear"/>
        <mapping class="com.shipment.erp.model.SystemSettings"/>
        <mapping class="com.shipment.erp.model.Supplier"/>
        <mapping class="com.shipment.erp.model.Item"/>
        <mapping class="com.shipment.erp.model.Shipment"/>
        <mapping class="com.shipment.erp.model.CustomsEntry"/>
        <mapping class="com.shipment.erp.model.Cost"/>
        <mapping class="com.shipment.erp.model.AuditLog"/>
    </session-factory>
</hibernate-configuration>
