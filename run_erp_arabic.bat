@echo off
title نظام إدارة الشحنات - Ship ERP System

REM تعيين ترميز UTF-8 للوحة التحكم
chcp 65001 >nul

echo ========================================
echo نظام إدارة الشحنات مع دعم النص العربي
echo Ship ERP System with Arabic Support
echo ========================================
echo.

REM الانتقال إلى مجلد الكود
cd /d "%~dp0src\main\java"

echo [1/3] تجميع الملفات...
echo [1/3] Compiling files...

REM تجميع TreeMenuPanel أولاً
javac -encoding UTF-8 -Dfile.encoding=UTF-8 TreeMenuPanel.java
if errorlevel 1 (
    echo ❌ خطأ في تجميع TreeMenuPanel
    echo ❌ Error compiling TreeMenuPanel
    pause
    exit /b 1
)

REM تجميع النظام الرئيسي
javac -encoding UTF-8 -Dfile.encoding=UTF-8 EnhancedShipERP.java
if errorlevel 1 (
    echo ❌ خطأ في تجميع النظام الرئيسي
    echo ❌ Error compiling main system
    pause
    exit /b 1
)

echo ✅ تم التجميع بنجاح
echo ✅ Compilation successful
echo.

echo [2/3] إعداد البيئة للنص العربي...
echo [2/3] Setting up Arabic environment...

REM إعداد متغيرات البيئة للنص العربي
set JAVA_OPTS=-Dfile.encoding=UTF-8
set JAVA_OPTS=%JAVA_OPTS% -Dsun.jnu.encoding=UTF-8
set JAVA_OPTS=%JAVA_OPTS% -Duser.language=ar
set JAVA_OPTS=%JAVA_OPTS% -Duser.country=SA
set JAVA_OPTS=%JAVA_OPTS% -Duser.region=SA
set JAVA_OPTS=%JAVA_OPTS% -Dawt.useSystemAAFontSettings=lcd
set JAVA_OPTS=%JAVA_OPTS% -Dswing.aatext=true
set JAVA_OPTS=%JAVA_OPTS% -Dswing.useSystemAAFontSettings=lcd
set JAVA_OPTS=%JAVA_OPTS% -Dsun.java2d.xrender=true

echo ✅ تم إعداد البيئة
echo ✅ Environment setup complete
echo.

echo [3/3] تشغيل النظام...
echo [3/3] Starting system...
echo.
echo 🚀 نظام إدارة الشحنات جاهز للاستخدام
echo 🚀 Ship ERP System ready to use
echo.

REM تشغيل النظام مع الخيارات المحسنة
java %JAVA_OPTS% EnhancedShipERP

echo.
echo 📋 تم إغلاق النظام
echo 📋 System closed
echo.
pause
