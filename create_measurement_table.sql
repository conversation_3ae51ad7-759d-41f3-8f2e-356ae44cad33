-- إن<PERSON><PERSON><PERSON> جدول وحدات القياس في قاعدة البيانات SHIP_ERP
-- Create Measurement Table in SHIP_ERP Database

-- حذ<PERSON> الجدول إذا كان موجوداً
DROP TABLE ERP_MEASUREMENT CASCADE CONSTRAINTS;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> الجدول بنفس بنية MEASUREMENT الأصلي
CREATE TABLE ERP_MEASUREMENT (
    MEASURE_CODE VARCHAR2(10) NOT NULL,
    MEASURE VARCHAR2(100) NOT NULL,
    MEASURE_F_NM VARCHAR2(100),
    MEASURE_CODE_GB VARCHAR2(10),
    MEASURE_TYPE NUMBER(1) DEFAULT 1,
    MEASURE_WT_TYPE NUMBER(2),
    MEASURE_WT_CONN NUMBER(1) DEFAULT 0,
    DFLT_SIZE NUMBER(22,4),
    ALLOW_UPD NUMBER(1) DEFAULT 1,
    UNT_SALE_TYP NUMBER(2) DEFAULT 3,
    AD_U_ID NUMBER(5),
    AD_DATE DATE DEFAULT SYSDATE,
    UP_U_ID NUMBER(5),
    UP_DATE DATE,
    UP_CNT NUMBER(10) DEFAULT 0,
    AD_TRMNL_NM VARCHAR2(50),
    UP_TRMNL_NM VARCHAR2(50),
    CONSTRAINT PK_ERP_MEASUREMENT PRIMARY KEY (MEASURE_CODE)
);

-- إضافة التعليقات
COMMENT ON TABLE ERP_MEASUREMENT IS 'جدول وحدات القياس - مبني على جدول MEASUREMENT الأصلي من IAS20251';
COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_CODE IS 'كود وحدة القياس (مفتاح أساسي)';
COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE IS 'اسم وحدة القياس بالعربية';
COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_F_NM IS 'اسم وحدة القياس بالإنجليزية';
COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_CODE_GB IS 'الكود العالمي لوحدة القياس';
COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_TYPE IS 'نوع وحدة القياس (1=عادي، 2=وزن، 3=حجم، 4=طول، 5=مساحة)';
COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_WT_TYPE IS 'نوع الوزن';
COMMENT ON COLUMN ERP_MEASUREMENT.MEASURE_WT_CONN IS 'ربط الوزن (0=لا، 1=نعم)';
COMMENT ON COLUMN ERP_MEASUREMENT.DFLT_SIZE IS 'الحجم الافتراضي';
COMMENT ON COLUMN ERP_MEASUREMENT.ALLOW_UPD IS 'السماح بالتحديث (1=نعم، 0=لا)';
COMMENT ON COLUMN ERP_MEASUREMENT.UNT_SALE_TYP IS 'نوع وحدة البيع (1=بالقطعة، 2=بالوزن، 3=عادي)';

-- إنشاء الفهارس
CREATE INDEX IDX_ERP_MEASURE_NAME ON ERP_MEASUREMENT(MEASURE);
CREATE INDEX IDX_ERP_MEASURE_TYPE ON ERP_MEASUREMENT(MEASURE_TYPE);
CREATE INDEX IDX_ERP_MEASURE_ALLOW_UPD ON ERP_MEASUREMENT(ALLOW_UPD);
CREATE INDEX IDX_ERP_MEASURE_F_NM ON ERP_MEASUREMENT(MEASURE_F_NM);

-- إدراج البيانات التجريبية
INSERT INTO ERP_MEASUREMENT (MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB, MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE, ALLOW_UPD, UNT_SALE_TYP, AD_U_ID) VALUES
('PIECE', 'قطعة', 'Piece', 'PCE', 1, NULL, 0, NULL, 1, 3, 1);

INSERT INTO ERP_MEASUREMENT (MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB, MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE, ALLOW_UPD, UNT_SALE_TYP, AD_U_ID) VALUES
('KG', 'كيلوجرام', 'Kilogram', 'KGM', 2, 1, 0, 1.0, 1, 2, 1);

INSERT INTO ERP_MEASUREMENT (MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB, MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE, ALLOW_UPD, UNT_SALE_TYP, AD_U_ID) VALUES
('GRAM', 'جرام', 'Gram', 'GRM', 2, 1, 0, 0.001, 1, 2, 1);

INSERT INTO ERP_MEASUREMENT (MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB, MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE, ALLOW_UPD, UNT_SALE_TYP, AD_U_ID) VALUES
('TON', 'طن', 'Ton', 'TNE', 2, 1, 0, 1000.0, 1, 2, 1);

INSERT INTO ERP_MEASUREMENT (MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB, MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE, ALLOW_UPD, UNT_SALE_TYP, AD_U_ID) VALUES
('LITER', 'لتر', 'Liter', 'LTR', 3, NULL, 0, 1.0, 1, 3, 1);

INSERT INTO ERP_MEASUREMENT (MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB, MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE, ALLOW_UPD, UNT_SALE_TYP, AD_U_ID) VALUES
('METER', 'متر', 'Meter', 'MTR', 4, NULL, 0, 1.0, 1, 3, 1);

INSERT INTO ERP_MEASUREMENT (MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB, MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE, ALLOW_UPD, UNT_SALE_TYP, AD_U_ID) VALUES
('CM', 'سنتيمتر', 'Centimeter', 'CMT', 4, NULL, 0, 0.01, 1, 3, 1);

INSERT INTO ERP_MEASUREMENT (MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB, MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE, ALLOW_UPD, UNT_SALE_TYP, AD_U_ID) VALUES
('SQM', 'متر مربع', 'Square Meter', 'MTK', 5, NULL, 0, 1.0, 1, 3, 1);

INSERT INTO ERP_MEASUREMENT (MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB, MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE, ALLOW_UPD, UNT_SALE_TYP, AD_U_ID) VALUES
('BOX', 'صندوق', 'Box', 'BX', 1, NULL, 0, NULL, 1, 1, 1);

INSERT INTO ERP_MEASUREMENT (MEASURE_CODE, MEASURE, MEASURE_F_NM, MEASURE_CODE_GB, MEASURE_TYPE, MEASURE_WT_TYPE, MEASURE_WT_CONN, DFLT_SIZE, ALLOW_UPD, UNT_SALE_TYP, AD_U_ID) VALUES
('DOZEN', 'دزينة', 'Dozen', 'DZN', 1, NULL, 0, 12.0, 1, 1, 1);

-- تأكيد الحفظ
COMMIT;

-- عرض النتائج
SELECT COUNT(*) AS "عدد وحدات القياس المدرجة" FROM ERP_MEASUREMENT;
SELECT MEASURE_CODE, MEASURE, MEASURE_F_NM FROM ERP_MEASUREMENT ORDER BY MEASURE;
