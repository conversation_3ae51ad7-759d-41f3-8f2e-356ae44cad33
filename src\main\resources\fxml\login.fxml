<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.shipment.erp.controller.LoginController">
   <center>
      <VBox alignment="CENTER" maxHeight="500.0" maxWidth="400.0" spacing="20.0" styleClass="form-container">
         <padding>
            <Insets bottom="40.0" left="40.0" right="40.0" top="40.0" />
         </padding>
         
         <!-- شعار التطبيق -->
         <VBox alignment="CENTER" spacing="10.0">
            <ImageView fitHeight="80.0" fitWidth="80.0" pickOnBounds="true" preserveRatio="true">
               <image>
                  <Image url="@../images/app-logo.png" />
               </image>
            </ImageView>
            <Label styleClass="header" text="%app.title">
               <font>
                  <Font size="24.0" />
               </font>
            </Label>
            <Label text="%app.version" />
         </VBox>
         
         <!-- نموذج تسجيل الدخول -->
         <VBox spacing="15.0">
            <!-- اسم المستخدم -->
            <VBox spacing="5.0">
               <Label styleClass="form-label" text="%user.username" />
               <TextField fx:id="usernameField" promptText="%user.username" />
            </VBox>
            
            <!-- كلمة المرور -->
            <VBox spacing="5.0">
               <Label styleClass="form-label" text="%user.password" />
               <PasswordField fx:id="passwordField" promptText="%user.password" />
            </VBox>
            
            <!-- رسالة الخطأ -->
            <Label fx:id="errorLabel" styleClass="error-message" visible="false" wrapText="true" />
            
            <!-- أزرار -->
            <HBox alignment="CENTER" spacing="10.0">
               <Button fx:id="loginButton" defaultButton="true" onAction="#handleLogin" prefWidth="120.0" styleClass="primary-button" text="%button.login" />
               <Button fx:id="cancelButton" cancelButton="true" onAction="#handleCancel" prefWidth="120.0" text="%button.cancel" />
            </HBox>
         </VBox>
         
         <!-- روابط إضافية -->
         <VBox alignment="CENTER" spacing="5.0">
            <Hyperlink fx:id="forgotPasswordLink" onAction="#handleForgotPassword" text="%link.forgot.password" />
            <Separator />
            <Label text="%login.help.text" />
         </VBox>
      </VBox>
   </center>
   
   <!-- شريط الحالة -->
   <bottom>
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="status-bar">
         <padding>
            <Insets bottom="5.0" left="10.0" right="10.0" top="5.0" />
         </padding>
         <Label fx:id="statusLabel" text="%status.ready" />
         <Region HBox.hgrow="ALWAYS" />
         <Label fx:id="dateTimeLabel" />
      </HBox>
   </bottom>
</BorderPane>
