package com.shipment.erp.model;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * نموذج مجموعة الأصناف
 * Item Category Entity
 */
@Entity
@Table(name = "item_categories")
public class ItemCategory extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "code", unique = true, nullable = false, length = 20)
    private String code;
    
    @Column(name = "name_ar", nullable = false, length = 100)
    private String nameAr;
    
    @Column(name = "name_en", length = 100)
    private String nameEn;
    
    @Column(name = "description", length = 500)
    private String description;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_category_id")
    private ItemCategory parentCategory;
    
    @OneToMany(mappedBy = "parentCategory", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ItemCategory> subCategories = new ArrayList<>();
    
    @Column(name = "level")
    private Integer level = 1;
    
    @Column(name = "path", length = 500)
    private String path;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    @Column(name = "icon", length = 100)
    private String icon;
    
    @Column(name = "color", length = 20)
    private String color;
    
    @Column(name = "notes", length = 1000)
    private String notes;
    
    @Column(name = "has_items")
    private Boolean hasItems = false;
    
    // Constructors
    public ItemCategory() {}
    
    public ItemCategory(String code, String nameAr) {
        this.code = code;
        this.nameAr = nameAr;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getNameAr() {
        return nameAr;
    }
    
    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }
    
    public String getNameEn() {
        return nameEn;
    }
    
    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public ItemCategory getParentCategory() {
        return parentCategory;
    }
    
    public void setParentCategory(ItemCategory parentCategory) {
        this.parentCategory = parentCategory;
        if (parentCategory != null) {
            this.level = parentCategory.getLevel() + 1;
            this.path = parentCategory.getPath() + "/" + this.code;
        } else {
            this.level = 1;
            this.path = this.code;
        }
    }
    
    public List<ItemCategory> getSubCategories() {
        return subCategories;
    }
    
    public void setSubCategories(List<ItemCategory> subCategories) {
        this.subCategories = subCategories;
    }
    
    public Integer getLevel() {
        return level;
    }
    
    public void setLevel(Integer level) {
        this.level = level;
    }
    
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public String getIcon() {
        return icon;
    }
    
    public void setIcon(String icon) {
        this.icon = icon;
    }
    
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public Boolean getHasItems() {
        return hasItems;
    }
    
    public void setHasItems(Boolean hasItems) {
        this.hasItems = hasItems;
    }
    
    // Helper methods
    public void addSubCategory(ItemCategory subCategory) {
        subCategories.add(subCategory);
        subCategory.setParentCategory(this);
    }
    
    public void removeSubCategory(ItemCategory subCategory) {
        subCategories.remove(subCategory);
        subCategory.setParentCategory(null);
    }
    
    public boolean isRoot() {
        return parentCategory == null;
    }
    
    public boolean isLeaf() {
        return subCategories.isEmpty();
    }
    
    @Override
    public String toString() {
        return nameAr + " (" + code + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ItemCategory that = (ItemCategory) obj;
        return id != null && id.equals(that.id);
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
