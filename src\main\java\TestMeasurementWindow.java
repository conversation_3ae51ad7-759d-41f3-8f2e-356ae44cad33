import javax.swing.*;
import java.awt.*;
import java.util.Locale;

/**
 * اختبار نافذة وحدات القياس
 * Test Measurement Units Window
 */
public class TestMeasurementWindow {
    
    public static void main(String[] args) {
        // إعداد النظام للعربية
        System.setProperty("file.encoding", "UTF-8");
        System.setProperty("user.language", "ar");
        System.setProperty("user.country", "SA");
        Locale.setDefault(new Locale("ar", "SA"));
        
        SwingUtilities.invokeLater(() -> {
            try {
                // تطبيق مظهر النظام
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
                
                System.out.println("🔄 اختبار فتح نافذة وحدات القياس...");
                
                // إنشاء النافذة
                MeasurementUnitsWindow window = new MeasurementUnitsWindow();
                window.setVisible(true);
                
                System.out.println("✅ تم فتح نافذة وحدات القياس بنجاح!");
                
            } catch (Exception e) {
                System.err.println("❌ خطأ في فتح النافذة:");
                e.printStackTrace();
                
                JOptionPane.showMessageDialog(null,
                    "خطأ في فتح نافذة وحدات القياس:\n" + e.getMessage(),
                    "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
