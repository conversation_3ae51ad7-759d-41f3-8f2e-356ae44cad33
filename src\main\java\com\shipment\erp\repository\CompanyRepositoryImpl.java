package com.shipment.erp.repository;

import com.shipment.erp.model.Company;
import jakarta.persistence.TypedQuery;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * تطبيق Repository للشركات
 */
@Repository
@Transactional
public class CompanyRepositoryImpl extends BaseRepositoryImpl<Company> implements CompanyRepository {

    @Override
    @Transactional(readOnly = true)
    public Optional<Company> findByName(String name) {
        String queryStr = "SELECT c FROM Company c WHERE c.name = :name";
        TypedQuery<Company> query = entityManager.createQuery(queryStr, Company.class);
        query.setParameter("name", name);
        try {
            return Optional.of(query.getSingleResult());
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Company> findByNameEn(String nameEn) {
        String queryStr = "SELECT c FROM Company c WHERE c.nameEn = :nameEn";
        TypedQuery<Company> query = entityManager.createQuery(queryStr, Company.class);
        query.setParameter("nameEn", nameEn);
        try {
            return Optional.of(query.getSingleResult());
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Company> findByTaxNumber(String taxNumber) {
        String queryStr = "SELECT c FROM Company c WHERE c.taxNumber = :taxNumber";
        TypedQuery<Company> query = entityManager.createQuery(queryStr, Company.class);
        query.setParameter("taxNumber", taxNumber);
        try {
            return Optional.of(query.getSingleResult());
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Company> findByCommercialRegister(String commercialRegister) {
        String queryStr = "SELECT c FROM Company c WHERE c.commercialRegister = :commercialRegister";
        TypedQuery<Company> query = entityManager.createQuery(queryStr, Company.class);
        query.setParameter("commercialRegister", commercialRegister);
        try {
            return Optional.of(query.getSingleResult());
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Company> findByCity(String city) {
        String queryStr = "SELECT c FROM Company c WHERE c.city = :city ORDER BY c.name";
        TypedQuery<Company> query = entityManager.createQuery(queryStr, Company.class);
        query.setParameter("city", city);
        return query.getResultList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Company> findByCountry(String country) {
        String queryStr = "SELECT c FROM Company c WHERE c.country = :country ORDER BY c.name";
        TypedQuery<Company> query = entityManager.createQuery(queryStr, Company.class);
        query.setParameter("country", country);
        return query.getResultList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Company> findByNameContaining(String name) {
        String queryStr = "SELECT c FROM Company c WHERE LOWER(c.name) LIKE LOWER(:name) " +
                         "OR LOWER(c.nameEn) LIKE LOWER(:name) ORDER BY c.name";
        TypedQuery<Company> query = entityManager.createQuery(queryStr, Company.class);
        query.setParameter("name", "%" + name + "%");
        return query.getResultList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Company> findActiveCompanies() {
        String queryStr = "SELECT c FROM Company c WHERE c.isActive = true ORDER BY c.name";
        TypedQuery<Company> query = entityManager.createQuery(queryStr, Company.class);
        return query.getResultList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Company> findInactiveCompanies() {
        String queryStr = "SELECT c FROM Company c WHERE c.isActive = false ORDER BY c.name";
        TypedQuery<Company> query = entityManager.createQuery(queryStr, Company.class);
        return query.getResultList();
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByName(String name) {
        String queryStr = "SELECT COUNT(c) FROM Company c WHERE c.name = :name";
        TypedQuery<Long> query = entityManager.createQuery(queryStr, Long.class);
        query.setParameter("name", name);
        return query.getSingleResult() > 0;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByTaxNumber(String taxNumber) {
        if (taxNumber == null || taxNumber.trim().isEmpty()) {
            return false;
        }
        String queryStr = "SELECT COUNT(c) FROM Company c WHERE c.taxNumber = :taxNumber";
        TypedQuery<Long> query = entityManager.createQuery(queryStr, Long.class);
        query.setParameter("taxNumber", taxNumber);
        return query.getSingleResult() > 0;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByCommercialRegister(String commercialRegister) {
        if (commercialRegister == null || commercialRegister.trim().isEmpty()) {
            return false;
        }
        String queryStr = "SELECT COUNT(c) FROM Company c WHERE c.commercialRegister = :commercialRegister";
        TypedQuery<Long> query = entityManager.createQuery(queryStr, Long.class);
        query.setParameter("commercialRegister", commercialRegister);
        return query.getSingleResult() > 0;
    }

    @Override
    @Transactional(readOnly = true)
    public long countActiveCompanies() {
        String queryStr = "SELECT COUNT(c) FROM Company c WHERE c.isActive = true";
        TypedQuery<Long> query = entityManager.createQuery(queryStr, Long.class);
        return query.getSingleResult();
    }

    @Override
    @Transactional(readOnly = true)
    public long countInactiveCompanies() {
        String queryStr = "SELECT COUNT(c) FROM Company c WHERE c.isActive = false";
        TypedQuery<Long> query = entityManager.createQuery(queryStr, Long.class);
        return query.getSingleResult();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Company> advancedSearch(String name, String city, String country, Boolean isActive) {
        StringBuilder queryStr = new StringBuilder("SELECT c FROM Company c WHERE 1=1");
        
        if (name != null && !name.trim().isEmpty()) {
            queryStr.append(" AND (LOWER(c.name) LIKE LOWER(:name) OR LOWER(c.nameEn) LIKE LOWER(:name))");
        }
        if (city != null && !city.trim().isEmpty()) {
            queryStr.append(" AND LOWER(c.city) LIKE LOWER(:city)");
        }
        if (country != null && !country.trim().isEmpty()) {
            queryStr.append(" AND LOWER(c.country) LIKE LOWER(:country)");
        }
        if (isActive != null) {
            queryStr.append(" AND c.isActive = :isActive");
        }
        
        queryStr.append(" ORDER BY c.name");
        
        TypedQuery<Company> query = entityManager.createQuery(queryStr.toString(), Company.class);
        
        if (name != null && !name.trim().isEmpty()) {
            query.setParameter("name", "%" + name + "%");
        }
        if (city != null && !city.trim().isEmpty()) {
            query.setParameter("city", "%" + city + "%");
        }
        if (country != null && !country.trim().isEmpty()) {
            query.setParameter("country", "%" + country + "%");
        }
        if (isActive != null) {
            query.setParameter("isActive", isActive);
        }
        
        return query.getResultList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Company> findCompaniesWithBranches() {
        String queryStr = "SELECT DISTINCT c FROM Company c JOIN c.branches b WHERE c.isActive = true ORDER BY c.name";
        TypedQuery<Company> query = entityManager.createQuery(queryStr, Company.class);
        return query.getResultList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Company> findCompaniesWithoutBranches() {
        String queryStr = "SELECT c FROM Company c WHERE c.branches IS EMPTY AND c.isActive = true ORDER BY c.name";
        TypedQuery<Company> query = entityManager.createQuery(queryStr, Company.class);
        return query.getResultList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Company> search(String searchText, int page, int size) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return findAll(page, size);
        }
        
        String queryStr = "SELECT c FROM Company c WHERE " +
                         "LOWER(c.name) LIKE LOWER(:searchText) OR " +
                         "LOWER(c.nameEn) LIKE LOWER(:searchText) OR " +
                         "LOWER(c.city) LIKE LOWER(:searchText) OR " +
                         "LOWER(c.country) LIKE LOWER(:searchText) OR " +
                         "LOWER(c.taxNumber) LIKE LOWER(:searchText) OR " +
                         "LOWER(c.commercialRegister) LIKE LOWER(:searchText) " +
                         "ORDER BY c.name";
        
        TypedQuery<Company> query = entityManager.createQuery(queryStr, Company.class);
        query.setParameter("searchText", "%" + searchText + "%");
        query.setFirstResult(page * size);
        query.setMaxResults(size);
        
        return query.getResultList();
    }

    @Override
    @Transactional(readOnly = true)
    public long countSearch(String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return count();
        }
        
        String queryStr = "SELECT COUNT(c) FROM Company c WHERE " +
                         "LOWER(c.name) LIKE LOWER(:searchText) OR " +
                         "LOWER(c.nameEn) LIKE LOWER(:searchText) OR " +
                         "LOWER(c.city) LIKE LOWER(:searchText) OR " +
                         "LOWER(c.country) LIKE LOWER(:searchText) OR " +
                         "LOWER(c.taxNumber) LIKE LOWER(:searchText) OR " +
                         "LOWER(c.commercialRegister) LIKE LOWER(:searchText)";
        
        TypedQuery<Long> query = entityManager.createQuery(queryStr, Long.class);
        query.setParameter("searchText", "%" + searchText + "%");
        
        return query.getSingleResult();
    }
}
