/**
 * اختبار نظام حفظ وتحميل الإعدادات
 * Test Settings Save and Load System
 */
public class TestSettings {
    
    public static void main(String[] args) {
        System.out.println("=== اختبار نظام الإعدادات ===");
        
        // اختبار حفظ الإعدادات
        System.out.println("1. حفظ إعدادات تجريبية...");
        SettingsManager.saveSettings("مظلم", "العربية");
        
        // اختبار تحميل الإعدادات
        System.out.println("2. تحميل الإعدادات...");
        String savedTheme = SettingsManager.getSavedTheme();
        String savedLanguage = SettingsManager.getSavedLanguage();
        
        System.out.println("المظهر المحفوظ: " + savedTheme);
        System.out.println("اللغة المحفوظة: " + savedLanguage);
        
        // اختبار تطبيق المظهر
        System.out.println("3. تطبيق المظهر المحفوظ...");
        SettingsManager.applyStoredTheme();
        
        System.out.println("=== انتهى الاختبار ===");
    }
}
