# دليل البدء السريع - Ship ERP System

## 🚀 البدء السريع (5 دقائق)

### المتطلبات الأساسية
- ☑️ Java JDK 17+
- ☑️ Apache Maven 3.8+
- ☑️ Oracle Database (XE أو 19c+)

### خطوات التشغيل السريع

#### 1️⃣ فحص المتطلبات
```bash
check-requirements.bat
```
✅ **النتيجة المتوقعة:** جميع المتطلبات متوفرة

#### 2️⃣ إعداد قاعدة البيانات
```bash
setup-database.bat
```
✅ **النتيجة المتوقعة:** تم إنشاء قاعدة البيانات والجداول

#### 3️⃣ تشغيل التطبيق
```bash
run-ship-erp.bat
```
✅ **النتيجة المتوقعة:** فتح نافذة تسجيل الدخول

### 🔐 تسجيل الدخول الافتراضي
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

## 📋 قائمة التحقق السريع

### ✅ قبل البدء
- [ ] تثبيت Java JDK 17 (`java -version`)
- [ ] تثبيت Apache Maven (`mvn -version`)
- [ ] تثبيت Oracle Database
- [ ] تعيين متغيرات البيئة (JAVA_HOME, M2_HOME)

### ✅ بعد التثبيت
- [ ] فحص المتطلبات نجح ✅
- [ ] إعداد قاعدة البيانات نجح ✅
- [ ] تشغيل التطبيق نجح ✅
- [ ] تسجيل الدخول نجح ✅

## ⚙️ إعدادات سريعة (أول استخدام)

### 🏢 إعداد بيانات الشركة
1. انتقل إلى: **الإعدادات** > **بيانات الشركة**
2. أدخل معلومات شركتك:
   - اسم الشركة
   - العنوان
   - رقم الهاتف
   - البريد الإلكتروني
   - الرقم الضريبي
3. احفظ التغييرات

### 👤 إضافة مستخدم جديد
1. انتقل إلى: **الإعدادات** > **المستخدمين**
2. اضغط **"إضافة مستخدم"**
3. أدخل البيانات المطلوبة:
   - اسم المستخدم
   - كلمة المرور
   - الاسم الكامل
   - البريد الإلكتروني
4. اختر الدور المناسب
5. احفظ

### 💰 إعداد العملات
1. انتقل إلى: **الإعدادات** > **العملات**
2. أضف العملات المطلوبة:
   - الريال السعودي (SAR)
   - الدولار الأمريكي (USD)
   - اليورو (EUR)
3. حدد العملة الافتراضية
4. أدخل أسعار الصرف

### 📅 إعداد السنة المالية
1. انتقل إلى: **الإعدادات** > **السنة المالية**
2. أنشئ سنة مالية جديدة:
   - اسم السنة (مثل: 2025)
   - تاريخ البداية (01/01/2025)
   - تاريخ النهاية (31/12/2025)
3. فعّل السنة المالية

## 🎯 الخطوات التالية

بعد الإعداد الأساسي، يمكنك:

1. **استكشاف الواجهة**
   - تصفح القائمة الشجرية
   - جرب فتح التبويبات المختلفة
   - تعرف على شريط الأدوات

2. **إعداد الصلاحيات**
   - أنشئ أدوار جديدة
   - حدد الصلاحيات لكل دور
   - اربط المستخدمين بالأدوار

3. **إعداد الفروع**
   - أضف فروع الشركة
   - حدد مدير لكل فرع
   - اربط المستخدمين بالفروع

## ❗ استكشاف الأخطاء السريع

### 🔴 Java غير موجود
```bash
# تحقق من التثبيت
java -version

# إذا لم يعمل، تأكد من JAVA_HOME
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-17.x.x
set PATH=%JAVA_HOME%\bin;%PATH%
```

### 🔴 Maven غير موجود
```bash
# تحقق من التثبيت
mvn -version

# إذا لم يعمل، أضف إلى PATH
set M2_HOME=C:\apache-maven-3.9.x
set PATH=%M2_HOME%\bin;%PATH%
```

### 🔴 قاعدة البيانات لا تعمل
```bash
# تحقق من خدمة Oracle
net start OracleServiceXE

# اختبار الاتصال
sqlplus system/password@localhost:1521/XE

# إذا فشل، تحقق من:
# 1. تثبيت Oracle صحيح
# 2. الخدمة تعمل
# 3. المنفذ 1521 مفتوح
```

### 🔴 خطأ في تسجيل الدخول
- تأكد من اسم المستخدم: `admin`
- تأكد من كلمة المرور: `admin123`
- تحقق من إعداد قاعدة البيانات
- راجع ملف السجل: `logs/ship-erp.log`

### 🔴 مشاكل الترميز العربي
```bash
# في Command Prompt
chcp 65001

# تأكد من حفظ الملفات بـ UTF-8
# تحقق من إعدادات IDE
```

## 📊 مؤشرات النجاح

### ✅ التطبيق يعمل بنجاح إذا:
- [x] نافذة تسجيل الدخول تظهر بالعربية
- [x] تسجيل الدخول يعمل
- [x] الواجهة الرئيسية تظهر
- [x] القائمة الشجرية تعمل
- [x] يمكن فتح نوافذ الإعدادات
- [x] البيانات تُحفظ في قاعدة البيانات

## 🔧 أوامر مفيدة

### بناء المشروع
```bash
# تنظيف وبناء
mvn clean compile

# تشغيل الاختبارات
mvn test

# إنشاء JAR
mvn clean package
```

### تشغيل التطبيق
```bash
# تشغيل مع Maven
mvn javafx:run

# تشغيل JAR مباشرة
java -jar target/ship-erp-system-1.0.0.jar

# تشغيل مع تصحيح الأخطاء
mvn javafx:run -Dlogging.level.com.shipment.erp=DEBUG
```

### إدارة قاعدة البيانات
```bash
# الاتصال بقاعدة البيانات
sqlplus ship_erp/ship_erp_password@localhost:1521/XE

# نسخ احتياطي
expdp ship_erp/ship_erp_password directory=backup_dir dumpfile=ship_erp_backup.dmp

# استعادة
impdp ship_erp/ship_erp_password directory=backup_dir dumpfile=ship_erp_backup.dmp
```

## 📞 الحصول على المساعدة

### 📚 مراجع سريعة
- 📖 `README_COMPLETE.md` - الدليل الشامل
- 📋 `INSTALLATION_GUIDE.md` - دليل التثبيت المفصل
- 🔧 `logs/ship-erp.log` - ملف السجل الرئيسي

### 🆘 إذا واجهت مشاكل
1. **تحقق من ملفات السجل** في مجلد `logs/`
2. **راجع الأخطاء الشائعة** في هذا الدليل
3. **تأكد من المتطلبات** باستخدام `check-requirements.bat`
4. **أعد تشغيل** قاعدة البيانات والتطبيق

### 📋 معلومات مفيدة للدعم
عند طلب المساعدة، قدم:
- إصدار Java (`java -version`)
- إصدار Maven (`mvn -version`)
- نوع قاعدة البيانات وإصدارها
- نظام التشغيل
- رسالة الخطأ الكاملة
- محتوى ملف السجل

## 🎉 تهانينا!

إذا وصلت إلى هنا، فأنت الآن جاهز لاستخدام **نظام إدارة الشحنات**!

### الخطوات التالية:
1. 🏢 أكمل إعداد بيانات شركتك
2. 👥 أضف المستخدمين والأدوار
3. 💰 اضبط العملات وأسعار الصرف
4. 📅 فعّل السنة المالية
5. 🚀 ابدأ في استخدام النظام

---

**مرحباً بك في عالم إدارة الشحنات الاحترافية! 🚢**
