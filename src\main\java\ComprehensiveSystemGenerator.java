import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.GridLayout;
import java.awt.Insets;
import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Properties;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.SwingUtilities;
import javax.swing.SwingWorker;
import javax.swing.border.TitledBorder;

/**
 * أداة توليد التقرير الشامل ومولد النظام Comprehensive System Generator and Report Tool
 */
public class ComprehensiveSystemGenerator extends JFrame {

    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 12);

    private JTextArea logArea;
    private JProgressBar progressBar;
    private Connection connection;

    // ألوان الواجهة
    private Color primaryColor = new Color(52, 152, 219);
    private Color successColor = new Color(46, 204, 113);
    private Color warningColor = new Color(241, 196, 15);
    private Color dangerColor = new Color(231, 76, 60);

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new ComprehensiveSystemGenerator().setVisible(true);
        });
    }

    public ComprehensiveSystemGenerator() {
        // إصلاح regex فوراً
        fixRegexIssue();

        initializeComponents();
        connectToDatabase();
    }

    /**
     * إصلاح مشكلة regex فوراً
     */
    private void fixRegexIssue() {
        try {
            Locale.setDefault(Locale.ENGLISH);
            System.setProperty("user.language", "en");
            System.setProperty("user.country", "US");
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("oracle.jdbc.autoCommitSpecCompliant", "false");
            System.setProperty("oracle.jdbc.timezoneAsRegion", "false");
        } catch (Exception e) {
            // تجاهل الأخطاء
        }
    }

    private void initializeComponents() {
        setTitle("🏗️ أداة توليد التقرير الشامل ومولد النظام - Comprehensive System Generator");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1400, 900);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // لوحة الأزرار العلوية
        JPanel topPanel = createTopPanel();
        mainPanel.add(topPanel, BorderLayout.NORTH);

        // اللوحة الوسطى - تبويبات
        JTabbedPane tabbedPane = createTabbedPane();
        mainPanel.add(tabbedPane, BorderLayout.CENTER);

        // شريط التقدم
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setFont(arabicFont);
        progressBar.setString("جاهز للبدء");
        mainPanel.add(progressBar, BorderLayout.SOUTH);

        add(mainPanel);
    }

    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 4, 10, 10));
        panel.setBorder(BorderFactory.createTitledBorder(BorderFactory.createEtchedBorder(),
                "أدوات التوليد الرئيسية", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        // الصف الأول - أدوات التقرير
        JButton generateReportBtn = createStyledButton("📄 تقرير شامل", successColor);
        generateReportBtn.addActionListener(e -> generateComprehensiveReport());

        JButton exportSQLBtn = createStyledButton("🏗️ سكريبتات SQL", primaryColor);
        exportSQLBtn.addActionListener(e -> generateSQLScripts());

        JButton compareDataBtn = createStyledButton("📊 مقارنة البيانات", warningColor);
        compareDataBtn.addActionListener(e -> compareTableData());

        JButton importToolBtn = createStyledButton("🔄 أداة الاستيراد", new Color(155, 89, 182));
        importToolBtn.addActionListener(e -> openAdvancedImportTool());

        // الصف الثاني - أدوات إضافية
        JButton analyzeStructureBtn = createStyledButton("🔍 تحليل البنية", new Color(52, 73, 94));
        analyzeStructureBtn.addActionListener(e -> analyzeCompleteStructure());

        JButton generateDocsBtn = createStyledButton("📚 توليد الوثائق", new Color(230, 126, 34));
        generateDocsBtn.addActionListener(e -> generateDocumentation());

        JButton backupBtn = createStyledButton("💾 نسخ احتياطي", new Color(39, 174, 96));
        backupBtn.addActionListener(e -> createBackupScripts());

        JButton clearBtn = createStyledButton("🗑️ مسح السجل", dangerColor);
        clearBtn.addActionListener(e -> logArea.setText(""));

        panel.add(generateReportBtn);
        panel.add(exportSQLBtn);
        panel.add(compareDataBtn);
        panel.add(importToolBtn);
        panel.add(analyzeStructureBtn);
        panel.add(generateDocsBtn);
        panel.add(backupBtn);
        panel.add(clearBtn);

        return panel;
    }

    private JTabbedPane createTabbedPane() {
        JTabbedPane tabbedPane = new JTabbedPane();
        tabbedPane.setFont(arabicBoldFont);

        // تبويب السجل
        logArea = new JTextArea();
        logArea.setFont(arabicFont);
        logArea.setEditable(false);
        logArea.setBackground(Color.BLACK);
        logArea.setForeground(Color.GREEN);

        JScrollPane logScrollPane = new JScrollPane(logArea);
        logScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        tabbedPane.addTab("📋 سجل العمليات", logScrollPane);

        // تبويب الإعدادات
        JPanel settingsPanel = createSettingsPanel();
        tabbedPane.addTab("⚙️ الإعدادات", settingsPanel);

        // تبويب النتائج
        JPanel resultsPanel = createResultsPanel();
        tabbedPane.addTab("📊 النتائج", resultsPanel);

        return tabbedPane;
    }

    private JPanel createSettingsPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        // إعدادات قاعدة البيانات الجديدة
        gbc.gridx = 0;
        gbc.gridy = 0;
        panel.add(new JLabel("اسم قاعدة البيانات الجديدة:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        JTextField newDbNameField = new JTextField("ship_erp", 20);
        newDbNameField.setFont(arabicFont);
        panel.add(newDbNameField, gbc);

        // بادئة الجداول
        gbc.gridx = 0;
        gbc.gridy = 1;
        panel.add(new JLabel("بادئة الجداول:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        JTextField tablePrefixField = new JTextField("ERP_", 20);
        tablePrefixField.setFont(arabicFont);
        panel.add(tablePrefixField, gbc);

        // مسار التصدير
        gbc.gridx = 0;
        gbc.gridy = 2;
        panel.add(new JLabel("مسار التصدير:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        JTextField exportPathField = new JTextField("./exports/", 20);
        exportPathField.setFont(arabicFont);
        panel.add(exportPathField, gbc);

        return panel;
    }

    private JPanel createResultsPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        JTextArea resultsArea = new JTextArea();
        resultsArea.setFont(arabicFont);
        resultsArea.setEditable(false);
        resultsArea.setText("النتائج ستظهر هنا بعد تشغيل العمليات...");

        JScrollPane scrollPane = new JScrollPane(resultsArea);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setFont(arabicBoldFont);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setOpaque(true);
        return button;
    }

    private void connectToDatabase() {
        appendLog("🔄 بدء الاتصال بقاعدة البيانات IAS20251...");

        SwingWorker<Boolean, String> worker = new SwingWorker<Boolean, String>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                try {
                    String url = "*************************************";
                    String username = "ias20251";
                    String password = "ys123";

                    publish("📋 إعدادات الاتصال: " + url);
                    publish("👤 المستخدم: " + username);

                    Class.forName("oracle.jdbc.driver.OracleDriver");
                    publish("✅ تم تحميل تعريف Oracle JDBC");

                    Properties props = new Properties();
                    props.setProperty("user", username);
                    props.setProperty("password", password);
                    props.setProperty("oracle.jdbc.ReadTimeout", "30000");

                    connection = DriverManager.getConnection(url, props);

                    if (connection != null && !connection.isClosed()) {
                        publish("✅ نجح الاتصال بقاعدة البيانات!");

                        DatabaseMetaData metaData = connection.getMetaData();
                        publish("📋 " + metaData.getDatabaseProductName() + " "
                                + metaData.getDatabaseProductVersion());

                        return true;
                    }

                    return false;

                } catch (Exception e) {
                    publish("❌ خطأ في الاتصال: " + e.getMessage());
                    return false;
                }
            }

            @Override
            protected void process(List<String> chunks) {
                for (String message : chunks) {
                    appendLog(message);
                }
            }

            @Override
            protected void done() {
                try {
                    boolean success = get();
                    if (success) {
                        appendLog("🎉 النظام جاهز للاستخدام!");
                        progressBar.setString("متصل - جاهز للعمل");
                        progressBar.setForeground(successColor);
                    } else {
                        appendLog("❌ فشل الاتصال - تحقق من الإعدادات");
                        progressBar.setString("فشل الاتصال");
                        progressBar.setForeground(dangerColor);
                    }
                } catch (Exception e) {
                    appendLog("❌ خطأ: " + e.getMessage());
                }
            }
        };

        worker.execute();
    }

    private void appendLog(String message) {
        SwingUtilities.invokeLater(() -> {
            String timestamp = new SimpleDateFormat("HH:mm:ss").format(new Date());
            logArea.append("[" + timestamp + "] " + message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
    }

    // الوظائف الرئيسية - سيتم تطويرها في الخطوات التالية
    private void generateComprehensiveReport() {
        if (connection == null) {
            appendLog("❌ لا يوجد اتصال بقاعدة البيانات");
            return;
        }

        appendLog("🚀 بدء توليد التقرير الشامل...");
        progressBar.setString("جاري توليد التقرير...");

        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                try {
                    // إنشاء مجلد التصدير
                    File exportDir = new File("./exports");
                    if (!exportDir.exists()) {
                        exportDir.mkdirs();
                    }

                    String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
                    String reportFileName = "IAS20251_Comprehensive_Report_" + timestamp + ".md";
                    File reportFile = new File(exportDir, reportFileName);

                    try (PrintWriter writer = new PrintWriter(new FileWriter(reportFile, true))) {

                        // كتابة رأس التقرير
                        writeReportHeader(writer);

                        // تحليل جميع الجداول
                        publish("📋 تحليل جميع الجداول...");
                        analyzeAllTablesForReport(writer);

                        // تحليل العلاقات
                        publish("🔗 تحليل العلاقات والمفاتيح...");
                        analyzeRelationshipsForReport(writer);

                        // تحليل البنية
                        publish("🏗️ تحليل بنية الجداول الرئيسية...");
                        analyzeStructureForReport(writer);

                        // إحصائيات مفصلة
                        publish("📊 توليد الإحصائيات المفصلة...");
                        generateDetailedStatistics(writer);

                        // توصيات للنظام الجديد
                        publish("💡 إنشاء التوصيات...");
                        generateRecommendations(writer);

                        writer.flush();
                    }

                    publish("✅ تم حفظ التقرير في: " + reportFile.getAbsolutePath());

                } catch (SQLException e) {
                    publish("❌ خطأ SQL في توليد التقرير:");
                    publish("   الرمز: " + e.getErrorCode());
                    publish("   الرسالة: " + e.getMessage());
                    e.printStackTrace();
                } catch (Exception e) {
                    publish("❌ خطأ عام في توليد التقرير: " + e.getMessage());
                    e.printStackTrace();
                }

                return null;
            }

            @Override
            protected void process(List<String> chunks) {
                for (String message : chunks) {
                    appendLog(message);
                }
            }

            @Override
            protected void done() {
                progressBar.setString("تم إكمال التقرير");
                appendLog("🎉 تم إكمال توليد التقرير الشامل!");
            }
        };

        worker.execute();
    }

    private void writeReportHeader(PrintWriter writer) {
        writer.println("# 📊 التقرير الشامل لقاعدة البيانات IAS20251");
        writer.println("## Comprehensive Analysis Report for IAS20251 Database");
        writer.println();
        writer.println("**تاريخ التقرير:** "
                + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        writer.println("**الهدف:** تحليل شامل لبنية قاعدة البيانات لبناء نظام ERP جديد");
        writer.println();
        writer.println("---");
        writer.println();
    }

    private void analyzeAllTablesForReport(PrintWriter writer) throws SQLException {
        writer.println("## 📋 تحليل جميع الجداول");
        writer.println();

        String query = """
                    SELECT table_name, num_rows, blocks, avg_row_len, last_analyzed
                    FROM all_tables
                    WHERE owner = 'IAS20251'
                    ORDER BY table_name
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            writer.println("| اسم الجدول | عدد الصفوف | الكتل | متوسط طول الصف | آخر تحليل |");
            writer.println("|------------|------------|-------|----------------|-----------|");

            int tableCount = 0;
            while (rs.next()) {
                String tableName = rs.getString("table_name");
                String numRows = rs.getString("num_rows");
                String blocks = rs.getString("blocks");
                String avgRowLen = rs.getString("avg_row_len");
                String lastAnalyzed = rs.getString("last_analyzed");

                writer.printf("| %s | %s | %s | %s | %s |\n", tableName,
                        numRows != null ? String.format("%,d", Integer.parseInt(numRows))
                                : "غير محدد",
                        blocks != null ? blocks : "-", avgRowLen != null ? avgRowLen : "-",
                        lastAnalyzed != null ? lastAnalyzed.substring(0, 10) : "غير محدد");

                tableCount++;
            }

            writer.println();
            writer.println("**إجمالي الجداول:** " + tableCount);
            writer.println();
        }
    }

    private void analyzeRelationshipsForReport(PrintWriter writer) throws SQLException {
        writer.println("## 🔗 تحليل العلاقات والمفاتيح");
        writer.println();

        try {
            // المفاتيح الخارجية - استعلام مبسط
            writer.println("### المفاتيح الخارجية:");
            writer.println();

            String simpleFkQuery = """
                        SELECT
                            constraint_name,
                            table_name,
                            r_constraint_name
                        FROM all_constraints
                        WHERE constraint_type = 'R'
                        AND owner = 'IAS20251'
                        ORDER BY table_name
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(simpleFkQuery);
                    ResultSet rs = stmt.executeQuery()) {

                writer.println("| الجدول | اسم القيد | يشير إلى قيد |");
                writer.println("|--------|-----------|-------------|");

                int fkCount = 0;
                while (rs.next()) {
                    writer.printf("| %s | %s | %s |\n", rs.getString("table_name"),
                            rs.getString("constraint_name"),
                            rs.getString("r_constraint_name") != null
                                    ? rs.getString("r_constraint_name")
                                    : "-");
                    fkCount++;
                }

                writer.println();
                writer.println("**إجمالي المفاتيح الخارجية:** " + fkCount);
                writer.println();

                if (fkCount == 0) {
                    writer.println("*ملاحظة: لا توجد مفاتيح خارجية مُعرَّفة في schema IAS20251*");
                    writer.println();
                }
            }

        } catch (SQLException e) {
            writer.println("❌ خطأ في تحليل المفاتيح الخارجية: " + e.getMessage());
            writer.println();
        }

        try {
            // المفاتيح الأساسية - استعلام مبسط
            writer.println("### المفاتيح الأساسية:");
            writer.println();

            String simplePkQuery = """
                        SELECT
                            table_name,
                            constraint_name
                        FROM all_constraints
                        WHERE constraint_type = 'P'
                        AND owner = 'IAS20251'
                        ORDER BY table_name
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(simplePkQuery);
                    ResultSet rs = stmt.executeQuery()) {

                writer.println("| الجدول | اسم المفتاح الأساسي |");
                writer.println("|--------|-------------------|");

                int pkCount = 0;
                while (rs.next()) {
                    writer.printf("| %s | %s |\n", rs.getString("table_name"),
                            rs.getString("constraint_name"));
                    pkCount++;
                }

                writer.println();
                writer.println("**إجمالي الجداول مع مفاتيح أساسية:** " + pkCount);
                writer.println();
            }

        } catch (SQLException e) {
            writer.println("❌ خطأ في تحليل المفاتيح الأساسية: " + e.getMessage());
            writer.println();
        }

        // تحليل الفهارس
        try {
            writer.println("### الفهارس:");
            writer.println();

            String indexQuery = """
                        SELECT
                            index_name,
                            table_name,
                            uniqueness
                        FROM all_indexes
                        WHERE owner = 'IAS20251'
                        AND index_type = 'NORMAL'
                        ORDER BY table_name, index_name
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(indexQuery);
                    ResultSet rs = stmt.executeQuery()) {

                writer.println("| الجدول | اسم الفهرس | فريد |");
                writer.println("|--------|------------|------|");

                int indexCount = 0;
                while (rs.next()) {
                    writer.printf("| %s | %s | %s |\n", rs.getString("table_name"),
                            rs.getString("index_name"),
                            "UNIQUE".equals(rs.getString("uniqueness")) ? "نعم" : "لا");
                    indexCount++;
                }

                writer.println();
                writer.println("**إجمالي الفهارس:** " + indexCount);
                writer.println();
            }

        } catch (SQLException e) {
            writer.println("❌ خطأ في تحليل الفهارس: " + e.getMessage());
            writer.println();
        }
    }

    private void analyzeStructureForReport(PrintWriter writer) throws SQLException {
        writer.println("## 🏗️ تحليل بنية الجداول الرئيسية");
        writer.println();

        String[] mainTables = {"IAS_ITM_MST", "IAS_ITM_DTL", "IAS_CUSTOMER", "IAS_VENDOR"};

        for (String tableName : mainTables) {
            writer.println("### 📋 جدول: " + tableName);
            writer.println();

            // فحص وجود الجدول
            String checkQuery =
                    "SELECT COUNT(*) as count FROM all_tables WHERE owner = 'IAS20251' AND table_name = ?";

            try (PreparedStatement checkStmt = connection.prepareStatement(checkQuery)) {
                checkStmt.setString(1, tableName);
                try (ResultSet checkRs = checkStmt.executeQuery()) {
                    if (checkRs.next() && checkRs.getInt("count") == 0) {
                        writer.println("❌ الجدول غير موجود");
                        writer.println();
                        continue;
                    }
                }
            }

            // تحليل أعمدة الجدول
            String query = """
                        SELECT
                            column_name,
                            data_type,
                            data_length,
                            data_precision,
                            data_scale,
                            nullable,
                            column_id
                        FROM all_tab_columns
                        WHERE owner = 'IAS20251' AND table_name = ?
                        ORDER BY column_id
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(query)) {
                stmt.setString(1, tableName);

                try (ResultSet rs = stmt.executeQuery()) {
                    writer.println("| العمود | نوع البيانات | الطول | يقبل NULL |");
                    writer.println("|--------|---------------|-------|-----------|");

                    int columnCount = 0;
                    while (rs.next()) {
                        String columnName = rs.getString("column_name");
                        String dataType = rs.getString("data_type");
                        String dataLength = rs.getString("data_length");
                        String dataPrecision = rs.getString("data_precision");
                        String dataScale = rs.getString("data_scale");
                        String nullable = rs.getString("nullable");

                        // تنسيق نوع البيانات
                        String typeInfo = dataType;
                        if (dataPrecision != null) {
                            typeInfo += "(" + dataPrecision;
                            if (dataScale != null && !dataScale.equals("0")) {
                                typeInfo += "," + dataScale;
                            }
                            typeInfo += ")";
                        } else if (dataLength != null && !dataType.equals("DATE")) {
                            typeInfo += "(" + dataLength + ")";
                        }

                        writer.printf("| %s | %s | %s | %s |\n", columnName, typeInfo,
                                dataLength != null ? dataLength : "-",
                                "Y".equals(nullable) ? "نعم" : "لا");

                        columnCount++;
                    }

                    writer.println();
                    writer.println("**عدد الأعمدة:** " + columnCount);

                    // إحصائيات الجدول
                    String statsQuery = "SELECT COUNT(*) as row_count FROM IAS20251." + tableName;
                    try (PreparedStatement statsStmt = connection.prepareStatement(statsQuery);
                            ResultSet statsRs = statsStmt.executeQuery()) {

                        if (statsRs.next()) {
                            int rowCount = statsRs.getInt("row_count");
                            writer.println("**عدد الصفوف:** " + String.format("%,d", rowCount));
                        }
                    }

                    writer.println();
                }
            } catch (SQLException e) {
                writer.println("❌ خطأ في تحليل الجدول: " + e.getMessage());
                writer.println();
            }
        }
    }

    private void generateDetailedStatistics(PrintWriter writer) throws SQLException {
        writer.println("## 📊 الإحصائيات المفصلة");
        writer.println();

        // إحصائيات عامة
        writer.println("### 🏢 إحصائيات عامة:");
        writer.println();

        String tablesQuery =
                "SELECT COUNT(*) as table_count FROM all_tables WHERE owner = 'IAS20251'";
        try (PreparedStatement stmt = connection.prepareStatement(tablesQuery);
                ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                int tableCount = rs.getInt("table_count");
                writer.println("- **إجمالي الجداول:** " + tableCount);
            }
        }

        // إحصائيات الجداول الرئيسية
        writer.println("- **الجداول الرئيسية:**");
        writer.println();

        String[] mainTables = {"IAS_ITM_MST", "IAS_ITM_DTL", "IAS_CUSTOMER", "IAS_VENDOR",
                "IAS_INVOICE", "IAS_RECEIPT", "IAS_PAYMENT", "IAS_STOCK"};

        long totalRows = 0;
        int existingTables = 0;

        for (String tableName : mainTables) {
            try {
                String query =
                        String.format("SELECT COUNT(*) as total FROM IAS20251.%s", tableName);
                try (PreparedStatement stmt = connection.prepareStatement(query);
                        ResultSet rs = stmt.executeQuery()) {

                    if (rs.next()) {
                        int total = rs.getInt("total");
                        totalRows += total;
                        existingTables++;

                        String status = total > 0 ? "✅" : "⚪";
                        writer.println("  " + status + " **" + tableName + ":** "
                                + String.format("%,d", total) + " صف");
                    }
                }
            } catch (SQLException e) {
                writer.println("  ❌ **" + tableName + ":** غير متاح");
            }
        }

        writer.println();
        writer.println("- **ملخص الإحصائيات:**");
        writer.println("  - الجداول الموجودة: " + existingTables + "/" + mainTables.length);
        writer.println("  - إجمالي الصفوف: " + String.format("%,d", totalRows));
        writer.println();

        // إحصائيات خاصة بالأصناف
        generateItemsStatisticsForReport(writer);
    }

    private void generateItemsStatisticsForReport(PrintWriter writer) throws SQLException {
        writer.println("### 🏷️ إحصائيات مفصلة للأصناف:");
        writer.println();

        try {
            String query = """
                        SELECT
                            COUNT(*) as total_items,
                            COUNT(CASE WHEN INACTIVE = 0 THEN 1 END) as active_items,
                            COUNT(CASE WHEN INACTIVE = 1 THEN 1 END) as inactive_items,
                            MIN(AD_DATE) as oldest_date,
                            MAX(AD_DATE) as newest_date
                        FROM IAS20251.IAS_ITM_MST
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(query);
                    ResultSet rs = stmt.executeQuery()) {

                if (rs.next()) {
                    int total = rs.getInt("total_items");
                    int active = rs.getInt("active_items");
                    int inactive = rs.getInt("inactive_items");

                    writer.println("- **إجمالي الأصناف:** " + String.format("%,d", total));
                    writer.println("- **الأصناف النشطة:** " + String.format("%,d", active)
                            + String.format(" (%.1f%%)", (double) active / total * 100));
                    writer.println("- **الأصناف غير النشطة:** " + String.format("%,d", inactive)
                            + String.format(" (%.1f%%)", (double) inactive / total * 100));

                    Date oldest = rs.getDate("oldest_date");
                    Date newest = rs.getDate("newest_date");
                    if (oldest != null)
                        writer.println("- **أقدم صنف:** " + oldest);
                    if (newest != null)
                        writer.println("- **أحدث صنف:** " + newest);
                }
            }

            writer.println();

        } catch (SQLException e) {
            writer.println("❌ خطأ في تحليل إحصائيات الأصناف: " + e.getMessage());
            writer.println();
        }
    }

    private void generateRecommendations(PrintWriter writer) throws SQLException {
        writer.println("## 💡 التوصيات لبناء النظام الجديد");
        writer.println();

        writer.println("### 🎯 التوصيات الرئيسية:");
        writer.println();
        writer.println("1. **الجداول الأساسية للنسخ:**");
        writer.println("   - IAS_ITM_MST (الأصناف الرئيسي) - 4647 صنف");
        writer.println("   - IAS_ITM_DTL (تفاصيل الأصناف)");
        writer.println("   - جداول العملاء والموردين");
        writer.println("   - جداول الفواتير والمعاملات");
        writer.println();

        writer.println("2. **التحسينات المقترحة:**");
        writer.println("   - توحيد أنماط التسمية (استخدام ERP_ كبادئة)");
        writer.println("   - إضافة قيود مرجعية محسنة");
        writer.println("   - تحسين الفهارس للأداء");
        writer.println("   - إضافة جداول التدقيق (audit tables)");
        writer.println("   - إضافة حقول الطوابع الزمنية");
        writer.println();

        writer.println("3. **الخطة المقترحة:**");
        writer.println("   - **المرحلة 1:** نسخ الجداول الأساسية مع تحسين الأسماء");
        writer.println("   - **المرحلة 2:** تحسين البنية وإضافة القيود");
        writer.println("   - **المرحلة 3:** إضافة الوظائف الجديدة والجداول الإضافية");
        writer.println("   - **المرحلة 4:** تطوير أدوات الاستيراد والمزامنة");
        writer.println();

        writer.println("4. **الجداول الجديدة المقترحة:**");
        writer.println("   - ERP_USERS (المستخدمين)");
        writer.println("   - ERP_PERMISSIONS (الصلاحيات)");
        writer.println("   - ERP_AUDIT_LOG (سجل التدقيق)");
        writer.println("   - ERP_SETTINGS (الإعدادات)");
        writer.println("   - ERP_REPORTS (التقارير)");
        writer.println();

        writer.println("### 🏗️ مخطط قاعدة البيانات المقترحة:");
        writer.println();
        writer.println("```");
        writer.println("ship_erp/");
        writer.println("├── الأصناف والمخزون/");
        writer.println("│   ├── ERP_ITEMS (من IAS_ITM_MST)");
        writer.println("│   ├── ERP_ITEM_DETAILS (من IAS_ITM_DTL)");
        writer.println("│   ├── ERP_CATEGORIES");
        writer.println("│   └── ERP_UNITS");
        writer.println("├── العملاء والموردين/");
        writer.println("│   ├── ERP_CUSTOMERS");
        writer.println("│   └── ERP_VENDORS");
        writer.println("├── المعاملات/");
        writer.println("│   ├── ERP_INVOICES");
        writer.println("│   ├── ERP_RECEIPTS");
        writer.println("│   └── ERP_PAYMENTS");
        writer.println("└── النظام/");
        writer.println("    ├── ERP_USERS");
        writer.println("    ├── ERP_PERMISSIONS");
        writer.println("    └── ERP_AUDIT_LOG");
        writer.println("```");
        writer.println();

        writer.println("---");
        writer.println();
        writer.println("**تم إنشاء هذا التقرير بواسطة أداة التحليل الشامل**");
        writer.println();
    }

    private void generateSQLScripts() {
        appendLog("🚀 بدء توليد سكريبتات SQL...");
        // سيتم تطوير هذه الوظيفة
    }

    private void compareTableData() {
        appendLog("🚀 بدء مقارنة البيانات...");
        // سيتم تطوير هذه الوظيفة
    }

    private void openAdvancedImportTool() {
        appendLog("🚀 فتح أداة الاستيراد المتقدمة...");
        // سيتم تطوير هذه الوظيفة
    }

    private void analyzeCompleteStructure() {
        appendLog("🚀 بدء تحليل البنية الكاملة...");
        // سيتم تطوير هذه الوظيفة
    }

    private void generateDocumentation() {
        appendLog("🚀 بدء توليد الوثائق...");
        // سيتم تطوير هذه الوظيفة
    }

    private void createBackupScripts() {
        appendLog("🚀 بدء إنشاء سكريبتات النسخ الاحتياطي...");
        // سيتم تطوير هذه الوظيفة
    }

    @Override
    public void dispose() {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                // تجاهل
            }
        }
        super.dispose();
    }
}
