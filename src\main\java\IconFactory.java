import javax.swing.*;
import java.awt.*;
import java.awt.geom.*;

/**
 * مصنع الأيقونات المخصصة
 * Custom Icon Factory
 */
public class IconFactory {
    
    /**
     * إنشاء أيقونة دائرية ملونة
     */
    public static Icon createCircleIcon(Color color, int size) {
        return new Icon() {
            @Override
            public void paintIcon(Component c, Graphics g, int x, int y) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                
                // رسم الدائرة
                g2d.setColor(color);
                g2d.fillOval(x + 1, y + 1, size - 2, size - 2);
                
                // رسم الحدود
                g2d.setColor(color.darker());
                g2d.drawOval(x + 1, y + 1, size - 2, size - 2);
                
                g2d.dispose();
            }
            
            @Override
            public int getIconWidth() { return size; }
            
            @Override
            public int getIconHeight() { return size; }
        };
    }
    
    /**
     * إنشاء أيقونة مربعة ملونة
     */
    public static Icon createSquareIcon(Color color, int size) {
        return new Icon() {
            @Override
            public void paintIcon(Component c, Graphics g, int x, int y) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                
                // رسم المربع
                g2d.setColor(color);
                g2d.fillRoundRect(x + 1, y + 1, size - 2, size - 2, 4, 4);
                
                // رسم الحدود
                g2d.setColor(color.darker());
                g2d.drawRoundRect(x + 1, y + 1, size - 2, size - 2, 4, 4);
                
                g2d.dispose();
            }
            
            @Override
            public int getIconWidth() { return size; }
            
            @Override
            public int getIconHeight() { return size; }
        };
    }
    
    /**
     * إنشاء أيقونة سهم
     */
    public static Icon createArrowIcon(Color color, int direction, int size) {
        return new Icon() {
            @Override
            public void paintIcon(Component c, Graphics g, int x, int y) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2d.setColor(color);
                
                int[] xPoints, yPoints;
                int centerX = x + size / 2;
                int centerY = y + size / 2;
                int arrowSize = size / 3;
                
                switch (direction) {
                    case SwingConstants.RIGHT: // سهم يمين
                        xPoints = new int[]{centerX - arrowSize, centerX + arrowSize, centerX - arrowSize};
                        yPoints = new int[]{centerY - arrowSize, centerY, centerY + arrowSize};
                        break;
                    case SwingConstants.LEFT: // سهم يسار
                        xPoints = new int[]{centerX + arrowSize, centerX - arrowSize, centerX + arrowSize};
                        yPoints = new int[]{centerY - arrowSize, centerY, centerY + arrowSize};
                        break;
                    case SwingConstants.NORTH: // سهم أعلى
                        xPoints = new int[]{centerX - arrowSize, centerX, centerX + arrowSize};
                        yPoints = new int[]{centerY + arrowSize, centerY - arrowSize, centerY + arrowSize};
                        break;
                    case SwingConstants.SOUTH: // سهم أسفل
                    default:
                        xPoints = new int[]{centerX - arrowSize, centerX, centerX + arrowSize};
                        yPoints = new int[]{centerY - arrowSize, centerY + arrowSize, centerY - arrowSize};
                        break;
                }
                
                g2d.fillPolygon(xPoints, yPoints, 3);
                g2d.dispose();
            }
            
            @Override
            public int getIconWidth() { return size; }
            
            @Override
            public int getIconHeight() { return size; }
        };
    }
    
    /**
     * إنشاء أيقونة علامة زائد
     */
    public static Icon createPlusIcon(Color color, int size) {
        return new Icon() {
            @Override
            public void paintIcon(Component c, Graphics g, int x, int y) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2d.setColor(color);
                g2d.setStroke(new BasicStroke(2));
                
                int centerX = x + size / 2;
                int centerY = y + size / 2;
                int lineSize = size / 3;
                
                // خط أفقي
                g2d.drawLine(centerX - lineSize, centerY, centerX + lineSize, centerY);
                // خط عمودي
                g2d.drawLine(centerX, centerY - lineSize, centerX, centerY + lineSize);
                
                g2d.dispose();
            }
            
            @Override
            public int getIconWidth() { return size; }
            
            @Override
            public int getIconHeight() { return size; }
        };
    }
    
    /**
     * إنشاء أيقونة علامة ناقص
     */
    public static Icon createMinusIcon(Color color, int size) {
        return new Icon() {
            @Override
            public void paintIcon(Component c, Graphics g, int x, int y) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2d.setColor(color);
                g2d.setStroke(new BasicStroke(2));
                
                int centerX = x + size / 2;
                int centerY = y + size / 2;
                int lineSize = size / 3;
                
                // خط أفقي
                g2d.drawLine(centerX - lineSize, centerY, centerX + lineSize, centerY);
                
                g2d.dispose();
            }
            
            @Override
            public int getIconWidth() { return size; }
            
            @Override
            public int getIconHeight() { return size; }
        };
    }
    
    /**
     * إنشاء أيقونة علامة X
     */
    public static Icon createCloseIcon(Color color, int size) {
        return new Icon() {
            @Override
            public void paintIcon(Component c, Graphics g, int x, int y) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2d.setColor(color);
                g2d.setStroke(new BasicStroke(2));
                
                int margin = size / 4;
                
                // خط قطري من اليسار العلوي لليمين السفلي
                g2d.drawLine(x + margin, y + margin, x + size - margin, y + size - margin);
                // خط قطري من اليمين العلوي لليسار السفلي
                g2d.drawLine(x + size - margin, y + margin, x + margin, y + size - margin);
                
                g2d.dispose();
            }
            
            @Override
            public int getIconWidth() { return size; }
            
            @Override
            public int getIconHeight() { return size; }
        };
    }
    
    /**
     * إنشاء أيقونة علامة صح
     */
    public static Icon createCheckIcon(Color color, int size) {
        return new Icon() {
            @Override
            public void paintIcon(Component c, Graphics g, int x, int y) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2d.setColor(color);
                g2d.setStroke(new BasicStroke(2));
                
                int[] xPoints = {x + size/4, x + size/2, x + size*3/4};
                int[] yPoints = {y + size/2, y + size*3/4, y + size/4};
                
                // رسم علامة الصح
                g2d.drawPolyline(xPoints, yPoints, 3);
                
                g2d.dispose();
            }
            
            @Override
            public int getIconWidth() { return size; }
            
            @Override
            public int getIconHeight() { return size; }
        };
    }
    
    /**
     * إنشاء أيقونة ترس (إعدادات)
     */
    public static Icon createGearIcon(Color color, int size) {
        return new Icon() {
            @Override
            public void paintIcon(Component c, Graphics g, int x, int y) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2d.setColor(color);
                
                int centerX = x + size / 2;
                int centerY = y + size / 2;
                int outerRadius = size / 2 - 2;
                int innerRadius = size / 4;
                
                // رسم الترس الخارجي
                for (int i = 0; i < 8; i++) {
                    double angle = i * Math.PI / 4;
                    int x1 = centerX + (int)(outerRadius * Math.cos(angle));
                    int y1 = centerY + (int)(outerRadius * Math.sin(angle));
                    int x2 = centerX + (int)((outerRadius - 3) * Math.cos(angle));
                    int y2 = centerY + (int)((outerRadius - 3) * Math.sin(angle));
                    g2d.drawLine(x1, y1, x2, y2);
                }
                
                // رسم الدائرة الداخلية
                g2d.drawOval(centerX - innerRadius, centerY - innerRadius, 
                           innerRadius * 2, innerRadius * 2);
                
                g2d.dispose();
            }
            
            @Override
            public int getIconWidth() { return size; }
            
            @Override
            public int getIconHeight() { return size; }
        };
    }
    
    /**
     * إنشاء أيقونة مجلد
     */
    public static Icon createFolderIcon(Color color, int size) {
        return new Icon() {
            @Override
            public void paintIcon(Component c, Graphics g, int x, int y) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2d.setColor(color);
                
                int folderWidth = size - 2;
                int folderHeight = size * 2 / 3;
                int tabWidth = size / 3;
                int tabHeight = size / 4;
                
                // رسم تبويب المجلد
                g2d.fillRoundRect(x + 1, y + 1, tabWidth, tabHeight, 2, 2);
                
                // رسم جسم المجلد
                g2d.fillRoundRect(x + 1, y + tabHeight, folderWidth, folderHeight, 3, 3);
                
                // رسم الحدود
                g2d.setColor(color.darker());
                g2d.drawRoundRect(x + 1, y + 1, tabWidth, tabHeight, 2, 2);
                g2d.drawRoundRect(x + 1, y + tabHeight, folderWidth, folderHeight, 3, 3);
                
                g2d.dispose();
            }
            
            @Override
            public int getIconWidth() { return size; }
            
            @Override
            public int getIconHeight() { return size; }
        };
    }
    
    /**
     * إنشاء أيقونة ملف
     */
    public static Icon createFileIcon(Color color, int size) {
        return new Icon() {
            @Override
            public void paintIcon(Component c, Graphics g, int x, int y) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2d.setColor(color);
                
                int fileWidth = size * 2 / 3;
                int fileHeight = size - 2;
                int cornerSize = size / 4;
                
                // رسم جسم الملف
                g2d.fillRect(x + 1, y + cornerSize + 1, fileWidth, fileHeight - cornerSize);
                
                // رسم الزاوية المطوية
                int[] xPoints = {x + 1 + fileWidth - cornerSize, x + 1 + fileWidth, x + 1 + fileWidth};
                int[] yPoints = {y + 1, y + 1, y + cornerSize + 1};
                g2d.fillPolygon(xPoints, yPoints, 3);
                
                // رسم الحدود
                g2d.setColor(color.darker());
                g2d.drawRect(x + 1, y + cornerSize + 1, fileWidth, fileHeight - cornerSize);
                g2d.drawPolyline(xPoints, yPoints, 3);
                
                g2d.dispose();
            }
            
            @Override
            public int getIconWidth() { return size; }
            
            @Override
            public int getIconHeight() { return size; }
        };
    }
}
