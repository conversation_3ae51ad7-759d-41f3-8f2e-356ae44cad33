import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * إنشاء ربط جداول مجموعات الأصناف في نظام الاستيراد Create Item Group Tables Mappings in Import
 * System
 */
public class CreateItemGroupMappings {

    public static void main(String[] args) {
        try {
            // تحميل Oracle JDBC driver
            Class.forName("oracle.jdbc.OracleDriver");
            System.out.println("✅ تم تحميل Oracle JDBC driver بنجاح");

            // الاتصال بقاعدة البيانات SHIP_ERP
            Connection conn = DriverManager.getConnection("*************************************",
                    "ship_erp", "ship_erp_password");
            conn.setAutoCommit(false);

            System.out.println("✅ متصل بقاعدة البيانات SHIP_ERP");

            // الحصول على CONNECTION_ID للنظام الأصلي
            int connectionId = getConnectionId(conn);

            if (connectionId <= 0) {
                System.out.println("❌ لم يتم العثور على اتصال IAS20251. سيتم إنشاء اتصال جديد.");
                connectionId = createConnection(conn);
            }

            System.out.println("✅ تم العثور على CONNECTION_ID: " + connectionId);

            // إنشاء ربط الجداول
            createMainGroupMapping(conn, connectionId);
            createMainSubGroupMapping(conn, connectionId);
            createSubGroupMapping(conn, connectionId);
            createAssistantGroupMapping(conn, connectionId);
            createDetailGroupMapping(conn, connectionId);

            conn.commit();
            System.out.println("🎉 تم إنشاء جميع ربط جداول مجموعات الأصناف بنجاح!");

            conn.close();

        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * الحصول على CONNECTION_ID للنظام الأصلي
     */
    private static int getConnectionId(Connection conn) throws SQLException {
        String sql =
                "SELECT CONNECTION_ID FROM ERP_SYSTEM_CONNECTIONS WHERE CONNECTION_NAME = 'IAS20251'";
        Statement stmt = conn.createStatement();
        ResultSet rs = stmt.executeQuery(sql);

        if (rs.next()) {
            return rs.getInt("CONNECTION_ID");
        }

        return 0;
    }

    /**
     * إنشاء اتصال جديد للنظام الأصلي
     */
    private static int createConnection(Connection conn) throws SQLException {
        String sql =
                """
                            INSERT INTO ERP_SYSTEM_CONNECTIONS
                            (CONNECTION_ID, CONNECTION_NAME, CONNECTION_TYPE, HOST_NAME, PORT_NUMBER,
                             DATABASE_NAME, USERNAME, PASSWORD, IS_ACTIVE, DESCRIPTION, CREATED_BY, CREATED_DATE)
                            VALUES
                            (SEQ_SYSTEM_CONNECTIONS.NEXTVAL, 'IAS20251', 'ORACLE', 'localhost', 1521,
                             'orcl', 'ias20251', 'ys123', 1, 'النظام المحاسبي الأصلي', 'SYSTEM', SYSDATE)
                        """;

        PreparedStatement pstmt = conn.prepareStatement(sql);
        pstmt.executeUpdate();

        // الحصول على CONNECTION_ID المُدرج
        String getIdSql =
                "SELECT CONNECTION_ID FROM ERP_SYSTEM_CONNECTIONS WHERE CONNECTION_NAME = 'IAS20251'";
        Statement stmt = conn.createStatement();
        ResultSet rs = stmt.executeQuery(getIdSql);

        if (rs.next()) {
            int connectionId = rs.getInt("CONNECTION_ID");
            System.out.println("✅ تم إنشاء اتصال جديد برقم: " + connectionId);
            return connectionId;
        }

        throw new SQLException("فشل في الحصول على CONNECTION_ID");
    }

    /**
     * إنشاء ربط جدول المجموعات الرئيسية
     */
    private static void createMainGroupMapping(Connection conn, int connectionId)
            throws SQLException {
        // التحقق من وجود الربط
        String checkSql =
                "SELECT COUNT(*) FROM ERP_TABLE_MAPPING WHERE SOURCE_TABLE_NAME = 'GROUP_DETAILS'";
        Statement checkStmt = conn.createStatement();
        ResultSet checkRs = checkStmt.executeQuery(checkSql);
        checkRs.next();
        int count = checkRs.getInt(1);

        if (count > 0) {
            System.out.println("⚠️ ربط جدول المجموعات الرئيسية موجود مسبقاً");
            return;
        }

        // إنشاء ربط الجدول
        String sql = """
                    INSERT INTO ERP_TABLE_MAPPING
                    (MAPPING_ID, CONNECTION_ID, SOURCE_TABLE_NAME, TARGET_TABLE_NAME,
                     MAPPING_NAME, MAPPING_TYPE, IS_ACTIVE, CREATED_BY, CREATED_DATE)
                    VALUES
                    (SEQ_TABLE_MAPPING.NEXTVAL, ?, 'GROUP_DETAILS', 'ERP_GROUP_DETAILS',
                     'استيراد المجموعات الرئيسية', 'IMPORT', 1, 'SYSTEM', SYSDATE)
                """;

        PreparedStatement pstmt = conn.prepareStatement(sql);
        pstmt.setInt(1, connectionId);
        pstmt.executeUpdate();

        System.out.println("✅ تم إنشاء ربط جدول المجموعات الرئيسية");

        // إنشاء ربط الحقول
        createFieldMappings(conn, "GROUP_DETAILS", "ERP_GROUP_DETAILS");
    }

    /**
     * إنشاء ربط جدول المجموعات الفرعية
     */
    private static void createMainSubGroupMapping(Connection conn, int connectionId)
            throws SQLException {
        // التحقق من وجود الربط
        String checkSql =
                "SELECT COUNT(*) FROM ERP_TABLE_MAPPING WHERE SOURCE_TABLE_NAME = 'IAS_MAINSUB_GRP_DTL'";
        Statement checkStmt = conn.createStatement();
        ResultSet checkRs = checkStmt.executeQuery(checkSql);
        checkRs.next();
        int count = checkRs.getInt(1);

        if (count > 0) {
            System.out.println("⚠️ ربط جدول المجموعات الفرعية موجود مسبقاً");
            return;
        }

        // إنشاء ربط الجدول
        String sql = """
                    INSERT INTO ERP_TABLE_MAPPING
                    (MAPPING_ID, CONNECTION_ID, SOURCE_TABLE_NAME, TARGET_TABLE_NAME,
                     MAPPING_NAME, MAPPING_TYPE, IS_ACTIVE, CREATED_BY, CREATED_DATE)
                    VALUES
                    (SEQ_TABLE_MAPPING.NEXTVAL, ?, 'IAS_MAINSUB_GRP_DTL', 'ERP_MAINSUB_GRP_DTL',
                     'استيراد المجموعات الفرعية', 'IMPORT', 1, 'SYSTEM', SYSDATE)
                """;

        PreparedStatement pstmt = conn.prepareStatement(sql);
        pstmt.setInt(1, connectionId);
        pstmt.executeUpdate();

        System.out.println("✅ تم إنشاء ربط جدول المجموعات الفرعية");

        // إنشاء ربط الحقول
        createFieldMappings(conn, "IAS_MAINSUB_GRP_DTL", "ERP_MAINSUB_GRP_DTL");
    }

    /**
     * إنشاء ربط جدول المجموعات تحت فرعية
     */
    private static void createSubGroupMapping(Connection conn, int connectionId)
            throws SQLException {
        // التحقق من وجود الربط
        String checkSql =
                "SELECT COUNT(*) FROM ERP_TABLE_MAPPING WHERE SOURCE_TABLE_NAME = 'IAS_SUB_GRP_DTL'";
        Statement checkStmt = conn.createStatement();
        ResultSet checkRs = checkStmt.executeQuery(checkSql);
        checkRs.next();
        int count = checkRs.getInt(1);

        if (count > 0) {
            System.out.println("⚠️ ربط جدول المجموعات تحت فرعية موجود مسبقاً");
            return;
        }

        // إنشاء ربط الجدول
        String sql = """
                    INSERT INTO ERP_TABLE_MAPPING
                    (MAPPING_ID, CONNECTION_ID, SOURCE_TABLE_NAME, TARGET_TABLE_NAME,
                     MAPPING_NAME, MAPPING_TYPE, IS_ACTIVE, CREATED_BY, CREATED_DATE)
                    VALUES
                    (SEQ_TABLE_MAPPING.NEXTVAL, ?, 'IAS_SUB_GRP_DTL', 'ERP_SUB_GRP_DTL',
                     'استيراد المجموعات تحت فرعية', 'IMPORT', 1, 'SYSTEM', SYSDATE)
                """;

        PreparedStatement pstmt = conn.prepareStatement(sql);
        pstmt.setInt(1, connectionId);
        pstmt.executeUpdate();

        System.out.println("✅ تم إنشاء ربط جدول المجموعات تحت فرعية");

        // إنشاء ربط الحقول
        createFieldMappings(conn, "IAS_SUB_GRP_DTL", "ERP_SUB_GRP_DTL");
    }

    /**
     * إنشاء ربط جدول المجموعات المساعدة
     */
    private static void createAssistantGroupMapping(Connection conn, int connectionId)
            throws SQLException {
        // التحقق من وجود الربط
        String checkSql =
                "SELECT COUNT(*) FROM ERP_TABLE_MAPPING WHERE SOURCE_TABLE_NAME = 'IAS_ASSISTANT_GROUP'";
        Statement checkStmt = conn.createStatement();
        ResultSet checkRs = checkStmt.executeQuery(checkSql);
        checkRs.next();
        int count = checkRs.getInt(1);

        if (count > 0) {
            System.out.println("⚠️ ربط جدول المجموعات المساعدة موجود مسبقاً");
            return;
        }

        // إنشاء ربط الجدول
        String sql = """
                    INSERT INTO ERP_TABLE_MAPPING
                    (MAPPING_ID, CONNECTION_ID, SOURCE_TABLE_NAME, TARGET_TABLE_NAME,
                     MAPPING_NAME, MAPPING_TYPE, IS_ACTIVE, CREATED_BY, CREATED_DATE)
                    VALUES
                    (SEQ_TABLE_MAPPING.NEXTVAL, ?, 'IAS_ASSISTANT_GROUP', 'ERP_ASSISTANT_GROUP',
                     'استيراد المجموعات المساعدة', 'IMPORT', 1, 'SYSTEM', SYSDATE)
                """;

        PreparedStatement pstmt = conn.prepareStatement(sql);
        pstmt.setInt(1, connectionId);
        pstmt.executeUpdate();

        System.out.println("✅ تم إنشاء ربط جدول المجموعات المساعدة");

        // إنشاء ربط الحقول
        createFieldMappings(conn, "IAS_ASSISTANT_GROUP", "ERP_ASSISTANT_GROUP");
    }

    /**
     * إنشاء ربط جدول المجموعات التفصيلية
     */
    private static void createDetailGroupMapping(Connection conn, int connectionId)
            throws SQLException {
        // التحقق من وجود الربط
        String checkSql =
                "SELECT COUNT(*) FROM ERP_TABLE_MAPPING WHERE SOURCE_TABLE_NAME = 'IAS_DETAIL_GROUP'";
        Statement checkStmt = conn.createStatement();
        ResultSet checkRs = checkStmt.executeQuery(checkSql);
        checkRs.next();
        int count = checkRs.getInt(1);

        if (count > 0) {
            System.out.println("⚠️ ربط جدول المجموعات التفصيلية موجود مسبقاً");
            return;
        }

        // إنشاء ربط الجدول
        String sql = """
                    INSERT INTO ERP_TABLE_MAPPING
                    (MAPPING_ID, CONNECTION_ID, SOURCE_TABLE_NAME, TARGET_TABLE_NAME,
                     MAPPING_NAME, MAPPING_TYPE, IS_ACTIVE, CREATED_BY, CREATED_DATE)
                    VALUES
                    (SEQ_TABLE_MAPPING.NEXTVAL, ?, 'IAS_DETAIL_GROUP', 'ERP_DETAIL_GROUP',
                     'استيراد المجموعات التفصيلية', 'IMPORT', 1, 'SYSTEM', SYSDATE)
                """;

        PreparedStatement pstmt = conn.prepareStatement(sql);
        pstmt.setInt(1, connectionId);
        pstmt.executeUpdate();

        System.out.println("✅ تم إنشاء ربط جدول المجموعات التفصيلية");

        // إنشاء ربط الحقول
        createFieldMappings(conn, "IAS_DETAIL_GROUP", "ERP_DETAIL_GROUP");
    }

    /**
     * إنشاء ربط الحقول بين الجدولين
     */
    private static void createFieldMappings(Connection conn, String sourceTable, String targetTable)
            throws SQLException {
        // الحصول على MAPPING_ID
        String mappingSql = "SELECT MAPPING_ID FROM ERP_TABLE_MAPPING WHERE SOURCE_TABLE_NAME = ?";
        PreparedStatement mappingStmt = conn.prepareStatement(mappingSql);
        mappingStmt.setString(1, sourceTable);
        ResultSet mappingRs = mappingStmt.executeQuery();

        if (!mappingRs.next()) {
            System.out.println("❌ لم يتم العثور على ربط الجدول: " + sourceTable);
            return;
        }

        int mappingId = mappingRs.getInt("MAPPING_ID");

        // الحصول على حقول الجدول الهدف
        String targetFieldsSql =
                "SELECT COLUMN_NAME, DATA_TYPE FROM USER_TAB_COLUMNS WHERE TABLE_NAME = ?";
        PreparedStatement targetFieldsStmt = conn.prepareStatement(targetFieldsSql);
        targetFieldsStmt.setString(1, targetTable);
        ResultSet targetFieldsRs = targetFieldsStmt.executeQuery();

        // إنشاء ربط الحقول
        int fieldOrder = 1;
        while (targetFieldsRs.next()) {
            String columnName = targetFieldsRs.getString("COLUMN_NAME");
            String dataType = targetFieldsRs.getString("DATA_TYPE");

            // تجاهل حقول النظام
            if (columnName.equals("CREATED_BY") || columnName.equals("CREATED_DATE")
                    || columnName.equals("UPDATED_BY") || columnName.equals("UPDATED_DATE")) {
                continue;
            }

            // التحقق من وجود الربط
            String checkSql =
                    "SELECT COUNT(*) FROM ERP_FIELD_MAPPING WHERE TABLE_MAPPING_ID = ? AND TARGET_FIELD_NAME = ?";
            PreparedStatement checkStmt = conn.prepareStatement(checkSql);
            checkStmt.setInt(1, mappingId);
            checkStmt.setString(2, columnName);
            ResultSet checkRs = checkStmt.executeQuery();
            checkRs.next();
            int count = checkRs.getInt(1);

            if (count > 0) {
                System.out.println("⚠️ ربط حقل " + columnName + " موجود مسبقاً");
                continue;
            }

            // إنشاء ربط الحقل
            String sql = """
                        INSERT INTO ERP_FIELD_MAPPING
                        (FIELD_MAPPING_ID, TABLE_MAPPING_ID, SOURCE_FIELD_NAME, TARGET_FIELD_NAME,
                         DATA_TYPE, IS_KEY_FIELD, IS_REQUIRED, FIELD_ORDER)
                        VALUES
                        (SEQ_FIELD_MAPPING.NEXTVAL, ?, ?, ?, ?, ?, ?, ?)
                    """;

            PreparedStatement pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, mappingId);
            pstmt.setString(2, columnName); // نفس اسم الحقل في المصدر والهدف
            pstmt.setString(3, columnName);
            pstmt.setString(4, dataType);

            // تحديد الحقول المفتاحية والمطلوبة
            boolean isKeyField = columnName.endsWith("_CODE") || columnName.endsWith("_NO");
            boolean isRequired = isKeyField || columnName.endsWith("_A_NAME");

            pstmt.setInt(5, isKeyField ? 1 : 0);
            pstmt.setInt(6, isRequired ? 1 : 0);
            pstmt.setInt(7, fieldOrder++);

            pstmt.executeUpdate();

            System.out.println("✅ تم إنشاء ربط حقل: " + columnName);
        }
    }
}
