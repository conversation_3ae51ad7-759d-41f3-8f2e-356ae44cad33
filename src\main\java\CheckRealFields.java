import java.sql.*;

/**
 * فحص الحقول الحقيقية في الجداول المنشأة
 */
public class CheckRealFields {
    
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            System.out.println("✅ تم الاتصال بـ SHIP_ERP");
            
            // فحص حقول IAS_ITM_MST
            checkTableFields(conn, "IAS_ITM_MST");
            
            // فحص حقول IAS_ITM_DTL
            checkTableFields(conn, "IAS_ITM_DTL");
            
            // فحص عينة من البيانات
            checkSampleData(conn);
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * فحص حقول جدول
     */
    private static void checkTableFields(Connection conn, String tableName) throws SQLException {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("📋 حقول جدول: " + tableName);
        System.out.println("=".repeat(60));
        
        String sql = """
            SELECT 
                COLUMN_ID,
                COLUMN_NAME, 
                DATA_TYPE, 
                DATA_LENGTH, 
                DATA_PRECISION, 
                DATA_SCALE, 
                NULLABLE
            FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = ?
            ORDER BY COLUMN_ID
        """;
        
        PreparedStatement pstmt = conn.prepareStatement(sql);
        pstmt.setString(1, tableName);
        ResultSet rs = pstmt.executeQuery();
        
        System.out.printf("%-3s %-30s %-15s %-10s %-8s\n", "#", "COLUMN_NAME", "DATA_TYPE", "SIZE", "NULL?");
        System.out.println("-".repeat(80));
        
        int count = 0;
        while (rs.next()) {
            count++;
            int columnId = rs.getInt("COLUMN_ID");
            String columnName = rs.getString("COLUMN_NAME");
            String dataType = rs.getString("DATA_TYPE");
            int dataLength = rs.getInt("DATA_LENGTH");
            int dataPrecision = rs.getInt("DATA_PRECISION");
            int dataScale = rs.getInt("DATA_SCALE");
            String nullable = rs.getString("NULLABLE");
            
            String sizeInfo = "";
            if (dataType.equals("VARCHAR2") || dataType.equals("CHAR")) {
                sizeInfo = "(" + dataLength + ")";
            } else if (dataType.equals("NUMBER")) {
                if (dataPrecision > 0) {
                    if (dataScale > 0) {
                        sizeInfo = "(" + dataPrecision + "," + dataScale + ")";
                    } else {
                        sizeInfo = "(" + dataPrecision + ")";
                    }
                }
            }
            
            String nullInfo = nullable.equals("Y") ? "NULL" : "NOT NULL";
            
            System.out.printf("%-3d %-30s %-15s %-10s %-8s\n", 
                columnId, columnName, dataType + sizeInfo, "", nullInfo);
        }
        
        rs.close();
        pstmt.close();
        
        System.out.println("-".repeat(80));
        System.out.println("📊 إجمالي الحقول: " + count);
    }
    
    /**
     * فحص عينة من البيانات
     */
    private static void checkSampleData(Connection conn) throws SQLException {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("📄 فحص عينة من البيانات");
        System.out.println("=".repeat(60));
        
        try {
            Statement stmt = conn.createStatement();
            
            // فحص عدد الصفوف في IAS_ITM_MST
            ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM IAS_ITM_MST");
            rs.next();
            int mstCount = rs.getInt(1);
            System.out.println("📊 عدد الصفوف في IAS_ITM_MST: " + mstCount);
            rs.close();
            
            // فحص عدد الصفوف في IAS_ITM_DTL
            rs = stmt.executeQuery("SELECT COUNT(*) FROM IAS_ITM_DTL");
            rs.next();
            int dtlCount = rs.getInt(1);
            System.out.println("📊 عدد الصفوف في IAS_ITM_DTL: " + dtlCount);
            rs.close();
            
            // عرض الحقول المهمة للنافذة
            System.out.println("\n🎯 الحقول المهمة للنافذة:");
            System.out.println("IAS_ITM_MST:");
            System.out.println("  - I_CODE (كود الصنف)");
            System.out.println("  - I_NAME (الاسم العربي)");
            System.out.println("  - I_E_NAME (الاسم الإنجليزي)");
            System.out.println("  - G_CODE (كود المجموعة)");
            System.out.println("  - PRIMARY_COST (التكلفة الأساسية)");
            System.out.println("  - INACTIVE (غير نشط: 0=نشط، 1=غير نشط)");
            System.out.println("  - I_DESC (الوصف)");
            System.out.println("  - MANF_CODE (كود المصنع)");
            System.out.println("  - ALTER_CODE (الكود البديل)");
            
            System.out.println("\nIAS_ITM_DTL:");
            System.out.println("  - I_CODE (كود الصنف)");
            System.out.println("  - ITM_UNT (وحدة الصنف)");
            System.out.println("  - P_SIZE (حجم العبوة)");
            System.out.println("  - MAIN_UNIT (الوحدة الرئيسية)");
            System.out.println("  - SALE_UNIT (وحدة البيع)");
            System.out.println("  - BARCODE (الباركود)");
            
            stmt.close();
            
        } catch (SQLException e) {
            System.out.println("⚠️ تحذير: " + e.getMessage());
        }
    }
}
