import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة إدارة وحدات القياس الأصلية مبنية بالضبط على بنية جدول MEASUREMENT من قاعدة البيانات
 * IAS20251 Original Measurement Units Window based on IAS20251.MEASUREMENT table structure
 */
public class OriginalMeasurementUnitsWindow extends JFrame {

    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 13);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 13);
    private Font fieldFont = new Font("Tahoma", Font.PLAIN, 14);
    private Font labelFont = new Font("Tahoma", Font.BOLD, 13);

    // مكونات الواجهة - بنفس ترتيب الحقول في الجدول الأصلي
    private JTextField measureCodeField; // MEASURE_CODE
    private JTextField measureField; // MEASURE
    private JTextField measureFNmField; // MEASURE_F_NM
    private JTextField measureCodeGbField; // MEASURE_CODE_GB
    private JComboBox<String> measureTypeCombo; // MEASURE_TYPE
    private JComboBox<String> measureWtTypeCombo; // MEASURE_WT_TYPE
    private JComboBox<String> measureWtConnCombo; // MEASURE_WT_CONN
    private JTextField dfltSizeField; // DFLT_SIZE
    private JComboBox<String> allowUpdCombo; // ALLOW_UPD
    private JComboBox<String> untSaleTypCombo; // UNT_SALE_TYP

    private JTable measurementsTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;

    // قاعدة البيانات
    private Connection connection;
    private OriginalMeasurementDAO measurementDAO;

    // الألوان
    private Color primaryColor = new Color(52, 152, 219);
    private Color successColor = new Color(46, 204, 113);
    private Color warningColor = new Color(241, 196, 15);
    private Color dangerColor = new Color(231, 76, 60);

    public OriginalMeasurementUnitsWindow() {
        initializeDatabase();
        initializeComponents();
        loadMeasurementsData();
    }

    /**
     * تهيئة قاعدة البيانات SHIP_ERP
     */
    private void initializeDatabase() {
        try {
            // تحميل Oracle JDBC driver
            try {
                Class.forName("oracle.jdbc.OracleDriver");
                System.out.println("✅ تم تحميل Oracle JDBC driver بنجاح");
            } catch (ClassNotFoundException e) {
                System.err.println("❌ فشل في تحميل Oracle JDBC driver: " + e.getMessage());
                System.err.println("تأكد من وجود ملف ojdbc11.jar في classpath");
                throw e;
            }

            // الاتصال بقاعدة البيانات SHIP_ERP
            String url = "*************************************";
            String username = "ship_erp";
            String password = "ship_erp_password";

            connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ تم الاتصال بقاعدة البيانات SHIP_ERP بنجاح");

            measurementDAO = new OriginalMeasurementDAO(connection);

            // التحقق من وجود الجدول وإنشاؤه إذا لم يكن موجوداً
            if (!checkTableExists()) {
                System.out.println("⚠️ جدول ERP_MEASUREMENT غير موجود، سيتم إنشاؤه...");
                measurementDAO.createTable();
                measurementDAO.insertSampleData();
                System.out
                        .println("✅ تم إنشاء جدول ERP_MEASUREMENT وإدراج البيانات التجريبية بنجاح");
            } else {
                System.out.println("✅ جدول ERP_MEASUREMENT موجود ومتاح");
            }

        } catch (ClassNotFoundException e) {
            System.err.println("❌ فشل في تحميل Oracle JDBC driver: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                    "خطأ: لم يتم العثور على Oracle JDBC driver\n"
                            + "تأكد من وجود ملف ojdbc11.jar في مجلد lib",
                    "خطأ في قاعدة البيانات", JOptionPane.ERROR_MESSAGE);
        } catch (SQLException e) {
            System.err.println("❌ خطأ في الاتصال بقاعدة البيانات SHIP_ERP: " + e.getMessage());

            String errorMessage = "خطأ في الاتصال بقاعدة البيانات SHIP_ERP:\n" + e.getMessage();

            if (e.getMessage().contains("invalid username")) {
                errorMessage += "\n\nتأكد من:\n" + "• وجود المستخدم ship_erp في Oracle\n"
                        + "• صحة كلمة المرور";
            } else if (e.getMessage().contains("Connection refused")) {
                errorMessage +=
                        "\n\nتأكد من:\n" + "• تشغيل Oracle Database\n" + "• المنفذ 1521 متاح";
            }

            JOptionPane.showMessageDialog(this, errorMessage, "خطأ في قاعدة البيانات",
                    JOptionPane.ERROR_MESSAGE);
        } catch (Exception e) {
            System.err.println("❌ خطأ عام في تهيئة قاعدة البيانات: " + e.getMessage());
            e.printStackTrace();

            JOptionPane.showMessageDialog(this, "خطأ في تهيئة قاعدة البيانات:\n" + e.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }



    /**
     * التحقق من وجود الجدول
     */
    private boolean checkTableExists() {
        try {
            String query = "SELECT COUNT(*) FROM ERP_MEASUREMENT WHERE ROWNUM <= 1";
            try (Statement stmt = connection.createStatement();
                    ResultSet rs = stmt.executeQuery(query)) {
                return true;
            }
        } catch (SQLException e) {
            return false;
        }
    }



    /**
     * تهيئة مكونات الواجهة
     */
    private void initializeComponents() {
        setTitle("إدارة وحدات القياس");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(1200, 750);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // لوحة الأزرار العلوية
        JPanel topPanel = createTopPanel();
        mainPanel.add(topPanel, BorderLayout.NORTH);

        // اللوحة الوسطى - تقسيم أفقي
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(420);

        // اللوحة اليمنى - نموذج الإدخال
        JPanel formPanel = createFormPanel();
        splitPane.setRightComponent(formPanel);

        // اللوحة اليسرى - جدول البيانات
        JPanel tablePanel = createTablePanel();
        splitPane.setLeftComponent(tablePanel);

        mainPanel.add(splitPane, BorderLayout.CENTER);

        // شريط الحالة
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);

        add(mainPanel);
    }

    /**
     * إنشاء لوحة الأزرار العلوية
     */
    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 5));

        JButton newBtn = createStyledButton("🆕 جديد", successColor);
        newBtn.addActionListener(e -> clearForm());

        JButton saveBtn = createStyledButton("💾 حفظ", primaryColor);
        saveBtn.addActionListener(e -> saveMeasurement());

        JButton deleteBtn = createStyledButton("🗑️ حذف", dangerColor);
        deleteBtn.addActionListener(e -> deleteMeasurement());

        JButton refreshBtn = createStyledButton("🔄 تحديث", new Color(52, 73, 94));
        refreshBtn.addActionListener(e -> loadMeasurementsData());

        JButton exportBtn = createStyledButton("📤 تصدير", warningColor);
        exportBtn.addActionListener(e -> exportData());

        JButton importBtn = createStyledButton("📥 استيراد", new Color(155, 89, 182));
        importBtn.addActionListener(e -> importFromOriginal());

        panel.add(newBtn);
        panel.add(saveBtn);
        panel.add(deleteBtn);
        panel.add(refreshBtn);
        panel.add(exportBtn);
        panel.add(importBtn);

        return panel;
    }

    /**
     * إنشاء نموذج الإدخال بنفس ترتيب الحقول في الجدول الأصلي
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder(BorderFactory.createEtchedBorder(),
                "بيانات وحدة القياس", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(6, 10, 6, 10);

        int row = 0;

        // إضافة الحقول بالترتيب الصحيح (التسمية على اليمين، الحقل على اليسار)

        // MEASURE_CODE
        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel codeLabel = new JLabel("كود وحدة القياس:");
        codeLabel.setFont(labelFont);
        panel.add(codeLabel, gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.anchor = GridBagConstraints.WEST;
        measureCodeField = new JTextField(18);
        measureCodeField.setFont(fieldFont);
        panel.add(measureCodeField, gbc);
        row++;

        // MEASURE
        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel nameLabel = new JLabel("اسم وحدة القياس:");
        nameLabel.setFont(labelFont);
        panel.add(nameLabel, gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.anchor = GridBagConstraints.WEST;
        measureField = new JTextField(18);
        measureField.setFont(fieldFont);
        panel.add(measureField, gbc);
        row++;

        // MEASURE_F_NM
        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel nameEnLabel = new JLabel("الاسم الإنجليزي:");
        nameEnLabel.setFont(labelFont);
        panel.add(nameEnLabel, gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.anchor = GridBagConstraints.WEST;
        measureFNmField = new JTextField(18);
        measureFNmField.setFont(fieldFont);
        panel.add(measureFNmField, gbc);
        row++;

        // MEASURE_CODE_GB
        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel codeGbLabel = new JLabel("الكود العالمي:");
        codeGbLabel.setFont(labelFont);
        panel.add(codeGbLabel, gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.anchor = GridBagConstraints.WEST;
        measureCodeGbField = new JTextField(18);
        measureCodeGbField.setFont(fieldFont);
        panel.add(measureCodeGbField, gbc);
        row++;

        // MEASURE_TYPE
        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel typeLabel = new JLabel("نوع القياس:");
        typeLabel.setFont(labelFont);
        panel.add(typeLabel, gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.anchor = GridBagConstraints.WEST;
        measureTypeCombo = new JComboBox<>(new String[] {"عادي", "وزن", "حجم", "طول", "مساحة"});
        measureTypeCombo.setFont(new Font("Tahoma", Font.PLAIN, 14));
        panel.add(measureTypeCombo, gbc);
        row++;

        // MEASURE_WT_TYPE
        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel wtTypeLabel = new JLabel("نوع الوزن:");
        wtTypeLabel.setFont(labelFont);
        panel.add(wtTypeLabel, gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.anchor = GridBagConstraints.WEST;
        measureWtTypeCombo =
                new JComboBox<>(new String[] {"غير محدد", "وزن خفيف", "وزن متوسط", "وزن ثقيل"});
        measureWtTypeCombo.setFont(fieldFont);
        panel.add(measureWtTypeCombo, gbc);
        row++;

        // MEASURE_WT_CONN
        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel wtConnLabel = new JLabel("ربط الوزن:");
        wtConnLabel.setFont(labelFont);
        panel.add(wtConnLabel, gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.anchor = GridBagConstraints.WEST;
        measureWtConnCombo = new JComboBox<>(new String[] {"لا", "نعم"});
        measureWtConnCombo.setFont(fieldFont);
        panel.add(measureWtConnCombo, gbc);
        row++;

        // DFLT_SIZE
        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel sizeLabel = new JLabel("الحجم الافتراضي:");
        sizeLabel.setFont(labelFont);
        panel.add(sizeLabel, gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.anchor = GridBagConstraints.WEST;
        dfltSizeField = new JTextField(18);
        dfltSizeField.setFont(fieldFont);
        panel.add(dfltSizeField, gbc);
        row++;

        // ALLOW_UPD
        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel allowLabel = new JLabel("السماح بالتحديث:");
        allowLabel.setFont(labelFont);
        panel.add(allowLabel, gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.anchor = GridBagConstraints.WEST;
        allowUpdCombo = new JComboBox<>(new String[] {"نعم", "لا"});
        allowUpdCombo.setFont(fieldFont);
        panel.add(allowUpdCombo, gbc);
        row++;

        // UNT_SALE_TYP
        gbc.gridx = 1;
        gbc.gridy = row;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.EAST;
        JLabel saleLabel = new JLabel("نوع البيع:");
        saleLabel.setFont(labelFont);
        panel.add(saleLabel, gbc);
        gbc.gridx = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.anchor = GridBagConstraints.WEST;
        untSaleTypCombo = new JComboBox<>(new String[] {"بالقطعة", "بالوزن", "عادي"});
        untSaleTypCombo.setFont(fieldFont);
        panel.add(untSaleTypCombo, gbc);
        row++;

        return panel;
    }



    /**
     * إنشاء لوحة الجدول
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout(5, 5));
        panel.setBorder(BorderFactory.createTitledBorder(BorderFactory.createEtchedBorder(),
                "قائمة وحدات القياس", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        // لوحة البحث
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchPanel.add(new JLabel("البحث:", JLabel.RIGHT));
        searchField = new JTextField(20);
        searchField.setFont(arabicFont);
        searchField.addActionListener(e -> searchMeasurements());
        searchPanel.add(searchField);

        JButton searchBtn = createStyledButton("🔍", primaryColor);
        searchBtn.addActionListener(e -> searchMeasurements());
        searchPanel.add(searchBtn);

        panel.add(searchPanel, BorderLayout.NORTH);

        // الجدول
        String[] columnNames = {"الكود", "الاسم", "الاسم الأجنبي", "الكود العالمي", "النوع",
                "نوع البيع", "التحديث"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        measurementsTable = new JTable(tableModel);
        measurementsTable.setFont(arabicFont);
        measurementsTable.getTableHeader().setFont(arabicBoldFont);
        measurementsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        measurementsTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedMeasurement();
            }
        });

        JScrollPane tableScrollPane = new JScrollPane(measurementsTable);
        panel.add(tableScrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء شريط الحالة
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEtchedBorder());

        JLabel statusLabel = new JLabel("جاهز - نظام إدارة وحدات القياس");
        statusLabel.setFont(arabicFont);
        panel.add(statusLabel, BorderLayout.WEST);

        JLabel dbLabel = new JLabel("قاعدة البيانات: Oracle SHIP_ERP");
        dbLabel.setFont(arabicFont);
        dbLabel.setForeground(successColor);
        panel.add(dbLabel, BorderLayout.EAST);

        return panel;
    }

    /**
     * إنشاء زر منسق
     */
    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setFont(arabicBoldFont);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setOpaque(true);
        return button;
    }

    /**
     * تحميل بيانات وحدات القياس
     */
    private void loadMeasurementsData() {
        try {
            List<OriginalMeasurement> measurements = measurementDAO.findAll();

            // مسح البيانات الحالية
            tableModel.setRowCount(0);

            // إضافة البيانات الجديدة
            for (OriginalMeasurement measurement : measurements) {
                Object[] row = {measurement.getMeasureCode(), measurement.getMeasure(),
                        measurement.getMeasureFNm(), measurement.getMeasureCodeGb(),
                        measurement.getMeasureTypeText(), measurement.getUntSaleTypText(),
                        measurement.isUpdateAllowed() ? "نعم" : "لا"};
                tableModel.addRow(row);
            }

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تحميل البيانات:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * تحميل الوحدة المحددة في النموذج
     */
    private void loadSelectedMeasurement() {
        int selectedRow = measurementsTable.getSelectedRow();
        if (selectedRow >= 0) {
            String code = (String) tableModel.getValueAt(selectedRow, 0);

            try {
                OriginalMeasurement measurement = measurementDAO.findByCode(code);
                if (measurement != null) {
                    populateForm(measurement);
                }
            } catch (SQLException e) {
                JOptionPane.showMessageDialog(this,
                        "خطأ في تحميل بيانات الوحدة:\n" + e.getMessage(), "خطأ",
                        JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * ملء النموذج ببيانات الوحدة
     */
    private void populateForm(OriginalMeasurement measurement) {
        measureCodeField.setText(measurement.getMeasureCode());
        measureField.setText(measurement.getMeasure());
        measureFNmField.setText(measurement.getMeasureFNm());
        measureCodeGbField.setText(measurement.getMeasureCodeGb());
        dfltSizeField.setText(
                measurement.getDfltSize() != null ? measurement.getDfltSize().toString() : "");

        // تحديد القيم في ComboBox
        if (measurement.getMeasureType() != null) {
            measureTypeCombo.setSelectedIndex(measurement.getMeasureType() - 1);
        }

        if (measurement.getMeasureWtType() != null) {
            measureWtTypeCombo.setSelectedIndex(measurement.getMeasureWtType());
        } else {
            measureWtTypeCombo.setSelectedIndex(0);
        }

        measureWtConnCombo.setSelectedIndex(
                measurement.getMeasureWtConn() != null ? measurement.getMeasureWtConn() : 0);

        allowUpdCombo.setSelectedIndex(
                measurement.getAllowUpd() != null && measurement.getAllowUpd() == 1 ? 0 : 1);

        if (measurement.getUntSaleTyp() != null) {
            untSaleTypCombo.setSelectedIndex(measurement.getUntSaleTyp() - 1);
        }
    }

    /**
     * مسح النموذج
     */
    private void clearForm() {
        measureCodeField.setText("");
        measureField.setText("");
        measureFNmField.setText("");
        measureCodeGbField.setText("");
        dfltSizeField.setText("");
        measureTypeCombo.setSelectedIndex(0);
        measureWtTypeCombo.setSelectedIndex(0);
        measureWtConnCombo.setSelectedIndex(0);
        allowUpdCombo.setSelectedIndex(0);
        untSaleTypCombo.setSelectedIndex(2);

        measurementsTable.clearSelection();
    }

    /**
     * حفظ وحدة القياس
     */
    private void saveMeasurement() {
        try {
            // التحقق من صحة البيانات
            if (!validateForm()) {
                return;
            }

            // إنشاء كائن الوحدة
            OriginalMeasurement measurement = createMeasurementFromForm();

            // التحقق من وجود الوحدة
            OriginalMeasurement existingMeasurement =
                    measurementDAO.findByCode(measurement.getMeasureCode());

            if (existingMeasurement != null) {
                // تحديث
                measurement.setAdUId(existingMeasurement.getAdUId());
                measurement.setAdDate(existingMeasurement.getAdDate());
                measurement.setUpUId(1); // معرف المستخدم الحالي
                measurementDAO.update(measurement);

                JOptionPane.showMessageDialog(this, "تم تحديث وحدة القياس بنجاح", "نجح",
                        JOptionPane.INFORMATION_MESSAGE);
            } else {
                // إضافة جديدة
                measurement.setAdUId(1); // معرف المستخدم الحالي
                measurementDAO.insert(measurement);

                JOptionPane.showMessageDialog(this, "تم إضافة وحدة القياس بنجاح", "نجح",
                        JOptionPane.INFORMATION_MESSAGE);
            }

            // تحديث الجدول
            loadMeasurementsData();
            clearForm();

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في حفظ البيانات:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * حذف وحدة القياس
     */
    private void deleteMeasurement() {
        int selectedRow = measurementsTable.getSelectedRow();
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار وحدة قياس للحذف", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        String code = (String) tableModel.getValueAt(selectedRow, 0);
        String name = (String) tableModel.getValueAt(selectedRow, 1);

        int result = JOptionPane.showConfirmDialog(this,
                "هل أنت متأكد من حذف وحدة القياس:\n" + name + " (" + code + ")?", "تأكيد الحذف",
                JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            try {
                measurementDAO.delete(code);

                JOptionPane.showMessageDialog(this, "تم حذف وحدة القياس بنجاح", "نجح",
                        JOptionPane.INFORMATION_MESSAGE);

                loadMeasurementsData();
                clearForm();

            } catch (SQLException e) {
                JOptionPane.showMessageDialog(this, "خطأ في حذف وحدة القياس:\n" + e.getMessage(),
                        "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * البحث في وحدات القياس
     */
    private void searchMeasurements() {
        String searchTerm = searchField.getText().trim();

        try {
            List<OriginalMeasurement> measurements;

            if (searchTerm.isEmpty()) {
                measurements = measurementDAO.findAll();
            } else {
                measurements = measurementDAO.search(searchTerm);
            }

            // مسح البيانات الحالية
            tableModel.setRowCount(0);

            // إضافة نتائج البحث
            for (OriginalMeasurement measurement : measurements) {
                Object[] row = {measurement.getMeasureCode(), measurement.getMeasure(),
                        measurement.getMeasureFNm(), measurement.getMeasureCodeGb(),
                        measurement.getMeasureTypeText(), measurement.getUntSaleTypText(),
                        measurement.isUpdateAllowed() ? "نعم" : "لا"};
                tableModel.addRow(row);
            }

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في البحث:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * تصدير البيانات
     */
    private void exportData() {
        try {
            List<OriginalMeasurement> measurements = measurementDAO.findAll();
            StringBuilder export = new StringBuilder();
            export.append(
                    "كود الوحدة\tاسم الوحدة\tالاسم الأجنبي\tالكود العالمي\tنوع القياس\tنوع الوزن\tربط الوزن\tالحجم الافتراضي\tالسماح بالتحديث\tنوع البيع\n");

            for (OriginalMeasurement measurement : measurements) {
                export.append(measurement.getMeasureCode()).append("\t");
                export.append(measurement.getMeasure()).append("\t");
                export.append(
                        measurement.getMeasureFNm() != null ? measurement.getMeasureFNm() : "")
                        .append("\t");
                export.append(
                        measurement.getMeasureCodeGb() != null ? measurement.getMeasureCodeGb()
                                : "")
                        .append("\t");
                export.append(measurement.getMeasureTypeText()).append("\t");
                export.append(
                        measurement.getMeasureWtType() != null ? measurement.getMeasureWtType()
                                : "")
                        .append("\t");
                export.append(
                        measurement.getMeasureWtConn() != null ? measurement.getMeasureWtConn()
                                : "")
                        .append("\t");
                export.append(measurement.getDfltSize() != null ? measurement.getDfltSize() : "")
                        .append("\t");
                export.append(measurement.isUpdateAllowed() ? "نعم" : "لا").append("\t");
                export.append(measurement.getUntSaleTypText()).append("\n");
            }

            // عرض البيانات في نافذة
            JTextArea textArea = new JTextArea(export.toString());
            textArea.setFont(arabicFont);
            textArea.setEditable(false);

            JScrollPane scrollPane = new JScrollPane(textArea);
            scrollPane.setPreferredSize(new Dimension(1000, 600));

            JOptionPane.showMessageDialog(this, scrollPane, "تصدير البيانات",
                    JOptionPane.INFORMATION_MESSAGE);

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تصدير البيانات:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * استيراد البيانات من النظام الأصلي IAS20251
     */
    private void importFromOriginal() {
        // تشغيل الاستيراد مباشرة مع معالجة أفضل للأخطاء
        try {
            System.out.println("🔄 بدء عملية الاستيراد من IAS20251...");

            // محاولة الاستيراد مباشرة
            ImportFromIAS20251.ImportResult result =
                    ImportFromIAS20251.importMeasurementUnits(this);

            // عرض النتيجة
            String message = result.message;
            if (result.success) {
                message += "\n\nتفاصيل الاستيراد:";
                message += "\n• إجمالي السجلات: " + result.totalRecords;
                message += "\n• تم استيرادها: " + result.importedRecords;
                if (result.errorRecords > 0) {
                    message += "\n• أخطاء: " + result.errorRecords;
                }

                JOptionPane.showMessageDialog(this, message, "نجح الاستيراد",
                        JOptionPane.INFORMATION_MESSAGE);

                // تحديث الجدول
                loadMeasurementsData();
                System.out.println("✅ تم الاستيراد وتحديث الجدول بنجاح");

            } else {
                JOptionPane.showMessageDialog(this, message, "فشل الاستيراد",
                        JOptionPane.ERROR_MESSAGE);
                System.err.println("❌ فشل الاستيراد: " + message);
            }

        } catch (Exception e) {
            String errorMsg = "خطأ في عملية الاستيراد:\n" + e.getMessage();
            JOptionPane.showMessageDialog(this, errorMsg, "خطأ", JOptionPane.ERROR_MESSAGE);
            System.err.println("❌ " + errorMsg);
            e.printStackTrace();
        }
    }



    /**
     * التحقق من صحة النموذج
     */
    private boolean validateForm() {
        if (measureCodeField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال كود وحدة القياس", "خطأ في البيانات",
                    JOptionPane.ERROR_MESSAGE);
            measureCodeField.requestFocus();
            return false;
        }

        if (measureField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال اسم وحدة القياس", "خطأ في البيانات",
                    JOptionPane.ERROR_MESSAGE);
            measureField.requestFocus();
            return false;
        }

        // التحقق من الحجم الافتراضي إذا تم إدخاله
        String dfltSizeText = dfltSizeField.getText().trim();
        if (!dfltSizeText.isEmpty()) {
            try {
                Double.parseDouble(dfltSizeText);
            } catch (NumberFormatException e) {
                JOptionPane.showMessageDialog(this, "الحجم الافتراضي يجب أن يكون رقماً صحيحاً",
                        "خطأ في البيانات", JOptionPane.ERROR_MESSAGE);
                dfltSizeField.requestFocus();
                return false;
            }
        }

        return true;
    }

    /**
     * إنشاء كائن الوحدة من النموذج
     */
    private OriginalMeasurement createMeasurementFromForm() {
        OriginalMeasurement measurement = new OriginalMeasurement();

        measurement.setMeasureCode(measureCodeField.getText().trim());
        measurement.setMeasure(measureField.getText().trim());
        measurement.setMeasureFNm(measureFNmField.getText().trim());
        measurement.setMeasureCodeGb(measureCodeGbField.getText().trim());

        measurement.setMeasureType(measureTypeCombo.getSelectedIndex() + 1);

        if (measureWtTypeCombo.getSelectedIndex() > 0) {
            measurement.setMeasureWtType(measureWtTypeCombo.getSelectedIndex());
        }

        measurement.setMeasureWtConn(measureWtConnCombo.getSelectedIndex());

        String dfltSizeText = dfltSizeField.getText().trim();
        if (!dfltSizeText.isEmpty()) {
            measurement.setDfltSize(Double.parseDouble(dfltSizeText));
        }

        measurement.setAllowUpd(allowUpdCombo.getSelectedIndex() == 0 ? 1 : 0);
        measurement.setUntSaleTyp(untSaleTypCombo.getSelectedIndex() + 1);

        return measurement;
    }

    @Override
    public void dispose() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        super.dispose();
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new OriginalMeasurementUnitsWindow().setVisible(true);
        });
    }
}
