# نافذة إضافة وحدة القياس المتقدمة
## Advanced Unit of Measure Dialog - Complete Development

---

## 🎯 نظرة عامة

تم تطوير **نافذة إضافة وحدة القياس المتقدمة** بشكل شامل ومتقدم لتوفير تجربة مستخدم احترافية ومتكاملة لإدارة وحدات القياس في نظام Ship ERP.

---

## ✨ الميزات الرئيسية

### **🔥 تصميم متقدم بالتبويبات**
- **4 تبويبات منظمة** لتجميع المعلومات بشكل منطقي
- **واجهة عربية كاملة** مع دعم RTL
- **تصميم احترافي** بألوان متناسقة وخطوط واضحة

### **📋 التبويبات المتاحة**:

#### **1. تبويب المعلومات الأساسية**
- **كود الوحدة** (إجباري) *
- **الاسم العربي** (إجباري) *
- **الاسم الإنجليزي**
- **الرمز**
- **الفئة** (إجباري) * - قائمة منسدلة شاملة
- **النظام** - النظام المتري/الإمبراطوري/الأمريكي/مخصص
- **الوصف**
- **مربعات اختيار**: نشط، وحدة أساسية، وحدة شائعة

#### **2. تبويب الإعدادات المتقدمة**
- **الوحدة الأساسية** - قائمة منسدلة ديناميكية
- **معامل التحويل** - مع التحقق من صحة البيانات
- **دقة العرض** - عدد الخانات العشرية
- **قيمة التقريب** - للحسابات الدقيقة
- **السماح بالكسور العشرية**
- **وحدة نظام**
- **منطقة ملاحظات** كبيرة ومريحة

#### **3. تبويب التحويلات**
- **جدول التحويلات** التفاعلي
- **إضافة تحويلات** مخصصة
- **حذف التحويلات** المحددة
- **صيغ التحويل** التلقائية

#### **4. تبويب الأمثلة والمعاينة**
- **منطقة أمثلة الاستخدام**
- **معاينة مباشرة** للوحدة
- **تحديث فوري** عند تغيير البيانات

---

## 🎨 التصميم والواجهة

### **الألوان المستخدمة**:
- **الأزرق الأساسي**: `#007BFF` - للعناوين والأزرار الرئيسية
- **الأخضر**: `#28A745` - لزر الحفظ والحالات الإيجابية
- **الأصفر**: `#FFC107` - لزر إعادة التعيين والتحذيرات
- **الأحمر**: `#DC3545` - لزر الإلغاء والأخطاء
- **الرمادي الفاتح**: `#F8F9FA` - للخلفيات
- **الرمادي الحدودي**: `#CED4DA` - للحدود

### **الخطوط**:
- **Tahoma عادي 12** - للنصوص العادية
- **Tahoma عريض 12** - للتسميات والأزرار
- **Tahoma عريض 14** - للعناوين

### **الأحجام**:
- **النافذة**: 900x700 بكسل
- **الحقول**: 250x35 بكسل
- **الأزرار**: 120x40 بكسل
- **التسميات**: 150x35 بكسل

---

## 🔧 الميزات التقنية

### **التحقق من صحة البيانات**:
- **التحقق من الحقول الإجبارية**
- **التحقق من صحة معامل التحويل**
- **التحقق من القيم الرقمية**
- **رسائل خطأ واضحة ومفيدة**

### **التفاعل الذكي**:
- **تفعيل/تعطيل الحقول** حسب نوع الوحدة
- **تحديث المعاينة الفوري** عند الكتابة
- **تأثيرات Hover** على الأزرار
- **مؤشر اليد** عند التمرير على الأزرار

### **إدارة التحويلات**:
- **جدول تفاعلي** لعرض التحويلات
- **إضافة تحويلات** بنوافذ حوار بسيطة
- **حذف التحويلات** المحددة
- **صيغ تحويل تلقائية**

---

## 📊 هيكل البيانات

### **الحقول المدعومة**:
```java
- String code              // كود الوحدة
- String nameAr            // الاسم العربي
- String nameEn            // الاسم الإنجليزي
- String symbol            // الرمز
- String description       // الوصف
- String category          // الفئة
- String system            // النظام
- boolean isBaseUnit       // وحدة أساسية
- boolean isActive         // نشط
- boolean allowDecimal     // السماح بالكسور
- boolean isSystemUnit     // وحدة نظام
- boolean isCommon         // وحدة شائعة
- String baseUnitCode      // كود الوحدة الأساسية
- double conversionFactor  // معامل التحويل
- int precision            // دقة العرض
- double rounding          // قيمة التقريب
- String notes             // ملاحظات
- String examples          // أمثلة الاستخدام
```

---

## 🚀 طريقة الاستخدام

### **من نافذة إدارة وحدات القياس**:
1. اضغط زر **"إضافة جديد"**
2. ستفتح **النافذة المتقدمة** بـ 4 تبويبات
3. املأ **المعلومات الأساسية** (التبويب الأول)
4. اضبط **الإعدادات المتقدمة** (التبويب الثاني)
5. أضف **التحويلات** إذا لزم الأمر (التبويب الثالث)
6. راجع **المعاينة والأمثلة** (التبويب الرابع)
7. اضغط **"حفظ"** لحفظ الوحدة

### **التحقق التلقائي**:
- **الحقول الإجبارية** مميزة بـ *
- **رسائل خطأ فورية** عند الأخطاء
- **معاينة مباشرة** للوحدة المدخلة
- **تعطيل الحقول** غير المناسبة تلقائياً

---

## 🎯 الفوائد المحققة

### **للمستخدم**:
- **واجهة سهلة ومنظمة** بالتبويبات
- **إدخال بيانات شامل** ومفصل
- **معاينة فورية** للنتائج
- **تحقق تلقائي** من صحة البيانات
- **تجربة مستخدم احترافية**

### **للنظام**:
- **بيانات دقيقة ومكتملة**
- **تحويلات مخصصة** للوحدات
- **مرونة في التكوين**
- **سهولة الصيانة والتطوير**

---

## 📁 الملفات المضافة

### **الملف الرئيسي**:
- **`AdvancedUnitOfMeasureDialog.java`** - النافذة المتقدمة الكاملة

### **التحديثات**:
- **`UnitOfMeasureWindow.java`** - تحديث لاستخدام النافذة الجديدة

### **الميزات المضافة**:
- **4 تبويبات منظمة** للبيانات
- **تحقق شامل** من صحة البيانات
- **جدول تحويلات** تفاعلي
- **معاينة مباشرة** للوحدة
- **تصميم احترافي** متكامل

---

## 🔄 التكامل مع النظام

### **الاستدعاء**:
```java
AdvancedUnitOfMeasureDialog dialog = new AdvancedUnitOfMeasureDialog(
    parentWindow, 
    "إضافة وحدة قياس جديدة - النافذة المتقدمة", 
    null,  // للإضافة الجديدة
    unitsList  // قائمة الوحدات الموجودة
);
dialog.setVisible(true);

if (dialog.isConfirmed()) {
    UnitOfMeasureData newUnit = dialog.getUnitData();
    // معالجة الوحدة الجديدة
}
```

### **الإرجاع**:
- **`isConfirmed()`** - للتحقق من تأكيد الحفظ
- **`getUnitData()`** - للحصول على بيانات الوحدة

---

## 🎊 النتيجة النهائية

**✅ نافذة إضافة وحدة قياس متقدمة ومتكاملة!**

- **🏆 4 تبويبات منظمة** للبيانات المختلفة
- **🏆 تصميم احترافي** بألوان وخطوط متناسقة
- **🏆 تحقق شامل** من صحة البيانات
- **🏆 معاينة مباشرة** وتفاعل ذكي
- **🏆 جدول تحويلات** قابل للتخصيص
- **🏆 واجهة عربية كاملة** مع دعم RTL
- **🏆 تجربة مستخدم متقدمة** وسهلة الاستخدام

**🎉 النافذة جاهزة للاستخدام وتوفر تجربة إدخال بيانات احترافية ومتكاملة!**
