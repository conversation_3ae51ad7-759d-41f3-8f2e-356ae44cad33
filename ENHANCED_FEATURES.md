# نظام إدارة الشحنات المحسن - الميزات المتقدمة
# Enhanced Ship ERP System - Advanced Features

## 🚀 المكتبات والأدوات المثبتة
## Installed Libraries and Tools

### 🎨 مكتبات تحسين الواجهة (UI Enhancement Libraries)

#### 1. **FlatLaf - Modern Look and Feel**
- **الوصف**: مكتبة Look and Feel حديثة ومسطحة
- **الميزات**:
  - ✅ تصميم مسطح حديث
  - ✅ دعم الوضع المظلم والفاتح
  - ✅ رسوم متحركة سلسة
  - ✅ تخصيص الألوان والخطوط
- **الاستخدام**: `FlatLightLaf.setup()` أو `FlatDarkLaf.setup()`

#### 2. **JFoenix - Material Design**
- **الوصف**: مكونات Material Design لـ JavaFX
- **الميزات**:
  - ✅ أزرار Material Design
  - ✅ حقول نص محسنة
  - ✅ تأثيرات الظلال
  - ✅ رسوم متحركة متقدمة

#### 3. **ControlsFX - Advanced Controls**
- **الوصف**: مكونات إضافية متقدمة لـ JavaFX
- **الميزات**:
  - ✅ مربعات حوار متقدمة
  - ✅ مكونات تحكم مخصصة
  - ✅ أدوات التحقق من صحة البيانات
  - ✅ مؤشرات التقدم المحسنة

### 📊 مكتبات الرسوم البيانية (Chart Libraries)

#### 1. **JFreeChart - Professional Charts**
- **الوصف**: مكتبة رسوم بيانية احترافية
- **الميزات**:
  - ✅ رسوم بيانية خطية ودائرية
  - ✅ رسوم بيانية شريطية ومساحية
  - ✅ تخصيص كامل للألوان والخطوط
  - ✅ تصدير للصور والـ PDF

#### 2. **TilesFX - Modern Tiles**
- **الوصف**: بلاطات حديثة للمعلومات
- **الميزات**:
  - ✅ بطاقات إحصائية جذابة
  - ✅ مؤشرات دائرية وخطية
  - ✅ رسوم متحركة للبيانات
  - ✅ تصميم responsive

### 🎭 مكتبات الرسوم المتحركة (Animation Libraries)

#### 1. **MaterialFX - Advanced Animations**
- **الوصف**: رسوم متحركة متقدمة ومكونات Material
- **الميزات**:
  - ✅ انتقالات سلسة بين الصفحات
  - ✅ تأثيرات hover متقدمة
  - ✅ رسوم متحركة للقوائم
  - ✅ تأثيرات الظهور والاختفاء

#### 2. **Trident - Timeline Animations**
- **الوصف**: إطار عمل للرسوم المتحركة المعتمدة على الوقت
- **الميزات**:
  - ✅ رسوم متحركة معقدة
  - ✅ تحكم دقيق في التوقيت
  - ✅ تأثيرات الانتقال المتقدمة

### 🔧 مكتبات التخطيط (Layout Libraries)

#### 1. **MigLayout - Advanced Layout Manager**
- **الوصف**: مدير تخطيط متقدم ومرن
- **الميزات**:
  - ✅ تخطيط مرن وقابل للتخصيص
  - ✅ دعم RTL كامل
  - ✅ تخطيط responsive
  - ✅ قيود متقدمة للمكونات

#### 2. **JGoodies Forms - Professional Layouts**
- **الوصف**: تخطيطات احترافية للنماذج
- **الميزات**:
  - ✅ نماذج منظمة ومرتبة
  - ✅ محاذاة تلقائية للمكونات
  - ✅ دعم متعدد الأعمدة

### 🎨 مكتبات الأيقونات (Icon Libraries)

#### 1. **FontAwesome Icons**
- **الوصف**: مجموعة شاملة من الأيقونات
- **الميزات**:
  - ✅ أكثر من 1000 أيقونة
  - ✅ أيقونات قابلة للتخصيص
  - ✅ دعم الألوان والأحجام المختلفة

#### 2. **Material Icons**
- **الوصف**: أيقونات Material Design
- **الميزات**:
  - ✅ تصميم Material متسق
  - ✅ أيقونات عالية الجودة
  - ✅ تكامل سهل مع التطبيق

## 🛠️ كيفية الاستخدام
## How to Use

### 1. **تحديث التبعيات**
```bash
# تشغيل ملف تحديث التبعيات
update-dependencies.bat
```

### 2. **تشغيل النظام المحسن**
```bash
# تشغيل النسخة المحسنة
run-enhanced-erp.bat
```

### 3. **تشغيل النسخة الأساسية**
```bash
# تشغيل النسخة الأساسية (بدون مكتبات خارجية)
run-ship-erp.bat
```

## 🎯 الميزات الجديدة في النسخة المحسنة
## New Features in Enhanced Version

### 🌙 **الوضع المظلم**
- تبديل فوري بين الوضع الفاتح والمظلم
- رسوم متحركة سلسة للانتقال
- حفظ تفضيلات المستخدم

### 📊 **لوحة التحكم التفاعلية**
- بطاقات إحصائية محسنة
- رسوم بيانية تفاعلية
- تحديث البيانات في الوقت الفعلي

### 🎨 **تصميم محسن**
- واجهة مسطحة حديثة
- ألوان متناسقة ومريحة للعين
- خطوط محسنة للنصوص العربية

### ⚡ **أداء محسن**
- تحميل أسرع للواجهات
- استهلاك ذاكرة محسن
- استجابة أفضل للتفاعلات

## 🔧 متطلبات النظام
## System Requirements

### الحد الأدنى:
- **Java**: 17 أو أحدث
- **الذاكرة**: 512 MB RAM
- **المساحة**: 100 MB مساحة فارغة
- **الشاشة**: 1024x768 أو أكبر

### المستحسن:
- **Java**: 21 أو أحدث
- **الذاكرة**: 1 GB RAM أو أكثر
- **المساحة**: 500 MB مساحة فارغة
- **الشاشة**: 1920x1080 أو أكبر

## 🐛 استكشاف الأخطاء
## Troubleshooting

### مشكلة: فشل تحميل المكتبات
**الحل**: 
1. تشغيل `update-dependencies.bat`
2. التأكد من الاتصال بالإنترنت
3. استخدام النسخة المبسطة إذا لزم الأمر

### مشكلة: عدم ظهور الرسوم البيانية
**الحل**:
1. التأكد من تثبيت JFreeChart
2. إعادة تجميع التطبيق
3. استخدام النسخة البديلة

### مشكلة: مشاكل في الخطوط العربية
**الحل**:
1. التأكد من تثبيت خطوط النظام
2. تحديث Java للإصدار الأحدث
3. تعيين ترميز UTF-8

## 📞 الدعم الفني
## Technical Support

للحصول على المساعدة:
1. مراجعة ملف الأخطاء (logs)
2. التأكد من متطلبات النظام
3. تجربة النسخة المبسطة أولاً

---

**تم تطوير النظام بواسطة فريق تطوير نظم إدارة الشحنات**
**Developed by Ship ERP Development Team**
