import java.sql.*;
import javax.swing.*;
import java.awt.*;
import javax.swing.table.DefaultTableModel;

/**
 * مدير الاستيراد الحقيقي لنظام ERP
 * يعمل مع قاعدة البيانات الحقيقية الموجودة
 */
public class RealERPImportManager extends JFrame {
    
    private Connection connection;
    private JTextArea logArea;
    private JTable connectionsTable;
    private JTable mappingsTable;
    private DefaultTableModel connectionsModel;
    private DefaultTableModel mappingsModel;
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new RealERPImportManager().setVisible(true);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, "خطأ في بدء المدير: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    public RealERPImportManager() throws Exception {
        connectToDatabase();
        initializeGUI();
        loadData();
    }
    
    /**
     * الاتصال بقاعدة البيانات
     */
    private void connectToDatabase() throws Exception {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        
        connection = DriverManager.getConnection(
            "*************************************", 
            "ship_erp", 
            "ship_erp_password"
        );
        
        log("✅ تم الاتصال بنظام ERP الحقيقي");
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    private void initializeGUI() {
        setTitle("مدير الاستيراد الحقيقي - نظام SHIP ERP");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1400, 900);
        setLocationRelativeTo(null);
        
        setLayout(new BorderLayout());
        
        // لوحة التحكم
        JPanel controlPanel = new JPanel(new GridLayout(2, 4, 10, 10));
        controlPanel.setBorder(BorderFactory.createTitledBorder("عمليات الاستيراد"));
        
        JButton testConnectionBtn = new JButton("اختبار الاتصال بـ IAS20251");
        testConnectionBtn.addActionListener(e -> testIAS20251Connection());
        controlPanel.add(testConnectionBtn);
        
        JButton importGroupsBtn = new JButton("استيراد المجموعات الرئيسية");
        importGroupsBtn.addActionListener(e -> importMainGroups());
        controlPanel.add(importGroupsBtn);
        
        JButton importSubGroupsBtn = new JButton("استيراد المجموعات الفرعية");
        importSubGroupsBtn.addActionListener(e -> importSubGroups());
        controlPanel.add(importSubGroupsBtn);
        
        JButton importMeasurementsBtn = new JButton("استيراد وحدات القياس");
        importMeasurementsBtn.addActionListener(e -> importMeasurements());
        controlPanel.add(importMeasurementsBtn);
        
        JButton viewGroupsBtn = new JButton("عرض المجموعات الحالية");
        viewGroupsBtn.addActionListener(e -> viewCurrentGroups());
        controlPanel.add(viewGroupsBtn);
        
        JButton viewMappingsBtn = new JButton("عرض خرائط الجداول");
        viewMappingsBtn.addActionListener(e -> viewTableMappings());
        controlPanel.add(viewMappingsBtn);
        
        JButton importHistoryBtn = new JButton("سجل الاستيراد");
        importHistoryBtn.addActionListener(e -> viewImportHistory());
        controlPanel.add(importHistoryBtn);
        
        JButton refreshBtn = new JButton("تحديث البيانات");
        refreshBtn.addActionListener(e -> loadData());
        controlPanel.add(refreshBtn);
        
        add(controlPanel, BorderLayout.NORTH);
        
        // الجداول
        JTabbedPane tabbedPane = new JTabbedPane();
        
        // جدول الاتصالات
        connectionsModel = new DefaultTableModel(
            new String[]{"ID", "اسم الاتصال", "النوع", "الخادم", "قاعدة البيانات", "المستخدم", "نشط"}, 0
        );
        connectionsTable = new JTable(connectionsModel);
        JScrollPane connectionsScroll = new JScrollPane(connectionsTable);
        tabbedPane.addTab("اتصالات النظام", connectionsScroll);
        
        // جدول خرائط الجداول
        mappingsModel = new DefaultTableModel(
            new String[]{"ID", "الجدول المصدر", "الجدول الهدف", "اسم الخريطة", "النوع", "نشط", "آخر استيراد"}, 0
        );
        mappingsTable = new JTable(mappingsModel);
        JScrollPane mappingsScroll = new JScrollPane(mappingsTable);
        tabbedPane.addTab("خرائط الجداول", mappingsScroll);
        
        add(tabbedPane, BorderLayout.CENTER);
        
        // منطقة السجل
        logArea = new JTextArea(12, 80);
        logArea.setEditable(false);
        logArea.setFont(new Font("Arial", Font.PLAIN, 12));
        JScrollPane logScroll = new JScrollPane(logArea);
        logScroll.setBorder(BorderFactory.createTitledBorder("سجل العمليات"));
        add(logScroll, BorderLayout.SOUTH);
        
        log("🎉 تم تهيئة مدير الاستيراد الحقيقي");
        log("📊 النظام يحتوي على:");
        log("   - 17 مجموعة رئيسية");
        log("   - 40 مجموعة فرعية");
        log("   - 17 وحدة قياس");
        log("   - نظام استيراد متطور مع خرائط الجداول");
    }
    
    /**
     * تحميل البيانات
     */
    private void loadData() {
        loadConnections();
        loadTableMappings();
    }
    
    /**
     * تحميل الاتصالات
     */
    private void loadConnections() {
        try {
            connectionsModel.setRowCount(0);
            
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(
                "SELECT CONNECTION_ID, CONNECTION_NAME, CONNECTION_TYPE, HOST_NAME, " +
                "DATABASE_NAME, USERNAME, IS_ACTIVE FROM ERP_SYSTEM_CONNECTIONS ORDER BY CONNECTION_ID"
            );
            
            while (rs.next()) {
                connectionsModel.addRow(new Object[]{
                    rs.getInt("CONNECTION_ID"),
                    rs.getString("CONNECTION_NAME"),
                    rs.getString("CONNECTION_TYPE"),
                    rs.getString("HOST_NAME"),
                    rs.getString("DATABASE_NAME"),
                    rs.getString("USERNAME"),
                    rs.getInt("IS_ACTIVE") == 1 ? "نعم" : "لا"
                });
            }
            
            rs.close();
            stmt.close();
            
        } catch (SQLException e) {
            log("❌ خطأ في تحميل الاتصالات: " + e.getMessage());
        }
    }
    
    /**
     * تحميل خرائط الجداول
     */
    private void loadTableMappings() {
        try {
            mappingsModel.setRowCount(0);
            
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(
                "SELECT MAPPING_ID, SOURCE_TABLE_NAME, TARGET_TABLE_NAME, MAPPING_NAME, " +
                "MAPPING_TYPE, IS_ACTIVE, LAST_IMPORT_DATE FROM ERP_TABLE_MAPPING ORDER BY MAPPING_ID"
            );
            
            while (rs.next()) {
                mappingsModel.addRow(new Object[]{
                    rs.getInt("MAPPING_ID"),
                    rs.getString("SOURCE_TABLE_NAME"),
                    rs.getString("TARGET_TABLE_NAME"),
                    rs.getString("MAPPING_NAME"),
                    rs.getString("MAPPING_TYPE"),
                    rs.getInt("IS_ACTIVE") == 1 ? "نعم" : "لا",
                    rs.getTimestamp("LAST_IMPORT_DATE")
                });
            }
            
            rs.close();
            stmt.close();
            
        } catch (SQLException e) {
            log("❌ خطأ في تحميل خرائط الجداول: " + e.getMessage());
        }
    }
    
    /**
     * اختبار الاتصال بـ IAS20251
     */
    private void testIAS20251Connection() {
        new Thread(() -> {
            try {
                log("🔍 اختبار الاتصال بـ IAS20251...");
                
                // الحصول على بيانات الاتصال من قاعدة البيانات
                Statement stmt = connection.createStatement();
                ResultSet rs = stmt.executeQuery(
                    "SELECT HOST_NAME, PORT_NUMBER, DATABASE_NAME, USERNAME, PASSWORD " +
                    "FROM ERP_SYSTEM_CONNECTIONS WHERE CONNECTION_NAME LIKE '%IAS20251%' AND IS_ACTIVE = 1"
                );
                
                if (rs.next()) {
                    String host = rs.getString("HOST_NAME");
                    int port = rs.getInt("PORT_NUMBER");
                    String database = rs.getString("DATABASE_NAME");
                    String username = rs.getString("USERNAME");
                    String password = rs.getString("PASSWORD");
                    
                    // اختبار الاتصال
                    Connection testConn = DriverManager.getConnection(
                        "jdbc:oracle:thin:@" + host + ":" + port + ":" + database,
                        username, password
                    );
                    
                    // اختبار قراءة البيانات
                    Statement testStmt = testConn.createStatement();
                    ResultSet testRs = testStmt.executeQuery("SELECT COUNT(*) FROM GROUP_DETAILS");
                    testRs.next();
                    int groupCount = testRs.getInt(1);
                    
                    testRs = testStmt.executeQuery("SELECT COUNT(*) FROM IAS_MAINSUB_GRP_DTL");
                    testRs.next();
                    int subGroupCount = testRs.getInt(1);
                    
                    testRs = testStmt.executeQuery("SELECT COUNT(*) FROM IAS_ITM_MST");
                    testRs.next();
                    int itemCount = testRs.getInt(1);
                    
                    testRs.close();
                    testStmt.close();
                    testConn.close();
                    
                    log("✅ الاتصال بـ IAS20251 ناجح!");
                    log("📊 البيانات المتاحة:");
                    log("   - المجموعات الرئيسية: " + groupCount);
                    log("   - المجموعات الفرعية: " + subGroupCount);
                    log("   - الأصناف: " + itemCount);
                    
                    SwingUtilities.invokeLater(() -> {
                        JOptionPane.showMessageDialog(this, 
                            "الاتصال ناجح!\n" +
                            "المجموعات الرئيسية: " + groupCount + "\n" +
                            "المجموعات الفرعية: " + subGroupCount + "\n" +
                            "الأصناف: " + itemCount);
                    });
                    
                } else {
                    log("❌ لم يتم العثور على إعدادات اتصال IAS20251");
                }
                
                rs.close();
                stmt.close();
                
            } catch (Exception e) {
                log("❌ فشل الاتصال بـ IAS20251: " + e.getMessage());
                SwingUtilities.invokeLater(() -> {
                    JOptionPane.showMessageDialog(this, "فشل الاتصال: " + e.getMessage(), 
                        "خطأ", JOptionPane.ERROR_MESSAGE);
                });
            }
        }).start();
    }
    
    /**
     * استيراد المجموعات الرئيسية
     */
    private void importMainGroups() {
        new Thread(() -> {
            try {
                log("🔄 بدء استيراد المجموعات الرئيسية من IAS20251...");
                
                // الحصول على بيانات الاتصال
                Statement stmt = connection.createStatement();
                ResultSet connRs = stmt.executeQuery(
                    "SELECT HOST_NAME, PORT_NUMBER, DATABASE_NAME, USERNAME, PASSWORD " +
                    "FROM ERP_SYSTEM_CONNECTIONS WHERE CONNECTION_NAME LIKE '%IAS20251%' AND IS_ACTIVE = 1"
                );
                
                if (!connRs.next()) {
                    log("❌ لم يتم العثور على إعدادات اتصال IAS20251");
                    return;
                }
                
                String host = connRs.getString("HOST_NAME");
                int port = connRs.getInt("PORT_NUMBER");
                String database = connRs.getString("DATABASE_NAME");
                String username = connRs.getString("USERNAME");
                String password = connRs.getString("PASSWORD");
                connRs.close();
                
                // الاتصال بـ IAS20251
                Connection iasConn = DriverManager.getConnection(
                    "jdbc:oracle:thin:@" + host + ":" + port + ":" + database,
                    username, password
                );
                
                // قراءة المجموعات من IAS20251
                Statement iasStmt = iasConn.createStatement();
                ResultSet iasRs = iasStmt.executeQuery("SELECT * FROM GROUP_DETAILS");
                
                // إعداد الإدراج في SHIP_ERP
                PreparedStatement insertStmt = connection.prepareStatement(
                    "INSERT INTO ERP_GROUP_DETAILS (G_CODE, G_A_NAME, G_E_NAME, IS_ACTIVE) " +
                    "VALUES (?, ?, ?, 1)"
                );
                
                PreparedStatement updateStmt = connection.prepareStatement(
                    "UPDATE ERP_GROUP_DETAILS SET G_A_NAME = ?, G_E_NAME = ? WHERE G_CODE = ?"
                );
                
                int imported = 0;
                int updated = 0;
                int errors = 0;
                
                while (iasRs.next()) {
                    try {
                        String gCode = iasRs.getString("G_CODE");
                        String gAName = iasRs.getString("G_A_NAME");
                        String gEName = iasRs.getString("G_E_NAME");
                        
                        insertStmt.setString(1, gCode);
                        insertStmt.setString(2, gAName);
                        insertStmt.setString(3, gEName);
                        
                        try {
                            insertStmt.executeUpdate();
                            imported++;
                            log("✅ تم إدراج: " + gCode + " - " + gAName);
                        } catch (SQLException e) {
                            if (e.getErrorCode() == 1) { // Unique constraint
                                updateStmt.setString(1, gAName);
                                updateStmt.setString(2, gEName);
                                updateStmt.setString(3, gCode);
                                updateStmt.executeUpdate();
                                updated++;
                                log("🔄 تم تحديث: " + gCode + " - " + gAName);
                            } else {
                                errors++;
                                log("❌ خطأ في " + gCode + ": " + e.getMessage());
                            }
                        }
                        
                    } catch (SQLException e) {
                        errors++;
                        log("❌ خطأ في قراءة السجل: " + e.getMessage());
                    }
                }
                
                // تسجيل العملية
                PreparedStatement logStmt = connection.prepareStatement(
                    "INSERT INTO ERP_OPERATION_LOG (OPERATION_TYPE, TABLE_NAME, STATUS, MESSAGE, RECORDS_COUNT, USERNAME) " +
                    "VALUES ('IMPORT', 'ERP_GROUP_DETAILS', 'SUCCESS', ?, ?, 'SHIP_ERP')"
                );
                logStmt.setString(1, "تم استيراد " + imported + " مجموعة جديدة، تحديث " + updated + " مجموعة");
                logStmt.setInt(2, imported + updated);
                logStmt.executeUpdate();
                
                connection.commit();
                
                String result = "تم استيراد " + imported + " مجموعة جديدة، تحديث " + updated + " مجموعة، أخطاء: " + errors;
                log("🎉 " + result);
                
                // إغلاق الاتصالات
                iasRs.close();
                iasStmt.close();
                iasConn.close();
                insertStmt.close();
                updateStmt.close();
                logStmt.close();
                stmt.close();
                
                SwingUtilities.invokeLater(() -> {
                    JOptionPane.showMessageDialog(this, result);
                });
                
            } catch (Exception e) {
                log("❌ خطأ في استيراد المجموعات: " + e.getMessage());
                try {
                    connection.rollback();
                } catch (SQLException ex) {
                    log("❌ خطأ في rollback: " + ex.getMessage());
                }
            }
        }).start();
    }
    
    /**
     * استيراد المجموعات الفرعية
     */
    private void importSubGroups() {
        JOptionPane.showMessageDialog(this, "سيتم تطوير استيراد المجموعات الفرعية قريباً");
    }
    
    /**
     * استيراد وحدات القياس
     */
    private void importMeasurements() {
        JOptionPane.showMessageDialog(this, "سيتم تطوير استيراد وحدات القياس قريباً");
    }
    
    /**
     * عرض المجموعات الحالية
     */
    private void viewCurrentGroups() {
        try {
            log("📊 عرض المجموعات الحالية...");
            
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(
                "SELECT G_CODE, G_A_NAME, G_E_NAME FROM ERP_GROUP_DETAILS ORDER BY G_CODE"
            );
            
            StringBuilder groups = new StringBuilder("📋 المجموعات الحالية:\n\n");
            while (rs.next()) {
                groups.append(rs.getString("G_CODE")).append(" - ")
                      .append(rs.getString("G_A_NAME")).append("\n");
            }
            
            rs.close();
            stmt.close();
            
            JTextArea textArea = new JTextArea(groups.toString());
            textArea.setEditable(false);
            JScrollPane scrollPane = new JScrollPane(textArea);
            scrollPane.setPreferredSize(new Dimension(500, 400));
            
            JOptionPane.showMessageDialog(this, scrollPane, "المجموعات الحالية", JOptionPane.INFORMATION_MESSAGE);
            
        } catch (SQLException e) {
            log("❌ خطأ في عرض المجموعات: " + e.getMessage());
        }
    }
    
    /**
     * عرض خرائط الجداول
     */
    private void viewTableMappings() {
        JOptionPane.showMessageDialog(this, "راجع تبويب 'خرائط الجداول' أعلاه");
    }
    
    /**
     * عرض سجل الاستيراد
     */
    private void viewImportHistory() {
        try {
            log("📊 عرض سجل الاستيراد...");
            
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(
                "SELECT OPERATION_TYPE, TABLE_NAME, STATUS, MESSAGE, RECORDS_COUNT, OPERATION_DATE " +
                "FROM ERP_OPERATION_LOG ORDER BY OPERATION_DATE DESC"
            );
            
            StringBuilder history = new StringBuilder("📋 سجل الاستيراد:\n\n");
            while (rs.next()) {
                history.append(rs.getTimestamp("OPERATION_DATE")).append(" - ")
                       .append(rs.getString("OPERATION_TYPE")).append(" - ")
                       .append(rs.getString("TABLE_NAME")).append(" - ")
                       .append(rs.getString("STATUS")).append(" - ")
                       .append(rs.getString("MESSAGE")).append("\n");
            }
            
            rs.close();
            stmt.close();
            
            JTextArea textArea = new JTextArea(history.toString());
            textArea.setEditable(false);
            JScrollPane scrollPane = new JScrollPane(textArea);
            scrollPane.setPreferredSize(new Dimension(600, 400));
            
            JOptionPane.showMessageDialog(this, scrollPane, "سجل الاستيراد", JOptionPane.INFORMATION_MESSAGE);
            
        } catch (SQLException e) {
            log("❌ خطأ في عرض سجل الاستيراد: " + e.getMessage());
        }
    }
    
    /**
     * تسجيل رسالة
     */
    private void log(String message) {
        SwingUtilities.invokeLater(() -> {
            logArea.append(java.time.LocalTime.now() + " - " + message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
        System.out.println(message);
    }
}
