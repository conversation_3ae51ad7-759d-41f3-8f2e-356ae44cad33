-- ====================================
-- إدراج البيانات الأساسية لتطبيق ERP
-- Insert Basic Data for ERP Application
-- User: ship_erp
-- ====================================

-- 1. إدراج وحدات القياس الأساسية
-- ====================================

INSERT INTO UNITS_OF_MEASURE (UNIT_ID, UNIT_CODE, UNIT_NAME, UNIT_DESC, CREATED_BY) VALUES 
(SEQ_UNITS_OF_MEASURE.NEXTVAL, 'PCS', 'قطعة', 'وحدة العد بالقطع', 'SYSTEM');

INSERT INTO UNITS_OF_MEASURE (UNIT_ID, UNIT_CODE, UNIT_NAME, UNIT_DESC, CREATED_BY) VALUES 
(SEQ_UNITS_OF_MEASURE.NEXTVAL, 'KG', 'كيلوجرام', 'وحدة الوزن بالكيلوجرام', 'SYSTEM');

INSERT INTO UNITS_OF_MEASURE (UNIT_ID, UNIT_CODE, UNIT_NAME, UNIT_DESC, CREATED_BY) VALUES 
(SEQ_UNITS_OF_MEASURE.NEXTVAL, 'LTR', 'لتر', 'وحدة الحجم باللتر', 'SYSTEM');

INSERT INTO UNITS_OF_MEASURE (UNIT_ID, UNIT_CODE, UNIT_NAME, UNIT_DESC, CREATED_BY) VALUES 
(SEQ_UNITS_OF_MEASURE.NEXTVAL, 'MTR', 'متر', 'وحدة الطول بالمتر', 'SYSTEM');

INSERT INTO UNITS_OF_MEASURE (UNIT_ID, UNIT_CODE, UNIT_NAME, UNIT_DESC, CREATED_BY) VALUES 
(SEQ_UNITS_OF_MEASURE.NEXTVAL, 'BOX', 'صندوق', 'وحدة التعبئة بالصندوق', 'SYSTEM');

-- 2. إدراج فئات الأصناف الأساسية
-- ====================================

INSERT INTO ITEM_CATEGORIES (CAT_ID, CAT_CODE, CAT_NAME, CAT_DESC, CREATED_BY) VALUES 
(SEQ_ITEM_CATEGORIES.NEXTVAL, '001', 'مواد غذائية', 'المواد الغذائية والمشروبات', 'SYSTEM');

INSERT INTO ITEM_CATEGORIES (CAT_ID, CAT_CODE, CAT_NAME, CAT_DESC, CREATED_BY) VALUES 
(SEQ_ITEM_CATEGORIES.NEXTVAL, '002', 'مواد تنظيف', 'مواد التنظيف والمطهرات', 'SYSTEM');

INSERT INTO ITEM_CATEGORIES (CAT_ID, CAT_CODE, CAT_NAME, CAT_DESC, CREATED_BY) VALUES 
(SEQ_ITEM_CATEGORIES.NEXTVAL, '003', 'أدوات مكتبية', 'الأدوات والمستلزمات المكتبية', 'SYSTEM');

INSERT INTO ITEM_CATEGORIES (CAT_ID, CAT_CODE, CAT_NAME, CAT_DESC, CREATED_BY) VALUES 
(SEQ_ITEM_CATEGORIES.NEXTVAL, '004', 'قطع غيار', 'قطع الغيار والمعدات', 'SYSTEM');

INSERT INTO ITEM_CATEGORIES (CAT_ID, CAT_CODE, CAT_NAME, CAT_DESC, CREATED_BY) VALUES 
(SEQ_ITEM_CATEGORIES.NEXTVAL, '005', 'أدوية ومستلزمات طبية', 'الأدوية والمستلزمات الطبية', 'SYSTEM');

-- 3. إدراج المواقع الأساسية
-- ====================================

INSERT INTO LOCATIONS (LOCATION_ID, LOCATION_CODE, LOCATION_NAME, LOCATION_DESC, CREATED_BY) VALUES 
(SEQ_LOCATIONS.NEXTVAL, 'MAIN', 'المخزن الرئيسي', 'المخزن الرئيسي للمواد', 'SYSTEM');

INSERT INTO LOCATIONS (LOCATION_ID, LOCATION_CODE, LOCATION_NAME, LOCATION_DESC, CREATED_BY) VALUES 
(SEQ_LOCATIONS.NEXTVAL, 'SEC', 'المخزن الثانوي', 'المخزن الثانوي للمواد', 'SYSTEM');

INSERT INTO LOCATIONS (LOCATION_ID, LOCATION_CODE, LOCATION_NAME, LOCATION_DESC, CREATED_BY) VALUES 
(SEQ_LOCATIONS.NEXTVAL, 'TEMP', 'المخزن المؤقت', 'المخزن المؤقت للمواد الواردة', 'SYSTEM');

-- 4. إدراج الموردين الأساسيين
-- ====================================

INSERT INTO SUPPLIERS (SUPPLIER_ID, SUPPLIER_CODE, SUPPLIER_NAME, CONTACT_PERSON, PHONE, CREATED_BY) VALUES 
(SEQ_SUPPLIERS.NEXTVAL, 'DEFAULT', 'مورد افتراضي', 'مدير المبيعات', '************', 'SYSTEM');

INSERT INTO SUPPLIERS (SUPPLIER_ID, SUPPLIER_CODE, SUPPLIER_NAME, CONTACT_PERSON, PHONE, CREATED_BY) VALUES 
(SEQ_SUPPLIERS.NEXTVAL, 'LOCAL', 'مورد محلي', 'مندوب المبيعات', '************', 'SYSTEM');

-- 5. إدراج الأدوار الأساسية
-- ====================================

INSERT INTO ROLES (ROLE_ID, ROLE_NAME, ROLE_DESC, CREATED_BY) VALUES 
(SEQ_ROLES.NEXTVAL, 'ADMIN', 'مدير النظام', 'SYSTEM');

INSERT INTO ROLES (ROLE_ID, ROLE_NAME, ROLE_DESC, CREATED_BY) VALUES 
(SEQ_ROLES.NEXTVAL, 'USER', 'مستخدم عادي', 'SYSTEM');

INSERT INTO ROLES (ROLE_ID, ROLE_NAME, ROLE_DESC, CREATED_BY) VALUES 
(SEQ_ROLES.NEXTVAL, 'VIEWER', 'مستخدم للعرض فقط', 'SYSTEM');

-- 6. إدراج المستخدم الافتراضي
-- ====================================

INSERT INTO USERS (USER_ID, USERNAME, PASSWORD_HASH, FULL_NAME, EMAIL, CREATED_BY) VALUES 
(SEQ_USERS.NEXTVAL, 'admin', 'admin123', 'مدير النظام', 'admin@ship_erp.com', 'SYSTEM');

-- ربط المستخدم الافتراضي بدور المدير
INSERT INTO USER_ROLES (USER_ID, ROLE_ID, ASSIGNED_BY) VALUES 
(1, 1, 'SYSTEM');

-- 7. إدراج الإعدادات الأساسية
-- ====================================

INSERT INTO SYSTEM_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESC, SETTING_TYPE, MODIFIED_BY) VALUES 
(SEQ_SYSTEM_SETTINGS.NEXTVAL, 'APP_NAME', 'نظام إدارة الأصناف', 'اسم التطبيق', 'STRING', 'SYSTEM');

INSERT INTO SYSTEM_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESC, SETTING_TYPE, MODIFIED_BY) VALUES 
(SEQ_SYSTEM_SETTINGS.NEXTVAL, 'APP_VERSION', '1.0.0', 'إصدار التطبيق', 'STRING', 'SYSTEM');

INSERT INTO SYSTEM_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESC, SETTING_TYPE, MODIFIED_BY) VALUES 
(SEQ_SYSTEM_SETTINGS.NEXTVAL, 'DEFAULT_LANGUAGE', 'AR', 'اللغة الافتراضية', 'STRING', 'SYSTEM');

INSERT INTO SYSTEM_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESC, SETTING_TYPE, MODIFIED_BY) VALUES 
(SEQ_SYSTEM_SETTINGS.NEXTVAL, 'AUTO_BACKUP', '1', 'النسخ الاحتياطي التلقائي', 'BOOLEAN', 'SYSTEM');

INSERT INTO SYSTEM_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESC, SETTING_TYPE, MODIFIED_BY) VALUES 
(SEQ_SYSTEM_SETTINGS.NEXTVAL, 'MAX_IMPORT_RECORDS', '10000', 'الحد الأقصى لسجلات الاستيراد', 'NUMBER', 'SYSTEM');

INSERT INTO SYSTEM_SETTINGS (SETTING_ID, SETTING_KEY, SETTING_VALUE, SETTING_DESC, SETTING_TYPE, MODIFIED_BY) VALUES 
(SEQ_SYSTEM_SETTINGS.NEXTVAL, 'ENABLE_AUDIT_LOG', '1', 'تفعيل سجل المراجعة', 'BOOLEAN', 'SYSTEM');

-- 8. إدراج بيانات تجريبية للأصناف
-- ====================================

-- صنف تجريبي 1
INSERT INTO ITEMS (ITM_ID, ITM_CODE, ITM_NAME, ITM_DESC, CAT_ID, UNIT_ID, COST_PRICE, SELL_PRICE, LOCATION_CODE, SUPPLIER_ID, CREATED_BY) VALUES 
(SEQ_ITEMS.NEXTVAL, 'TEST-001', 'صنف تجريبي 1', 'صنف تجريبي للاختبار', 1, 1, 10.50, 15.75, 'MAIN', 1, 'SYSTEM');

-- صنف تجريبي 2
INSERT INTO ITEMS (ITM_ID, ITM_CODE, ITM_NAME, ITM_DESC, CAT_ID, UNIT_ID, COST_PRICE, SELL_PRICE, LOCATION_CODE, SUPPLIER_ID, CREATED_BY) VALUES 
(SEQ_ITEMS.NEXTVAL, 'TEST-002', 'صنف تجريبي 2', 'صنف تجريبي آخر للاختبار', 2, 2, 25.00, 35.00, 'MAIN', 2, 'SYSTEM');

-- صنف تجريبي 3
INSERT INTO ITEMS (ITM_ID, ITM_CODE, ITM_NAME, ITM_DESC, CAT_ID, UNIT_ID, COST_PRICE, SELL_PRICE, LOCATION_CODE, SUPPLIER_ID, CREATED_BY) VALUES 
(SEQ_ITEMS.NEXTVAL, 'TEST-003', 'صنف تجريبي 3', 'صنف تجريبي ثالث للاختبار', 3, 1, 5.25, 8.50, 'SEC', 1, 'SYSTEM');

COMMIT;

-- تم إنشاء البنية الأساسية بنجاح
-- Basic structure created successfully
