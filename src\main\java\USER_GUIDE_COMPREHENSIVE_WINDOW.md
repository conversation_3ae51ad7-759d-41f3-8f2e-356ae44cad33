# 📖 دليل المستخدم - نافذة بيانات الأصناف الشاملة
## User Guide - Comprehensive Item Data Window

---

## 🚀 **كيفية الوصول للنافذة:**

### **الطريق في شجرة الأنظمة:**
```
الشجرة الرئيسية
├── إدارة الأصناف
    └── بيانات الأصناف الشاملة  ← اضغط هنا
```

### **أو عبر سكريپت التشغيل:**
```bash
run_comprehensive_system.bat
```

---

## 📋 **هيكل النافذة الجديد:**

### **تبويب البيانات الأساسية (محدث):**

#### **قسم البيانات الأساسية:**
- **الصف الأول:** كود الصنف | اسم الصنف | الاسم الإنجليزي
- **الصف الثاني:** وصف الصنف | كود المجموعة | كود المجموعة الإدارية
- **الصف الثالث:** كود المجموعة الفرعية | رقم المساعد | رقم التفصيل
- **الصف الرابع:** الكود البديل | كود الشركة المصنعة | كود المورد
- **الصف الخامس:** حجم الصنف | نوع الصنف | مستوى الصنف

#### **قسم البيانات الرئيسية (جديد):**
- **الصف الأول:** الاسم الإنجليزي الإضافي | كود تصنيف المجموعة | التكلفة الأولية
- **الصف الثاني:** متوسط السعر المرجح | فترة الإرجاع | أيام انتهاء الصلاحية
- **الصف الثالث:** فترة الضمان | فترة الإرجاع قبل الانتهاء | سبب التعطيل
- **الصف الرابع:** تاريخ التعطيل | مستخدم التعطيل | معرف مستخدم الإضافة
- **الصف الخامس:** معرف مستخدم التحديث | اسم محطة الإضافة | اسم محطة التحديث
- **الصف السادس:** عداد التحديث | تقرير الطباعة | رقم المستند المرجعي
- **الصف السابع:** مسلسل المستند المرجعي

---

## 🎯 **المزايا الجديدة:**

### **1. تنظيم محسن:**
- ✅ **جميع حقول المجموعات** في قسم البيانات الأساسية
- ✅ **حقول متقدمة** في قسم البيانات الرئيسية
- ✅ **ترتيب منطقي** للحقول حسب الاستخدام

### **2. حقول من قاعدة البيانات الفعلية:**
- ✅ **جميع الحقول** من جداول `ias_itm_mst` و `ias_itm_dtl`
- ✅ **أسماء الحقول الصحيحة** كما هي في قاعدة البيانات
- ✅ **تسميات عربية** دقيقة من نظام التعليقات

### **3. استغلال أفضل للمساحة:**
- ✅ **6 أعمدة** بدلاً من 4 في البيانات الأساسية
- ✅ **34 حقل** في تبويب واحد
- ✅ **تجميع الحقول ذات الصلة** في صفوف منطقية

---

## 🔧 **كيفية الاستخدام:**

### **1. فتح النافذة:**
1. شغل النظام عبر `run_comprehensive_system.bat`
2. انتقل إلى شجرة الأنظمة
3. اختر "إدارة الأصناف" ← "بيانات الأصناف الشاملة"

### **2. استعراض البيانات:**
- **تبويب البيانات الأساسية:** للحقول الأساسية والمجموعات
- **تبويب التكاليف والأسعار:** للمعلومات المالية
- **تبويب المواصفات الفيزيائية:** للأبعاد والأوزان
- **تبويب الخيارات:** لصناديق الاختيار
- **تبويب قائمة الأصناف:** لاستعراض جميع الأصناف

### **3. البحث والتصفية:**
- استخدم **تبويب قائمة الأصناف** للبحث
- اكتب في حقل البحث واضغط "بحث"
- اختر صنف من القائمة لعرض تفاصيله

---

## 📊 **الحقول المتاحة:**

### **حقول البيانات الأساسية (15 حقل):**
1. كود الصنف (I_CODE)
2. اسم الصنف (I_NAME)
3. الاسم الإنجليزي (I_E_NAME)
4. وصف الصنف (I_DESC)
5. كود المجموعة (G_CODE)
6. كود المجموعة الإدارية (MNG_CODE)
7. كود المجموعة الفرعية (SUBG_CODE)
8. رقم المساعد (ASSISTANT_NO)
9. رقم التفصيل (DETAIL_NO)
10. الكود البديل (ALTER_CODE)
11. كود الشركة المصنعة (MANF_CODE)
12. كود المورد (V_CODE)
13. حجم الصنف (ITEM_SIZE)
14. نوع الصنف (ITEM_TYPE)
15. مستوى الصنف (ILEV_NO)

### **حقول البيانات الرئيسية (19 حقل):**
1. الاسم الإنجليزي الإضافي
2. كود تصنيف المجموعة (GRP_CLASS_CODE)
3. التكلفة الأولية (INIT_PRIMARY_COST)
4. متوسط السعر المرجح (I_CWTAVG)
5. فترة الإرجاع (RETURN_PERIOD)
6. أيام انتهاء الصلاحية (DAY_ITM_EXPIRE)
7. فترة الضمان (GRANT_PERIOD)
8. فترة الإرجاع قبل الانتهاء (RET_ITM_BEFOR_EXP_PRD)
9. سبب التعطيل (INACTIVE_RES)
10. تاريخ التعطيل (INACTIVE_DATE)
11. مستخدم التعطيل (INACTIVE_U_ID)
12. معرف مستخدم الإضافة (AD_U_ID)
13. معرف مستخدم التحديث (UP_U_ID)
14. اسم محطة الإضافة (AD_TRMNL_NM)
15. اسم محطة التحديث (UP_TRMNL_NM)
16. عداد التحديث (UP_CNT)
17. تقرير الطباعة (PR_REP)
18. رقم المستند المرجعي (DOC_NO_REF)
19. مسلسل المستند المرجعي (DOC_SER_REF)

---

## 💡 **نصائح للاستخدام:**

### **1. للمطورين:**
- جميع الحقول تستخدم نظام التعليقات للتسميات العربية
- يمكن إضافة حقول جديدة بسهولة باتباع نفس النمط
- الحقول مرتبطة بقاعدة البيانات الفعلية

### **2. للمستخدمين:**
- استخدم البحث في تبويب قائمة الأصناف للعثور على الأصناف
- جميع التسميات باللغة العربية لسهولة الفهم
- الحقول منظمة منطقياً من الأساسي إلى المتقدم

### **3. للإدارة:**
- النافذة تعرض جميع المعلومات المهمة في مكان واحد
- يمكن طباعة التقارير من الأزرار المتاحة
- النظام يحفظ سجل كامل للتعديلات والمستخدمين

---

## 🎉 **النتيجة:**

الآن لديك نافذة شاملة تعرض **34 حقل** من قاعدة البيانات الفعلية مع تنظيم منطقي وتسميات عربية دقيقة! 🚀
