package com.shipment.erp.repository;

import com.shipment.erp.model.BaseEntity;
import java.util.List;
import java.util.Optional;

/**
 * Repository أساسي لجميع الكيانات
 * يحتوي على العمليات الأساسية للوصول لقاعدة البيانات
 */
public interface BaseRepository<T extends BaseEntity> {

    /**
     * حفظ كيان جديد أو تحديث موجود
     */
    T save(T entity);

    /**
     * حفظ قائمة من الكيانات
     */
    List<T> saveAll(List<T> entities);

    /**
     * البحث عن كيان بالمعرف
     */
    Optional<T> findById(Long id);

    /**
     * التحقق من وجود كيان بالمعرف
     */
    boolean existsById(Long id);

    /**
     * الحصول على جميع الكيانات
     */
    List<T> findAll();

    /**
     * الحصول على جميع الكيانات مع ترقيم الصفحات
     */
    List<T> findAll(int page, int size);

    /**
     * الحصول على عدد الكيانات
     */
    long count();

    /**
     * حذف كيان بالمعرف
     */
    void deleteById(Long id);

    /**
     * حذف كيان
     */
    void delete(T entity);

    /**
     * حذف قائمة من الكيانات
     */
    void deleteAll(List<T> entities);

    /**
     * حذف جميع الكيانات
     */
    void deleteAll();

    /**
     * البحث عن الكيانات النشطة
     */
    List<T> findActive();

    /**
     * البحث عن الكيانات غير النشطة
     */
    List<T> findInactive();

    /**
     * البحث بالنص
     */
    List<T> search(String searchText);

    /**
     * البحث بالنص مع ترقيم الصفحات
     */
    List<T> search(String searchText, int page, int size);

    /**
     * عدد نتائج البحث
     */
    long countSearch(String searchText);

    /**
     * تحديث حالة النشاط
     */
    void updateActiveStatus(Long id, boolean isActive);

    /**
     * الحصول على آخر الكيانات المضافة
     */
    List<T> findLatest(int limit);

    /**
     * تنفيذ استعلام مخصص
     */
    List<T> executeQuery(String query, Object... parameters);

    /**
     * تنفيذ استعلام مخصص مع نتيجة واحدة
     */
    Optional<T> executeSingleQuery(String query, Object... parameters);

    /**
     * تنفيذ استعلام تحديث
     */
    int executeUpdate(String query, Object... parameters);

    /**
     * تحديث الكيان في الجلسة
     */
    void refresh(T entity);

    /**
     * فصل الكيان من الجلسة
     */
    void detach(T entity);

    /**
     * مسح ذاكرة التخزين المؤقت
     */
    void clear();

    /**
     * تنفيذ العمليات المؤجلة
     */
    void flush();
}
