#!/bin/bash

echo "========================================"
echo "تشغيل نظام إدارة الشحنات مع دعم النص العربي"
echo "Running ERP System with Arabic Text Support"
echo "========================================"

# إعداد متغيرات البيئة للنص العربي
export LANG=ar_SA.UTF-8
export LC_ALL=ar_SA.UTF-8
export JAVA_OPTS="-Dfile.encoding=UTF-8"
export JAVA_OPTS="$JAVA_OPTS -Dsun.jnu.encoding=UTF-8"
export JAVA_OPTS="$JAVA_OPTS -Duser.language=ar"
export JAVA_OPTS="$JAVA_OPTS -Duser.country=SA"
export JAVA_OPTS="$JAVA_OPTS -Duser.region=SA"
export JAVA_OPTS="$JAVA_OPTS -Dawt.useSystemAAFontSettings=lcd"
export JAVA_OPTS="$JAVA_OPTS -Dswing.aatext=true"
export JAVA_OPTS="$JAVA_OPTS -Dswing.useSystemAAFontSettings=lcd"
export JAVA_OPTS="$JAVA_OPTS -Dsun.java2d.xrender=true"
export JAVA_OPTS="$JAVA_OPTS -Dsun.java2d.pmoffscreen=false"

# الانتقال إلى مجلد الكود المصدري
cd "$(dirname "$0")/src/main/java"

echo "تجميع الملفات..."
echo "Compiling files..."

# تجميع مدير النصوص العربية أولاً
javac $JAVA_OPTS ArabicTextManager.java
if [ $? -ne 0 ]; then
    echo "خطأ في تجميع مدير النصوص العربية"
    echo "Error compiling ArabicTextManager"
    exit 1
fi

# تجميع TreeMenuPanel
javac $JAVA_OPTS TreeMenuPanel.java
if [ $? -ne 0 ]; then
    echo "خطأ في تجميع TreeMenuPanel"
    echo "Error compiling TreeMenuPanel"
    exit 1
fi

# تجميع النظام الرئيسي
javac $JAVA_OPTS EnhancedShipERP.java
if [ $? -ne 0 ]; then
    echo "خطأ في تجميع النظام الرئيسي"
    echo "Error compiling main system"
    exit 1
fi

echo "تم التجميع بنجاح!"
echo "Compilation successful!"
echo

echo "تشغيل النظام..."
echo "Starting system..."
echo

# تشغيل النظام مع الخيارات المحسنة للنص العربي
java $JAVA_OPTS EnhancedShipERP

echo
echo "تم إغلاق النظام"
echo "System closed"
