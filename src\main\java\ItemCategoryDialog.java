import javax.swing.*;
import java.awt.*;
import java.util.List;

/**
 * نافذة حوار إضافة/تعديل مجموعة الأصناف
 * Item Category Add/Edit Dialog
 */
public class ItemCategoryDialog extends JDialog {
    
    private JTextField codeField, nameArField, nameEnField, descriptionField, iconField, notesField;
    private JCheckBox isActiveCheck;
    private JLabel parentLabel;
    private JButton saveButton, cancelButton;
    private Font arabicFont;
    private boolean confirmed = false;
    private ItemCategoryData categoryData;
    private ItemCategoryData parentCategory;
    private List<ItemCategoryData> allCategories;
    
    public ItemCategoryDialog(JFrame parent, String title, ItemCategoryData editCategory, 
                             ItemCategoryData parentCategory, List<ItemCategoryData> allCategories) {
        super(parent, title, true);
        this.arabicFont = new Font("<PERSON><PERSON><PERSON>", Font.PLAIN, 12);
        this.categoryData = editCategory;
        this.parentCategory = parentCategory;
        this.allCategories = allCategories;
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        
        if (editCategory != null) {
            populateFields(editCategory);
        } else if (parentCategory != null) {
            parentLabel.setText("المجموعة الأب: " + parentCategory.getNameAr());
        }
        
        setSize(500, 450);
        setLocationRelativeTo(parent);
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
    }
    
    /**
     * تهيئة المكونات
     */
    private void initializeComponents() {
        // الحقول النصية
        codeField = createTextField();
        nameArField = createTextField();
        nameEnField = createTextField();
        descriptionField = createTextField();
        iconField = createTextField();
        iconField.setText("📁");
        notesField = createTextField();
        
        // مربع الاختيار
        isActiveCheck = new JCheckBox("نشط");
        isActiveCheck.setFont(arabicFont);
        isActiveCheck.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        isActiveCheck.setSelected(true);
        
        // تسمية المجموعة الأب
        parentLabel = new JLabel("مجموعة رئيسية");
        parentLabel.setFont(arabicFont);
        parentLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // الأزرار
        saveButton = createButton("حفظ", new Color(40, 167, 69));
        cancelButton = createButton("إلغاء", new Color(108, 117, 125));
    }
    
    /**
     * إنشاء حقل نصي مخصص
     */
    private JTextField createTextField() {
        JTextField field = new JTextField();
        field.setFont(arabicFont);
        field.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        field.setPreferredSize(new Dimension(250, 30));
        return field;
    }
    
    /**
     * إنشاء زر مخصص
     */
    private JButton createButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setFont(arabicFont);
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(100, 35));
        return button;
    }
    
    /**
     * إعداد التخطيط
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(8, 8, 8, 8);
        gbc.anchor = GridBagConstraints.EAST;
        
        // الصف الأول - الكود
        gbc.gridx = 1; gbc.gridy = 0;
        mainPanel.add(createLabel("الكود:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(codeField, gbc);
        
        // الصف الثاني - الاسم العربي
        gbc.gridx = 1; gbc.gridy = 1;
        mainPanel.add(createLabel("الاسم العربي:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(nameArField, gbc);
        
        // الصف الثالث - الاسم الإنجليزي
        gbc.gridx = 1; gbc.gridy = 2;
        mainPanel.add(createLabel("الاسم الإنجليزي:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(nameEnField, gbc);
        
        // الصف الرابع - الوصف
        gbc.gridx = 1; gbc.gridy = 3;
        mainPanel.add(createLabel("الوصف:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(descriptionField, gbc);
        
        // الصف الخامس - الأيقونة
        gbc.gridx = 1; gbc.gridy = 4;
        mainPanel.add(createLabel("الأيقونة:"), gbc);
        gbc.gridx = 0;
        JPanel iconPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 0, 0));
        iconPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        iconPanel.add(iconField);
        
        JButton iconHelpButton = new JButton("؟");
        iconHelpButton.setFont(arabicFont);
        iconHelpButton.setPreferredSize(new Dimension(30, 30));
        iconHelpButton.addActionListener(e -> showIconHelp());
        iconPanel.add(iconHelpButton);
        
        mainPanel.add(iconPanel, gbc);
        
        // الصف السادس - المجموعة الأب
        gbc.gridx = 1; gbc.gridy = 5;
        mainPanel.add(createLabel("المجموعة الأب:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(parentLabel, gbc);
        
        // الصف السابع - نشط
        gbc.gridx = 1; gbc.gridy = 6;
        mainPanel.add(new JLabel(), gbc);
        gbc.gridx = 0;
        mainPanel.add(isActiveCheck, gbc);
        
        // الصف الثامن - الملاحظات
        gbc.gridx = 1; gbc.gridy = 7;
        mainPanel.add(createLabel("ملاحظات:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(notesField, gbc);
        
        // لوحة الأزرار
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        buttonPanel.add(saveButton);
        buttonPanel.add(cancelButton);
        
        add(mainPanel, BorderLayout.CENTER);
        add(buttonPanel, BorderLayout.SOUTH);
    }
    
    /**
     * إنشاء تسمية مخصصة
     */
    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }
    
    /**
     * إعداد معالجات الأحداث
     */
    private void setupEventHandlers() {
        saveButton.addActionListener(e -> saveCategory());
        cancelButton.addActionListener(e -> dispose());
    }
    
    /**
     * عرض مساعدة الأيقونات
     */
    private void showIconHelp() {
        String iconHelp = "أمثلة على الأيقونات:\n\n" +
                         "📱 للإلكترونيات\n" +
                         "👔 للملابس\n" +
                         "🍎 للمواد الغذائية\n" +
                         "🏠 للأجهزة المنزلية\n" +
                         "📚 للكتب\n" +
                         "🚗 للسيارات\n" +
                         "⚽ للرياضة\n" +
                         "🎵 للموسيقى\n" +
                         "📁 افتراضي";
        
        JOptionPane.showMessageDialog(this, iconHelp, "مساعدة الأيقونات", JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * ملء الحقول بالبيانات الموجودة (للتعديل)
     */
    private void populateFields(ItemCategoryData category) {
        codeField.setText(category.getCode());
        nameArField.setText(category.getNameAr());
        nameEnField.setText(category.getNameEn());
        descriptionField.setText(category.getDescription());
        iconField.setText(category.getIcon());
        isActiveCheck.setSelected(category.isActive());
        notesField.setText(category.getNotes());
        
        if (category.getParentCode() != null) {
            ItemCategoryData parent = findCategoryByCode(category.getParentCode());
            if (parent != null) {
                parentLabel.setText("المجموعة الأب: " + parent.getNameAr());
            }
        }
    }
    
    /**
     * حفظ مجموعة الأصناف
     */
    private void saveCategory() {
        // التحقق من صحة البيانات
        if (!validateInput()) {
            return;
        }
        
        // إنشاء أو تحديث البيانات
        if (categoryData == null) {
            categoryData = new ItemCategoryData();
        }
        
        categoryData.setCode(codeField.getText().trim().toUpperCase());
        categoryData.setNameAr(nameArField.getText().trim());
        categoryData.setNameEn(nameEnField.getText().trim());
        categoryData.setDescription(descriptionField.getText().trim());
        categoryData.setIcon(iconField.getText().trim());
        categoryData.setActive(isActiveCheck.isSelected());
        categoryData.setNotes(notesField.getText().trim());
        
        if (parentCategory != null) {
            categoryData.setParentCode(parentCategory.getCode());
            categoryData.setLevel(parentCategory.getLevel() + 1);
        } else {
            categoryData.setParentCode(null);
            categoryData.setLevel(1);
        }
        
        confirmed = true;
        dispose();
    }
    
    /**
     * التحقق من صحة البيانات المدخلة
     */
    private boolean validateInput() {
        if (codeField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال كود المجموعة", "خطأ", JOptionPane.ERROR_MESSAGE);
            codeField.requestFocus();
            return false;
        }
        
        if (nameArField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال الاسم العربي", "خطأ", JOptionPane.ERROR_MESSAGE);
            nameArField.requestFocus();
            return false;
        }
        
        // التحقق من عدم تكرار الكود
        String code = codeField.getText().trim().toUpperCase();
        for (ItemCategoryData category : allCategories) {
            if (category.getCode().equals(code) && 
                (categoryData == null || !category.equals(categoryData))) {
                JOptionPane.showMessageDialog(this, "كود المجموعة موجود مسبقاً", "خطأ", JOptionPane.ERROR_MESSAGE);
                codeField.requestFocus();
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * البحث عن مجموعة بالكود
     */
    private ItemCategoryData findCategoryByCode(String code) {
        return allCategories.stream()
            .filter(cat -> cat.getCode().equals(code))
            .findFirst()
            .orElse(null);
    }
    
    public boolean isConfirmed() {
        return confirmed;
    }
    
    public ItemCategoryData getCategoryData() {
        return categoryData;
    }
}
