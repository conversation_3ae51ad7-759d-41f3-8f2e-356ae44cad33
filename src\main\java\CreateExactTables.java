import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * إنشاء الجداول بالضبط كما هي في المصدر بناءً على البنية المستخرجة الحقيقية
 */
public class CreateExactTables {

    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");

            Connection conn = DriverManager.getConnection("*************************************",
                    "ship_erp", "ship_erp_password");
            System.out.println("✅ تم الاتصال بـ SHIP_ERP");

            Statement stmt = conn.createStatement();

            // حذف الجداول القديمة
            try {
                stmt.execute("DROP TABLE IAS_ITM_DTL CASCADE CONSTRAINTS");
                System.out.println("🗑️ تم حذف IAS_ITM_DTL القديم");
            } catch (SQLException e) {
                // الجدول غير موجود
            }

            try {
                stmt.execute("DROP TABLE IAS_ITM_MST CASCADE CONSTRAINTS");
                System.out.println("🗑️ تم حذف IAS_ITM_MST القديم");
            } catch (SQLException e) {
                // الجدول غير موجود
            }

            // إنشاء IAS_ITM_MST بالضبط
            createIasMstTable(stmt);

            // إنشاء IAS_ITM_DTL بالضبط
            createIasDtlTable(stmt);

            // إنشاء المفاتيح والقيود
            createConstraints(stmt);

            stmt.close();
            conn.close();

            System.out.println("\n🎉 تم إنشاء الجداول بالضبط كما هي في المصدر!");

        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * إنشاء جدول IAS_ITM_MST بالضبط - 229 عمود
     */
    private static void createIasMstTable(Statement stmt) throws SQLException {
        System.out.println("🔨 إنشاء IAS_ITM_MST (229 عمود)...");

        String sql = """
                    CREATE TABLE IAS_ITM_MST (
                      I_CODE VARCHAR2(30) NOT NULL,
                      I_NAME VARCHAR2(100) NOT NULL,
                      I_E_NAME VARCHAR2(100),
                      G_CODE VARCHAR2(10) NOT NULL,
                      GRP_CLASS_CODE VARCHAR2(20),
                      MNG_CODE VARCHAR2(10),
                      SUBG_CODE VARCHAR2(10),
                      ITEM_SIZE NUMBER DEFAULT 1,
                      ITEM_TYPE NUMBER(5),
                      INIT_PRIMARY_COST NUMBER,
                      PRIMARY_COST NUMBER DEFAULT 0,
                      I_CWTAVG NUMBER DEFAULT 0,
                      I_DESC VARCHAR2(2000),
                      ALTER_CODE VARCHAR2(30),
                      MANF_CODE VARCHAR2(60),
                      BLOCKED NUMBER(1),
                      INACTIVE NUMBER(1) DEFAULT 0,
                      INACTIVE_RES VARCHAR2(250),
                      INACTIVE_DATE DATE,
                      INACTIVE_U_ID NUMBER(5),
                      SERVICE_ITM NUMBER(1) DEFAULT 0,
                      CASH_SALE NUMBER(1) DEFAULT 0,
                      NO_RETURN_SALE NUMBER(1) DEFAULT 0,
                      RETURN_PERIOD NUMBER(5),
                      KIT_ITM NUMBER(1) DEFAULT 0,
                      USE_EXP_DATE NUMBER(1) DEFAULT 0,
                      USE_BATCH_NO NUMBER(1) DEFAULT 0,
                      USE_SERIALNO NUMBER(1) DEFAULT 0,
                      USE_ATTCH NUMBER(1) DEFAULT 0,
                      VAT_TYPE NUMBER(1) DEFAULT 3,
                      VAT_PER NUMBER(7,4) DEFAULT 0,
                      ALLOW_DISC NUMBER(1) DEFAULT 0,
                      DISC_PER NUMBER(7,4),
                      ALLOW_DISC_PI NUMBER(1) DEFAULT 0,
                      REST_ITM NUMBER(1) DEFAULT 0,
                      DISC_PER_PI NUMBER(7,4),
                      ALLOW_FREE_QTY NUMBER(1) DEFAULT 0,
                      FREE_QTY_PER NUMBER(7,4),
                      USE_QTY_FRACTION NUMBER(1) DEFAULT 0,
                      UNDER_SELLING NUMBER(1) DEFAULT 0,
                      USE_ITM_IN_CSS_SYS_FLG NUMBER(1) DEFAULT 0,
                      RMS_ITM_TYP NUMBER(1),
                      GROUP_NO NUMBER(5),
                      ILEV_NO NUMBER(3),
                      I_IMG VARCHAR2(30),
                      DAY_ITM_EXPIRE NUMBER(3),
                      MIN_LMT_COST_PER NUMBER(3),
                      MAX_LMT_COST_PER NUMBER(3),
                      FIELD1 VARCHAR2(1000),
                      FIELD2 VARCHAR2(1000),
                      FIELD3 VARCHAR2(1000),
                      FIELD4 VARCHAR2(1000),
                      FIELD5 VARCHAR2(1000),
                      FIELD6 VARCHAR2(1000),
                      FIELD7 VARCHAR2(1000),
                      FIELD8 VARCHAR2(1000),
                      FIELD9 VARCHAR2(1000),
                      FIELD10 VARCHAR2(1000),
                      ASSISTANT_NO VARCHAR2(10),
                      DETAIL_NO VARCHAR2(10),
                      LENGTH_ITM NUMBER,
                      WIDTH_ITM NUMBER,
                      HEIGHT_ITM NUMBER,
                      SIZE_ITM NUMBER,
                      AREA_ITM NUMBER,
                      WEIGHT_ITM NUMBER,
                      SEASON_ITM VARCHAR2(60),
                      ORE_ITM VARCHAR2(60),
                      MARK_ITM VARCHAR2(60),
                      COMPANY_ITM VARCHAR2(60),
                      COUNTRY_ITM VARCHAR2(60),
                      INCOME_DATE DATE,
                      REQUIREMENT NUMBER(1) DEFAULT 0,
                      ITEM_STORE NUMBER(1) DEFAULT 0,
                      ASSETS NUMBER(1) DEFAULT 0,
                      HAS_COMM NUMBER(1) DEFAULT 0,
                      COMM_TYPE NUMBER(1),
                      COMM_AMT NUMBER,
                      ACTIVITY_NO NUMBER(5),
                      V_CODE VARCHAR2(15),
                      VNDR_A_CY VARCHAR2(7),
                      VNDR_PRICE NUMBER,
                      VNDR_I_CODE VARCHAR2(30),
                      HOT_KEY NUMBER(10),
                      LOW_LMT_PRNT_FRST NUMBER(5),
                      LOW_LMT_PRNT_SCND NUMBER(5),
                      LOW_LMT_APP_REQ_ATTCH NUMBER(5),
                      LOW_LMT_NOT_APP_REQ_ATTCH NUMBER(5),
                      IMP_XLS NUMBER(1) DEFAULT 0,
                      DOC_TYPE_REF NUMBER(5),
                      DOC_NO_REF NUMBER(15),
                      DOC_SER_REF NUMBER(38),
                      WEIGHTED NUMBER(1) DEFAULT 0,
                      BALANCE_NO NUMBER,
                      RET_ITM_BEFOR_EXP_PRD NUMBER(3),
                      MSUR_UNT_REP VARCHAR2(10),
                      PI_BILL_NO NUMBER,
                      USE_GRANT NUMBER(1) DEFAULT 0,
                      GRANT_PERIOD NUMBER(4),
                      ITM_COLOR VARCHAR2(60),
                      ITM_MEASURE VARCHAR2(60),
                      USE_WEIGHT NUMBER(1),
                      CONN_ITM_SO_INC NUMBER(1) DEFAULT 1,
                      LNK_BRCHR VARCHAR2(255),
                      LNK_YOUTUBE VARCHAR2(255),
                      USE_EMP_FLG NUMBER(1) DEFAULT 0,
                      ALTR_MLT_FLG NUMBER(1) DEFAULT 0,
                      I_F_DESC VARCHAR2(2000),
                      REST_ITM_COMBO NUMBER(1) DEFAULT 0,
                      USED_ITM NUMBER(1) DEFAULT 0,
                      MRP_ITM NUMBER(1) DEFAULT 0,
                      MRP_ITM_CLSS NUMBER(2),
                      CST_PER NUMBER(1),
                      PRFT_MRGN_PRCNT NUMBER,
                      CHK_AVL_QTY_IN_RES NUMBER(1) DEFAULT 0,
                      FOOD_GRP_NO VARCHAR2(7),
                      SUB_FOOD_GRP_NO VARCHAR2(7),
                      USE_AUTO_PST_RMS_DATA_TO_INV NUMBER(1) DEFAULT 0,
                      HPS_ITM NUMBER(1) DEFAULT 0,
                      PRCDR_TYP NUMBER(5),
                      SMPL_TYP NUMBER(5),
                      RNT_SRVC NUMBER(1) DEFAULT 0,
                      PST_WITH_ADMT NUMBER(1) DEFAULT 0,
                      PST_WITH_RNT NUMBER(1) DEFAULT 0,
                      HAS_PRCNT NUMBER(1) DEFAULT 0,
                      BRTH_SRVC NUMBER(1) DEFAULT 0,
                      EQPMNT_SRVC NUMBER(1) DEFAULT 0,
                      SRGRY_CLSS NUMBER(5),
                      SRVC_SORT NUMBER,
                      CSSD_FLG NUMBER(1) DEFAULT 0,
                      LNDRY_FLG NUMBER(1) DEFAULT 0,
                      GNR_ITM_FLG NUMBER(1) DEFAULT 0,
                      HSN_CODE VARCHAR2(100),
                      CST_STANDR NUMBER,
                      CST_LAST_PROD NUMBER,
                      ITM_ORDR_NO NUMBER,
                      USE_QR_CODE NUMBER(1) DEFAULT 0,
                      USE_PRICE_EXPIRE_DATE_OPTIONAL NUMBER(1) DEFAULT 0,
                      USE_PRICE_BATCH_NO_OPTIONAL NUMBER(1) DEFAULT 0,
                      FILL_ITM_CMPNNT_IN_RMS_INVC NUMBER(1) DEFAULT 0,
                      ITM_MIN_LMT_QTY NUMBER,
                      ITM_MAX_LMT_QTY NUMBER,
                      ITM_ROL_LMT_QTY NUMBER,
                      DFLT_ASM_ORD_QTY NUMBER,
                      PRCHS_SRVC_AC_CODE VARCHAR2(30),
                      INSRNC_FLG NUMBER(1) DEFAULT 0,
                      GTIN_CODE VARCHAR2(100),
                      USE_SRLNO_TYPE NUMBER(1),
                      USE_QR_CODE_TYPE NUMBER(1),
                      QR_CODE_MTHD_NO NUMBER(1),
                      FEED_ITM_FLG NUMBER(1) DEFAULT 0,
                      ITM_MIN_QTY NUMBER,
                      ITM_MAX_QTY NUMBER,
                      SHRT_ITM_L_NM VARCHAR2(100),
                      SHRT_ITM_F_NM VARCHAR2(100),
                      USE_BATCH_NO_AUTO_SQ_FLG NUMBER(1) DEFAULT 0,
                      BATCH_NO_MTHD_NO_SQ NUMBER(10),
                      HIDE_ITM_CHF_SCR_FLG NUMBER(1) DEFAULT 0,
                      EXEC_TAT NUMBER,
                      EXEC_TAT_UNT NUMBER(5),
                      KIT_ITM_CLSSFCTN_TYP NUMBER(1),
                      GET_AVL_QTY_FROM_CMPNNT_FLG NUMBER(1) DEFAULT 0,
                      STATCL_CLSS NUMBER(5),
                      LMT_QTY_SAL_CST NUMBER,
                      KIT_ITEM_RANK NUMBER,
                      WGHT_VAL_ITM NUMBER(5),
                      CHK_ITM_PCS_MNDTRY_FLG NUMBER(1) DEFAULT 0,
                      EXCD_PER NUMBER(10),
                      USED_IN_KIT_ITM NUMBER(1) DEFAULT 0,
                      MWS_CHK_FLG NUMBER(1),
                      DFLT_TIME_MNT NUMBER(10),
                      LOST_PRCNT_IN_PRCHS NUMBER,
                      CLC_AVG_FCTR_FOR_NUM_QTY NUMBER(1) DEFAULT 1,
                      CNTRLLD_ITM NUMBER(1) DEFAULT 0,
                      HPS_ITM_GNDR_TYP NUMBER(1) DEFAULT 3,
                      AD_U_ID NUMBER(5),
                      AD_DATE DATE,
                      UP_U_ID NUMBER(5),
                      UP_DATE DATE,
                      UP_CNT NUMBER(10),
                      PR_REP NUMBER(6) DEFAULT 0,
                      AD_TRMNL_NM VARCHAR2(50),
                      UP_TRMNL_NM VARCHAR2(50),
                      NO_SALE NUMBER(1) DEFAULT 0,
                      ITM_SAL_LMT_PRD_DAY NUMBER,
                      FREE_SMPL NUMBER(1) DEFAULT 0,
                      SPCLZTION NUMBER(6),
                      LOW_SAL_PRICE_ALLW_TYP NUMBER(1),
                      LOW_SAL_PRICE_ALLW_SGN CHAR(1),
                      LOW_SAL_PRICE_ALLW_VAL_TYP NUMBER(1),
                      LOW_SAL_PRICE_ALLW_VAL NUMBER,
                      USE_IN_SCNTFC_OFFICE NUMBER(1) DEFAULT 0,
                      ITM_BATCH_NO_COL VARCHAR2(10),
                      USE_IN_CRM_FLG NUMBER(1) DEFAULT 0,
                      MAX_CST_ORDR_LIMT NUMBER(10),
                      MIN_CST_ORDR_LIMT NUMBER(10),
                      FOOD_FLG NUMBER(1) DEFAULT 0,
                      ALLRGY_FLG NUMBER(1) DEFAULT 0,
                      USE_FDA_FLG NUMBER(1) DEFAULT 0,
                      STNDRD_CST_VAL NUMBER,
                      STNDRD_CST_PRCNT NUMBER,
                      RMS_SHW_NOTE_MNDTRY_FLG NUMBER(1) DEFAULT 0,
                      SRVC_EXEC_FLG NUMBER(1) DEFAULT 0,
                      APPRVD_ICODE_AS_SERIALNO NUMBER(1) DEFAULT 0,
                      GRNT_DSTNC NUMBER,
                      RMS_HID_FRM_CSTMR_ORDR_APP NUMBER(1) DEFAULT 0,
                      ALLW_UPDT_PRICE NUMBER(1) DEFAULT 0,
                      ITM_BARCODE_TYP NUMBER(2) DEFAULT 0,
                      ICODE_QTY_FRC NUMBER,
                      I_CODE_OPPST VARCHAR2(30),
                      USE_PARTITION NUMBER(1) DEFAULT 0,
                      USE_IN_VSS_FLG NUMBER(1) DEFAULT 0,
                      HPS_USE_MORT_FLG NUMBER(1) DEFAULT 0,
                      LAB_FARMS_TST NUMBER(1) DEFAULT 0,
                      RAY_HAS_NO_RPRT NUMBER(1) DEFAULT 0,
                      RAY_PLC_NO NUMBER(5),
                      RAY_RPRT_TYP NUMBER(1) DEFAULT 1,
                      SRVC_APRV_REQRD NUMBER(1) DEFAULT 0,
                      FIELD11 VARCHAR2(1000),
                      FIELD12 VARCHAR2(1000),
                      FIELD13 VARCHAR2(1000),
                      FIELD14 VARCHAR2(1000),
                      FIELD15 VARCHAR2(1000),
                      FIELD16 VARCHAR2(1000),
                      FIELD17 VARCHAR2(1000),
                      FIELD18 VARCHAR2(1000),
                      FIELD19 VARCHAR2(1000),
                      FIELD20 VARCHAR2(1000),
                      WT_SYS_RND_TYP NUMBER(1) DEFAULT 0
                    )
                """;

        stmt.execute(sql);
        System.out.println("✅ تم إنشاء IAS_ITM_MST بنجاح (229 عمود)");
    }

    /**
     * إنشاء جدول IAS_ITM_DTL بالضبط - 32 عمود
     */
    private static void createIasDtlTable(Statement stmt) throws SQLException {
        System.out.println("🔨 إنشاء IAS_ITM_DTL (32 عمود)...");

        String sql = """
                    CREATE TABLE IAS_ITM_DTL (
                      I_CODE VARCHAR2(30) NOT NULL,
                      ITM_UNT VARCHAR2(10) NOT NULL,
                      P_SIZE NUMBER DEFAULT 1 NOT NULL,
                      ITM_UNT_L_DSC VARCHAR2(250),
                      ITM_UNT_F_DSC VARCHAR2(250),
                      MAIN_UNIT NUMBER(1) DEFAULT 0,
                      SALE_UNIT NUMBER(1) DEFAULT 0,
                      PUR_UNIT NUMBER(1) DEFAULT 0,
                      STOCK_UNIT NUMBER(1) DEFAULT 0,
                      PRICE_UNIT NUMBER(1) DEFAULT 0,
                      CHF_UNT_FLG NUMBER(1) DEFAULT 0,
                      NO_SALE NUMBER(1) DEFAULT 0,
                      STORE_UNIT NUMBER(1) DEFAULT 0,
                      WEIGHT_UNIT NUMBER,
                      BARCODE VARCHAR2(100),
                      LVL_UNIT NUMBER,
                      EXCPTN_DISC_CRD_FLG NUMBER(1) DEFAULT 0,
                      QR_CODE_MTHD_NO NUMBER(1),
                      USE_SRLNO NUMBER(1) DEFAULT 0,
                      INACTIVE NUMBER(1) DEFAULT 0,
                      EXCLD_PNT_CLC_FLG NUMBER(1) DEFAULT 0,
                      INACTIVE_RES VARCHAR2(250),
                      INACTIVE_U_ID NUMBER(5),
                      INACTIVE_DATE DATE,
                      AD_U_ID NUMBER(5) NOT NULL,
                      AD_DATE DATE NOT NULL,
                      UP_U_ID NUMBER(5),
                      UP_DATE DATE,
                      UP_CNT NUMBER(10) DEFAULT 0,
                      TRNS_UNIT NUMBER(1) DEFAULT 0,
                      CSS_UNIT NUMBER(1) DEFAULT 0,
                      AUTO_BRCD_FLG NUMBER(1) DEFAULT 0
                    )
                """;

        stmt.execute(sql);
        System.out.println("✅ تم إنشاء IAS_ITM_DTL بنجاح (32 عمود)");
    }

    /**
     * إنشاء المفاتيح والقيود بالضبط
     */
    private static void createConstraints(Statement stmt) throws SQLException {
        System.out.println("🔗 إنشاء المفاتيح والقيود...");

        try {
            // المفتاح الأساسي لـ IAS_ITM_MST
            stmt.execute(
                    "ALTER TABLE IAS_ITM_MST ADD CONSTRAINT INV_ITM_MST_PK PRIMARY KEY (I_CODE)");
            System.out.println("  🔑 تم إنشاء المفتاح الأساسي لـ IAS_ITM_MST");

            // المفتاح الأساسي لـ IAS_ITM_DTL (مفتاح مركب)
            stmt.execute(
                    "ALTER TABLE IAS_ITM_DTL ADD CONSTRAINT IASITMDTL_UNT_PK PRIMARY KEY (I_CODE, ITM_UNT)");
            System.out.println("  🔑 تم إنشاء المفتاح الأساسي لـ IAS_ITM_DTL");

            // المفتاح الخارجي
            stmt.execute(
                    "ALTER TABLE IAS_ITM_DTL ADD CONSTRAINT IASITMDTL_I_CODE_FK FOREIGN KEY (I_CODE) REFERENCES IAS_ITM_MST(I_CODE)");
            System.out.println("  🔗 تم إنشاء المفتاح الخارجي");

        } catch (SQLException e) {
            System.out.println("  ⚠️ تحذير في إنشاء القيود: " + e.getMessage());
        }
    }
}
