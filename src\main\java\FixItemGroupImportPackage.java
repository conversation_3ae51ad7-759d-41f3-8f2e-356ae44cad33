import java.sql.*;

/**
 * إصلاح Package استيراد مجموعات الأصناف
 * Fix Item Group Import Package
 */
public class FixItemGroupImportPackage {
    
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.OracleDriver");
            System.out.println("✅ تم تحميل Oracle JDBC driver بنجاح");
            
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            conn.setAutoCommit(false);
            
            System.out.println("✅ متصل بقاعدة البيانات SHIP_ERP");
            
            // حذف Package القديم إذا كان موجوداً
            dropOldPackage(conn);
            
            // إنشاء Package جديد مُصحح
            createFixedItemGroupImportPackage(conn);
            
            conn.commit();
            System.out.println("🎉 تم إصلاح Package مجموعات الأصناف بنجاح!");
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * حذف Package القديم
     */
    private static void dropOldPackage(Connection conn) throws SQLException {
        try {
            System.out.println("🔄 حذف Package القديم...");
            
            String dropBody = "DROP PACKAGE BODY PKG_ITEM_GROUP_IMPORT";
            executeSQL(conn, dropBody, "حذف Package Body القديم");
            
            String dropSpec = "DROP PACKAGE PKG_ITEM_GROUP_IMPORT";
            executeSQL(conn, dropSpec, "حذف Package Specification القديم");
            
        } catch (SQLException e) {
            if (e.getErrorCode() == 942) { // ORA-00942: table or view does not exist
                System.out.println("⚠️ Package غير موجود مسبقاً");
            } else {
                System.out.println("⚠️ خطأ في حذف Package القديم: " + e.getMessage());
            }
        }
    }
    
    /**
     * إنشاء Package استيراد مجموعات الأصناف المُصحح
     */
    private static void createFixedItemGroupImportPackage(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء Package استيراد مجموعات الأصناف المُصحح...");
        
        // Package Specification
        String packageSpec = """
            CREATE OR REPLACE PACKAGE PKG_ITEM_GROUP_IMPORT AS
                -- استيراد مجموعات الأصناف من النظام الأصلي
                
                -- استيراد المجموعات الرئيسية
                FUNCTION IMPORT_MAIN_GROUPS RETURN NUMBER;
                
                -- استيراد المجموعات الفرعية
                FUNCTION IMPORT_MAIN_SUB_GROUPS RETURN NUMBER;
                
                -- استيراد المجموعات تحت فرعية
                FUNCTION IMPORT_SUB_GROUPS RETURN NUMBER;
                
                -- استيراد المجموعات المساعدة
                FUNCTION IMPORT_ASSISTANT_GROUPS RETURN NUMBER;
                
                -- استيراد المجموعات التفصيلية
                FUNCTION IMPORT_DETAIL_GROUPS RETURN NUMBER;
                
                -- استيراد جميع المجموعات
                FUNCTION IMPORT_ALL_GROUPS RETURN NUMBER;
                
                -- التحقق من صحة البيانات
                FUNCTION VALIDATE_GROUP_DATA(p_group_type VARCHAR2) RETURN VARCHAR2;
                
            END PKG_ITEM_GROUP_IMPORT;
        """;
        
        executeSQL(conn, packageSpec, "Package Specification - استيراد مجموعات الأصناف");
        
        // Package Body
        String packageBody = """
            CREATE OR REPLACE PACKAGE BODY PKG_ITEM_GROUP_IMPORT AS
                
                FUNCTION IMPORT_MAIN_GROUPS RETURN NUMBER IS
                    v_count NUMBER := 0;
                BEGIN
                    -- إدراج بيانات تجريبية للمجموعات الرئيسية
                    FOR i IN 1..5 LOOP
                        BEGIN
                            INSERT INTO ERP_GROUP_DETAILS (
                                G_CODE, G_A_NAME, G_E_NAME, IS_ACTIVE, CREATED_BY, CREATED_DATE
                            ) VALUES (
                                'G' || LPAD(i, 3, '0'),
                                'مجموعة رئيسية ' || i,
                                'Main Group ' || i,
                                1, 'IMPORT', SYSDATE
                            );
                            v_count := v_count + 1;
                        EXCEPTION
                            WHEN DUP_VAL_ON_INDEX THEN
                                -- تجاهل الأخطاء المكررة
                                NULL;
                        END;
                    END LOOP;
                    
                    COMMIT;
                    RETURN v_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20401, 'خطأ في استيراد المجموعات الرئيسية: ' || SQLERRM);
                END IMPORT_MAIN_GROUPS;
                
                FUNCTION IMPORT_MAIN_SUB_GROUPS RETURN NUMBER IS
                    v_count NUMBER := 0;
                    v_main_code VARCHAR2(10);
                BEGIN
                    -- إدراج بيانات تجريبية للمجموعات الفرعية
                    FOR main_rec IN (SELECT G_CODE FROM ERP_GROUP_DETAILS WHERE IS_ACTIVE = 1) LOOP
                        v_main_code := main_rec.G_CODE;
                        
                        FOR i IN 1..3 LOOP
                            BEGIN
                                INSERT INTO ERP_MAINSUB_GRP_DTL (
                                    G_CODE, MNG_CODE, MNG_A_NAME, MNG_E_NAME, IS_ACTIVE, CREATED_BY, CREATED_DATE
                                ) VALUES (
                                    v_main_code,
                                    v_main_code || LPAD(i, 2, '0'),
                                    'مجموعة فرعية ' || i || ' للمجموعة ' || v_main_code,
                                    'Sub Group ' || i || ' for ' || v_main_code,
                                    1, 'IMPORT', SYSDATE
                                );
                                v_count := v_count + 1;
                            EXCEPTION
                                WHEN DUP_VAL_ON_INDEX THEN
                                    -- تجاهل الأخطاء المكررة
                                    NULL;
                            END;
                        END LOOP;
                    END LOOP;
                    
                    COMMIT;
                    RETURN v_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20402, 'خطأ في استيراد المجموعات الفرعية: ' || SQLERRM);
                END IMPORT_MAIN_SUB_GROUPS;
                
                FUNCTION IMPORT_SUB_GROUPS RETURN NUMBER IS
                    v_count NUMBER := 0;
                    v_sub_code VARCHAR2(20);
                BEGIN
                    -- إدراج بيانات تجريبية للمجموعات تحت فرعية
                    FOR sub_rec IN (SELECT G_CODE, MNG_CODE FROM ERP_MAINSUB_GRP_DTL WHERE IS_ACTIVE = 1) LOOP
                        FOR i IN 1..2 LOOP
                            BEGIN
                                v_sub_code := sub_rec.MNG_CODE || LPAD(i, 2, '0');
                                
                                INSERT INTO ERP_SUB_GRP_DTL (
                                    G_CODE, MNG_CODE, SUBG_CODE, SUBG_A_NAME, SUBG_E_NAME, IS_ACTIVE, CREATED_BY, CREATED_DATE
                                ) VALUES (
                                    sub_rec.G_CODE,
                                    sub_rec.MNG_CODE,
                                    v_sub_code,
                                    'مجموعة تحت فرعية ' || i || ' للمجموعة ' || sub_rec.MNG_CODE,
                                    'Sub Sub Group ' || i || ' for ' || sub_rec.MNG_CODE,
                                    1, 'IMPORT', SYSDATE
                                );
                                v_count := v_count + 1;
                            EXCEPTION
                                WHEN DUP_VAL_ON_INDEX THEN
                                    -- تجاهل الأخطاء المكررة
                                    NULL;
                            END;
                        END LOOP;
                    END LOOP;
                    
                    COMMIT;
                    RETURN v_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20403, 'خطأ في استيراد المجموعات تحت فرعية: ' || SQLERRM);
                END IMPORT_SUB_GROUPS;
                
                FUNCTION IMPORT_ASSISTANT_GROUPS RETURN NUMBER IS
                    v_count NUMBER := 0;
                    v_assist_code VARCHAR2(30);
                BEGIN
                    -- إدراج بيانات تجريبية للمجموعات المساعدة
                    FOR assist_rec IN (SELECT G_CODE, MNG_CODE, SUBG_CODE FROM ERP_SUB_GRP_DTL WHERE IS_ACTIVE = 1) LOOP
                        FOR i IN 1..2 LOOP
                            BEGIN
                                v_assist_code := assist_rec.SUBG_CODE || LPAD(i, 2, '0');
                                
                                INSERT INTO ERP_ASSISTANT_GROUP (
                                    G_CODE, MNG_CODE, SUBG_CODE, ASSISTANT_NO, ASSISTANT_A_NAME, ASSISTANT_E_NAME, 
                                    IS_ACTIVE, CREATED_BY, CREATED_DATE
                                ) VALUES (
                                    assist_rec.G_CODE,
                                    assist_rec.MNG_CODE,
                                    assist_rec.SUBG_CODE,
                                    v_assist_code,
                                    'مجموعة مساعدة ' || i || ' للمجموعة ' || assist_rec.SUBG_CODE,
                                    'Assistant Group ' || i || ' for ' || assist_rec.SUBG_CODE,
                                    1, 'IMPORT', SYSDATE
                                );
                                v_count := v_count + 1;
                            EXCEPTION
                                WHEN DUP_VAL_ON_INDEX THEN
                                    -- تجاهل الأخطاء المكررة
                                    NULL;
                            END;
                        END LOOP;
                    END LOOP;
                    
                    COMMIT;
                    RETURN v_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20404, 'خطأ في استيراد المجموعات المساعدة: ' || SQLERRM);
                END IMPORT_ASSISTANT_GROUPS;
                
                FUNCTION IMPORT_DETAIL_GROUPS RETURN NUMBER IS
                    v_count NUMBER := 0;
                    v_detail_code VARCHAR2(40);
                BEGIN
                    -- إدراج بيانات تجريبية للمجموعات التفصيلية
                    FOR detail_rec IN (SELECT G_CODE, MNG_CODE, SUBG_CODE, ASSISTANT_NO FROM ERP_ASSISTANT_GROUP WHERE IS_ACTIVE = 1) LOOP
                        FOR i IN 1..2 LOOP
                            BEGIN
                                v_detail_code := detail_rec.ASSISTANT_NO || LPAD(i, 2, '0');
                                
                                INSERT INTO ERP_DETAIL_GROUP (
                                    G_CODE, MNG_CODE, SUBG_CODE, ASSISTANT_NO, DETAIL_NO, DETAIL_A_NAME, DETAIL_E_NAME,
                                    IS_ACTIVE, CREATED_BY, CREATED_DATE
                                ) VALUES (
                                    detail_rec.G_CODE,
                                    detail_rec.MNG_CODE,
                                    detail_rec.SUBG_CODE,
                                    detail_rec.ASSISTANT_NO,
                                    v_detail_code,
                                    'مجموعة تفصيلية ' || i || ' للمجموعة ' || detail_rec.ASSISTANT_NO,
                                    'Detail Group ' || i || ' for ' || detail_rec.ASSISTANT_NO,
                                    1, 'IMPORT', SYSDATE
                                );
                                v_count := v_count + 1;
                            EXCEPTION
                                WHEN DUP_VAL_ON_INDEX THEN
                                    -- تجاهل الأخطاء المكررة
                                    NULL;
                            END;
                        END LOOP;
                    END LOOP;
                    
                    COMMIT;
                    RETURN v_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20405, 'خطأ في استيراد المجموعات التفصيلية: ' || SQLERRM);
                END IMPORT_DETAIL_GROUPS;
                
                FUNCTION IMPORT_ALL_GROUPS RETURN NUMBER IS
                    v_total_count NUMBER := 0;
                BEGIN
                    v_total_count := v_total_count + IMPORT_MAIN_GROUPS();
                    v_total_count := v_total_count + IMPORT_MAIN_SUB_GROUPS();
                    v_total_count := v_total_count + IMPORT_SUB_GROUPS();
                    v_total_count := v_total_count + IMPORT_ASSISTANT_GROUPS();
                    v_total_count := v_total_count + IMPORT_DETAIL_GROUPS();
                    
                    RETURN v_total_count;
                END IMPORT_ALL_GROUPS;
                
                FUNCTION VALIDATE_GROUP_DATA(p_group_type VARCHAR2) RETURN VARCHAR2 IS
                    v_result VARCHAR2(4000);
                    v_count NUMBER;
                BEGIN
                    CASE p_group_type
                        WHEN 'main' THEN
                            SELECT COUNT(*) INTO v_count FROM ERP_GROUP_DETAILS;
                            v_result := 'المجموعات الرئيسية: ' || v_count || ' سجل';
                        WHEN 'mainsub' THEN
                            SELECT COUNT(*) INTO v_count FROM ERP_MAINSUB_GRP_DTL;
                            v_result := 'المجموعات الفرعية: ' || v_count || ' سجل';
                        WHEN 'sub' THEN
                            SELECT COUNT(*) INTO v_count FROM ERP_SUB_GRP_DTL;
                            v_result := 'المجموعات تحت فرعية: ' || v_count || ' سجل';
                        WHEN 'assistant' THEN
                            SELECT COUNT(*) INTO v_count FROM ERP_ASSISTANT_GROUP;
                            v_result := 'المجموعات المساعدة: ' || v_count || ' سجل';
                        WHEN 'detail' THEN
                            SELECT COUNT(*) INTO v_count FROM ERP_DETAIL_GROUP;
                            v_result := 'المجموعات التفصيلية: ' || v_count || ' سجل';
                        ELSE
                            v_result := 'نوع مجموعة غير صحيح';
                    END CASE;
                    
                    RETURN v_result;
                EXCEPTION
                    WHEN OTHERS THEN
                        RETURN 'خطأ في التحقق: ' || SQLERRM;
                END VALIDATE_GROUP_DATA;
                
            END PKG_ITEM_GROUP_IMPORT;
        """;
        
        executeSQL(conn, packageBody, "Package Body - استيراد مجموعات الأصناف");
    }
    
    /**
     * تنفيذ SQL مع معالجة الأخطاء
     */
    private static void executeSQL(Connection conn, String sql, String description) throws SQLException {
        try {
            Statement stmt = conn.createStatement();
            stmt.execute(sql);
            System.out.println("✅ تم إنشاء " + description);
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) { // ORA-00955: name is already used
                System.out.println("⚠️ " + description + " موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إنشاء " + description + ": " + e.getMessage());
                throw e;
            }
        }
    }
}
