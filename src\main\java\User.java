import java.util.Date;
import java.util.List;
import java.util.ArrayList;

/**
 * فئة المستخدم - تمثل بيانات المستخدم في النظام
 * User Class - Represents user data in the system
 */
public class User {
    
    private String userId;
    private String fullName;
    private String username;
    private String email;
    private String role;
    private String department;
    private String status;
    private String createdDate;
    private String expiryDate;
    private String city;
    private String phoneNumber;
    private String address;
    private String nationalId;
    private String emergencyContact;
    private List<String> permissions;
    private String lastLoginDate;
    private boolean isFirstLogin;
    private String notes;
    
    // المنشئ الأساسي
    public User(String userId, String fullName, String username, String email, 
                String role, String department, String status, String createdDate, 
                String expiryDate, String city) {
        this.userId = userId;
        this.fullName = fullName;
        this.username = username;
        this.email = email;
        this.role = role;
        this.department = department;
        this.status = status;
        this.createdDate = createdDate;
        this.expiryDate = expiryDate;
        this.city = city;
        this.permissions = new ArrayList<>();
        this.isFirstLogin = true;
        this.notes = "";
    }
    
    // المنشئ الكامل
    public User(String userId, String fullName, String username, String email, 
                String role, String department, String status, String createdDate, 
                String expiryDate, String city, String phoneNumber, String address, 
                String nationalId, String emergencyContact) {
        this(userId, fullName, username, email, role, department, status, 
             createdDate, expiryDate, city);
        this.phoneNumber = phoneNumber;
        this.address = address;
        this.nationalId = nationalId;
        this.emergencyContact = emergencyContact;
    }
    
    // Getters
    public String getUserId() { return userId; }
    public String getFullName() { return fullName; }
    public String getUsername() { return username; }
    public String getEmail() { return email; }
    public String getRole() { return role; }
    public String getDepartment() { return department; }
    public String getStatus() { return status; }
    public String getCreatedDate() { return createdDate; }
    public String getExpiryDate() { return expiryDate; }
    public String getCity() { return city; }
    public String getPhoneNumber() { return phoneNumber; }
    public String getAddress() { return address; }
    public String getNationalId() { return nationalId; }
    public String getEmergencyContact() { return emergencyContact; }
    public List<String> getPermissions() { return permissions; }
    public String getLastLoginDate() { return lastLoginDate; }
    public boolean isFirstLogin() { return isFirstLogin; }
    public String getNotes() { return notes; }
    
    // Setters
    public void setUserId(String userId) { this.userId = userId; }
    public void setFullName(String fullName) { this.fullName = fullName; }
    public void setUsername(String username) { this.username = username; }
    public void setEmail(String email) { this.email = email; }
    public void setRole(String role) { this.role = role; }
    public void setDepartment(String department) { this.department = department; }
    public void setStatus(String status) { this.status = status; }
    public void setCreatedDate(String createdDate) { this.createdDate = createdDate; }
    public void setExpiryDate(String expiryDate) { this.expiryDate = expiryDate; }
    public void setCity(String city) { this.city = city; }
    public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }
    public void setAddress(String address) { this.address = address; }
    public void setNationalId(String nationalId) { this.nationalId = nationalId; }
    public void setEmergencyContact(String emergencyContact) { this.emergencyContact = emergencyContact; }
    public void setPermissions(List<String> permissions) { this.permissions = permissions; }
    public void setLastLoginDate(String lastLoginDate) { this.lastLoginDate = lastLoginDate; }
    public void setFirstLogin(boolean firstLogin) { this.isFirstLogin = firstLogin; }
    public void setNotes(String notes) { this.notes = notes; }
    
    // دوال مساعدة
    public void addPermission(String permission) {
        if (!permissions.contains(permission)) {
            permissions.add(permission);
        }
    }
    
    public void removePermission(String permission) {
        permissions.remove(permission);
    }
    
    public boolean hasPermission(String permission) {
        return permissions.contains(permission);
    }
    
    public boolean isActive() {
        return "نشط".equals(status);
    }
    
    public boolean isExpired() {
        // في التطبيق الحقيقي، سيتم مقارنة تاريخ الانتهاء مع التاريخ الحالي
        return false;
    }
    
    @Override
    public String toString() {
        return fullName + " (" + username + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        User user = (User) obj;
        return userId.equals(user.userId);
    }
    
    @Override
    public int hashCode() {
        return userId.hashCode();
    }
    
    /**
     * إنشاء نسخة من المستخدم
     */
    public User copy() {
        User copy = new User(userId, fullName, username, email, role, department, 
                           status, createdDate, expiryDate, city, phoneNumber, 
                           address, nationalId, emergencyContact);
        copy.setPermissions(new ArrayList<>(permissions));
        copy.setLastLoginDate(lastLoginDate);
        copy.setFirstLogin(isFirstLogin);
        copy.setNotes(notes);
        return copy;
    }
    
    /**
     * التحقق من صحة بيانات المستخدم
     */
    public boolean isValid() {
        return userId != null && !userId.trim().isEmpty() &&
               fullName != null && !fullName.trim().isEmpty() &&
               username != null && !username.trim().isEmpty() &&
               email != null && !email.trim().isEmpty() && isValidEmail(email) &&
               role != null && !role.trim().isEmpty() &&
               department != null && !department.trim().isEmpty();
    }
    
    /**
     * التحقق من صحة البريد الإلكتروني
     */
    private boolean isValidEmail(String email) {
        return email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }
    
    /**
     * الحصول على معلومات المستخدم كنص
     */
    public String getDisplayInfo() {
        StringBuilder info = new StringBuilder();
        info.append("رقم المستخدم: ").append(userId).append("\n");
        info.append("الاسم الكامل: ").append(fullName).append("\n");
        info.append("اسم المستخدم: ").append(username).append("\n");
        info.append("البريد الإلكتروني: ").append(email).append("\n");
        info.append("الدور: ").append(role).append("\n");
        info.append("القسم: ").append(department).append("\n");
        info.append("الحالة: ").append(status).append("\n");
        info.append("تاريخ الإنشاء: ").append(createdDate).append("\n");
        info.append("تاريخ الانتهاء: ").append(expiryDate).append("\n");
        info.append("المدينة: ").append(city);
        
        if (phoneNumber != null && !phoneNumber.trim().isEmpty()) {
            info.append("\nرقم الهاتف: ").append(phoneNumber);
        }
        
        if (address != null && !address.trim().isEmpty()) {
            info.append("\nالعنوان: ").append(address);
        }
        
        return info.toString();
    }
}
