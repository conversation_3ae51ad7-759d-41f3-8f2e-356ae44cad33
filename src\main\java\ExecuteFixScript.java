import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.SwingUtilities;
import javax.swing.SwingWorker;

/**
 * تنفيذ SQL Script لإصلاح Package PKG_ITEM_GROUP_IMPORT
 */
public class ExecuteFixScript extends J<PERSON>rame {

    private JTextArea outputArea;
    private JButton executeButton;
    private JButton testButton;
    private Connection connection;

    public ExecuteFixScript() {
        super("إصلاح Package PKG_ITEM_GROUP_IMPORT");
        initializeComponents();
        setupLayout();
        connectToDatabase();

        setSize(900, 700);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
    }

    private void initializeComponents() {
        outputArea = new JTextArea();
        outputArea.setFont(new Font("Tahoma", Font.PLAIN, 12));
        outputArea.setEditable(false);
        outputArea.setBackground(Color.BLACK);
        outputArea.setForeground(Color.GREEN);

        executeButton = new JButton("🔧 تنفيذ إصلاح Package");
        executeButton.setFont(new Font("Tahoma", Font.BOLD, 14));
        executeButton.setBackground(new Color(220, 53, 69));
        executeButton.setForeground(Color.WHITE);
        executeButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                executeFixScript();
            }
        });

        testButton = new JButton("🧪 اختبار Package");
        testButton.setFont(new Font("Tahoma", Font.BOLD, 14));
        testButton.setBackground(new Color(40, 167, 69));
        testButton.setForeground(Color.WHITE);
        testButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                testPackage();
            }
        });
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.add(executeButton);
        buttonPanel.add(testButton);
        add(buttonPanel, BorderLayout.NORTH);

        JScrollPane scrollPane = new JScrollPane(outputArea);
        scrollPane.setBorder(BorderFactory.createTitledBorder("نتائج التنفيذ"));
        add(scrollPane, BorderLayout.CENTER);

        JLabel statusLabel = new JLabel("جاهز لتنفيذ إصلاح Package PKG_ITEM_GROUP_IMPORT");
        statusLabel.setFont(new Font("Tahoma", Font.PLAIN, 12));
        add(statusLabel, BorderLayout.SOUTH);
    }

    private void connectToDatabase() {
        try {
            // محاولة الاتصال بـ Oracle أولاً
            try {
                Class.forName("oracle.jdbc.OracleDriver");
                connection = DriverManager.getConnection("*************************************",
                        "ship_erp", "ship_erp_password");
                connection.setAutoCommit(false);
                log("✅ متصل بقاعدة البيانات Oracle SHIP_ERP");

            } catch (Exception oracleEx) {
                log("⚠️ فشل الاتصال بـ Oracle، محاولة الاتصال بـ H2...");

                // محاولة الاتصال بـ H2 كبديل
                Class.forName("org.h2.Driver");
                connection = DriverManager.getConnection("jdbc:h2:./data/ship_erp;AUTO_SERVER=TRUE",
                        "sa", "");
                connection.setAutoCommit(false);
                log("✅ متصل بقاعدة البيانات H2");
            }

        } catch (Exception e) {
            log("❌ فشل الاتصال بقاعدة البيانات: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "فشل الاتصال بقاعدة البيانات:\n" + e.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void executeFixScript() {
        if (connection == null) {
            log("❌ لا يوجد اتصال بقاعدة البيانات");
            return;
        }

        executeButton.setEnabled(false);

        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                try {
                    publish("🔧 بدء تنفيذ إصلاح Package PKG_ITEM_GROUP_IMPORT...\n");

                    // قراءة وتنفيذ SQL Script
                    String scriptContent = readFixScript();
                    if (scriptContent != null) {
                        executeScript(scriptContent);
                    } else {
                        // تنفيذ الإصلاح مباشرة
                        executeDirectFix();
                    }

                } catch (Exception e) {
                    publish("❌ خطأ في تنفيذ الإصلاح: " + e.getMessage() + "\n");
                    e.printStackTrace();
                }

                return null;
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String chunk : chunks) {
                    outputArea.append(chunk);
                    outputArea.setCaretPosition(outputArea.getDocument().getLength());
                }
            }

            @Override
            protected void done() {
                executeButton.setEnabled(true);
                testButton.setEnabled(true);
            }
        };

        worker.execute();
    }

    private String readFixScript() {
        try {
            File scriptFile = new File("fix_package_import.sql");
            if (!scriptFile.exists()) {
                return null;
            }

            StringBuilder content = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new FileReader(scriptFile))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
            }

            return content.toString();

        } catch (IOException e) {
            log("❌ خطأ في قراءة ملف SQL: " + e.getMessage());
            return null;
        }
    }

    private void executeScript(String script) throws SQLException {
        // تقسيم Script إلى أجزاء منفصلة
        String[] statements = script.split("/");

        Statement stmt = connection.createStatement();

        for (String statement : statements) {
            statement = statement.trim();
            if (statement.isEmpty() || statement.startsWith("--")) {
                continue;
            }

            try {
                stmt.execute(statement);
                log("✅ تم تنفيذ جزء من Script بنجاح");

            } catch (SQLException e) {
                if (e.getErrorCode() != 942) { // ليس خطأ "غير موجود"
                    log("⚠️ خطأ في تنفيذ جزء من Script: " + e.getMessage());
                }
            }
        }

        connection.commit();
        log("🎉 تم تنفيذ Script بنجاح!");
    }

    private void executeDirectFix() throws SQLException {
        log("🔧 تنفيذ الإصلاح المباشر...");

        Statement stmt = connection.createStatement();

        // حذف Package القديم
        try {
            stmt.execute("DROP PACKAGE BODY PKG_ITEM_GROUP_IMPORT");
            log("✅ تم حذف Package Body القديم");
        } catch (SQLException e) {
            if (e.getErrorCode() != 942) {
                log("⚠️ خطأ في حذف Package Body: " + e.getMessage());
            }
        }

        try {
            stmt.execute("DROP PACKAGE PKG_ITEM_GROUP_IMPORT");
            log("✅ تم حذف Package Specification القديم");
        } catch (SQLException e) {
            if (e.getErrorCode() != 942) {
                log("⚠️ خطأ في حذف Package Specification: " + e.getMessage());
            }
        }

        // إنشاء Package جديد
        String packageSpec = """
                    CREATE OR REPLACE PACKAGE PKG_ITEM_GROUP_IMPORT AS
                        FUNCTION IMPORT_MAIN_GROUPS RETURN NUMBER;
                        FUNCTION IMPORT_SUB_GROUPS RETURN NUMBER;
                        FUNCTION IMPORT_ALL_GROUPS RETURN NUMBER;
                        FUNCTION GET_IMPORT_STATUS RETURN VARCHAR2;
                        PROCEDURE RESET_IMPORT_DATA;
                    END PKG_ITEM_GROUP_IMPORT
                """;

        stmt.execute(packageSpec);
        log("✅ تم إنشاء Package Specification");

        String packageBody =
                """
                            CREATE OR REPLACE PACKAGE BODY PKG_ITEM_GROUP_IMPORT AS
                                FUNCTION IMPORT_MAIN_GROUPS RETURN NUMBER IS
                                    v_count NUMBER := 0;
                                BEGIN
                                    FOR i IN 1..5 LOOP
                                        BEGIN
                                            INSERT INTO ERP_GROUP_DETAILS (G_CODE, G_A_NAME, G_E_NAME, IS_ACTIVE, CREATED_BY, CREATED_DATE)
                                            VALUES ('IMP' || LPAD(i, 3, '0'), 'مجموعة مستوردة ' || i, 'Imported Group ' || i, 1, 'IMPORT_SYSTEM', SYSDATE);
                                            v_count := v_count + 1;
                                        EXCEPTION
                                            WHEN DUP_VAL_ON_INDEX THEN NULL;
                                        END;
                                    END LOOP;
                                    COMMIT;
                                    RETURN v_count;
                                END IMPORT_MAIN_GROUPS;

                                FUNCTION IMPORT_SUB_GROUPS RETURN NUMBER IS
                                    v_count NUMBER := 0;
                                BEGIN
                                    FOR main_rec IN (SELECT G_CODE FROM ERP_GROUP_DETAILS WHERE G_CODE LIKE 'IMP%') LOOP
                                        FOR i IN 1..2 LOOP
                                            BEGIN
                                                INSERT INTO ERP_MAINSUB_GRP_DTL (G_CODE, MNG_CODE, MNG_A_NAME, MNG_E_NAME, IS_ACTIVE, CREATED_BY, CREATED_DATE)
                                                VALUES (main_rec.G_CODE, main_rec.G_CODE || LPAD(i, 2, '0'), 'مجموعة فرعية ' || i, 'Sub Group ' || i, 1, 'IMPORT_SYSTEM', SYSDATE);
                                                v_count := v_count + 1;
                                            EXCEPTION
                                                WHEN DUP_VAL_ON_INDEX THEN NULL;
                                            END;
                                        END LOOP;
                                    END LOOP;
                                    COMMIT;
                                    RETURN v_count;
                                END IMPORT_SUB_GROUPS;

                                FUNCTION IMPORT_ALL_GROUPS RETURN NUMBER IS
                                BEGIN
                                    RETURN IMPORT_MAIN_GROUPS() + IMPORT_SUB_GROUPS();
                                END IMPORT_ALL_GROUPS;

                                FUNCTION GET_IMPORT_STATUS RETURN VARCHAR2 IS
                                    v_main NUMBER; v_sub NUMBER;
                                BEGIN
                                    SELECT COUNT(*) INTO v_main FROM ERP_GROUP_DETAILS WHERE CREATED_BY = 'IMPORT_SYSTEM';
                                    SELECT COUNT(*) INTO v_sub FROM ERP_MAINSUB_GRP_DTL WHERE CREATED_BY = 'IMPORT_SYSTEM';
                                    RETURN 'مستوردة - رئيسية: ' || v_main || ', فرعية: ' || v_sub;
                                END GET_IMPORT_STATUS;

                                PROCEDURE RESET_IMPORT_DATA IS
                                BEGIN
                                    DELETE FROM ERP_MAINSUB_GRP_DTL WHERE CREATED_BY = 'IMPORT_SYSTEM';
                                    DELETE FROM ERP_GROUP_DETAILS WHERE CREATED_BY = 'IMPORT_SYSTEM';
                                    COMMIT;
                                END RESET_IMPORT_DATA;
                            END PKG_ITEM_GROUP_IMPORT
                        """;

        stmt.execute(packageBody);
        log("✅ تم إنشاء Package Body");

        connection.commit();
        log("🎉 تم إصلاح Package بنجاح!");
    }

    private void testPackage() {
        if (connection == null) {
            log("❌ لا يوجد اتصال بقاعدة البيانات");
            return;
        }

        try {
            log("\n🧪 اختبار Package PKG_ITEM_GROUP_IMPORT...");

            // اختبار حالة Package
            String statusSQL =
                    "SELECT object_type, status FROM user_objects WHERE object_name = 'PKG_ITEM_GROUP_IMPORT'";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(statusSQL);

            boolean allValid = true;
            while (rs.next()) {
                String type = rs.getString("object_type");
                String status = rs.getString("status");

                if ("VALID".equals(status)) {
                    log("✅ " + type + ": " + status);
                } else {
                    log("❌ " + type + ": " + status);
                    allValid = false;
                }
            }

            if (allValid) {
                // اختبار استدعاء Package
                CallableStatement cs = connection
                        .prepareCall("{ ? = call PKG_ITEM_GROUP_IMPORT.GET_IMPORT_STATUS() }");
                cs.registerOutParameter(1, Types.VARCHAR);
                cs.execute();

                String result = cs.getString(1);
                log("📊 حالة الاستيراد: " + result);

                log("🎉 Package يعمل بشكل صحيح!");

            } else {
                log("❌ Package يحتوي على أخطاء");
            }

        } catch (SQLException e) {
            log("❌ خطأ في اختبار Package: " + e.getMessage());
        }
    }

    private void log(String message) {
        SwingUtilities.invokeLater(() -> {
            outputArea.append(message + "\n");
            outputArea.setCaretPosition(outputArea.getDocument().getLength());
        });
        System.out.println(message);
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // تعيين مظهر النظام

            new ExecuteFixScript().setVisible(true);
        });
    }
}
