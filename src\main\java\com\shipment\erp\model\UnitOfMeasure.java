package com.shipment.erp.model;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * نموذج وحدة القياس
 * Unit of Measure Entity
 */
@Entity
@Table(name = "units_of_measure")
public class UnitOfMeasure extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "code", unique = true, nullable = false, length = 20)
    private String code;
    
    @Column(name = "name_ar", nullable = false, length = 100)
    private String nameAr;
    
    @Column(name = "name_en", length = 100)
    private String nameEn;
    
    @Column(name = "description", length = 500)
    private String description;
    
    @Column(name = "symbol", length = 10)
    private String symbol;
    
    @Column(name = "is_base_unit")
    private Boolean isBaseUnit = false;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "base_unit_id")
    private UnitOfMeasure baseUnit;
    
    @Column(name = "conversion_factor")
    private Double conversionFactor = 1.0;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    @Column(name = "notes", length = 1000)
    private String notes;
    
    // Constructors
    public UnitOfMeasure() {}
    
    public UnitOfMeasure(String code, String nameAr) {
        this.code = code;
        this.nameAr = nameAr;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getNameAr() {
        return nameAr;
    }
    
    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }
    
    public String getNameEn() {
        return nameEn;
    }
    
    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getSymbol() {
        return symbol;
    }
    
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
    
    public Boolean getIsBaseUnit() {
        return isBaseUnit;
    }
    
    public void setIsBaseUnit(Boolean isBaseUnit) {
        this.isBaseUnit = isBaseUnit;
    }
    
    public UnitOfMeasure getBaseUnit() {
        return baseUnit;
    }
    
    public void setBaseUnit(UnitOfMeasure baseUnit) {
        this.baseUnit = baseUnit;
    }
    
    public Double getConversionFactor() {
        return conversionFactor;
    }
    
    public void setConversionFactor(Double conversionFactor) {
        this.conversionFactor = conversionFactor;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    @Override
    public String toString() {
        return nameAr + " (" + code + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        UnitOfMeasure that = (UnitOfMeasure) obj;
        return id != null && id.equals(that.id);
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
