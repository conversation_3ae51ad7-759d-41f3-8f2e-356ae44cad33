import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * إنشاء Packages و Procedures و Functions لنظام الربط والاستيراد في قاعدة بيانات SHIP_ERP
 */
public class CreateERPIntegrationPackages {

    private static Connection shipErpConnection;
    private static Connection ias20251Connection;

    public static void main(String[] args) {
        try {
            initializeConnections();

            System.out.println("🏗️ إنشاء Packages و Procedures و Functions للربط والاستيراد...");

            // إنشاء Package للربط والاستيراد
            createIntegrationPackage();

            // إنشاء Package لإدارة مجموعات الأصناف
            createItemGroupsPackage();

            // إنشاء Package للمرافق المساعدة
            createUtilitiesPackage();

            // إنشاء جداول التكوين
            createConfigurationTables();

            System.out.println("🎉 تم إنشاء جميع Packages و Functions بنجاح!");

        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        } finally {
            closeConnections();
        }
    }

    /**
     * تهيئة الاتصالات
     */
    private static void initializeConnections() throws Exception {
        Class.forName("oracle.jdbc.driver.OracleDriver");

        System.out.println("🔗 الاتصال بقواعد البيانات...");

        shipErpConnection = DriverManager.getConnection("*************************************",
                "ship_erp", "ship_erp_password");
        System.out.println("✅ تم الاتصال بـ SHIP_ERP");

        ias20251Connection = DriverManager.getConnection("*************************************",
                "ias20251", "ys123");
        System.out.println("✅ تم الاتصال بـ IAS20251");
    }

    /**
     * إنشاء Package الرئيسي للربط والاستيراد
     */
    private static void createIntegrationPackage() throws SQLException {
        System.out.println("📦 إنشاء Package ERP_INTEGRATION...");

        // إنشاء Package Specification
        String packageSpec = """
                    CREATE OR REPLACE PACKAGE ERP_INTEGRATION AS
                        -- إعدادات الاتصال
                        TYPE connection_config_rec IS RECORD (
                            host VARCHAR2(100),
                            port NUMBER,
                            service_name VARCHAR2(50),
                            username VARCHAR2(50),
                            password VARCHAR2(100)
                        );

                        -- نتائج العمليات
                        TYPE operation_result_rec IS RECORD (
                            success BOOLEAN,
                            message VARCHAR2(4000),
                            records_processed NUMBER,
                            errors_count NUMBER
                        );

                        -- استيراد مجموعات الأصناف من IAS20251
                        FUNCTION import_item_groups(
                            p_group_type VARCHAR2 DEFAULT 'ALL',
                            p_delete_existing BOOLEAN DEFAULT FALSE
                        ) RETURN operation_result_rec;

                        -- مزامنة البيانات
                        FUNCTION sync_data_from_ias(
                            p_table_name VARCHAR2,
                            p_sync_mode VARCHAR2 DEFAULT 'INCREMENTAL'
                        ) RETURN operation_result_rec;

                        -- التحقق من الاتصال
                        FUNCTION test_ias_connection RETURN BOOLEAN;

                        -- إحصائيات الاستيراد
                        FUNCTION get_import_statistics(
                            p_table_name VARCHAR2 DEFAULT NULL
                        ) RETURN SYS_REFCURSOR;

                        -- تسجيل العمليات
                        PROCEDURE log_operation(
                            p_operation_type VARCHAR2,
                            p_table_name VARCHAR2,
                            p_status VARCHAR2,
                            p_message VARCHAR2,
                            p_records_count NUMBER DEFAULT 0
                        );

                    END ERP_INTEGRATION;
                """;

        executeSQL(packageSpec, "Package Specification ERP_INTEGRATION");

        // إنشاء Package Body
        String packageBody = """
                    CREATE OR REPLACE PACKAGE BODY ERP_INTEGRATION AS

                        -- متغيرات عامة
                        g_ias_config connection_config_rec;

                        -- تهيئة إعدادات الاتصال
                        PROCEDURE init_ias_config IS
                        BEGIN
                            g_ias_config.host := 'localhost';
                            g_ias_config.port := 1521;
                            g_ias_config.service_name := 'orcl';
                            g_ias_config.username := 'ias20251';
                            g_ias_config.password := 'ys123';
                        END init_ias_config;

                        -- استيراد مجموعات الأصناف
                        FUNCTION import_item_groups(
                            p_group_type VARCHAR2 DEFAULT 'ALL',
                            p_delete_existing BOOLEAN DEFAULT FALSE
                        ) RETURN operation_result_rec IS

                            l_result operation_result_rec;
                            l_count NUMBER := 0;
                            l_errors NUMBER := 0;

                        BEGIN
                            l_result.success := TRUE;
                            l_result.records_processed := 0;
                            l_result.errors_count := 0;

                            -- تسجيل بداية العملية
                            log_operation('IMPORT', 'ITEM_GROUPS', 'STARTED',
                                'بدء استيراد مجموعات الأصناف - النوع: ' || p_group_type);

                            -- استيراد حسب النوع
                            IF p_group_type IN ('ALL', 'MAIN') THEN
                                l_count := l_count + import_main_groups(p_delete_existing);
                            END IF;

                            IF p_group_type IN ('ALL', 'SUB') THEN
                                l_count := l_count + import_sub_groups(p_delete_existing);
                            END IF;

                            IF p_group_type IN ('ALL', 'DETAIL') THEN
                                l_count := l_count + import_detail_groups(p_delete_existing);
                            END IF;

                            l_result.records_processed := l_count;
                            l_result.message := 'تم استيراد ' || l_count || ' سجل بنجاح';

                            -- تسجيل نهاية العملية
                            log_operation('IMPORT', 'ITEM_GROUPS', 'COMPLETED',
                                l_result.message, l_count);

                            COMMIT;
                            RETURN l_result;

                        EXCEPTION
                            WHEN OTHERS THEN
                                ROLLBACK;
                                l_result.success := FALSE;
                                l_result.message := 'خطأ في الاستيراد: ' || SQLERRM;
                                log_operation('IMPORT', 'ITEM_GROUPS', 'ERROR', l_result.message);
                                RETURN l_result;
                        END import_item_groups;

                        -- استيراد المجموعات الرئيسية
                        FUNCTION import_main_groups(p_delete_existing BOOLEAN) RETURN NUMBER IS
                            l_count NUMBER := 0;
                        BEGIN
                            IF p_delete_existing THEN
                                DELETE FROM ERP_GROUP_DETAILS;
                            END IF;

                            -- هنا يتم الاستيراد الفعلي من IAS20251
                            -- سيتم تطوير هذا الجزء لاحقاً مع Database Link

                            RETURN l_count;
                        END import_main_groups;

                        -- استيراد المجموعات الفرعية
                        FUNCTION import_sub_groups(p_delete_existing BOOLEAN) RETURN NUMBER IS
                            l_count NUMBER := 0;
                        BEGIN
                            IF p_delete_existing THEN
                                DELETE FROM ERP_MAINSUB_GRP_DTL;
                                DELETE FROM ERP_SUB_GRP_DTL;
                            END IF;

                            -- الاستيراد الفعلي
                            RETURN l_count;
                        END import_sub_groups;

                        -- استيراد المجموعات التفصيلية
                        FUNCTION import_detail_groups(p_delete_existing BOOLEAN) RETURN NUMBER IS
                            l_count NUMBER := 0;
                        BEGIN
                            IF p_delete_existing THEN
                                DELETE FROM ERP_ASSISTANT_GROUP;
                                DELETE FROM ERP_DETAIL_GROUP;
                            END IF;

                            -- الاستيراد الفعلي
                            RETURN l_count;
                        END import_detail_groups;

                        -- مزامنة البيانات
                        FUNCTION sync_data_from_ias(
                            p_table_name VARCHAR2,
                            p_sync_mode VARCHAR2 DEFAULT 'INCREMENTAL'
                        ) RETURN operation_result_rec IS

                            l_result operation_result_rec;
                        BEGIN
                            l_result.success := TRUE;
                            l_result.message := 'تم تنفيذ المزامنة بنجاح';
                            l_result.records_processed := 0;
                            l_result.errors_count := 0;

                            -- تسجيل العملية
                            log_operation('SYNC', p_table_name, 'COMPLETED',
                                'مزامنة ' || p_sync_mode || ' للجدول ' || p_table_name);

                            RETURN l_result;
                        END sync_data_from_ias;

                        -- اختبار الاتصال
                        FUNCTION test_ias_connection RETURN BOOLEAN IS
                        BEGIN
                            -- سيتم تطوير هذا لاحقاً
                            RETURN TRUE;
                        END test_ias_connection;

                        -- إحصائيات الاستيراد
                        FUNCTION get_import_statistics(
                            p_table_name VARCHAR2 DEFAULT NULL
                        ) RETURN SYS_REFCURSOR IS

                            l_cursor SYS_REFCURSOR;
                        BEGIN
                            IF p_table_name IS NULL THEN
                                OPEN l_cursor FOR
                                    SELECT operation_type, table_name, status,
                                           COUNT(*) as operation_count,
                                           SUM(records_count) as total_records,
                                           MAX(operation_date) as last_operation
                                    FROM ERP_OPERATION_LOG
                                    GROUP BY operation_type, table_name, status
                                    ORDER BY last_operation DESC;
                            ELSE
                                OPEN l_cursor FOR
                                    SELECT * FROM ERP_OPERATION_LOG
                                    WHERE table_name = p_table_name
                                    ORDER BY operation_date DESC;
                            END IF;

                            RETURN l_cursor;
                        END get_import_statistics;

                        -- تسجيل العمليات
                        PROCEDURE log_operation(
                            p_operation_type VARCHAR2,
                            p_table_name VARCHAR2,
                            p_status VARCHAR2,
                            p_message VARCHAR2,
                            p_records_count NUMBER DEFAULT 0
                        ) IS
                            PRAGMA AUTONOMOUS_TRANSACTION;
                        BEGIN
                            INSERT INTO ERP_OPERATION_LOG (
                                log_id, operation_type, table_name, status,
                                message, records_count, operation_date, username
                            ) VALUES (
                                ERP_LOG_SEQ.NEXTVAL, p_operation_type, p_table_name, p_status,
                                p_message, p_records_count, SYSDATE, USER
                            );
                            COMMIT;
                        EXCEPTION
                            WHEN OTHERS THEN
                                ROLLBACK;
                                -- تجاهل أخطاء التسجيل
                                NULL;
                        END log_operation;

                    BEGIN
                        -- تهيئة Package
                        init_ias_config;

                    END ERP_INTEGRATION;
                """;

        executeSQL(packageBody, "Package Body ERP_INTEGRATION");
    }

    /**
     * إنشاء Package لإدارة مجموعات الأصناف
     */
    private static void createItemGroupsPackage() throws SQLException {
        System.out.println("📦 إنشاء Package ERP_ITEM_GROUPS...");

        // Package Specification
        String packageSpec = """
                    CREATE OR REPLACE PACKAGE ERP_ITEM_GROUPS AS

                        -- إضافة مجموعة رئيسية
                        FUNCTION add_main_group(
                            p_g_code VARCHAR2,
                            p_g_a_name VARCHAR2,
                            p_g_e_name VARCHAR2,
                            p_tax_prcnt_dflt NUMBER DEFAULT 0,
                            p_rol_lmt_qty NUMBER DEFAULT 0
                        ) RETURN VARCHAR2;

                        -- تعديل مجموعة رئيسية
                        FUNCTION update_main_group(
                            p_g_code VARCHAR2,
                            p_g_a_name VARCHAR2,
                            p_g_e_name VARCHAR2,
                            p_tax_prcnt_dflt NUMBER DEFAULT NULL,
                            p_is_active VARCHAR2 DEFAULT 'Y'
                        ) RETURN VARCHAR2;

                        -- حذف مجموعة رئيسية
                        FUNCTION delete_main_group(p_g_code VARCHAR2) RETURN VARCHAR2;

                        -- إضافة مجموعة فرعية
                        FUNCTION add_sub_group(
                            p_g_code VARCHAR2,
                            p_mng_code VARCHAR2,
                            p_mng_a_name VARCHAR2,
                            p_mng_e_name VARCHAR2
                        ) RETURN VARCHAR2;

                        -- التحقق من صحة البيانات
                        FUNCTION validate_group_data(
                            p_table_name VARCHAR2,
                            p_data_xml XMLTYPE
                        ) RETURN VARCHAR2;

                        -- الحصول على التسلسل الهرمي
                        FUNCTION get_group_hierarchy(
                            p_g_code VARCHAR2 DEFAULT NULL
                        ) RETURN SYS_REFCURSOR;

                        -- إحصائيات المجموعات
                        FUNCTION get_groups_statistics RETURN SYS_REFCURSOR;

                    END ERP_ITEM_GROUPS;
                """;

        executeSQL(packageSpec, "Package Specification ERP_ITEM_GROUPS");

        // Package Body
        String packageBody =
                """
                            CREATE OR REPLACE PACKAGE BODY ERP_ITEM_GROUPS AS

                                -- إضافة مجموعة رئيسية
                                FUNCTION add_main_group(
                                    p_g_code VARCHAR2,
                                    p_g_a_name VARCHAR2,
                                    p_g_e_name VARCHAR2,
                                    p_tax_prcnt_dflt NUMBER DEFAULT 0,
                                    p_rol_lmt_qty NUMBER DEFAULT 0
                                ) RETURN VARCHAR2 IS

                                    l_count NUMBER;
                                    l_result VARCHAR2(4000);

                                BEGIN
                                    -- التحقق من وجود الكود
                                    SELECT COUNT(*) INTO l_count
                                    FROM ERP_GROUP_DETAILS
                                    WHERE G_CODE = p_g_code;

                                    IF l_count > 0 THEN
                                        RETURN 'ERROR: كود المجموعة موجود مسبقاً';
                                    END IF;

                                    -- إدراج المجموعة الجديدة
                                    INSERT INTO ERP_GROUP_DETAILS (
                                        G_CODE, G_A_NAME, G_E_NAME, TAX_PRCNT_DFLT, ROL_LMT_QTY,
                                        IS_ACTIVE, CREATED_BY, CREATED_DATE
                                    ) VALUES (
                                        p_g_code, p_g_a_name, p_g_e_name, p_tax_prcnt_dflt, p_rol_lmt_qty,
                                        'Y', USER, SYSDATE
                                    );

                                    -- تسجيل العملية
                                    ERP_INTEGRATION.log_operation('INSERT', 'ERP_GROUP_DETAILS', 'SUCCESS',
                                        'تم إضافة المجموعة الرئيسية: ' || p_g_code, 1);

                                    COMMIT;
                                    RETURN 'SUCCESS: تم إضافة المجموعة بنجاح';

                                EXCEPTION
                                    WHEN OTHERS THEN
                                        ROLLBACK;
                                        l_result := 'ERROR: ' || SQLERRM;
                                        ERP_INTEGRATION.log_operation('INSERT', 'ERP_GROUP_DETAILS', 'ERROR', l_result);
                                        RETURN l_result;
                                END add_main_group;

                                -- تعديل مجموعة رئيسية
                                FUNCTION update_main_group(
                                    p_g_code VARCHAR2,
                                    p_g_a_name VARCHAR2,
                                    p_g_e_name VARCHAR2,
                                    p_tax_prcnt_dflt NUMBER DEFAULT NULL,
                                    p_is_active VARCHAR2 DEFAULT 'Y'
                                ) RETURN VARCHAR2 IS

                                    l_count NUMBER;
                                    l_result VARCHAR2(4000);

                                BEGIN
                                    -- التحقق من وجود المجموعة
                                    SELECT COUNT(*) INTO l_count
                                    FROM ERP_GROUP_DETAILS
                                    WHERE G_CODE = p_g_code;

                                    IF l_count = 0 THEN
                                        RETURN 'ERROR: المجموعة غير موجودة';
                                    END IF;

                                    -- تحديث البيانات
                                    UPDATE ERP_GROUP_DETAILS SET
                                        G_A_NAME = p_g_a_name,
                                        G_E_NAME = p_g_e_name,
                                        TAX_PRCNT_DFLT = NVL(p_tax_prcnt_dflt, TAX_PRCNT_DFLT),
                                        IS_ACTIVE = p_is_active,
                                        UPDATED_BY = USER,
                                        UPDATED_DATE = SYSDATE
                                    WHERE G_CODE = p_g_code;

                                    ERP_INTEGRATION.log_operation('UPDATE', 'ERP_GROUP_DETAILS', 'SUCCESS',
                                        'تم تعديل المجموعة الرئيسية: ' || p_g_code, 1);

                                    COMMIT;
                                    RETURN 'SUCCESS: تم تعديل المجموعة بنجاح';

                                EXCEPTION
                                    WHEN OTHERS THEN
                                        ROLLBACK;
                                        l_result := 'ERROR: ' || SQLERRM;
                                        ERP_INTEGRATION.log_operation('UPDATE', 'ERP_GROUP_DETAILS', 'ERROR', l_result);
                                        RETURN l_result;
                                END update_main_group;

                                -- حذف مجموعة رئيسية
                                FUNCTION delete_main_group(p_g_code VARCHAR2) RETURN VARCHAR2 IS
                                    l_count NUMBER;
                                    l_result VARCHAR2(4000);
                                BEGIN
                                    -- التحقق من وجود مجموعات فرعية
                                    SELECT COUNT(*) INTO l_count
                                    FROM ERP_MAINSUB_GRP_DTL
                                    WHERE G_CODE = p_g_code;

                                    IF l_count > 0 THEN
                                        RETURN 'ERROR: لا يمكن حذف المجموعة لوجود مجموعات فرعية';
                                    END IF;

                                    -- حذف المجموعة
                                    DELETE FROM ERP_GROUP_DETAILS WHERE G_CODE = p_g_code;

                                    IF SQL%ROWCOUNT = 0 THEN
                                        RETURN 'ERROR: المجموعة غير موجودة';
                                    END IF;

                                    ERP_INTEGRATION.log_operation('DELETE', 'ERP_GROUP_DETAILS', 'SUCCESS',
                                        'تم حذف المجموعة الرئيسية: ' || p_g_code, 1);

                                    COMMIT;
                                    RETURN 'SUCCESS: تم حذف المجموعة بنجاح';

                                EXCEPTION
                                    WHEN OTHERS THEN
                                        ROLLBACK;
                                        l_result := 'ERROR: ' || SQLERRM;
                                        ERP_INTEGRATION.log_operation('DELETE', 'ERP_GROUP_DETAILS', 'ERROR', l_result);
                                        RETURN l_result;
                                END delete_main_group;

                                -- إضافة مجموعة فرعية
                                FUNCTION add_sub_group(
                                    p_g_code VARCHAR2,
                                    p_mng_code VARCHAR2,
                                    p_mng_a_name VARCHAR2,
                                    p_mng_e_name VARCHAR2
                                ) RETURN VARCHAR2 IS

                                    l_count NUMBER;
                                    l_result VARCHAR2(4000);

                                BEGIN
                                    -- التحقق من وجود المجموعة الرئيسية
                                    SELECT COUNT(*) INTO l_count
                                    FROM ERP_GROUP_DETAILS
                                    WHERE G_CODE = p_g_code;

                                    IF l_count = 0 THEN
                                        RETURN 'ERROR: المجموعة الرئيسية غير موجودة';
                                    END IF;

                                    -- التحقق من عدم تكرار الكود
                                    SELECT COUNT(*) INTO l_count
                                    FROM ERP_MAINSUB_GRP_DTL
                                    WHERE G_CODE = p_g_code AND MNG_CODE = p_mng_code;

                                    IF l_count > 0 THEN
                                        RETURN 'ERROR: كود المجموعة الفرعية موجود مسبقاً';
                                    END IF;

                                    -- إدراج المجموعة الفرعية
                                    INSERT INTO ERP_MAINSUB_GRP_DTL (
                                        G_CODE, MNG_CODE, MNG_A_NAME, MNG_E_NAME,
                                        IS_ACTIVE, CREATED_BY, CREATED_DATE
                                    ) VALUES (
                                        p_g_code, p_mng_code, p_mng_a_name, p_mng_e_name,
                                        'Y', USER, SYSDATE
                                    );

                                    ERP_INTEGRATION.log_operation('INSERT', 'ERP_MAINSUB_GRP_DTL', 'SUCCESS',
                                        'تم إضافة المجموعة الفرعية: ' || p_g_code || '/' || p_mng_code, 1);

                                    COMMIT;
                                    RETURN 'SUCCESS: تم إضافة المجموعة الفرعية بنجاح';

                                EXCEPTION
                                    WHEN OTHERS THEN
                                        ROLLBACK;
                                        l_result := 'ERROR: ' || SQLERRM;
                                        ERP_INTEGRATION.log_operation('INSERT', 'ERP_MAINSUB_GRP_DTL', 'ERROR', l_result);
                                        RETURN l_result;
                                END add_sub_group;

                                -- التحقق من صحة البيانات
                                FUNCTION validate_group_data(
                                    p_table_name VARCHAR2,
                                    p_data_xml XMLTYPE
                                ) RETURN VARCHAR2 IS
                                BEGIN
                                    -- سيتم تطوير هذا لاحقاً
                                    RETURN 'SUCCESS: البيانات صحيحة';
                                END validate_group_data;

                                -- الحصول على التسلسل الهرمي
                                FUNCTION get_group_hierarchy(
                                    p_g_code VARCHAR2 DEFAULT NULL
                                ) RETURN SYS_REFCURSOR IS

                                    l_cursor SYS_REFCURSOR;

                                BEGIN
                                    IF p_g_code IS NULL THEN
                                        OPEN l_cursor FOR
                                            SELECT 'MAIN' as level_type, G_CODE as code, G_A_NAME as name_ar,
                                                   G_E_NAME as name_en, NULL as parent_code
                                            FROM ERP_GROUP_DETAILS
                                            WHERE IS_ACTIVE = 'Y'
                                            UNION ALL
                                            SELECT 'SUB' as level_type, MNG_CODE as code, MNG_A_NAME as name_ar,
                                                   MNG_E_NAME as name_en, G_CODE as parent_code
                                            FROM ERP_MAINSUB_GRP_DTL
                                            WHERE IS_ACTIVE = 'Y'
                                            ORDER BY level_type, parent_code, code;
                                    ELSE
                                        OPEN l_cursor FOR
                                            SELECT 'MAIN' as level_type, G_CODE as code, G_A_NAME as name_ar,
                                                   G_E_NAME as name_en, NULL as parent_code
                                            FROM ERP_GROUP_DETAILS
                                            WHERE G_CODE = p_g_code AND IS_ACTIVE = 'Y'
                                            UNION ALL
                                            SELECT 'SUB' as level_type, MNG_CODE as code, MNG_A_NAME as name_ar,
                                                   MNG_E_NAME as name_en, G_CODE as parent_code
                                            FROM ERP_MAINSUB_GRP_DTL
                                            WHERE G_CODE = p_g_code AND IS_ACTIVE = 'Y'
                                            ORDER BY level_type, code;
                                    END IF;

                                    RETURN l_cursor;
                                END get_group_hierarchy;

                                -- إحصائيات المجموعات
                                FUNCTION get_groups_statistics RETURN SYS_REFCURSOR IS
                                    l_cursor SYS_REFCURSOR;
                                BEGIN
                                    OPEN l_cursor FOR
                                        SELECT 'المجموعات الرئيسية' as group_type,
                                               COUNT(*) as total_count,
                                               SUM(CASE WHEN IS_ACTIVE = 'Y' THEN 1 ELSE 0 END) as active_count
                                        FROM ERP_GROUP_DETAILS
                                        UNION ALL
                                        SELECT 'المجموعات الفرعية' as group_type,
                                               COUNT(*) as total_count,
                                               SUM(CASE WHEN IS_ACTIVE = 'Y' THEN 1 ELSE 0 END) as active_count
                                        FROM ERP_MAINSUB_GRP_DTL
                                        UNION ALL
                                        SELECT 'المجموعات تحت فرعية' as group_type,
                                               COUNT(*) as total_count,
                                               SUM(CASE WHEN IS_ACTIVE = 'Y' THEN 1 ELSE 0 END) as active_count
                                        FROM ERP_SUB_GRP_DTL
                                        UNION ALL
                                        SELECT 'المجموعات المساعدة' as group_type,
                                               COUNT(*) as total_count,
                                               SUM(CASE WHEN IS_ACTIVE = 'Y' THEN 1 ELSE 0 END) as active_count
                                        FROM ERP_ASSISTANT_GROUP
                                        UNION ALL
                                        SELECT 'المجموعات التفصيلية' as group_type,
                                               COUNT(*) as total_count,
                                               SUM(CASE WHEN IS_ACTIVE = 'Y' THEN 1 ELSE 0 END) as active_count
                                        FROM ERP_DETAIL_GROUP;

                                    RETURN l_cursor;
                                END get_groups_statistics;

                            END ERP_ITEM_GROUPS;
                        """;

        executeSQL(packageBody, "Package Body ERP_ITEM_GROUPS");
    }

    /**
     * إنشاء Package للمرافق المساعدة
     */
    private static void createUtilitiesPackage() throws SQLException {
        System.out.println("📦 إنشاء Package ERP_UTILITIES...");

        String packageSpec = """
                    CREATE OR REPLACE PACKAGE ERP_UTILITIES AS

                        -- تنظيف البيانات
                        FUNCTION cleanup_data(p_table_name VARCHAR2) RETURN VARCHAR2;

                        -- إنشاء نسخة احتياطية
                        FUNCTION backup_table(p_table_name VARCHAR2) RETURN VARCHAR2;

                        -- استعادة النسخة الاحتياطية
                        FUNCTION restore_table(p_table_name VARCHAR2) RETURN VARCHAR2;

                        -- تحسين الأداء
                        PROCEDURE optimize_tables;

                        -- إحصائيات النظام
                        FUNCTION get_system_stats RETURN SYS_REFCURSOR;

                    END ERP_UTILITIES;
                """;

        executeSQL(packageSpec, "Package Specification ERP_UTILITIES");

        String packageBody =
                """
                            CREATE OR REPLACE PACKAGE BODY ERP_UTILITIES AS

                                FUNCTION cleanup_data(p_table_name VARCHAR2) RETURN VARCHAR2 IS
                                BEGIN
                                    -- تنظيف البيانات المكررة والفارغة
                                    RETURN 'SUCCESS: تم تنظيف البيانات';
                                END cleanup_data;

                                FUNCTION backup_table(p_table_name VARCHAR2) RETURN VARCHAR2 IS
                                BEGIN
                                    -- إنشاء نسخة احتياطية
                                    EXECUTE IMMEDIATE 'CREATE TABLE ' || p_table_name || '_BACKUP AS SELECT * FROM ' || p_table_name;
                                    RETURN 'SUCCESS: تم إنشاء النسخة الاحتياطية';
                                EXCEPTION
                                    WHEN OTHERS THEN
                                        RETURN 'ERROR: ' || SQLERRM;
                                END backup_table;

                                FUNCTION restore_table(p_table_name VARCHAR2) RETURN VARCHAR2 IS
                                BEGIN
                                    -- استعادة من النسخة الاحتياطية
                                    EXECUTE IMMEDIATE 'DELETE FROM ' || p_table_name;
                                    EXECUTE IMMEDIATE 'INSERT INTO ' || p_table_name || ' SELECT * FROM ' || p_table_name || '_BACKUP';
                                    RETURN 'SUCCESS: تم استعادة البيانات';
                                EXCEPTION
                                    WHEN OTHERS THEN
                                        RETURN 'ERROR: ' || SQLERRM;
                                END restore_table;

                                PROCEDURE optimize_tables IS
                                BEGIN
                                    -- تحسين الجداول
                                    DBMS_STATS.GATHER_SCHEMA_STATS('SHIP_ERP');
                                END optimize_tables;

                                FUNCTION get_system_stats RETURN SYS_REFCURSOR IS
                                    l_cursor SYS_REFCURSOR;
                                BEGIN
                                    OPEN l_cursor FOR
                                        SELECT 'إجمالي المجموعات' as stat_name,
                                               (SELECT COUNT(*) FROM ERP_GROUP_DETAILS) as stat_value
                                        FROM DUAL
                                        UNION ALL
                                        SELECT 'العمليات اليوم' as stat_name,
                                               (SELECT COUNT(*) FROM ERP_OPERATION_LOG WHERE TRUNC(operation_date) = TRUNC(SYSDATE)) as stat_value
                                        FROM DUAL;
                                    RETURN l_cursor;
                                END get_system_stats;

                            END ERP_UTILITIES;
                        """;

        executeSQL(packageBody, "Package Body ERP_UTILITIES");
    }

    /**
     * إنشاء جداول التكوين
     */
    private static void createConfigurationTables() throws SQLException {
        System.out.println("🗃️ إنشاء جداول التكوين...");

        // جدول سجل العمليات
        String createLogTable =
                """
                            CREATE TABLE ERP_OPERATION_LOG (
                                log_id NUMBER PRIMARY KEY,
                                operation_type VARCHAR2(50) NOT NULL,
                                table_name VARCHAR2(100),
                                status VARCHAR2(20) NOT NULL,
                                message VARCHAR2(4000),
                                records_count NUMBER DEFAULT 0,
                                operation_date DATE DEFAULT SYSDATE,
                                username VARCHAR2(100) DEFAULT USER,
                                CONSTRAINT chk_operation_type CHECK (operation_type IN ('INSERT', 'UPDATE', 'DELETE', 'IMPORT', 'SYNC', 'BACKUP')),
                                CONSTRAINT chk_status CHECK (status IN ('SUCCESS', 'ERROR', 'WARNING', 'STARTED', 'COMPLETED'))
                            )
                        """;

        try {
            executeSQL("DROP TABLE ERP_OPERATION_LOG CASCADE CONSTRAINTS", "حذف جدول السجل القديم");
        } catch (SQLException e) {
            // تجاهل الخطأ إذا لم يكن الجدول موجوداً
        }

        executeSQL(createLogTable, "جدول سجل العمليات");

        // إنشاء Sequence
        try {
            executeSQL("DROP SEQUENCE ERP_LOG_SEQ", "حذف Sequence القديم");
        } catch (SQLException e) {
            // تجاهل الخطأ
        }

        executeSQL("CREATE SEQUENCE ERP_LOG_SEQ START WITH 1 INCREMENT BY 1",
                "Sequence سجل العمليات");

        // جدول إعدادات النظام
        String createConfigTable = """
                    CREATE TABLE ERP_SYSTEM_CONFIG (
                        config_id NUMBER PRIMARY KEY,
                        config_key VARCHAR2(100) UNIQUE NOT NULL,
                        config_value VARCHAR2(4000),
                        config_description VARCHAR2(500),
                        is_active VARCHAR2(1) DEFAULT 'Y',
                        created_by VARCHAR2(100) DEFAULT USER,
                        created_date DATE DEFAULT SYSDATE,
                        updated_by VARCHAR2(100),
                        updated_date DATE,
                        CONSTRAINT chk_config_active CHECK (is_active IN ('Y', 'N'))
                    )
                """;

        try {
            executeSQL("DROP TABLE ERP_SYSTEM_CONFIG CASCADE CONSTRAINTS",
                    "حذف جدول الإعدادات القديم");
        } catch (SQLException e) {
            // تجاهل الخطأ
        }

        executeSQL(createConfigTable, "جدول إعدادات النظام");

        // إنشاء Sequence للإعدادات
        try {
            executeSQL("DROP SEQUENCE ERP_CONFIG_SEQ", "حذف Sequence الإعدادات القديم");
        } catch (SQLException e) {
            // تجاهل الخطأ
        }

        executeSQL("CREATE SEQUENCE ERP_CONFIG_SEQ START WITH 1 INCREMENT BY 1",
                "Sequence إعدادات النظام");

        // إدراج إعدادات افتراضية
        String insertDefaultConfigs =
                """
                            INSERT INTO ERP_SYSTEM_CONFIG (config_id, config_key, config_value, config_description) VALUES
                            (ERP_CONFIG_SEQ.NEXTVAL, 'IAS_HOST', 'localhost', 'عنوان خادم IAS20251'),
                            (ERP_CONFIG_SEQ.NEXTVAL, 'IAS_PORT', '1521', 'منفذ قاعدة بيانات IAS20251'),
                            (ERP_CONFIG_SEQ.NEXTVAL, 'IAS_SERVICE', 'orcl', 'اسم خدمة قاعدة البيانات'),
                            (ERP_CONFIG_SEQ.NEXTVAL, 'IAS_USERNAME', 'ias20251', 'اسم مستخدم IAS20251'),
                            (ERP_CONFIG_SEQ.NEXTVAL, 'AUTO_SYNC_ENABLED', 'Y', 'تفعيل المزامنة التلقائية'),
                            (ERP_CONFIG_SEQ.NEXTVAL, 'SYNC_INTERVAL_MINUTES', '60', 'فترة المزامنة بالدقائق'),
                            (ERP_CONFIG_SEQ.NEXTVAL, 'BACKUP_ENABLED', 'Y', 'تفعيل النسخ الاحتياطي التلقائي'),
                            (ERP_CONFIG_SEQ.NEXTVAL, 'LOG_RETENTION_DAYS', '30', 'مدة الاحتفاظ بالسجلات بالأيام')
                        """;

        executeSQL(insertDefaultConfigs, "إدراج الإعدادات الافتراضية");

        // جدول ربط الجداول
        String createMappingTable =
                """
                            CREATE TABLE ERP_TABLE_MAPPING (
                                mapping_id NUMBER PRIMARY KEY,
                                source_table VARCHAR2(100) NOT NULL,
                                target_table VARCHAR2(100) NOT NULL,
                                mapping_type VARCHAR2(50) DEFAULT 'DIRECT',
                                is_active VARCHAR2(1) DEFAULT 'Y',
                                last_sync_date DATE,
                                sync_count NUMBER DEFAULT 0,
                                created_by VARCHAR2(100) DEFAULT USER,
                                created_date DATE DEFAULT SYSDATE,
                                CONSTRAINT chk_mapping_active CHECK (is_active IN ('Y', 'N')),
                                CONSTRAINT chk_mapping_type CHECK (mapping_type IN ('DIRECT', 'TRANSFORM', 'CUSTOM'))
                            )
                        """;

        try {
            executeSQL("DROP TABLE ERP_TABLE_MAPPING CASCADE CONSTRAINTS", "حذف جدول الربط القديم");
        } catch (SQLException e) {
            // تجاهل الخطأ
        }

        executeSQL(createMappingTable, "جدول ربط الجداول");

        // إنشاء Sequence للربط
        try {
            executeSQL("DROP SEQUENCE ERP_MAPPING_SEQ", "حذف Sequence الربط القديم");
        } catch (SQLException e) {
            // تجاهل الخطأ
        }

        executeSQL("CREATE SEQUENCE ERP_MAPPING_SEQ START WITH 1 INCREMENT BY 1",
                "Sequence ربط الجداول");

        // إدراج ربط الجداول الافتراضي
        String insertDefaultMappings =
                """
                            INSERT INTO ERP_TABLE_MAPPING (mapping_id, source_table, target_table, mapping_type) VALUES
                            (ERP_MAPPING_SEQ.NEXTVAL, 'GROUP_DETAILS', 'ERP_GROUP_DETAILS', 'DIRECT'),
                            (ERP_MAPPING_SEQ.NEXTVAL, 'IAS_MAINSUB_GRP_DTL', 'ERP_MAINSUB_GRP_DTL', 'DIRECT'),
                            (ERP_MAPPING_SEQ.NEXTVAL, 'IAS_SUB_GRP_DTL', 'ERP_SUB_GRP_DTL', 'DIRECT'),
                            (ERP_MAPPING_SEQ.NEXTVAL, 'IAS_ASSISTANT_GROUP', 'ERP_ASSISTANT_GROUP', 'DIRECT'),
                            (ERP_MAPPING_SEQ.NEXTVAL, 'IAS_DETAIL_GROUP', 'ERP_DETAIL_GROUP', 'DIRECT')
                        """;

        executeSQL(insertDefaultMappings, "إدراج ربط الجداول الافتراضي");

        System.out.println("✅ تم إنشاء جميع جداول التكوين");
    }

    /**
     * تنفيذ استعلام SQL
     */
    private static void executeSQL(String sql, String description) throws SQLException {
        try (Statement stmt = shipErpConnection.createStatement()) {
            stmt.execute(sql);
            System.out.println("✅ " + description);
        } catch (SQLException e) {
            System.err.println("❌ خطأ في " + description + ": " + e.getMessage());
            throw e;
        }
    }

    /**
     * إغلاق الاتصالات
     */
    private static void closeConnections() {
        try {
            if (shipErpConnection != null && !shipErpConnection.isClosed()) {
                shipErpConnection.close();
                System.out.println("✅ تم إغلاق اتصال SHIP_ERP");
            }
            if (ias20251Connection != null && !ias20251Connection.isClosed()) {
                ias20251Connection.close();
                System.out.println("✅ تم إغلاق اتصال IAS20251");
            }
        } catch (SQLException e) {
            System.err.println("❌ خطأ في إغلاق الاتصالات: " + e.getMessage());
        }
    }
}
