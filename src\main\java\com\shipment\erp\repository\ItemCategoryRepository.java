package com.shipment.erp.repository;

import com.shipment.erp.model.ItemCategory;
import java.util.List;
import java.util.Optional;

/**
 * مستودع مجموعات الأصناف
 * Item Category Repository Interface
 */
public interface ItemCategoryRepository extends BaseRepository<ItemCategory, Long> {
    
    /**
     * البحث عن مجموعة بالكود
     * Find category by code
     */
    Optional<ItemCategory> findByCode(String code);
    
    /**
     * البحث عن مجموعة بالاسم العربي
     * Find category by Arabic name
     */
    Optional<ItemCategory> findByNameAr(String nameAr);
    
    /**
     * الحصول على المجموعات الجذرية (بدون مجموعة أب)
     * Get root categories (without parent)
     */
    List<ItemCategory> findRootCategories();
    
    /**
     * الحصول على المجموعات الفرعية لمجموعة معينة
     * Get subcategories of a specific category
     */
    List<ItemCategory> findByParentCategory(ItemCategory parentCategory);
    
    /**
     * الحصول على جميع المجموعات النشطة
     * Get all active categories
     */
    List<ItemCategory> findByIsActiveTrue();
    
    /**
     * الحصول على المجموعات حسب المستوى
     * Get categories by level
     */
    List<ItemCategory> findByLevel(Integer level);
    
    /**
     * البحث في المجموعات بالنص
     * Search categories by text
     */
    List<ItemCategory> searchByText(String searchText);
    
    /**
     * الحصول على المجموعات مرتبة حسب ترتيب العرض
     * Get categories ordered by sort order
     */
    List<ItemCategory> findAllOrderBySortOrder();
    
    /**
     * الحصول على المجموعات التي تحتوي على أصناف
     * Get categories that have items
     */
    List<ItemCategory> findByHasItemsTrue();
    
    /**
     * التحقق من وجود مجموعة بالكود
     * Check if category exists by code
     */
    boolean existsByCode(String code);
    
    /**
     * التحقق من وجود مجموعة بالاسم العربي
     * Check if category exists by Arabic name
     */
    boolean existsByNameAr(String nameAr);
    
    /**
     * عد المجموعات النشطة
     * Count active categories
     */
    long countByIsActiveTrue();
    
    /**
     * عد المجموعات الفرعية لمجموعة معينة
     * Count subcategories of a specific category
     */
    long countByParentCategory(ItemCategory parentCategory);
    
    /**
     * الحصول على المسار الكامل للمجموعة
     * Get full path of category
     */
    String getCategoryPath(Long categoryId);
}
