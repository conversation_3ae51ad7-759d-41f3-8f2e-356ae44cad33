import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * نافذة وحدات القياس المبسطة Simple Measurement Units Window
 */
public class SimpleMeasurementWindow extends JFrame {

    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 12);

    public SimpleMeasurementWindow() {
        initializeComponents();
    }

    private void initializeComponents() {
        setTitle("إدارة وحدات القياس - Measurement Units Management");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(800, 600);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // العنوان
        JLabel titleLabel = new JLabel("إدارة وحدات القياس", JLabel.CENTER);
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 18));
        titleLabel.setForeground(new Color(52, 152, 219));
        mainPanel.add(titleLabel, BorderLayout.NORTH);

        // المحتوى الوسطي
        JPanel centerPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);

        // رسالة ترحيب
        JLabel welcomeLabel = new JLabel("مرحباً بك في نظام إدارة وحدات القياس المحسن");
        welcomeLabel.setFont(arabicBoldFont);
        welcomeLabel.setHorizontalAlignment(JLabel.CENTER);
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.gridwidth = 2;
        centerPanel.add(welcomeLabel, gbc);

        // معلومات النظام
        JTextArea infoArea = new JTextArea();
        infoArea.setFont(arabicFont);
        infoArea.setEditable(false);
        infoArea.setOpaque(false);
        infoArea.setText("هذا النظام يوفر:\n\n" + "• إدارة شاملة لوحدات القياس\n"
                + "• استيراد البيانات من النظام الأصلي\n" + "• واجهة عربية محسنة\n"
                + "• قاعدة بيانات محسنة\n" + "• بحث متقدم وتصفية\n" + "• تقارير مفصلة\n\n"
                + "للمتابعة، يرجى التأكد من:\n" + "• الاتصال بقاعدة البيانات\n"
                + "• وجود الجداول المطلوبة\n" + "• صلاحيات المستخدم");

        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 1.0;
        centerPanel.add(new JScrollPane(infoArea), gbc);

        mainPanel.add(centerPanel, BorderLayout.CENTER);

        // الأزرار السفلية
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 10));

        JButton testConnectionBtn = createStyledButton("اختبار الاتصال", new Color(52, 152, 219));
        testConnectionBtn.addActionListener(e -> testConnection());

        JButton createTableBtn = createStyledButton("إنشاء الجداول", new Color(46, 204, 113));
        createTableBtn.addActionListener(e -> createTables());

        JButton openFullWindowBtn =
                createStyledButton("فتح النافذة الكاملة", new Color(155, 89, 182));
        openFullWindowBtn.addActionListener(e -> openFullWindow());

        JButton closeBtn = createStyledButton("إغلاق", new Color(231, 76, 60));
        closeBtn.addActionListener(e -> dispose());

        buttonPanel.add(testConnectionBtn);
        buttonPanel.add(createTableBtn);
        buttonPanel.add(openFullWindowBtn);
        buttonPanel.add(closeBtn);

        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        add(mainPanel);
    }

    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setFont(arabicBoldFont);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setOpaque(true);
        button.setPreferredSize(new Dimension(150, 35));
        return button;
    }

    private void testConnection() {
        try {
            // محاولة الاتصال بقاعدة البيانات
            String url = "*************************************";
            String username = "ship_erp";
            String password = "ship_erp_password";

            java.sql.Connection connection =
                    java.sql.DriverManager.getConnection(url, username, password);
            connection.close();

            JOptionPane.showMessageDialog(this, "تم الاتصال بقاعدة البيانات بنجاح!", "نجح الاتصال",
                    JOptionPane.INFORMATION_MESSAGE);

        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "فشل الاتصال بقاعدة البيانات:\n" + e.getMessage(),
                    "خطأ في الاتصال", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void createTables() {
        try {
            String url = "*************************************";
            String username = "ship_erp";
            String password = "ship_erp_password";

            java.sql.Connection connection =
                    java.sql.DriverManager.getConnection(url, username, password);

            // إنشاء جدول وحدات القياس
            String createTableSQL = """
                        CREATE TABLE ERP_MEASUREMENT_UNITS (
                            MEASURE_CODE VARCHAR2(10) NOT NULL,
                            MEASURE_NAME VARCHAR2(100) NOT NULL,
                            MEASURE_NAME_EN VARCHAR2(100),
                            IS_ACTIVE NUMBER(1) DEFAULT 1,
                            SYMBOL VARCHAR2(10),
                            DESCRIPTION VARCHAR2(500),
                            AD_DATE DATE DEFAULT SYSDATE,
                            CONSTRAINT PK_ERP_MEASUREMENT_UNITS PRIMARY KEY (MEASURE_CODE)
                        )
                    """;

            java.sql.Statement stmt = connection.createStatement();
            stmt.execute(createTableSQL);
            stmt.close();
            connection.close();

            JOptionPane.showMessageDialog(this, "تم إنشاء جدول وحدات القياس بنجاح!", "نجح الإنشاء",
                    JOptionPane.INFORMATION_MESSAGE);

        } catch (Exception e) {
            if (e.getMessage().contains("already exists")) {
                JOptionPane.showMessageDialog(this, "جدول وحدات القياس موجود مسبقاً", "معلومات",
                        JOptionPane.INFORMATION_MESSAGE);
            } else {
                JOptionPane.showMessageDialog(this, "خطأ في إنشاء الجدول:\n" + e.getMessage(),
                        "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    private void openFullWindow() {
        try {
            // محاولة فتح النافذة الكاملة
            MeasurementUnitsWindow fullWindow = new MeasurementUnitsWindow();
            fullWindow.setVisible(true);

            JOptionPane.showMessageDialog(this, "تم فتح النافذة الكاملة بنجاح!", "نجح",
                    JOptionPane.INFORMATION_MESSAGE);

        } catch (Exception e) {
            JOptionPane.showMessageDialog(this,
                    "خطأ في فتح النافذة الكاملة:\n" + e.getMessage()
                            + "\n\nسيتم استخدام النافذة المبسطة حالياً",
                    "تنبيه", JOptionPane.WARNING_MESSAGE);
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
            } catch (Exception e) {
                // تجاهل
            }

            new SimpleMeasurementWindow().setVisible(true);
        });
    }
}
