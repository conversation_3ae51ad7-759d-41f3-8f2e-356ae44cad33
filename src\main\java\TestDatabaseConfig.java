import java.sql.*;
import java.util.Properties;

/**
 * إعداد قاعدة بيانات تجريبية للاختبار
 * Test Database Configuration for Testing
 */
public class TestDatabaseConfig {
    
    // إعدادات قاعدة البيانات التجريبية (H2)
    private static final String TEST_URL = "jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE";
    private static final String TEST_USERNAME = "sa";
    private static final String TEST_PASSWORD = "";
    
    // إعدادات Oracle (للاستخدام الفعلي)
    private String host = "localhost";
    private String port = "1521";
    private String serviceName = "XE";
    private String username = "hr";
    private String password = "hr";
    private boolean useTestMode = true; // تفعيل الوضع التجريبي
    
    private Connection connection;
    
    public TestDatabaseConfig() {
        // افتراضياً يستخدم الوضع التجريبي
    }
    
    public TestDatabaseConfig(String host, String port, String serviceName, String username, String password) {
        this.host = host;
        this.port = port;
        this.serviceName = serviceName;
        this.username = username;
        this.password = password;
        this.useTestMode = false; // استخدام Oracle الفعلي
    }
    
    /**
     * إنشاء اتصال بقاعدة البيانات
     */
    public Connection createConnection() throws SQLException {
        try {
            if (useTestMode) {
                return createTestConnection();
            } else {
                return createOracleConnection();
            }
        } catch (Exception e) {
            throw new SQLException("فشل في إنشاء الاتصال: " + e.getMessage(), e);
        }
    }
    
    /**
     * إنشاء اتصال تجريبي بقاعدة H2
     */
    private Connection createTestConnection() throws SQLException {
        try {
            // تحميل تعريف H2
            Class.forName("org.h2.Driver");
            
            connection = DriverManager.getConnection(TEST_URL, TEST_USERNAME, TEST_PASSWORD);
            connection.setAutoCommit(false);
            
            // إنشاء جداول تجريبية
            createTestTables();
            
            return connection;
            
        } catch (ClassNotFoundException e) {
            // إذا لم تكن H2 متوفرة، استخدم قاعدة بيانات وهمية
            return createMockConnection();
        }
    }
    
    /**
     * إنشاء اتصال وهمي للاختبار
     */
    private Connection createMockConnection() throws SQLException {
        // إنشاء اتصال وهمي يعمل في الذاكرة
        return new MockConnection();
    }
    
    /**
     * إنشاء اتصال Oracle الفعلي
     */
    private Connection createOracleConnection() throws SQLException {
        try {
            // تحميل تعريف Oracle JDBC
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            // بناء URL الاتصال
            String url = String.format("**************************", host, port, serviceName);
            
            // إعداد خصائص الاتصال
            Properties props = new Properties();
            props.setProperty("user", username);
            props.setProperty("password", password);
            props.setProperty("oracle.jdbc.autoCommitSpecCompliant", "false");
            props.setProperty("oracle.net.CONNECT_TIMEOUT", "10000");
            props.setProperty("oracle.jdbc.ReadTimeout", "30000");
            
            // إنشاء الاتصال
            connection = DriverManager.getConnection(url, props);
            connection.setAutoCommit(false);
            
            return connection;
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("تعذر العثور على تعريف Oracle JDBC. يرجى تحميل مكتبة ojdbc11.jar", e);
        }
    }
    
    /**
     * إنشاء جداول تجريبية
     */
    private void createTestTables() throws SQLException {
        if (connection == null) return;
        
        try (Statement stmt = connection.createStatement()) {
            // حذف الجداول إذا كانت موجودة
            try {
                stmt.execute("DROP TABLE IF EXISTS ITEMS");
                stmt.execute("DROP TABLE IF EXISTS PRODUCTS");
                stmt.execute("DROP TABLE IF EXISTS ITEM_MASTER");
            } catch (SQLException e) {
                // تجاهل الأخطاء
            }
            
            // إنشاء جدول ITEMS
            stmt.execute("""
                CREATE TABLE ITEMS (
                    ITEM_ID NUMBER PRIMARY KEY,
                    ITEM_CODE VARCHAR2(50) UNIQUE NOT NULL,
                    ITEM_NAME_AR VARCHAR2(200) NOT NULL,
                    ITEM_NAME_EN VARCHAR2(200),
                    DESCRIPTION VARCHAR2(1000),
                    CATEGORY_CODE VARCHAR2(50),
                    UNIT_CODE VARCHAR2(20),
                    SALES_PRICE NUMBER(10,2),
                    COST_PRICE NUMBER(10,2),
                    CURRENT_STOCK NUMBER(10,2),
                    MIN_STOCK NUMBER(10,2),
                    IS_ACTIVE CHAR(1) DEFAULT 'Y',
                    BARCODE VARCHAR2(50),
                    SUPPLIER VARCHAR2(200),
                    MANUFACTURER VARCHAR2(200),
                    CREATED_DATE DATE DEFAULT SYSDATE
                )
            """);
            
            // إنشاء جدول PRODUCTS
            stmt.execute("""
                CREATE TABLE PRODUCTS (
                    PRODUCT_ID NUMBER PRIMARY KEY,
                    PRODUCT_CODE VARCHAR2(50) UNIQUE NOT NULL,
                    PRODUCT_NAME VARCHAR2(200) NOT NULL,
                    PRODUCT_DESC VARCHAR2(1000),
                    CATEGORY VARCHAR2(50),
                    UNIT VARCHAR2(20),
                    PRICE NUMBER(10,2),
                    COST NUMBER(10,2),
                    QTY_ON_HAND NUMBER(10,2),
                    MIN_QTY NUMBER(10,2),
                    ACTIVE_FLAG CHAR(1) DEFAULT 'Y',
                    UPC_CODE VARCHAR2(50),
                    VENDOR VARCHAR2(200)
                )
            """);
            
            // إدراج بيانات تجريبية
            insertTestData();
            
            connection.commit();
        }
    }
    
    /**
     * إدراج بيانات تجريبية
     */
    private void insertTestData() throws SQLException {
        try (PreparedStatement stmt = connection.prepareStatement(
            "INSERT INTO ITEMS (ITEM_ID, ITEM_CODE, ITEM_NAME_AR, ITEM_NAME_EN, DESCRIPTION, CATEGORY_CODE, UNIT_CODE, SALES_PRICE, COST_PRICE, CURRENT_STOCK, MIN_STOCK, IS_ACTIVE, BARCODE, SUPPLIER, MANUFACTURER) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")) {
            
            // بيانات تجريبية للأصناف
            Object[][] testData = {
                {1, "PHONE001", "هاتف سامسونج جالاكسي S23", "Samsung Galaxy S23", "هاتف ذكي متطور", "ELECTRONICS", "PCS", 2500.00, 2000.00, 50.0, 10.0, "Y", "1234567890123", "شركة التقنية المتقدمة", "سامسونج"},
                {2, "LAPTOP001", "لابتوب ديل انسبايرون", "Dell Inspiron Laptop", "لابتوب للاستخدام المكتبي", "ELECTRONICS", "PCS", 3500.00, 3000.00, 25.0, 5.0, "Y", "1234567890124", "شركة الحاسوب", "ديل"},
                {3, "SHIRT001", "قميص قطني رجالي", "Men's Cotton Shirt", "قميص قطني عالي الجودة", "CLOTHING", "PCS", 150.00, 100.00, 100.0, 20.0, "Y", "1234567890125", "مصنع الملابس", "ماركة محلية"},
                {4, "BOOK001", "كتاب البرمجة بجافا", "Java Programming Book", "كتاب تعليمي للبرمجة", "BOOKS", "PCS", 80.00, 60.00, 30.0, 5.0, "Y", "1234567890126", "دار النشر", "مؤلف محلي"},
                {5, "JUICE001", "عصير برتقال طبيعي", "Natural Orange Juice", "عصير برتقال طازج", "BEVERAGES", "L", 25.00, 18.00, 200.0, 50.0, "Y", "1234567890127", "مصنع العصائر", "ماركة طبيعية"}
            };
            
            for (Object[] row : testData) {
                for (int i = 0; i < row.length; i++) {
                    stmt.setObject(i + 1, row[i]);
                }
                stmt.addBatch();
            }
            
            stmt.executeBatch();
        }
        
        // إدراج بيانات في جدول PRODUCTS
        try (PreparedStatement stmt = connection.prepareStatement(
            "INSERT INTO PRODUCTS (PRODUCT_ID, PRODUCT_CODE, PRODUCT_NAME, PRODUCT_DESC, CATEGORY, UNIT, PRICE, COST, QTY_ON_HAND, MIN_QTY, ACTIVE_FLAG, UPC_CODE, VENDOR) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")) {
            
            Object[][] productData = {
                {1, "PROD001", "منتج تجريبي 1", "وصف المنتج التجريبي الأول", "CAT1", "PCS", 100.00, 80.00, 50.0, 10.0, "Y", "9876543210123", "مورد تجريبي"},
                {2, "PROD002", "منتج تجريبي 2", "وصف المنتج التجريبي الثاني", "CAT2", "KG", 200.00, 150.00, 30.0, 5.0, "Y", "9876543210124", "مورد آخر"},
                {3, "PROD003", "منتج تجريبي 3", "وصف المنتج التجريبي الثالث", "CAT1", "L", 50.00, 35.00, 100.0, 20.0, "Y", "9876543210125", "مورد ثالث"}
            };
            
            for (Object[] row : productData) {
                for (int i = 0; i < row.length; i++) {
                    stmt.setObject(i + 1, row[i]);
                }
                stmt.addBatch();
            }
            
            stmt.executeBatch();
        }
    }
    
    /**
     * اختبار الاتصال بقاعدة البيانات
     */
    public boolean testConnection() {
        try (Connection testConn = createConnection()) {
            if (testConn != null && !testConn.isClosed()) {
                // تنفيذ استعلام بسيط للتأكد من الاتصال
                try (Statement stmt = testConn.createStatement();
                     ResultSet rs = stmt.executeQuery(useTestMode ? "SELECT 1" : "SELECT 1 FROM DUAL")) {
                    return rs.next();
                }
            }
            return false;
        } catch (SQLException e) {
            System.err.println("خطأ في اختبار الاتصال: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على معلومات قاعدة البيانات
     */
    public DatabaseInfo getDatabaseInfo() throws SQLException {
        try (Connection conn = createConnection()) {
            DatabaseMetaData metaData = conn.getMetaData();
            
            DatabaseInfo info = new DatabaseInfo();
            info.setDatabaseProductName(metaData.getDatabaseProductName());
            info.setDatabaseProductVersion(metaData.getDatabaseProductVersion());
            info.setDriverName(metaData.getDriverName());
            info.setDriverVersion(metaData.getDriverVersion());
            info.setUrl(metaData.getURL());
            info.setUserName(metaData.getUserName());
            
            return info;
        }
    }
    
    /**
     * إغلاق الاتصال
     */
    public void closeConnection() {
        if (connection != null) {
            try {
                if (!connection.isClosed()) {
                    connection.close();
                }
            } catch (SQLException e) {
                System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
            }
        }
    }
    
    // Getters and Setters
    public String getHost() { return host; }
    public void setHost(String host) { this.host = host; }
    
    public String getPort() { return port; }
    public void setPort(String port) { this.port = port; }
    
    public String getServiceName() { return serviceName; }
    public void setServiceName(String serviceName) { this.serviceName = serviceName; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    
    public boolean isUseTestMode() { return useTestMode; }
    public void setUseTestMode(boolean useTestMode) { this.useTestMode = useTestMode; }
    
    public Connection getConnection() { return connection; }
    
    /**
     * فئة معلومات قاعدة البيانات
     */
    public static class DatabaseInfo {
        private String databaseProductName;
        private String databaseProductVersion;
        private String driverName;
        private String driverVersion;
        private String url;
        private String userName;
        
        // Getters and Setters
        public String getDatabaseProductName() { return databaseProductName; }
        public void setDatabaseProductName(String databaseProductName) { this.databaseProductName = databaseProductName; }
        
        public String getDatabaseProductVersion() { return databaseProductVersion; }
        public void setDatabaseProductVersion(String databaseProductVersion) { this.databaseProductVersion = databaseProductVersion; }
        
        public String getDriverName() { return driverName; }
        public void setDriverName(String driverName) { this.driverName = driverName; }
        
        public String getDriverVersion() { return driverVersion; }
        public void setDriverVersion(String driverVersion) { this.driverVersion = driverVersion; }
        
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        
        @Override
        public String toString() {
            return String.format(
                "قاعدة البيانات: %s %s\nالتعريف: %s %s\nالمستخدم: %s\nالرابط: %s",
                databaseProductName, databaseProductVersion,
                driverName, driverVersion,
                userName, url
            );
        }
    }
}
