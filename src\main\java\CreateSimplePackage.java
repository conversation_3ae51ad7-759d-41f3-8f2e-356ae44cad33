import java.sql.*;

public class CreateSimplePackage {
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            System.out.println("🔗 الاتصال بـ SHIP_ERP...");
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            System.out.println("✅ تم الاتصال");
            
            Statement stmt = conn.createStatement();
            
            // حذف Package القديم
            try {
                stmt.execute("DROP PACKAGE ERP_ITEM_GROUPS");
                System.out.println("🗑️ تم حذف Package القديم");
            } catch (SQLException e) {
                System.out.println("ℹ️ Package غير موجود مسبقاً");
            }
            
            // إنشاء Package بسيط جداً
            System.out.println("📦 إنشاء Package ERP_ITEM_GROUPS البسيط...");
            
            String packageSpec = """
                CREATE OR REPLACE PACKAGE ERP_ITEM_GROUPS AS
                    
                    -- الحصول على عدد المجموعات
                    FUNCTION get_groups_count RETURN NUMBER;
                    
                    -- إضافة مجموعة رئيسية بسيطة
                    FUNCTION add_main_group_simple(
                        p_g_code VARCHAR2,
                        p_g_a_name VARCHAR2,
                        p_g_e_name VARCHAR2
                    ) RETURN VARCHAR2;
                    
                    -- تسجيل العمليات
                    PROCEDURE log_operation(
                        p_operation VARCHAR2,
                        p_message VARCHAR2
                    );
                    
                END ERP_ITEM_GROUPS;
            """;
            
            stmt.execute(packageSpec);
            System.out.println("✅ تم إنشاء Package Specification");
            
            String packageBody = """
                CREATE OR REPLACE PACKAGE BODY ERP_ITEM_GROUPS AS
                    
                    -- الحصول على عدد المجموعات
                    FUNCTION get_groups_count RETURN NUMBER IS
                        l_count NUMBER;
                    BEGIN
                        SELECT COUNT(*) INTO l_count FROM ERP_GROUP_DETAILS;
                        RETURN l_count;
                    EXCEPTION
                        WHEN OTHERS THEN
                            RETURN 0;
                    END get_groups_count;
                    
                    -- إضافة مجموعة رئيسية بسيطة
                    FUNCTION add_main_group_simple(
                        p_g_code VARCHAR2,
                        p_g_a_name VARCHAR2,
                        p_g_e_name VARCHAR2
                    ) RETURN VARCHAR2 IS
                        l_count NUMBER;
                    BEGIN
                        -- التحقق من وجود الكود
                        SELECT COUNT(*) INTO l_count
                        FROM ERP_GROUP_DETAILS
                        WHERE G_CODE = p_g_code;
                        
                        IF l_count > 0 THEN
                            RETURN 'ERROR: كود المجموعة موجود مسبقاً';
                        END IF;
                        
                        -- إدراج المجموعة الجديدة
                        INSERT INTO ERP_GROUP_DETAILS (
                            G_CODE, G_A_NAME, G_E_NAME, IS_ACTIVE
                        ) VALUES (
                            p_g_code, p_g_a_name, p_g_e_name, 'Y'
                        );
                        
                        COMMIT;
                        RETURN 'SUCCESS: تم إضافة المجموعة بنجاح';
                        
                    EXCEPTION
                        WHEN OTHERS THEN
                            ROLLBACK;
                            RETURN 'ERROR: ' || SQLERRM;
                    END add_main_group_simple;
                    
                    -- تسجيل العمليات
                    PROCEDURE log_operation(
                        p_operation VARCHAR2,
                        p_message VARCHAR2
                    ) IS
                        PRAGMA AUTONOMOUS_TRANSACTION;
                    BEGIN
                        INSERT INTO ERP_OPERATION_LOG (
                            log_id, operation_type, table_name, status,
                            message, records_count, operation_date, username
                        ) VALUES (
                            ERP_LOG_SEQ.NEXTVAL, p_operation, 'ERP_GROUP_DETAILS', 'INFO',
                            p_message, 0, SYSDATE, USER
                        );
                        COMMIT;
                    EXCEPTION
                        WHEN OTHERS THEN
                            ROLLBACK;
                            NULL;
                    END log_operation;
                    
                END ERP_ITEM_GROUPS;
            """;
            
            stmt.execute(packageBody);
            System.out.println("✅ تم إنشاء Package Body");
            
            // اختبار Package
            System.out.println("🧪 اختبار Package...");
            
            // اختبار عدد المجموعات
            CallableStatement countCs = conn.prepareCall("{ ? = call ERP_ITEM_GROUPS.get_groups_count }");
            countCs.registerOutParameter(1, Types.NUMERIC);
            countCs.execute();
            int count = countCs.getInt(1);
            System.out.println("📊 عدد المجموعات الحالي: " + count);
            
            // اختبار إضافة مجموعة
            CallableStatement cs = conn.prepareCall("{ ? = call ERP_ITEM_GROUPS.add_main_group_simple(?, ?, ?) }");
            cs.registerOutParameter(1, Types.VARCHAR);
            cs.setString(2, "SIMPLE001");
            cs.setString(3, "مجموعة بسيطة");
            cs.setString(4, "Simple Group");
            cs.execute();
            String result = cs.getString(1);
            System.out.println("📋 نتيجة إضافة المجموعة: " + result);
            
            // اختبار تسجيل العمليات
            CallableStatement logCs = conn.prepareCall("{ call ERP_ITEM_GROUPS.log_operation(?, ?) }");
            logCs.setString(1, "TEST");
            logCs.setString(2, "اختبار Package ERP_ITEM_GROUPS");
            logCs.execute();
            System.out.println("✅ تم تسجيل العملية");
            
            // اختبار العدد مرة أخرى
            countCs.execute();
            int newCount = countCs.getInt(1);
            System.out.println("📊 عدد المجموعات بعد الإضافة: " + newCount);
            
            conn.close();
            System.out.println("🎉 تم إنشاء Package ERP_ITEM_GROUPS البسيط بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
