@echo off
echo ==========================================
echo    Ship ERP System Setup
echo ==========================================

echo.
echo Checking libraries...

if not exist "lib" (
    echo Creating lib directory...
    mkdir lib
)

echo.
echo Compiling system files...

echo Compiling CommentsManager...
javac -encoding UTF-8 -cp "lib/*;." CommentsManager.java
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to compile CommentsManager
    pause
    exit /b 1
)

echo Compiling BaseDataImporter...
javac -encoding UTF-8 -cp "lib/*;." BaseDataImporter.java
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to compile BaseDataImporter
    pause
    exit /b 1
)

echo Compiling ItemsDataImporter...
javac -encoding UTF-8 -cp "lib/*;." ItemsDataImporter.java
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to compile ItemsDataImporter
    pause
    exit /b 1
)

echo Compiling ImportManager...
javac -encoding UTF-8 -cp "lib/*;." ImportManager.java
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to compile ImportManager
    pause
    exit /b 1
)

echo Compiling ComprehensiveItemDataWindow...
javac -encoding UTF-8 -cp "lib/*;." ComprehensiveItemDataWindow.java
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to compile ComprehensiveItemDataWindow
    pause
    exit /b 1
)

echo Compiling TreeMenuPanel...
javac -encoding UTF-8 -cp "lib/*;." TreeMenuPanel.java
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to compile TreeMenuPanel
    pause
    exit /b 1
)

echo Compiling EnhancedMainWindow...
javac -encoding UTF-8 -cp "lib/*;." EnhancedMainWindow.java
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to compile EnhancedMainWindow
    pause
    exit /b 1
)

echo Compiling ShipERPMain...
javac -encoding UTF-8 -cp "lib/*;." ShipERPMain.java
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to compile ShipERPMain
    pause
    exit /b 1
)

echo.
echo SUCCESS: All files compiled successfully!

echo.
echo Creating run script...

echo @echo off > run_ship_erp.bat
echo echo ========================================== >> run_ship_erp.bat
echo echo     Ship ERP System >> run_ship_erp.bat
echo echo ========================================== >> run_ship_erp.bat
echo echo. >> run_ship_erp.bat
echo echo Starting system... >> run_ship_erp.bat
echo java -Duser.language=ar -Duser.country=SA -Dfile.encoding=UTF-8 -cp "lib/*;." ShipERPMain >> run_ship_erp.bat
echo if %%ERRORLEVEL%% neq 0 ^( >> run_ship_erp.bat
echo     echo ERROR: System failed to start >> run_ship_erp.bat
echo     pause >> run_ship_erp.bat
echo ^) >> run_ship_erp.bat

echo.
echo Creating test script...

echo @echo off > test_system.bat
echo echo Testing database connection... >> test_system.bat
echo java -cp "lib/*;." AutoDatabaseConnectionTest >> test_system.bat
echo echo. >> test_system.bat
echo echo Testing comments system... >> test_system.bat
echo java -cp "lib/*;." IAS20251CommentsAnalyzer2 >> test_system.bat
echo pause >> test_system.bat

echo.
echo Setup completed successfully!
echo.
echo Available scripts:
echo   - run_ship_erp.bat : Run the main system
echo   - test_system.bat  : Test system components
echo.
echo To start the system: run_ship_erp.bat
echo.
pause
