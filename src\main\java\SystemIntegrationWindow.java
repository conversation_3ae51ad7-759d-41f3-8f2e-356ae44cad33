import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة إدارة الربط والاستيراد System Integration Management Window
 */
public class SystemIntegrationWindow extends JFrame {

    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 13);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 13);
    private Font fieldFont = new Font("Tahoma", Font.PLAIN, 14);

    private Color primaryColor = new Color(41, 128, 185);
    private Color successColor = new Color(39, 174, 96);
    private Color warningColor = new Color(243, 156, 18);
    private Color dangerColor = new Color(231, 76, 60);

    // مكونات الواجهة
    private JTabbedPane tabbedPane;
    private JTable connectionsTable;
    private JTable mappingsTable;
    private JTable historyTable;
    private DefaultTableModel connectionsModel;
    private DefaultTableModel mappingsModel;
    private DefaultTableModel historyModel;

    // قاعدة البيانات
    private Connection connection;
    private DatabasePackageManager packageManager;

    public SystemIntegrationWindow() {
        initializeDatabase();
        initializeComponents();
        loadData();
    }

    /**
     * تهيئة قاعدة البيانات
     */
    private void initializeDatabase() {
        try {
            Class.forName("oracle.jdbc.OracleDriver");
            connection = DriverManager.getConnection("*************************************",
                    "ship_erp", "ship_erp_password");

            // إنشاء مدير الـ Packages
            packageManager = new DatabasePackageManager(connection);

            System.out.println("✅ متصل بقاعدة البيانات SHIP_ERP");
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this,
                    "خطأ في الاتصال بقاعدة البيانات:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * تهيئة مكونات الواجهة
     */
    private void initializeComponents() {
        setTitle("إدارة الربط والاستيراد");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // شريط الأدوات العلوي
        JPanel toolbarPanel = createToolbarPanel();
        mainPanel.add(toolbarPanel, BorderLayout.NORTH);

        // التبويبات الرئيسية
        tabbedPane = new JTabbedPane();
        tabbedPane.setFont(arabicBoldFont);
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // تبويب اتصالات الأنظمة
        JPanel connectionsPanel = createConnectionsPanel();
        tabbedPane.addTab("🔗 اتصالات الأنظمة", connectionsPanel);

        // تبويب ربط الجداول
        JPanel mappingsPanel = createMappingsPanel();
        tabbedPane.addTab("🔄 ربط الجداول", mappingsPanel);

        // تبويب تاريخ الاستيراد
        JPanel historyPanel = createHistoryPanel();
        tabbedPane.addTab("📊 تاريخ الاستيراد", historyPanel);

        // تبويب الإعدادات
        JPanel settingsPanel = createSettingsPanel();
        tabbedPane.addTab("⚙️ الإعدادات", settingsPanel);

        mainPanel.add(tabbedPane, BorderLayout.CENTER);

        // شريط الحالة
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);

        add(mainPanel);
    }

    /**
     * إنشاء شريط الأدوات
     */
    private JPanel createToolbarPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton refreshBtn = createStyledButton("🔄 تحديث", primaryColor);
        refreshBtn.addActionListener(e -> loadData());

        JButton testConnectionBtn = createStyledButton("🔍 اختبار الاتصال", successColor);
        testConnectionBtn.addActionListener(e -> testSelectedConnection());

        JButton importBtn = createStyledButton("📥 تشغيل الاستيراد", warningColor);
        importBtn.addActionListener(e -> runImport());

        JButton exportConfigBtn = createStyledButton("📤 تصدير الإعدادات", primaryColor);
        exportConfigBtn.addActionListener(e -> exportConfiguration());

        panel.add(refreshBtn);
        panel.add(testConnectionBtn);
        panel.add(importBtn);
        panel.add(exportConfigBtn);

        return panel;
    }

    /**
     * إنشاء تبويب اتصالات الأنظمة
     */
    private JPanel createConnectionsPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));

        // جدول الاتصالات
        String[] connectionColumns = {"ID", "اسم الاتصال", "نوع النظام", "الخادم", "المنفذ",
                "قاعدة البيانات", "المستخدم", "نشط", "الوصف"};

        connectionsModel = new DefaultTableModel(connectionColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        connectionsTable = new JTable(connectionsModel);
        connectionsTable.setFont(arabicFont);
        connectionsTable.getTableHeader().setFont(arabicBoldFont);
        connectionsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        connectionsTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JScrollPane connectionsScrollPane = new JScrollPane(connectionsTable);
        panel.add(connectionsScrollPane, BorderLayout.CENTER);

        // أزرار الإدارة
        JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton addConnectionBtn = createStyledButton("➕ إضافة اتصال", successColor);
        addConnectionBtn.addActionListener(e -> addConnection());

        JButton editConnectionBtn = createStyledButton("✏️ تعديل", primaryColor);
        editConnectionBtn.addActionListener(e -> editConnection());

        JButton deleteConnectionBtn = createStyledButton("🗑️ حذف", dangerColor);
        deleteConnectionBtn.addActionListener(e -> deleteConnection());

        buttonsPanel.add(addConnectionBtn);
        buttonsPanel.add(editConnectionBtn);
        buttonsPanel.add(deleteConnectionBtn);

        panel.add(buttonsPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * إنشاء تبويب ربط الجداول
     */
    private JPanel createMappingsPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));

        // جدول الربط
        String[] mappingColumns = {"ID", "اسم الربط", "الجدول المصدر", "الجدول الهدف", "نوع الربط",
                "آخر استيراد", "عدد السجلات", "نشط"};

        mappingsModel = new DefaultTableModel(mappingColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        mappingsTable = new JTable(mappingsModel);
        mappingsTable.setFont(arabicFont);
        mappingsTable.getTableHeader().setFont(arabicBoldFont);
        mappingsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        mappingsTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JScrollPane mappingsScrollPane = new JScrollPane(mappingsTable);
        panel.add(mappingsScrollPane, BorderLayout.CENTER);

        // أزرار الإدارة
        JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton addMappingBtn = createStyledButton("➕ إضافة ربط", successColor);
        addMappingBtn.addActionListener(e -> addMapping());

        JButton editMappingBtn = createStyledButton("✏️ تعديل", primaryColor);
        editMappingBtn.addActionListener(e -> editMapping());

        JButton viewFieldsBtn = createStyledButton("🔍 عرض الحقول", primaryColor);
        viewFieldsBtn.addActionListener(e -> viewFieldMapping());

        JButton runImportBtn = createStyledButton("▶️ تشغيل", warningColor);
        runImportBtn.addActionListener(e -> runSelectedImport());

        buttonsPanel.add(addMappingBtn);
        buttonsPanel.add(editMappingBtn);
        buttonsPanel.add(viewFieldsBtn);
        buttonsPanel.add(runImportBtn);

        panel.add(buttonsPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * إنشاء تبويب تاريخ الاستيراد
     */
    private JPanel createHistoryPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));

        // جدول التاريخ
        String[] historyColumns = {"ID", "اسم الربط", "تاريخ الاستيراد", "النوع", "إجمالي السجلات",
                "تم الاستيراد", "فشل", "الحالة", "المدة"};

        historyModel = new DefaultTableModel(historyColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        historyTable = new JTable(historyModel);
        historyTable.setFont(arabicFont);
        historyTable.getTableHeader().setFont(arabicBoldFont);
        historyTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        historyTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JScrollPane historyScrollPane = new JScrollPane(historyTable);
        panel.add(historyScrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء تبويب الإعدادات
     */
    private JPanel createSettingsPanel() {
        JPanel panel = new JPanel(new BorderLayout(10, 10));

        // جدول الإعدادات
        String[] configColumns =
                {"ID", "اسم الإعداد", "نوع الإعداد", "القيمة", "الوصف", "إعداد نظام", "نشط"};

        DefaultTableModel configModel = new DefaultTableModel(configColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        JTable configTable = new JTable(configModel);
        configTable.setFont(arabicFont);
        configTable.getTableHeader().setFont(arabicBoldFont);
        configTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        configTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // تحميل الإعدادات
        loadConfigurations(configModel);

        JScrollPane configScrollPane = new JScrollPane(configTable);
        panel.add(configScrollPane, BorderLayout.CENTER);

        // أزرار الإدارة
        JPanel buttonsPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton addConfigBtn = createStyledButton("➕ إضافة إعداد", successColor);
        JButton editConfigBtn = createStyledButton("✏️ تعديل", primaryColor);
        JButton deleteConfigBtn = createStyledButton("🗑️ حذف", dangerColor);
        JButton refreshConfigBtn = createStyledButton("🔄 تحديث", primaryColor);

        buttonsPanel.add(addConfigBtn);
        buttonsPanel.add(editConfigBtn);
        buttonsPanel.add(deleteConfigBtn);
        buttonsPanel.add(refreshConfigBtn);

        // إضافة الأحداث
        refreshConfigBtn.addActionListener(e -> loadConfigurations(configModel));

        panel.add(buttonsPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * إنشاء شريط الحالة
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEtchedBorder());

        JLabel statusLabel = new JLabel("جاهز - نظام إدارة الربط والاستيراد");
        statusLabel.setFont(arabicFont);
        panel.add(statusLabel, BorderLayout.WEST);

        JLabel dbLabel = new JLabel("قاعدة البيانات: Oracle SHIP_ERP");
        dbLabel.setFont(arabicFont);
        dbLabel.setForeground(successColor);
        panel.add(dbLabel, BorderLayout.EAST);

        return panel;
    }

    /**
     * إنشاء زر منسق
     */
    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setFont(arabicFont);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(120, 35));
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return button;
    }

    /**
     * تحميل البيانات
     */
    private void loadData() {
        loadConnections();
        loadMappings();
        loadHistory();
    }

    /**
     * تحميل الاتصالات
     */
    private void loadConnections() {
        try {
            connectionsModel.setRowCount(0);
            // استخدام Package لتحميل الاتصالات
            DatabasePackageManager.ConnectionManager connManager =
                    packageManager.getConnectionManager();
            ResultSet rs = connManager.getAllConnections();

            while (rs.next()) {
                Object[] row = {rs.getInt("CONNECTION_ID"), rs.getString("CONNECTION_NAME"),
                        rs.getString("CONNECTION_TYPE"), rs.getString("HOST_NAME"),
                        rs.getInt("PORT_NUMBER"), rs.getString("DATABASE_NAME"),
                        rs.getString("USERNAME"), rs.getInt("IS_ACTIVE") == 1 ? "نعم" : "لا",
                        rs.getString("DESCRIPTION")};
                connectionsModel.addRow(row);
            }
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تحميل الاتصالات:\n" + e.getMessage());
        }
    }

    /**
     * تحميل الربط
     */
    private void loadMappings() {
        try {
            mappingsModel.setRowCount(0);
            String sql =
                    """
                                SELECT tm.MAPPING_ID, tm.MAPPING_NAME, tm.SOURCE_TABLE_NAME, tm.TARGET_TABLE_NAME,
                                       tm.MAPPING_TYPE, tm.LAST_IMPORT_DATE, tm.LAST_IMPORT_COUNT, tm.IS_ACTIVE,
                                       sc.CONNECTION_NAME
                                FROM ERP_TABLE_MAPPING tm
                                LEFT JOIN ERP_SYSTEM_CONNECTIONS sc ON tm.CONNECTION_ID = sc.CONNECTION_ID
                                ORDER BY tm.MAPPING_NAME
                            """;

            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                Object[] row = {rs.getInt("MAPPING_ID"), rs.getString("MAPPING_NAME"),
                        rs.getString("SOURCE_TABLE_NAME"), rs.getString("TARGET_TABLE_NAME"),
                        rs.getString("MAPPING_TYPE"), rs.getDate("LAST_IMPORT_DATE"),
                        rs.getObject("LAST_IMPORT_COUNT"),
                        rs.getInt("IS_ACTIVE") == 1 ? "نعم" : "لا"};
                mappingsModel.addRow(row);
            }

            System.out.println("✅ تم تحميل " + mappingsModel.getRowCount() + " ربط جدول");

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تحميل ربط الجداول:\n" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * تحميل التاريخ
     */
    private void loadHistory() {
        try {
            historyModel.setRowCount(0);
            String sql = """
                        SELECT ih.IMPORT_ID, tm.MAPPING_NAME, ih.IMPORT_DATE, ih.IMPORT_TYPE,
                               ih.TOTAL_RECORDS, ih.IMPORTED_RECORDS, ih.FAILED_RECORDS,
                               ih.IMPORT_STATUS, ih.IMPORT_DURATION
                        FROM ERP_IMPORT_HISTORY ih
                        LEFT JOIN ERP_TABLE_MAPPING tm ON ih.TABLE_MAPPING_ID = tm.MAPPING_ID
                        ORDER BY ih.IMPORT_DATE DESC
                    """;

            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                Object[] row = {rs.getInt("IMPORT_ID"), rs.getString("MAPPING_NAME"),
                        rs.getTimestamp("IMPORT_DATE"), rs.getString("IMPORT_TYPE"),
                        rs.getObject("TOTAL_RECORDS"), rs.getObject("IMPORTED_RECORDS"),
                        rs.getObject("FAILED_RECORDS"), rs.getString("IMPORT_STATUS"),
                        rs.getObject("IMPORT_DURATION") != null
                                ? rs.getInt("IMPORT_DURATION") + " ثانية"
                                : null};
                historyModel.addRow(row);
            }

            System.out.println("✅ تم تحميل " + historyModel.getRowCount() + " سجل تاريخ");

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تحميل تاريخ الاستيراد:\n" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * تحميل الإعدادات
     */
    private void loadConfigurations(DefaultTableModel configModel) {
        try {
            configModel.setRowCount(0);
            String sql = """
                        SELECT CONFIG_ID, CONFIG_NAME, CONFIG_TYPE, CONFIG_VALUE,
                               CONFIG_DESCRIPTION, IS_SYSTEM_CONFIG, IS_ACTIVE
                        FROM ERP_IMPORT_CONFIG
                        ORDER BY CONFIG_NAME
                    """;

            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                Object[] row = {rs.getInt("CONFIG_ID"), rs.getString("CONFIG_NAME"),
                        rs.getString("CONFIG_TYPE"), rs.getString("CONFIG_VALUE"),
                        rs.getString("CONFIG_DESCRIPTION"),
                        rs.getInt("IS_SYSTEM_CONFIG") == 1 ? "نعم" : "لا",
                        rs.getInt("IS_ACTIVE") == 1 ? "نعم" : "لا"};
                configModel.addRow(row);
            }

            System.out.println("✅ تم تحميل " + configModel.getRowCount() + " إعداد");

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تحميل الإعدادات:\n" + e.getMessage());
            e.printStackTrace();
        }
    }

    // الوظائف الأخرى سيتم إضافتها لاحقاً
    private void testSelectedConnection() {}

    private void runImport() {}

    private void exportConfiguration() {}

    private void addConnection() {}

    private void editConnection() {}

    private void deleteConnection() {}

    private void addMapping() {}

    private void editMapping() {}

    private void viewFieldMapping() {
        int selectedRow = mappingsTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار ربط جدول أولاً", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        int mappingId = (Integer) mappingsModel.getValueAt(selectedRow, 0);
        String mappingName = (String) mappingsModel.getValueAt(selectedRow, 1);

        // إنشاء نافذة عرض ربط الحقول
        JDialog fieldDialog = new JDialog(this, "ربط الحقول - " + mappingName, true);
        fieldDialog.setSize(800, 600);
        fieldDialog.setLocationRelativeTo(this);
        fieldDialog.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // جدول ربط الحقول
        String[] fieldColumns = {"ID", "الحقل المصدر", "الحقل الهدف", "نوع البيانات",
                "تحويل البيانات", "حقل مفتاحي", "مطلوب", "القيمة الافتراضية", "ترتيب الحقل"};

        DefaultTableModel fieldModel = new DefaultTableModel(fieldColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        JTable fieldTable = new JTable(fieldModel);
        fieldTable.setFont(arabicFont);
        fieldTable.getTableHeader().setFont(arabicBoldFont);
        fieldTable.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // تحميل بيانات ربط الحقول
        try {
            String sql =
                    """
                                SELECT FIELD_MAPPING_ID, SOURCE_FIELD_NAME, TARGET_FIELD_NAME, DATA_TYPE,
                                       DATA_TRANSFORMATION, IS_KEY_FIELD, IS_REQUIRED, DEFAULT_VALUE, FIELD_ORDER
                                FROM ERP_FIELD_MAPPING
                                WHERE TABLE_MAPPING_ID = ?
                                ORDER BY FIELD_ORDER
                            """;

            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setInt(1, mappingId);
            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                Object[] row = {rs.getInt("FIELD_MAPPING_ID"), rs.getString("SOURCE_FIELD_NAME"),
                        rs.getString("TARGET_FIELD_NAME"), rs.getString("DATA_TYPE"),
                        rs.getString("DATA_TRANSFORMATION"),
                        rs.getInt("IS_KEY_FIELD") == 1 ? "نعم" : "لا",
                        rs.getInt("IS_REQUIRED") == 1 ? "نعم" : "لا", rs.getString("DEFAULT_VALUE"),
                        rs.getInt("FIELD_ORDER")};
                fieldModel.addRow(row);
            }

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تحميل ربط الحقول:\n" + e.getMessage());
            return;
        }

        JScrollPane scrollPane = new JScrollPane(fieldTable);
        fieldDialog.add(scrollPane, BorderLayout.CENTER);

        // شريط الحالة
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setBorder(BorderFactory.createEtchedBorder());
        JLabel statusLabel = new JLabel("عدد الحقول: " + fieldModel.getRowCount());
        statusLabel.setFont(arabicFont);
        statusPanel.add(statusLabel, BorderLayout.WEST);
        fieldDialog.add(statusPanel, BorderLayout.SOUTH);

        fieldDialog.setVisible(true);
    }

    private void runSelectedImport() {}

    @Override
    public void dispose() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        super.dispose();
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new SystemIntegrationWindow().setVisible(true);
        });
    }
}
