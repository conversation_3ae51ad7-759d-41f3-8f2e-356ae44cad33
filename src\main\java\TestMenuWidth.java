import javax.swing.*;
import java.awt.*;

/**
 * اختبار عرض القائمة المضغوط
 */
public class TestMenuWidth {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                // تشغيل النافذة الرئيسية المحسنة
                EnhancedMainWindow mainWindow = new EnhancedMainWindow();
                mainWindow.setVisible(true);
                
                // عرض رسالة تأكيد
                JOptionPane.showMessageDialog(null, 
                    "تم تطبيق التعديلات على عرض القائمة:\n" +
                    "• العرض المفضل: 150 بكسل (بدلاً من 180)\n" +
                    "• الحد الأدنى: 140 بكسل (بدلاً من 160)\n" +
                    "• العرض الافتراضي: 150 بكسل (بدلاً من 200)\n" +
                    "• الحد الأدنى المحفوظ: 140 بكسل (بدلاً من 180)\n\n" +
                    "القائمة الآن مضغوطة إلى أقصى حد ممكن مع الحفاظ على وضوح شجرة الأنظمة",
                    "تأكيد التعديلات", 
                    JOptionPane.INFORMATION_MESSAGE);
                    
            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, 
                    "خطأ في تشغيل النافذة الرئيسية: " + e.getMessage(),
                    "خطأ", 
                    JOptionPane.ERROR_MESSAGE);
                e.printStackTrace();
            }
        });
    }
}
