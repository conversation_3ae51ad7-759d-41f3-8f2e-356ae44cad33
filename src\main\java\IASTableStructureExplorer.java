import java.sql.*;

/**
 * استكشاف هيكل جداول IAS_ITM_MST و IAS_ITM_DTL الفعلي
 * IAS Table Structure Explorer
 */
public class IASTableStructureExplorer {
    
    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("   استكشاف هيكل جداول IAS");
        System.out.println("   IAS Table Structure Explorer");
        System.out.println("====================================");
        System.out.println();
        
        IASTableStructureExplorer explorer = new IASTableStructureExplorer();
        explorer.exploreStructure();
    }
    
    /**
     * استكشاف هيكل الجداول
     */
    public void exploreStructure() {
        // بيانات الاتصال
        String url = "*************************************";
        String username = "ysdba2";
        String password = "ys123";
        
        Connection connection = null;
        
        try {
            System.out.println("🔄 الاتصال بقاعدة البيانات...");
            connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ تم الاتصال بنجاح!");
            System.out.println();
            
            // فحص هيكل IAS_ITM_MST
            exploreTableStructure(connection, "IAS20251", "IAS_ITM_MST");
            
            // فحص هيكل IAS_ITM_DTL
            exploreTableStructure(connection, "IAS20251", "IAS_ITM_DTL");
            
            // اختبار عينة من البيانات
            testSampleData(connection);
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في الاتصال: " + e.getMessage());
            e.printStackTrace();
            
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                    System.out.println("🔒 تم إغلاق الاتصال");
                } catch (SQLException e) {
                    System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * استكشاف هيكل جدول محدد
     */
    private void exploreTableStructure(Connection connection, String owner, String tableName) throws SQLException {
        System.out.println("📋 هيكل جدول " + owner + "." + tableName + ":");
        System.out.println("=====================================");
        
        String query = """
            SELECT 
                column_name, 
                data_type, 
                data_length, 
                data_precision,
                data_scale,
                nullable, 
                data_default,
                column_id
            FROM all_tab_columns 
            WHERE owner = ? AND table_name = ?
            ORDER BY column_id
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, owner);
            stmt.setString(2, tableName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                boolean found = false;
                while (rs.next()) {
                    found = true;
                    String columnName = rs.getString("column_name");
                    String dataType = rs.getString("data_type");
                    int dataLength = rs.getInt("data_length");
                    Object dataPrecision = rs.getObject("data_precision");
                    Object dataScale = rs.getObject("data_scale");
                    String nullable = rs.getString("nullable");
                    String defaultValue = rs.getString("data_default");
                    int columnId = rs.getInt("column_id");
                    
                    System.out.printf("%2d. %-20s %s", columnId, columnName, dataType);
                    
                    if (dataPrecision != null && dataScale != null) {
                        System.out.printf("(%s,%s)", dataPrecision, dataScale);
                    } else if (dataLength > 0 && !"DATE".equals(dataType) && !"TIMESTAMP".equals(dataType)) {
                        System.out.printf("(%d)", dataLength);
                    }
                    
                    System.out.printf(" %s", "Y".equals(nullable) ? "NULL" : "NOT NULL");
                    
                    if (defaultValue != null && !defaultValue.trim().isEmpty()) {
                        System.out.printf(" DEFAULT %s", defaultValue.trim());
                    }
                    
                    System.out.println();
                }
                
                if (!found) {
                    System.out.println("❌ الجدول غير موجود أو لا يمكن الوصول إليه");
                }
            }
        }
        
        System.out.println();
    }
    
    /**
     * اختبار عينة من البيانات
     */
    private void testSampleData(Connection connection) throws SQLException {
        System.out.println("📊 اختبار عينة من البيانات:");
        System.out.println("============================");
        
        // اختبار IAS_ITM_MST
        System.out.println("🔍 عينة من IAS_ITM_MST:");
        String mstQuery = "SELECT * FROM IAS20251.IAS_ITM_MST WHERE ROWNUM <= 3";
        
        try (PreparedStatement stmt = connection.prepareStatement(mstQuery);
             ResultSet rs = stmt.executeQuery()) {
            
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            // عرض أسماء الأعمدة
            System.out.print("  ");
            for (int i = 1; i <= columnCount; i++) {
                System.out.printf("%-15s", metaData.getColumnName(i));
            }
            System.out.println();
            
            // عرض البيانات
            int rowCount = 0;
            while (rs.next() && rowCount < 3) {
                System.out.print("  ");
                for (int i = 1; i <= columnCount; i++) {
                    Object value = rs.getObject(i);
                    String displayValue = value != null ? value.toString() : "NULL";
                    if (displayValue.length() > 12) {
                        displayValue = displayValue.substring(0, 12) + "...";
                    }
                    System.out.printf("%-15s", displayValue);
                }
                System.out.println();
                rowCount++;
            }
            
            if (rowCount == 0) {
                System.out.println("  ❌ الجدول فارغ");
            }
            
        } catch (SQLException e) {
            System.out.println("  ❌ خطأ في قراءة IAS_ITM_MST: " + e.getMessage());
        }
        
        System.out.println();
        
        // اختبار IAS_ITM_DTL
        System.out.println("🔍 عينة من IAS_ITM_DTL:");
        String dtlQuery = "SELECT * FROM IAS20251.IAS_ITM_DTL WHERE ROWNUM <= 3";
        
        try (PreparedStatement stmt = connection.prepareStatement(dtlQuery);
             ResultSet rs = stmt.executeQuery()) {
            
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            // عرض أسماء الأعمدة
            System.out.print("  ");
            for (int i = 1; i <= columnCount; i++) {
                System.out.printf("%-15s", metaData.getColumnName(i));
            }
            System.out.println();
            
            // عرض البيانات
            int rowCount = 0;
            while (rs.next() && rowCount < 3) {
                System.out.print("  ");
                for (int i = 1; i <= columnCount; i++) {
                    Object value = rs.getObject(i);
                    String displayValue = value != null ? value.toString() : "NULL";
                    if (displayValue.length() > 12) {
                        displayValue = displayValue.substring(0, 12) + "...";
                    }
                    System.out.printf("%-15s", displayValue);
                }
                System.out.println();
                rowCount++;
            }
            
            if (rowCount == 0) {
                System.out.println("  ❌ الجدول فارغ");
            }
            
        } catch (SQLException e) {
            System.out.println("  ❌ خطأ في قراءة IAS_ITM_DTL: " + e.getMessage());
        }
        
        System.out.println();
        
        // اختبار الاستعلام المدمج البسيط
        System.out.println("🔗 اختبار الاستعلام المدمج:");
        String joinQuery = """
            SELECT 
                m.ITM_ID,
                m.ITM_CODE,
                m.ITM_NAME,
                d.COST_PRICE,
                d.SELL_PRICE
            FROM IAS20251.IAS_ITM_MST m
            LEFT JOIN IAS20251.IAS_ITM_DTL d ON m.ITM_ID = d.ITM_ID
            WHERE ROWNUM <= 5
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(joinQuery);
             ResultSet rs = stmt.executeQuery()) {
            
            System.out.println("  ITM_ID\t\tITM_CODE\t\tITM_NAME\t\tCOST_PRICE\tSELL_PRICE");
            System.out.println("  ----------------------------------------------------------------");
            
            while (rs.next()) {
                Object itmId = rs.getObject("ITM_ID");
                Object itmCode = rs.getObject("ITM_CODE");
                Object itmName = rs.getObject("ITM_NAME");
                Object costPrice = rs.getObject("COST_PRICE");
                Object sellPrice = rs.getObject("SELL_PRICE");
                
                System.out.printf("  %s\t\t%s\t\t%s\t\t%s\t\t%s%n",
                    itmId != null ? itmId : "NULL",
                    itmCode != null ? itmCode : "NULL",
                    itmName != null ? (itmName.toString().length() > 10 ? itmName.toString().substring(0, 10) + "..." : itmName) : "NULL",
                    costPrice != null ? costPrice : "NULL",
                    sellPrice != null ? sellPrice : "NULL"
                );
            }
            
            System.out.println("  ✅ الاستعلام المدمج يعمل بنجاح!");
            
        } catch (SQLException e) {
            System.out.println("  ❌ خطأ في الاستعلام المدمج: " + e.getMessage());
        }
        
        System.out.println();
    }
}
