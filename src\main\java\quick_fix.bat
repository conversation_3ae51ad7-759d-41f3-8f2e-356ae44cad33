@echo off
echo ====================================
echo    Quick Oracle JDBC Fix
echo ====================================
echo.

echo Step 1: Checking libraries...
if not exist "lib\ojdbc11.jar" (
    echo ERROR: ojdbc11.jar missing!
    java LibraryDownloader
)

if not exist "lib\orai18n.jar" (
    echo ERROR: orai18n.jar missing!
    java LibraryDownloader
)

echo Step 2: Compiling system...
javac -encoding UTF-8 -cp "lib/*;." *.java

echo Step 3: Starting system with Oracle libraries...
echo.
echo IMPORTANT: System will start with English locale to avoid regex issues
echo This fixes both ORA-17056 and regex pattern problems
echo.

java -Duser.language=en -Duser.country=US -Dfile.encoding=UTF-8 -cp "lib/*;." CompleteSystemTest

echo.
echo System finished.
pause
