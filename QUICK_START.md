# دليل البدء السريع - نظام إدارة الشحنات

## 🚀 التثبيت السريع

### الخطوة 1: التحقق من المتطلبات
```bash
# تشغيل فحص المتطلبات
check-requirements.bat
```

### الخطوة 2: تثبيت المتطلبات الأساسية

#### Java JDK 17+
1. تحميل من: https://adoptium.net/
2. تثبيت وإضافة إلى PATH
3. تحديد متغير JAVA_HOME

#### Apache Maven
1. تحميل من: https://maven.apache.org/download.cgi
2. استخراج إلى C:\apache-maven-3.9.x
3. إضافة bin إلى PATH
4. تحديد متغير M2_HOME

#### Oracle Database
1. تحميل Oracle XE من الموقع الرسمي
2. تثبيت مع كلمة مرور للمستخدم system
3. التأكد من تشغيل الخدمة

### الخطوة 3: إعداد قاعدة البيانات
```bash
# تشغيل سكريبت إعداد قاعدة البيانات
setup-database.bat
```

### الخطوة 4: تشغيل التطبيق
```bash
# تشغيل التطبيق
run-ship-erp.bat
```

## 📋 معلومات تسجيل الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 🔧 الأوامر المفيدة

### بناء المشروع
```bash
mvn clean compile
```

### تشغيل الاختبارات
```bash
mvn test
```

### إنشاء JAR
```bash
mvn clean package
```

### تشغيل مع Maven
```bash
mvn javafx:run
```

## 📁 هيكل المشروع

```
ship-erp-system/
├── src/main/java/          # الكود المصدري
├── src/main/resources/     # الموارد والإعدادات
├── src/test/java/          # اختبارات الوحدة
├── scripts/                # سكريبتات قاعدة البيانات
├── logs/                   # ملفات السجل
├── backup/                 # النسخ الاحتياطية
├── reports/                # التقارير المُنتجة
└── target/                 # ملفات البناء
```

## 🌐 دعم اللغة العربية

### الخطوط المدعومة
- Noto Sans Arabic (افتراضي)
- Tahoma
- Arial Unicode MS

### إعدادات RTL
- جميع الواجهات تدعم RTL
- النصوص العربية محاذاة لليمين
- التبويبات والقوائم بالاتجاه الصحيح

## 🔒 الأمان

### كلمات المرور
- تشفير BCrypt
- حد أدنى 8 أحرف
- قفل الحساب بعد 3 محاولات فاشلة

### الصلاحيات
- نظام أدوار مرن
- صلاحيات على مستوى الوحدات
- تسجيل جميع العمليات

## 📊 قاعدة البيانات

### معلومات الاتصال
- **الخادم**: localhost:1521
- **قاعدة البيانات**: XE
- **المستخدم**: ship_erp
- **كلمة المرور**: ship_erp_password

### الجداول الرئيسية
- COMPANIES (الشركات)
- BRANCHES (الفروع)
- USERS (المستخدمين)
- ROLES (الأدوار)
- PERMISSIONS (الصلاحيات)
- CURRENCIES (العملات)
- FISCAL_YEARS (السنوات المالية)
- SYSTEM_SETTINGS (الإعدادات)

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

#### Java غير موجود
```bash
# تحقق من تثبيت Java
java -version

# تحقق من JAVA_HOME
echo %JAVA_HOME%
```

#### Maven غير موجود
```bash
# تحقق من تثبيت Maven
mvn -version

# تحقق من M2_HOME
echo %M2_HOME%
```

#### مشاكل قاعدة البيانات
```bash
# تحقق من خدمة Oracle
net start OracleServiceXE

# اختبار الاتصال
sqlplus system/password@localhost:1521/XE
```

#### مشاكل الترميز
- تأكد من حفظ الملفات بترميز UTF-8
- تحقق من إعدادات IDE
- استخدم chcp 65001 في Command Prompt

## 📝 السجلات

### مواقع ملفات السجل
- `logs/ship-erp.log` - السجل العام
- `logs/ship-erp-error.log` - سجل الأخطاء
- `logs/ship-erp-audit.log` - سجل التدقيق

### مستويات التسجيل
- DEBUG - تفاصيل التطوير
- INFO - معلومات عامة
- WARN - تحذيرات
- ERROR - أخطاء

## 🔄 النسخ الاحتياطي

### نسخ تلقائي
- كل 24 ساعة افتراضياً
- حفظ في مجلد backup/
- الاحتفاظ لمدة 30 يوم

### نسخ يدوي
```bash
# إنشاء نسخة احتياطية يدوية
# استخدام VS Code Task: Create Backup
```

## 📞 الدعم

### الوثائق
- README.md - دليل شامل
- INSTALLATION_GUIDE.md - دليل التثبيت المفصل
- docs/ - وثائق إضافية

### الإبلاغ عن المشاكل
1. تحقق من ملفات السجل
2. راجع الأخطاء الشائعة
3. أنشئ تقرير مشكلة مع التفاصيل

## 🎯 الميزات الحالية

### ✅ متوفر
- نظام الإعدادات العامة
- إدارة المستخدمين والصلاحيات
- إدارة الشركات والفروع
- إدارة العملات
- إدارة السنوات المالية
- دعم كامل للعربية و RTL

### 🚧 قيد التطوير
- نظام إدارة الأصناف
- نظام إدارة الموردين
- نظام متابعة الشحنات
- نظام الإدخالات الجمركية
- نظام إدارة التكاليف

## 🔮 الإصدارات القادمة

### v1.1.0
- نظام إدارة الأصناف
- تحسينات الأداء
- تقارير أساسية

### v1.2.0
- نظام إدارة الموردين
- واجهة محسنة
- تصدير البيانات

### v2.0.0
- نظام متابعة الشحنات
- لوحة تحكم تفاعلية
- تطبيق الهاتف المحمول
