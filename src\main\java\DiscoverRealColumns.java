import java.sql.*;

/**
 * اكتشاف أسماء الأعمدة الحقيقية في جداول IAS20251
 */
public class DiscoverRealColumns {
    
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            System.out.println("🔍 اكتشاف أسماء الأعمدة الحقيقية");
            System.out.println("=====================================");
            
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            System.out.println("✅ تم الاتصال بـ SHIP_ERP");
            
            Statement stmt = conn.createStatement();
            
            // اكتشاف أعمدة IAS_ITM_MST
            System.out.println("\n📋 أعمدة جدول IAS_ITM_MST:");
            discoverTableColumns(stmt, "IAS_ITM_MST@IAS20251_LINK");
            
            // اكتشاف أعمدة IAS_ITM_DTL
            System.out.println("\n📋 أعمدة جدول IAS_ITM_DTL:");
            discoverTableColumns(stmt, "IAS_ITM_DTL@IAS20251_LINK");
            
            // اكتشاف أعمدة GROUP_DETAILS
            System.out.println("\n📋 أعمدة جدول GROUP_DETAILS:");
            discoverTableColumns(stmt, "GROUP_DETAILS@IAS20251_LINK");
            
            // اكتشاف أعمدة IAS_MAINSUB_GRP_DTL
            System.out.println("\n📋 أعمدة جدول IAS_MAINSUB_GRP_DTL:");
            discoverTableColumns(stmt, "IAS_MAINSUB_GRP_DTL@IAS20251_LINK");
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ عام: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * اكتشاف أعمدة جدول
     */
    private static void discoverTableColumns(Statement stmt, String tableName) {
        try {
            // الحصول على أول صف لمعرفة أسماء الأعمدة
            ResultSet rs = stmt.executeQuery("SELECT * FROM " + tableName + " WHERE ROWNUM <= 1");
            
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            System.out.println("  عدد الأعمدة: " + columnCount);
            
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                String columnType = metaData.getColumnTypeName(i);
                int columnSize = metaData.getColumnDisplaySize(i);
                
                System.out.printf("  %-25s %-15s (%d)\n", columnName, columnType, columnSize);
            }
            
            // عرض عينة من البيانات
            System.out.println("\n  📄 عينة من البيانات:");
            rs = stmt.executeQuery("SELECT * FROM " + tableName + " WHERE ROWNUM <= 3");
            
            while (rs.next()) {
                System.out.print("    ");
                for (int i = 1; i <= columnCount; i++) {
                    String value = rs.getString(i);
                    if (value != null && value.length() > 15) {
                        value = value.substring(0, 15) + "...";
                    }
                    System.out.printf("%-18s ", value != null ? value : "NULL");
                }
                System.out.println();
            }
            
            rs.close();
            
        } catch (SQLException e) {
            System.err.println("  ❌ خطأ في اكتشاف أعمدة " + tableName + ": " + e.getMessage());
        }
    }
}
