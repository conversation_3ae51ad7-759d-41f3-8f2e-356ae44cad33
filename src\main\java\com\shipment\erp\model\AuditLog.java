package com.shipment.erp.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * كيان سجل التدقيق
 * يمثل سجل العمليات المنفذة في النظام
 */
@Entity
@Table(name = "AUDIT_LOG", indexes = {
    @Index(name = "idx_audit_user", columnList = "USER_ID"),
    @Index(name = "idx_audit_date", columnList = "ACTION_DATE"),
    @Index(name = "idx_audit_table", columnList = "TABLE_NAME"),
    @Index(name = "idx_audit_action", columnList = "ACTION")
})
@SequenceGenerator(name = "audit_log_seq", sequenceName = "SEQ_AUDIT_LOG", allocationSize = 1)
public class AuditLog {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "audit_log_seq")
    @Column(name = "ID")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "USER_ID")
    private User user;

    @Column(name = "ACTION", nullable = false, length = 50)
    @NotBlank(message = "نوع العملية مطلوب")
    @Size(max = 50, message = "نوع العملية يجب أن يكون أقل من 50 حرف")
    private String action;

    @Column(name = "TABLE_NAME", length = 100)
    @Size(max = 100, message = "اسم الجدول يجب أن يكون أقل من 100 حرف")
    private String tableName;

    @Column(name = "RECORD_ID")
    private Long recordId;

    @Lob
    @Column(name = "OLD_VALUES")
    private String oldValues;

    @Lob
    @Column(name = "NEW_VALUES")
    private String newValues;

    @Column(name = "IP_ADDRESS", length = 45)
    @Size(max = 45, message = "عنوان IP يجب أن يكون أقل من 45 حرف")
    private String ipAddress;

    @Column(name = "USER_AGENT", length = 500)
    @Size(max = 500, message = "User Agent يجب أن يكون أقل من 500 حرف")
    private String userAgent;

    @Column(name = "ACTION_DATE", nullable = false)
    private LocalDateTime actionDate;

    /**
     * أنواع العمليات المدعومة
     */
    public enum ActionType {
        CREATE("CREATE", "إنشاء"),
        UPDATE("UPDATE", "تحديث"),
        DELETE("DELETE", "حذف"),
        LOGIN("LOGIN", "تسجيل دخول"),
        LOGOUT("LOGOUT", "تسجيل خروج"),
        LOGIN_FAILED("LOGIN_FAILED", "فشل تسجيل دخول"),
        PASSWORD_CHANGE("PASSWORD_CHANGE", "تغيير كلمة المرور"),
        EXPORT("EXPORT", "تصدير"),
        IMPORT("IMPORT", "استيراد"),
        PRINT("PRINT", "طباعة"),
        BACKUP("BACKUP", "نسخ احتياطي"),
        RESTORE("RESTORE", "استعادة");

        private final String code;
        private final String description;

        ActionType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static ActionType fromCode(String code) {
            for (ActionType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * Constructor افتراضي
     */
    public AuditLog() {
        this.actionDate = LocalDateTime.now();
    }

    /**
     * Constructor مع المستخدم ونوع العملية
     */
    public AuditLog(User user, ActionType actionType) {
        this();
        this.user = user;
        this.action = actionType.getCode();
    }

    /**
     * Constructor مع المستخدم ونوع العملية واسم الجدول
     */
    public AuditLog(User user, ActionType actionType, String tableName, Long recordId) {
        this(user, actionType);
        this.tableName = tableName;
        this.recordId = recordId;
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public void setAction(ActionType actionType) {
        this.action = actionType.getCode();
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public String getOldValues() {
        return oldValues;
    }

    public void setOldValues(String oldValues) {
        this.oldValues = oldValues;
    }

    public String getNewValues() {
        return newValues;
    }

    public void setNewValues(String newValues) {
        this.newValues = newValues;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public LocalDateTime getActionDate() {
        return actionDate;
    }

    public void setActionDate(LocalDateTime actionDate) {
        this.actionDate = actionDate;
    }

    /**
     * الحصول على نوع العملية كـ enum
     */
    public ActionType getActionType() {
        return ActionType.fromCode(action);
    }

    /**
     * الحصول على وصف العملية
     */
    public String getActionDescription() {
        ActionType actionType = getActionType();
        return actionType != null ? actionType.getDescription() : action;
    }

    /**
     * الحصول على اسم المستخدم
     */
    public String getUserName() {
        return user != null ? user.getFullName() : "نظام";
    }

    /**
     * الحصول على اسم المستخدم للدخول
     */
    public String getUsername() {
        return user != null ? user.getUsername() : "system";
    }

    /**
     * إنشاء سجل تدقيق للإنشاء
     */
    public static AuditLog createInsertLog(User user, String tableName, Long recordId, String newValues) {
        AuditLog log = new AuditLog(user, ActionType.CREATE, tableName, recordId);
        log.setNewValues(newValues);
        return log;
    }

    /**
     * إنشاء سجل تدقيق للتحديث
     */
    public static AuditLog createUpdateLog(User user, String tableName, Long recordId, 
                                          String oldValues, String newValues) {
        AuditLog log = new AuditLog(user, ActionType.UPDATE, tableName, recordId);
        log.setOldValues(oldValues);
        log.setNewValues(newValues);
        return log;
    }

    /**
     * إنشاء سجل تدقيق للحذف
     */
    public static AuditLog createDeleteLog(User user, String tableName, Long recordId, String oldValues) {
        AuditLog log = new AuditLog(user, ActionType.DELETE, tableName, recordId);
        log.setOldValues(oldValues);
        return log;
    }

    /**
     * إنشاء سجل تدقيق لتسجيل الدخول
     */
    public static AuditLog createLoginLog(User user, String ipAddress, String userAgent) {
        AuditLog log = new AuditLog(user, ActionType.LOGIN);
        log.setIpAddress(ipAddress);
        log.setUserAgent(userAgent);
        return log;
    }

    /**
     * إنشاء سجل تدقيق لفشل تسجيل الدخول
     */
    public static AuditLog createLoginFailedLog(String username, String ipAddress, String userAgent) {
        AuditLog log = new AuditLog();
        log.setAction(ActionType.LOGIN_FAILED.getCode());
        log.setOldValues("Username: " + username);
        log.setIpAddress(ipAddress);
        log.setUserAgent(userAgent);
        return log;
    }

    @Override
    public String toString() {
        return "AuditLog{" +
                "id=" + id +
                ", action='" + action + '\'' +
                ", tableName='" + tableName + '\'' +
                ", recordId=" + recordId +
                ", userName='" + getUserName() + '\'' +
                ", actionDate=" + actionDate +
                '}';
    }
}
