import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.List;
import java.util.Locale;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة إدارة وحدات القياس Measurement Units Management Window
 */
public class MeasurementUnitsWindow extends JFrame {

    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 12);

    // مكونات الواجهة
    private JTextField codeField;
    private JTextField nameField;
    private JTextField nameEnField;
    private JTextField globalCodeField;
    private JTextField symbolField;
    private JTextField conversionFactorField;
    private JTextArea descriptionArea;
    private JComboBox<String> typeComboBox;
    private JCheckBox activeCheckBox;

    private JTable unitsTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;

    // قاعدة البيانات
    private Connection connection;
    private MeasurementUnitDAO unitDAO;

    // الألوان
    private Color primaryColor = new Color(52, 152, 219);
    private Color successColor = new Color(46, 204, 113);
    private Color warningColor = new Color(241, 196, 15);
    private Color dangerColor = new Color(231, 76, 60);

    public MeasurementUnitsWindow() {
        initializeDatabase();
        initializeComponents();
        loadUnitsData();
    }

    /**
     * تهيئة قاعدة البيانات
     */
    private void initializeDatabase() {
        try {
            // إصلاح regex
            Locale.setDefault(Locale.ENGLISH);
            System.setProperty("user.language", "en");
            System.setProperty("user.country", "US");

            // تحميل Oracle JDBC driver صراحة
            try {
                Class.forName("oracle.jdbc.driver.OracleDriver");
                System.out.println("✅ تم تحميل Oracle JDBC driver بنجاح");
            } catch (ClassNotFoundException e) {
                System.err.println("❌ فشل في تحميل Oracle JDBC driver: " + e.getMessage());
                throw new SQLException("Oracle JDBC driver غير متاح", e);
            }

            String url = "*************************************";
            String username = "ship_erp";
            String password = "ship_erp_password";

            System.out.println("🔄 محاولة الاتصال بقاعدة البيانات...");
            connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ تم الاتصال بقاعدة البيانات بنجاح");
            unitDAO = new MeasurementUnitDAO(connection);

            // إنشاء الجدول إذا لم يكن موجوداً
            try {
                unitDAO.createTable();
                System.out.println("✅ تم إنشاء جدول وحدات القياس");
            } catch (SQLException e) {
                if (!e.getMessage().contains("already exists")) {
                    System.out.println("ℹ️ جدول وحدات القياس موجود مسبقاً");
                }
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
            e.printStackTrace();

            String errorMessage = "خطأ في الاتصال بقاعدة البيانات:\n" + e.getMessage();

            // إضافة نصائح حسب نوع الخطأ
            if (e.getMessage().contains("No suitable driver")) {
                errorMessage += "\n\n💡 الحل المقترح:\n"
                        + "• تأكد من وجود Oracle JDBC driver في classpath\n"
                        + "• تحقق من ملف ojdbc11.jar في مجلد lib";
            } else if (e.getMessage().contains("Connection refused")) {
                errorMessage += "\n\n💡 الحل المقترح:\n" + "• تأكد من تشغيل Oracle Database\n"
                        + "• تحقق من رقم المنفذ (1521)\n" + "• تحقق من اسم الخدمة (orcl)";
            } else if (e.getMessage().contains("invalid username")) {
                errorMessage += "\n\n💡 الحل المقترح:\n" + "• تحقق من اسم المستخدم: ship_erp\n"
                        + "• تحقق من كلمة المرور\n" + "• تأكد من وجود المستخدم في قاعدة البيانات";
            }

            JOptionPane.showMessageDialog(this, errorMessage, "خطأ في قاعدة البيانات",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * تهيئة مكونات الواجهة
     */
    private void initializeComponents() {
        setTitle("إدارة وحدات القياس - Measurement Units Management");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // لوحة الأزرار العلوية
        JPanel topPanel = createTopPanel();
        mainPanel.add(topPanel, BorderLayout.NORTH);

        // اللوحة الوسطى - تقسيم أفقي
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(400);

        // اللوحة اليمنى - نموذج الإدخال
        JPanel formPanel = createFormPanel();
        splitPane.setRightComponent(formPanel);

        // اللوحة اليسرى - جدول البيانات
        JPanel tablePanel = createTablePanel();
        splitPane.setLeftComponent(tablePanel);

        mainPanel.add(splitPane, BorderLayout.CENTER);

        add(mainPanel);
    }

    /**
     * إنشاء لوحة الأزرار العلوية
     */
    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 5));

        JButton newBtn = createStyledButton("🆕 جديد", successColor);
        newBtn.addActionListener(e -> clearForm());

        JButton saveBtn = createStyledButton("💾 حفظ", primaryColor);
        saveBtn.addActionListener(e -> saveUnit());

        JButton deleteBtn = createStyledButton("🗑️ حذف", dangerColor);
        deleteBtn.addActionListener(e -> deleteUnit());

        JButton importBtn = createStyledButton("📥 استيراد", warningColor);
        importBtn.addActionListener(e -> importFromOriginal());

        JButton refreshBtn = createStyledButton("🔄 تحديث", new Color(52, 73, 94));
        refreshBtn.addActionListener(e -> loadUnitsData());

        panel.add(newBtn);
        panel.add(saveBtn);
        panel.add(deleteBtn);
        panel.add(importBtn);
        panel.add(refreshBtn);

        return panel;
    }

    /**
     * إنشاء نموذج الإدخال
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder(BorderFactory.createEtchedBorder(),
                "بيانات وحدة القياس", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        // كود وحدة القياس
        gbc.gridx = 0;
        gbc.gridy = 0;
        panel.add(new JLabel("كود وحدة القياس:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        codeField = new JTextField(15);
        codeField.setFont(arabicFont);
        panel.add(codeField, gbc);

        // اسم وحدة القياس
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("اسم وحدة القياس:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        nameField = new JTextField(15);
        nameField.setFont(arabicFont);
        panel.add(nameField, gbc);

        // الاسم بالإنجليزية
        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("الاسم بالإنجليزية:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        nameEnField = new JTextField(15);
        nameEnField.setFont(arabicFont);
        panel.add(nameEnField, gbc);

        // الكود العالمي
        gbc.gridx = 0;
        gbc.gridy = 3;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("الكود العالمي:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        globalCodeField = new JTextField(15);
        globalCodeField.setFont(arabicFont);
        panel.add(globalCodeField, gbc);

        // الرمز
        gbc.gridx = 0;
        gbc.gridy = 4;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("الرمز:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        symbolField = new JTextField(15);
        symbolField.setFont(arabicFont);
        panel.add(symbolField, gbc);

        // معامل التحويل
        gbc.gridx = 0;
        gbc.gridy = 5;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("معامل التحويل:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        conversionFactorField = new JTextField(15);
        conversionFactorField.setFont(arabicFont);
        conversionFactorField.setText("1.0");
        panel.add(conversionFactorField, gbc);

        // نوع وحدة القياس
        gbc.gridx = 0;
        gbc.gridy = 6;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("النوع:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        typeComboBox = new JComboBox<>(new String[] {"عادي", "وزن", "حجم", "طول", "مساحة"});
        typeComboBox.setFont(arabicFont);
        panel.add(typeComboBox, gbc);

        // حالة النشاط
        gbc.gridx = 0;
        gbc.gridy = 7;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("نشط:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        activeCheckBox = new JCheckBox();
        activeCheckBox.setSelected(true);
        panel.add(activeCheckBox, gbc);

        // الوصف
        gbc.gridx = 0;
        gbc.gridy = 8;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("الوصف:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.BOTH;
        gbc.weighty = 1.0;
        descriptionArea = new JTextArea(4, 15);
        descriptionArea.setFont(arabicFont);
        descriptionArea.setLineWrap(true);
        descriptionArea.setWrapStyleWord(true);
        JScrollPane descScrollPane = new JScrollPane(descriptionArea);
        panel.add(descScrollPane, gbc);

        return panel;
    }

    /**
     * إنشاء لوحة الجدول
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout(5, 5));
        panel.setBorder(BorderFactory.createTitledBorder(BorderFactory.createEtchedBorder(),
                "قائمة وحدات القياس", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        // لوحة البحث
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchPanel.add(new JLabel("البحث:", JLabel.RIGHT));
        searchField = new JTextField(20);
        searchField.setFont(arabicFont);
        searchField.addActionListener(e -> searchUnits());
        searchPanel.add(searchField);

        JButton searchBtn = createStyledButton("🔍", primaryColor);
        searchBtn.addActionListener(e -> searchUnits());
        searchPanel.add(searchBtn);

        panel.add(searchPanel, BorderLayout.NORTH);

        // الجدول
        String[] columnNames = {"الكود", "الاسم", "الاسم الإنجليزي", "الرمز", "النوع", "نشط"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        unitsTable = new JTable(tableModel);
        unitsTable.setFont(arabicFont);
        unitsTable.getTableHeader().setFont(arabicBoldFont);
        unitsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        unitsTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedUnit();
            }
        });

        JScrollPane tableScrollPane = new JScrollPane(unitsTable);
        panel.add(tableScrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء زر منسق
     */
    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setFont(arabicBoldFont);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setOpaque(true);
        return button;
    }

    /**
     * تحميل بيانات وحدات القياس
     */
    private void loadUnitsData() {
        try {
            List<MeasurementUnit> units = unitDAO.findAll();

            // مسح البيانات الحالية
            tableModel.setRowCount(0);

            // إضافة البيانات الجديدة
            for (MeasurementUnit unit : units) {
                Object[] row = {unit.getMeasureCode(), unit.getMeasureName(),
                        unit.getMeasureNameEn(), unit.getSymbol(),
                        getTypeText(unit.getMeasureType()), unit.isActive() ? "نعم" : "لا"};
                tableModel.addRow(row);
            }

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تحميل البيانات:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * تحميل الوحدة المحددة في النموذج
     */
    private void loadSelectedUnit() {
        int selectedRow = unitsTable.getSelectedRow();
        if (selectedRow >= 0) {
            String code = (String) tableModel.getValueAt(selectedRow, 0);

            try {
                MeasurementUnit unit = unitDAO.findByCode(code);
                if (unit != null) {
                    populateForm(unit);
                }
            } catch (SQLException e) {
                JOptionPane.showMessageDialog(this,
                        "خطأ في تحميل بيانات الوحدة:\n" + e.getMessage(), "خطأ",
                        JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * ملء النموذج ببيانات الوحدة
     */
    private void populateForm(MeasurementUnit unit) {
        codeField.setText(unit.getMeasureCode());
        nameField.setText(unit.getMeasureName());
        nameEnField.setText(unit.getMeasureNameEn());
        globalCodeField.setText(unit.getMeasureCodeGlobal());
        symbolField.setText(unit.getSymbol());
        conversionFactorField.setText(
                unit.getConversionFactor() != null ? unit.getConversionFactor().toString() : "1.0");
        descriptionArea.setText(unit.getDescription());

        if (unit.getMeasureType() != null) {
            typeComboBox.setSelectedIndex(unit.getMeasureType() - 1);
        }

        activeCheckBox.setSelected(unit.isActive());
    }

    /**
     * مسح النموذج
     */
    private void clearForm() {
        codeField.setText("");
        nameField.setText("");
        nameEnField.setText("");
        globalCodeField.setText("");
        symbolField.setText("");
        conversionFactorField.setText("1.0");
        descriptionArea.setText("");
        typeComboBox.setSelectedIndex(0);
        activeCheckBox.setSelected(true);

        unitsTable.clearSelection();
    }

    /**
     * حفظ وحدة القياس
     */
    private void saveUnit() {
        try {
            // التحقق من صحة البيانات
            if (!validateForm()) {
                return;
            }

            // إنشاء كائن الوحدة
            MeasurementUnit unit = createUnitFromForm();

            // التحقق من وجود الوحدة
            MeasurementUnit existingUnit = unitDAO.findByCode(unit.getMeasureCode());

            if (existingUnit != null) {
                // تحديث
                unit.setAddUserId(existingUnit.getAddUserId());
                unit.setAddDate(existingUnit.getAddDate());
                unit.setUpdateUserId(1); // معرف المستخدم الحالي
                unitDAO.update(unit);

                JOptionPane.showMessageDialog(this, "تم تحديث وحدة القياس بنجاح", "نجح",
                        JOptionPane.INFORMATION_MESSAGE);
            } else {
                // إضافة جديدة
                unit.setAddUserId(1); // معرف المستخدم الحالي
                unitDAO.insert(unit);

                JOptionPane.showMessageDialog(this, "تم إضافة وحدة القياس بنجاح", "نجح",
                        JOptionPane.INFORMATION_MESSAGE);
            }

            // تحديث الجدول
            loadUnitsData();
            clearForm();

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في حفظ البيانات:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * حذف وحدة القياس
     */
    private void deleteUnit() {
        int selectedRow = unitsTable.getSelectedRow();
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار وحدة قياس للحذف", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        String code = (String) tableModel.getValueAt(selectedRow, 0);
        String name = (String) tableModel.getValueAt(selectedRow, 1);

        int result = JOptionPane.showConfirmDialog(this,
                "هل أنت متأكد من حذف وحدة القياس:\n" + name + " (" + code + ")?", "تأكيد الحذف",
                JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            try {
                unitDAO.delete(code);

                JOptionPane.showMessageDialog(this, "تم حذف وحدة القياس بنجاح", "نجح",
                        JOptionPane.INFORMATION_MESSAGE);

                loadUnitsData();
                clearForm();

            } catch (SQLException e) {
                JOptionPane.showMessageDialog(this, "خطأ في حذف وحدة القياس:\n" + e.getMessage(),
                        "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * البحث في وحدات القياس
     */
    private void searchUnits() {
        String searchTerm = searchField.getText().trim();

        try {
            List<MeasurementUnit> units;

            if (searchTerm.isEmpty()) {
                units = unitDAO.findAll();
            } else {
                units = unitDAO.search(searchTerm);
            }

            // مسح البيانات الحالية
            tableModel.setRowCount(0);

            // إضافة نتائج البحث
            for (MeasurementUnit unit : units) {
                Object[] row = {unit.getMeasureCode(), unit.getMeasureName(),
                        unit.getMeasureNameEn(), unit.getSymbol(),
                        getTypeText(unit.getMeasureType()), unit.isActive() ? "نعم" : "لا"};
                tableModel.addRow(row);
            }

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في البحث:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * استيراد البيانات من الجدول الأصلي
     */
    private void importFromOriginal() {
        int result = JOptionPane.showConfirmDialog(this,
                "هل تريد استيراد وحدات القياس من النظام الأصلي؟\n"
                        + "سيتم استيراد البيانات من جدول MEASUREMENT في قاعدة البيانات IAS20251",
                "تأكيد الاستيراد", JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            try {
                int importedCount = unitDAO.importFromOriginalTable();

                JOptionPane.showMessageDialog(this,
                        "تم استيراد " + importedCount + " وحدة قياس بنجاح", "نجح الاستيراد",
                        JOptionPane.INFORMATION_MESSAGE);

                loadUnitsData();

            } catch (SQLException e) {
                JOptionPane.showMessageDialog(this, "خطأ في الاستيراد:\n" + e.getMessage(), "خطأ",
                        JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * التحقق من صحة النموذج
     */
    private boolean validateForm() {
        if (codeField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال كود وحدة القياس", "خطأ في البيانات",
                    JOptionPane.ERROR_MESSAGE);
            codeField.requestFocus();
            return false;
        }

        if (nameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال اسم وحدة القياس", "خطأ في البيانات",
                    JOptionPane.ERROR_MESSAGE);
            nameField.requestFocus();
            return false;
        }

        try {
            Double.parseDouble(conversionFactorField.getText());
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "معامل التحويل يجب أن يكون رقماً صحيحاً",
                    "خطأ في البيانات", JOptionPane.ERROR_MESSAGE);
            conversionFactorField.requestFocus();
            return false;
        }

        return true;
    }

    /**
     * إنشاء كائن الوحدة من النموذج
     */
    private MeasurementUnit createUnitFromForm() {
        MeasurementUnit unit = new MeasurementUnit();

        unit.setMeasureCode(codeField.getText().trim());
        unit.setMeasureName(nameField.getText().trim());
        unit.setMeasureNameEn(nameEnField.getText().trim());
        unit.setMeasureCodeGlobal(globalCodeField.getText().trim());
        unit.setSymbol(symbolField.getText().trim());
        unit.setConversionFactor(Double.parseDouble(conversionFactorField.getText()));
        unit.setDescription(descriptionArea.getText().trim());
        unit.setMeasureType(typeComboBox.getSelectedIndex() + 1);
        unit.setActive(activeCheckBox.isSelected());

        return unit;
    }

    /**
     * الحصول على نص النوع
     */
    private String getTypeText(Integer type) {
        if (type == null)
            return "غير محدد";

        switch (type) {
            case 1:
                return "عادي";
            case 2:
                return "وزن";
            case 3:
                return "حجم";
            case 4:
                return "طول";
            case 5:
                return "مساحة";
            default:
                return "غير محدد";
        }
    }

    @Override
    public void dispose() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        super.dispose();
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new MeasurementUnitsWindow().setVisible(true);
        });
    }
}
