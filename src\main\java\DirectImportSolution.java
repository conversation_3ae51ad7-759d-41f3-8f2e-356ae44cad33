import java.sql.*;
import javax.swing.*;
import java.awt.*;

/**
 * الحل المباشر للاستيراد بدون Database Link
 * يستخدم اتصالين منفصلين لقراءة البيانات من IAS20251 وكتابتها في SHIP_ERP
 */
public class DirectImportSolution extends JFrame {
    
    private Connection shipErpConnection;
    private Connection ias20251Connection;
    private JTextArea logArea;
    private JProgressBar progressBar;
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new DirectImportSolution().setVisible(true);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, "خطأ في بدء التطبيق: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    public DirectImportSolution() throws Exception {
        initializeConnections();
        initializeGUI();
    }
    
    /**
     * تهيئة الاتصالات
     */
    private void initializeConnections() throws Exception {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        
        // اتصال SHIP_ERP
        shipErpConnection = DriverManager.getConnection(
            "*************************************", 
            "ship_erp", 
            "ship_erp_password"
        );
        log("✅ تم الاتصال بـ SHIP_ERP");
        
        // اتصال IAS20251
        ias20251Connection = DriverManager.getConnection(
            "*************************************", 
            "ias20251", 
            "ys123"
        );
        log("✅ تم الاتصال بـ IAS20251");
        
        // اكتشاف أسماء الأعمدة الحقيقية
        discoverRealColumns();
    }
    
    /**
     * اكتشاف أسماء الأعمدة الحقيقية
     */
    private void discoverRealColumns() {
        try {
            log("🔍 اكتشاف أسماء الأعمدة الحقيقية...");
            
            // فحص جدول IAS_ITM_MST
            Statement stmt = ias20251Connection.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT * FROM IAS_ITM_MST WHERE ROWNUM <= 1");
            
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            log("📋 أعمدة جدول IAS_ITM_MST (" + columnCount + " عمود):");
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                String columnType = metaData.getColumnTypeName(i);
                log("  - " + columnName + " (" + columnType + ")");
            }
            
            rs.close();
            stmt.close();
            
        } catch (SQLException e) {
            log("❌ خطأ في اكتشاف الأعمدة: " + e.getMessage());
        }
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    private void initializeGUI() {
        setTitle("الاستيراد المباشر من IAS20251 إلى SHIP_ERP");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(900, 700);
        setLocationRelativeTo(null);
        
        setLayout(new BorderLayout());
        
        // لوحة الأزرار
        JPanel buttonPanel = new JPanel(new GridLayout(2, 3, 10, 10));
        buttonPanel.setBorder(BorderFactory.createTitledBorder("عمليات الاستيراد المباشر"));
        
        JButton discoverBtn = new JButton("اكتشاف البنية");
        discoverBtn.addActionListener(e -> discoverStructure());
        buttonPanel.add(discoverBtn);
        
        JButton importGroupsBtn = new JButton("استيراد المجموعات");
        importGroupsBtn.addActionListener(e -> importGroups());
        buttonPanel.add(importGroupsBtn);
        
        JButton importSubGroupsBtn = new JButton("استيراد المجموعات الفرعية");
        importSubGroupsBtn.addActionListener(e -> importSubGroups());
        buttonPanel.add(importSubGroupsBtn);
        
        JButton importItemsBtn = new JButton("استيراد الأصناف");
        importItemsBtn.addActionListener(e -> importItems());
        buttonPanel.add(importItemsBtn);
        
        JButton statsBtn = new JButton("إحصائيات");
        statsBtn.addActionListener(e -> showStats());
        buttonPanel.add(statsBtn);
        
        JButton testBtn = new JButton("اختبار الاتصالات");
        testBtn.addActionListener(e -> testConnections());
        buttonPanel.add(testBtn);
        
        add(buttonPanel, BorderLayout.NORTH);
        
        // شريط التقدم
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        add(progressBar, BorderLayout.CENTER);
        
        // منطقة السجل
        logArea = new JTextArea(20, 80);
        logArea.setEditable(false);
        logArea.setFont(new Font("Arial", Font.PLAIN, 12));
        JScrollPane scrollPane = new JScrollPane(logArea);
        scrollPane.setBorder(BorderFactory.createTitledBorder("سجل العمليات"));
        add(scrollPane, BorderLayout.SOUTH);
        
        log("🎉 تم تهيئة نظام الاستيراد المباشر!");
    }
    
    /**
     * اكتشاف البنية
     */
    private void discoverStructure() {
        new Thread(() -> {
            try {
                log("🔍 اكتشاف بنية الجداول...");
                progressBar.setIndeterminate(true);
                
                // فحص جداول IAS20251
                String[] tables = {"IAS_ITM_MST", "IAS_ITM_DTL", "GROUP_DETAILS", "IAS_MAINSUB_GRP_DTL"};
                
                for (String tableName : tables) {
                    try {
                        log("\n📋 جدول: " + tableName);
                        
                        Statement stmt = ias20251Connection.createStatement();
                        
                        // عدد الصفوف
                        ResultSet countRs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName);
                        countRs.next();
                        int rowCount = countRs.getInt(1);
                        log("  📊 عدد الصفوف: " + rowCount);
                        countRs.close();
                        
                        // أسماء الأعمدة
                        ResultSet rs = stmt.executeQuery("SELECT * FROM " + tableName + " WHERE ROWNUM <= 1");
                        ResultSetMetaData metaData = rs.getMetaData();
                        int columnCount = metaData.getColumnCount();
                        
                        log("  📋 الأعمدة (" + columnCount + "):");
                        for (int i = 1; i <= columnCount; i++) {
                            String columnName = metaData.getColumnName(i);
                            String columnType = metaData.getColumnTypeName(i);
                            log("    - " + columnName + " (" + columnType + ")");
                        }
                        
                        rs.close();
                        stmt.close();
                        
                    } catch (SQLException e) {
                        log("  ❌ خطأ في فحص " + tableName + ": " + e.getMessage());
                    }
                }
                
                progressBar.setIndeterminate(false);
                JOptionPane.showMessageDialog(this, "تم اكتشاف البنية بنجاح!");
                
            } catch (Exception e) {
                log("❌ خطأ في اكتشاف البنية: " + e.getMessage());
                progressBar.setIndeterminate(false);
            }
        }).start();
    }
    
    /**
     * استيراد المجموعات الرئيسية
     */
    private void importGroups() {
        new Thread(() -> {
            try {
                log("🔄 بدء استيراد المجموعات الرئيسية...");
                progressBar.setIndeterminate(true);
                
                int imported = 0;
                int updated = 0;
                int errors = 0;
                
                // قراءة من IAS20251
                Statement readStmt = ias20251Connection.createStatement();
                ResultSet rs = readStmt.executeQuery("SELECT * FROM GROUP_DETAILS");
                
                // إعداد الكتابة في SHIP_ERP
                PreparedStatement insertStmt = shipErpConnection.prepareStatement(
                    "INSERT INTO ERP_GROUP_DETAILS (G_CODE, G_A_NAME, G_E_NAME, IS_ACTIVE) VALUES (?, ?, ?, 'Y')"
                );
                
                PreparedStatement updateStmt = shipErpConnection.prepareStatement(
                    "UPDATE ERP_GROUP_DETAILS SET G_A_NAME = ?, G_E_NAME = ? WHERE G_CODE = ?"
                );
                
                while (rs.next()) {
                    try {
                        String gCode = rs.getString("G_CODE");
                        String gAName = rs.getString("G_A_NAME");
                        String gEName = rs.getString("G_E_NAME");
                        
                        // محاولة الإدراج
                        insertStmt.setString(1, gCode);
                        insertStmt.setString(2, gAName);
                        insertStmt.setString(3, gEName);
                        
                        try {
                            insertStmt.executeUpdate();
                            imported++;
                            log("✅ تم إدراج: " + gCode + " - " + gAName);
                        } catch (SQLException e) {
                            if (e.getErrorCode() == 1) { // Unique constraint violation
                                // تحديث البيانات الموجودة
                                updateStmt.setString(1, gAName);
                                updateStmt.setString(2, gEName);
                                updateStmt.setString(3, gCode);
                                updateStmt.executeUpdate();
                                updated++;
                                log("🔄 تم تحديث: " + gCode + " - " + gAName);
                            } else {
                                errors++;
                                log("❌ خطأ في " + gCode + ": " + e.getMessage());
                            }
                        }
                        
                    } catch (SQLException e) {
                        errors++;
                        log("❌ خطأ في قراءة السجل: " + e.getMessage());
                    }
                }
                
                shipErpConnection.commit();
                
                String result = "تم استيراد " + imported + " مجموعة جديدة، تحديث " + updated + " مجموعة، أخطاء: " + errors;
                log("🎉 " + result);
                
                rs.close();
                readStmt.close();
                insertStmt.close();
                updateStmt.close();
                
                progressBar.setIndeterminate(false);
                JOptionPane.showMessageDialog(this, result);
                
            } catch (Exception e) {
                log("❌ خطأ في استيراد المجموعات: " + e.getMessage());
                progressBar.setIndeterminate(false);
                try {
                    shipErpConnection.rollback();
                } catch (SQLException ex) {
                    log("❌ خطأ في rollback: " + ex.getMessage());
                }
            }
        }).start();
    }
    
    /**
     * استيراد المجموعات الفرعية
     */
    private void importSubGroups() {
        new Thread(() -> {
            try {
                log("🔄 بدء استيراد المجموعات الفرعية...");
                progressBar.setIndeterminate(true);
                
                int imported = 0;
                int updated = 0;
                int errors = 0;
                
                // قراءة من IAS20251
                Statement readStmt = ias20251Connection.createStatement();
                ResultSet rs = readStmt.executeQuery("SELECT * FROM IAS_MAINSUB_GRP_DTL");
                
                // إعداد الكتابة في SHIP_ERP
                PreparedStatement insertStmt = shipErpConnection.prepareStatement(
                    "INSERT INTO ERP_MAINSUB_GRP_DTL (G_CODE, MNG_CODE, MNG_A_NAME, MNG_E_NAME, IS_ACTIVE) VALUES (?, ?, ?, ?, 'Y')"
                );
                
                PreparedStatement updateStmt = shipErpConnection.prepareStatement(
                    "UPDATE ERP_MAINSUB_GRP_DTL SET MNG_A_NAME = ?, MNG_E_NAME = ? WHERE G_CODE = ? AND MNG_CODE = ?"
                );
                
                while (rs.next()) {
                    try {
                        String gCode = rs.getString("G_CODE");
                        String mngCode = rs.getString("MNG_CODE");
                        String mngAName = rs.getString("MNG_A_NAME");
                        String mngEName = rs.getString("MNG_E_NAME");
                        
                        // محاولة الإدراج
                        insertStmt.setString(1, gCode);
                        insertStmt.setString(2, mngCode);
                        insertStmt.setString(3, mngAName);
                        insertStmt.setString(4, mngEName);
                        
                        try {
                            insertStmt.executeUpdate();
                            imported++;
                            log("✅ تم إدراج: " + gCode + "/" + mngCode + " - " + mngAName);
                        } catch (SQLException e) {
                            if (e.getErrorCode() == 1) { // Unique constraint violation
                                // تحديث البيانات الموجودة
                                updateStmt.setString(1, mngAName);
                                updateStmt.setString(2, mngEName);
                                updateStmt.setString(3, gCode);
                                updateStmt.setString(4, mngCode);
                                updateStmt.executeUpdate();
                                updated++;
                                log("🔄 تم تحديث: " + gCode + "/" + mngCode + " - " + mngAName);
                            } else {
                                errors++;
                                log("❌ خطأ في " + gCode + "/" + mngCode + ": " + e.getMessage());
                            }
                        }
                        
                    } catch (SQLException e) {
                        errors++;
                        log("❌ خطأ في قراءة السجل: " + e.getMessage());
                    }
                }
                
                shipErpConnection.commit();
                
                String result = "تم استيراد " + imported + " مجموعة فرعية جديدة، تحديث " + updated + " مجموعة، أخطاء: " + errors;
                log("🎉 " + result);
                
                rs.close();
                readStmt.close();
                insertStmt.close();
                updateStmt.close();
                
                progressBar.setIndeterminate(false);
                JOptionPane.showMessageDialog(this, result);
                
            } catch (Exception e) {
                log("❌ خطأ في استيراد المجموعات الفرعية: " + e.getMessage());
                progressBar.setIndeterminate(false);
                try {
                    shipErpConnection.rollback();
                } catch (SQLException ex) {
                    log("❌ خطأ في rollback: " + ex.getMessage());
                }
            }
        }).start();
    }
    
    /**
     * استيراد الأصناف
     */
    private void importItems() {
        JOptionPane.showMessageDialog(this, "استيراد الأصناف سيتم تطويره بعد اكتشاف أسماء الأعمدة الصحيحة");
    }
    
    /**
     * عرض الإحصائيات
     */
    private void showStats() {
        try {
            log("📊 عرض الإحصائيات...");
            
            StringBuilder stats = new StringBuilder("📊 إحصائيات قواعد البيانات:\n\n");
            
            // إحصائيات SHIP_ERP
            Statement shipStmt = shipErpConnection.createStatement();
            
            ResultSet rs1 = shipStmt.executeQuery("SELECT COUNT(*) FROM ERP_GROUP_DETAILS");
            rs1.next();
            int shipGroups = rs1.getInt(1);
            rs1.close();
            
            ResultSet rs2 = shipStmt.executeQuery("SELECT COUNT(*) FROM ERP_MAINSUB_GRP_DTL");
            rs2.next();
            int shipSubGroups = rs2.getInt(1);
            rs2.close();
            
            stats.append("SHIP_ERP:\n");
            stats.append("  - المجموعات الرئيسية: ").append(shipGroups).append("\n");
            stats.append("  - المجموعات الفرعية: ").append(shipSubGroups).append("\n\n");
            
            // إحصائيات IAS20251
            Statement iasStmt = ias20251Connection.createStatement();
            
            ResultSet rs3 = iasStmt.executeQuery("SELECT COUNT(*) FROM GROUP_DETAILS");
            rs3.next();
            int iasGroups = rs3.getInt(1);
            rs3.close();
            
            ResultSet rs4 = iasStmt.executeQuery("SELECT COUNT(*) FROM IAS_MAINSUB_GRP_DTL");
            rs4.next();
            int iasSubGroups = rs4.getInt(1);
            rs4.close();
            
            ResultSet rs5 = iasStmt.executeQuery("SELECT COUNT(*) FROM IAS_ITM_MST");
            rs5.next();
            int iasItems = rs5.getInt(1);
            rs5.close();
            
            stats.append("IAS20251:\n");
            stats.append("  - المجموعات الرئيسية: ").append(iasGroups).append("\n");
            stats.append("  - المجموعات الفرعية: ").append(iasSubGroups).append("\n");
            stats.append("  - الأصناف: ").append(iasItems).append("\n");
            
            log(stats.toString());
            JOptionPane.showMessageDialog(this, stats.toString());
            
        } catch (Exception e) {
            log("❌ خطأ في عرض الإحصائيات: " + e.getMessage());
        }
    }
    
    /**
     * اختبار الاتصالات
     */
    private void testConnections() {
        try {
            log("🔍 اختبار الاتصالات...");
            
            // اختبار SHIP_ERP
            Statement stmt1 = shipErpConnection.createStatement();
            ResultSet rs1 = stmt1.executeQuery("SELECT 'SHIP_ERP' as db_name, COUNT(*) as groups FROM ERP_GROUP_DETAILS");
            rs1.next();
            String shipResult = rs1.getString("db_name") + ": " + rs1.getInt("groups") + " مجموعة";
            rs1.close();
            stmt1.close();
            
            // اختبار IAS20251
            Statement stmt2 = ias20251Connection.createStatement();
            ResultSet rs2 = stmt2.executeQuery("SELECT 'IAS20251' as db_name, COUNT(*) as groups FROM GROUP_DETAILS");
            rs2.next();
            String iasResult = rs2.getString("db_name") + ": " + rs2.getInt("groups") + " مجموعة";
            rs2.close();
            stmt2.close();
            
            String result = "الاتصالات تعمل بنجاح!\n" + shipResult + "\n" + iasResult;
            log("✅ " + result);
            JOptionPane.showMessageDialog(this, result);
            
        } catch (Exception e) {
            log("❌ خطأ في اختبار الاتصالات: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * تسجيل رسالة
     */
    private void log(String message) {
        SwingUtilities.invokeLater(() -> {
            logArea.append(java.time.LocalTime.now() + " - " + message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
        System.out.println(message);
    }
}
