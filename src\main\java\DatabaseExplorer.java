import java.sql.*;
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import javax.swing.table.DefaultTableModel;

/**
 * أداة فحص قواعد البيانات الحقيقية
 * لاستكشاف الجداول والأعمدة الفعلية في IAS20251 و SHIP_ERP
 */
public class DatabaseExplorer extends JFrame {
    
    private Connection shipErpConnection;
    private Connection ias20251Connection;
    private JTextArea logArea;
    private JTable tablesTable;
    private JTable columnsTable;
    private DefaultTableModel tablesModel;
    private DefaultTableModel columnsModel;
    private JComboBox<String> databaseCombo;
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new DatabaseExplorer().setVisible(true);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, "خطأ في بدء المستكشف: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    public DatabaseExplorer() throws Exception {
        initializeConnections();
        initializeGUI();
        loadTables();
    }
    
    /**
     * تهيئة الاتصالات
     */
    private void initializeConnections() throws Exception {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        
        // اتصال SHIP_ERP
        try {
            shipErpConnection = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            log("✅ تم الاتصال بـ SHIP_ERP");
        } catch (SQLException e) {
            log("❌ فشل الاتصال بـ SHIP_ERP: " + e.getMessage());
            throw e;
        }
        
        // اتصال IAS20251
        try {
            ias20251Connection = DriverManager.getConnection(
                "*************************************", 
                "ias20251", 
                "ys123"
            );
            log("✅ تم الاتصال بـ IAS20251");
        } catch (SQLException e) {
            log("❌ فشل الاتصال بـ IAS20251: " + e.getMessage());
            ias20251Connection = null;
        }
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    private void initializeGUI() {
        setTitle("مستكشف قواعد البيانات - Database Explorer");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        
        setLayout(new BorderLayout());
        
        // لوحة التحكم العلوية
        JPanel controlPanel = new JPanel(new FlowLayout());
        controlPanel.setBorder(BorderFactory.createTitledBorder("التحكم"));
        
        controlPanel.add(new JLabel("قاعدة البيانات:"));
        databaseCombo = new JComboBox<>(new String[]{"SHIP_ERP", "IAS20251"});
        databaseCombo.addActionListener(e -> loadTables());
        controlPanel.add(databaseCombo);
        
        JButton refreshButton = new JButton("تحديث");
        refreshButton.addActionListener(e -> loadTables());
        controlPanel.add(refreshButton);
        
        JButton analyzeButton = new JButton("تحليل جداول المجموعات");
        analyzeButton.addActionListener(e -> analyzeGroupTables());
        controlPanel.add(analyzeButton);
        
        JButton exportButton = new JButton("تصدير البنية");
        exportButton.addActionListener(e -> exportStructure());
        controlPanel.add(exportButton);
        
        add(controlPanel, BorderLayout.NORTH);
        
        // اللوحة الرئيسية
        JSplitPane mainSplit = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        
        // لوحة الجداول
        JPanel tablesPanel = new JPanel(new BorderLayout());
        tablesPanel.setBorder(BorderFactory.createTitledBorder("الجداول"));
        
        tablesModel = new DefaultTableModel(new String[]{"اسم الجدول", "النوع", "عدد الصفوف"}, 0);
        tablesTable = new JTable(tablesModel);
        tablesTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadColumns();
            }
        });
        
        JScrollPane tablesScroll = new JScrollPane(tablesTable);
        tablesPanel.add(tablesScroll, BorderLayout.CENTER);
        
        mainSplit.setLeftComponent(tablesPanel);
        
        // لوحة الأعمدة
        JPanel columnsPanel = new JPanel(new BorderLayout());
        columnsPanel.setBorder(BorderFactory.createTitledBorder("الأعمدة"));
        
        columnsModel = new DefaultTableModel(new String[]{"اسم العمود", "نوع البيانات", "الحجم", "NULL", "المفتاح"}, 0);
        columnsTable = new JTable(columnsModel);
        
        JScrollPane columnsScroll = new JScrollPane(columnsTable);
        columnsPanel.add(columnsScroll, BorderLayout.CENTER);
        
        mainSplit.setRightComponent(columnsPanel);
        mainSplit.setDividerLocation(400);
        
        // لوحة السجل
        JSplitPane bottomSplit = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        bottomSplit.setTopComponent(mainSplit);
        
        logArea = new JTextArea(10, 80);
        logArea.setEditable(false);
        logArea.setFont(new Font("Arial", Font.PLAIN, 12));
        JScrollPane logScroll = new JScrollPane(logArea);
        logScroll.setBorder(BorderFactory.createTitledBorder("سجل العمليات"));
        
        bottomSplit.setBottomComponent(logScroll);
        bottomSplit.setDividerLocation(500);
        
        add(bottomSplit, BorderLayout.CENTER);
        
        log("🎉 تم تهيئة مستكشف قواعد البيانات");
    }
    
    /**
     * تحميل الجداول
     */
    private void loadTables() {
        try {
            String selectedDB = (String) databaseCombo.getSelectedItem();
            Connection conn = selectedDB.equals("SHIP_ERP") ? shipErpConnection : ias20251Connection;
            
            if (conn == null) {
                log("❌ لا يوجد اتصال بـ " + selectedDB);
                return;
            }
            
            log("🔍 تحميل جداول " + selectedDB + "...");
            
            tablesModel.setRowCount(0);
            
            DatabaseMetaData metaData = conn.getMetaData();
            String schema = selectedDB.equals("SHIP_ERP") ? "SHIP_ERP" : "IAS20251";
            
            ResultSet tables = metaData.getTables(null, schema, "%", new String[]{"TABLE"});
            
            int tableCount = 0;
            while (tables.next()) {
                String tableName = tables.getString("TABLE_NAME");
                String tableType = tables.getString("TABLE_TYPE");
                
                // الحصول على عدد الصفوف
                int rowCount = 0;
                try {
                    Statement stmt = conn.createStatement();
                    ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName);
                    if (rs.next()) {
                        rowCount = rs.getInt(1);
                    }
                    rs.close();
                    stmt.close();
                } catch (SQLException e) {
                    // تجاهل الأخطاء
                }
                
                tablesModel.addRow(new Object[]{tableName, tableType, rowCount});
                tableCount++;
            }
            tables.close();
            
            log("✅ تم تحميل " + tableCount + " جدول من " + selectedDB);
            
        } catch (SQLException e) {
            log("❌ خطأ في تحميل الجداول: " + e.getMessage());
        }
    }
    
    /**
     * تحميل الأعمدة للجدول المحدد
     */
    private void loadColumns() {
        try {
            int selectedRow = tablesTable.getSelectedRow();
            if (selectedRow == -1) return;
            
            String tableName = (String) tablesModel.getValueAt(selectedRow, 0);
            String selectedDB = (String) databaseCombo.getSelectedItem();
            Connection conn = selectedDB.equals("SHIP_ERP") ? shipErpConnection : ias20251Connection;
            
            if (conn == null) return;
            
            log("🔍 تحميل أعمدة الجدول: " + tableName);
            
            columnsModel.setRowCount(0);
            
            DatabaseMetaData metaData = conn.getMetaData();
            String schema = selectedDB.equals("SHIP_ERP") ? "SHIP_ERP" : "IAS20251";
            
            // الحصول على الأعمدة
            ResultSet columns = metaData.getColumns(null, schema, tableName, null);
            
            while (columns.next()) {
                String columnName = columns.getString("COLUMN_NAME");
                String dataType = columns.getString("TYPE_NAME");
                int columnSize = columns.getInt("COLUMN_SIZE");
                String nullable = columns.getString("IS_NULLABLE");
                
                // التحقق من المفاتيح الأساسية
                String keyInfo = "";
                try {
                    ResultSet primaryKeys = metaData.getPrimaryKeys(null, schema, tableName);
                    while (primaryKeys.next()) {
                        if (primaryKeys.getString("COLUMN_NAME").equals(columnName)) {
                            keyInfo = "PK";
                            break;
                        }
                    }
                    primaryKeys.close();
                } catch (SQLException e) {
                    // تجاهل الأخطاء
                }
                
                String sizeInfo = columnSize > 0 ? "(" + columnSize + ")" : "";
                columnsModel.addRow(new Object[]{
                    columnName, 
                    dataType + sizeInfo, 
                    columnSize, 
                    nullable, 
                    keyInfo
                });
            }
            columns.close();
            
            log("✅ تم تحميل أعمدة الجدول: " + tableName);
            
        } catch (SQLException e) {
            log("❌ خطأ في تحميل الأعمدة: " + e.getMessage());
        }
    }
    
    /**
     * تحليل جداول المجموعات
     */
    private void analyzeGroupTables() {
        log("🔍 تحليل جداول المجموعات...");
        
        // تحليل SHIP_ERP
        analyzeDatabase("SHIP_ERP", shipErpConnection);
        
        // تحليل IAS20251
        if (ias20251Connection != null) {
            analyzeDatabase("IAS20251", ias20251Connection);
        }
    }
    
    /**
     * تحليل قاعدة بيانات محددة
     */
    private void analyzeDatabase(String dbName, Connection conn) {
        try {
            log("\n📊 تحليل " + dbName + ":");
            
            DatabaseMetaData metaData = conn.getMetaData();
            String schema = dbName;
            
            ResultSet tables = metaData.getTables(null, schema, "%", new String[]{"TABLE"});
            
            while (tables.next()) {
                String tableName = tables.getString("TABLE_NAME");
                
                // البحث عن جداول المجموعات
                if (tableName.toUpperCase().contains("GROUP") || 
                    tableName.toUpperCase().contains("GRP") ||
                    tableName.toUpperCase().contains("ITEM")) {
                    
                    log("  📋 جدول محتمل: " + tableName);
                    
                    // عرض الأعمدة
                    ResultSet columns = metaData.getColumns(null, schema, tableName, null);
                    while (columns.next()) {
                        String columnName = columns.getString("COLUMN_NAME");
                        String dataType = columns.getString("TYPE_NAME");
                        int columnSize = columns.getInt("COLUMN_SIZE");
                        
                        log("      - " + columnName + " (" + dataType + 
                            (columnSize > 0 ? "(" + columnSize + ")" : "") + ")");
                    }
                    columns.close();
                    
                    // عرض عينة من البيانات
                    try {
                        Statement stmt = conn.createStatement();
                        ResultSet rs = stmt.executeQuery(
                            "SELECT * FROM " + tableName + " WHERE ROWNUM <= 3"
                        );
                        
                        ResultSetMetaData rsmd = rs.getMetaData();
                        int columnCount = rsmd.getColumnCount();
                        
                        log("      📄 عينة من البيانات:");
                        while (rs.next()) {
                            StringBuilder row = new StringBuilder("        ");
                            for (int i = 1; i <= columnCount; i++) {
                                String value = rs.getString(i);
                                if (value != null && value.length() > 20) {
                                    value = value.substring(0, 20) + "...";
                                }
                                row.append(rsmd.getColumnName(i)).append("=").append(value).append(" | ");
                            }
                            log(row.toString());
                        }
                        rs.close();
                        stmt.close();
                    } catch (SQLException e) {
                        log("      ⚠️ لا يمكن قراءة البيانات: " + e.getMessage());
                    }
                    
                    log("");
                }
            }
            tables.close();
            
        } catch (SQLException e) {
            log("❌ خطأ في تحليل " + dbName + ": " + e.getMessage());
        }
    }
    
    /**
     * تصدير البنية
     */
    private void exportStructure() {
        try {
            log("📤 تصدير بنية قواعد البيانات...");
            
            StringBuilder structure = new StringBuilder();
            structure.append("=== بنية قواعد البيانات ===\n\n");
            
            // تصدير SHIP_ERP
            structure.append("📊 SHIP_ERP:\n");
            exportDatabaseStructure(structure, "SHIP_ERP", shipErpConnection);
            
            // تصدير IAS20251
            if (ias20251Connection != null) {
                structure.append("\n📊 IAS20251:\n");
                exportDatabaseStructure(structure, "IAS20251", ias20251Connection);
            }
            
            // عرض النتيجة في نافذة جديدة
            JFrame exportFrame = new JFrame("بنية قواعد البيانات");
            JTextArea exportArea = new JTextArea(structure.toString());
            exportArea.setEditable(false);
            exportArea.setFont(new Font("Monospaced", Font.PLAIN, 12));
            
            JScrollPane exportScroll = new JScrollPane(exportArea);
            exportFrame.add(exportScroll);
            exportFrame.setSize(800, 600);
            exportFrame.setLocationRelativeTo(this);
            exportFrame.setVisible(true);
            
            log("✅ تم تصدير البنية بنجاح");
            
        } catch (Exception e) {
            log("❌ خطأ في تصدير البنية: " + e.getMessage());
        }
    }
    
    /**
     * تصدير بنية قاعدة بيانات محددة
     */
    private void exportDatabaseStructure(StringBuilder sb, String dbName, Connection conn) {
        try {
            DatabaseMetaData metaData = conn.getMetaData();
            String schema = dbName;
            
            ResultSet tables = metaData.getTables(null, schema, "%", new String[]{"TABLE"});
            
            while (tables.next()) {
                String tableName = tables.getString("TABLE_NAME");
                sb.append("\n  📋 ").append(tableName).append(":\n");
                
                ResultSet columns = metaData.getColumns(null, schema, tableName, null);
                while (columns.next()) {
                    String columnName = columns.getString("COLUMN_NAME");
                    String dataType = columns.getString("TYPE_NAME");
                    int columnSize = columns.getInt("COLUMN_SIZE");
                    String nullable = columns.getString("IS_NULLABLE");
                    
                    sb.append("      - ").append(columnName)
                      .append(" (").append(dataType);
                    if (columnSize > 0) {
                        sb.append("(").append(columnSize).append(")");
                    }
                    sb.append(", ").append(nullable.equals("YES") ? "NULL" : "NOT NULL")
                      .append(")\n");
                }
                columns.close();
            }
            tables.close();
            
        } catch (SQLException e) {
            sb.append("❌ خطأ في تصدير ").append(dbName).append(": ").append(e.getMessage()).append("\n");
        }
    }
    
    /**
     * تسجيل رسالة
     */
    private void log(String message) {
        SwingUtilities.invokeLater(() -> {
            logArea.append(message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
        System.out.println(message);
    }
}
