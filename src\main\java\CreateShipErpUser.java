import java.sql.*;

/**
 * إنشاء مستخدم SHIP_ERP في Oracle
 */
public class CreateShipErpUser {
    
    public static void main(String[] args) {
        try {
            // الاتصال كـ SYSTEM أو SYS لإنشاء المستخدم
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            System.out.println("🔗 محاولة الاتصال كـ SYSTEM...");
            Connection sysConn = DriverManager.getConnection(
                "*************************************", 
                "system", 
                "oracle"
            );
            System.out.println("✅ تم الاتصال كـ SYSTEM");
            
            Statement stmt = sysConn.createStatement();
            
            // حذف المستخدم إن وجد
            try {
                System.out.println("🗑️ حذف المستخدم القديم...");
                stmt.execute("DROP USER ship_erp CASCADE");
                System.out.println("✅ تم حذف المستخدم القديم");
            } catch (SQLException e) {
                System.out.println("ℹ️ المستخدم غير موجود مسبقاً");
            }
            
            // إنشاء المستخدم الجديد
            System.out.println("👤 إنشاء مستخدم SHIP_ERP...");
            String createUserSQL = """
                CREATE USER ship_erp 
                IDENTIFIED BY ship_erp_password
                DEFAULT TABLESPACE USERS
                TEMPORARY TABLESPACE TEMP
                QUOTA UNLIMITED ON USERS
            """;
            
            stmt.execute(createUserSQL);
            System.out.println("✅ تم إنشاء المستخدم SHIP_ERP");
            
            // منح الصلاحيات
            System.out.println("🔐 منح الصلاحيات...");
            String[] privileges = {
                "GRANT CONNECT TO ship_erp",
                "GRANT RESOURCE TO ship_erp",
                "GRANT CREATE SESSION TO ship_erp",
                "GRANT CREATE TABLE TO ship_erp",
                "GRANT CREATE VIEW TO ship_erp",
                "GRANT CREATE SEQUENCE TO ship_erp",
                "GRANT CREATE PROCEDURE TO ship_erp",
                "GRANT CREATE TRIGGER TO ship_erp",
                "GRANT CREATE SYNONYM TO ship_erp",
                "GRANT CREATE DATABASE LINK TO ship_erp",
                "GRANT UNLIMITED TABLESPACE TO ship_erp"
            };
            
            for (String privilege : privileges) {
                stmt.execute(privilege);
                System.out.println("✅ " + privilege);
            }
            
            sysConn.commit();
            sysConn.close();
            
            // اختبار الاتصال بالمستخدم الجديد
            System.out.println("🧪 اختبار الاتصال بـ SHIP_ERP...");
            Connection testConn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            
            Statement testStmt = testConn.createStatement();
            ResultSet rs = testStmt.executeQuery("SELECT USER FROM DUAL");
            if (rs.next()) {
                System.out.println("✅ المستخدم الحالي: " + rs.getString(1));
            }
            
            testConn.close();
            
            System.out.println("🎉 تم إنشاء مستخدم SHIP_ERP بنجاح!");
            System.out.println("📋 بيانات الاتصال:");
            System.out.println("   المستخدم: ship_erp");
            System.out.println("   كلمة المرور: ship_erp_password");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
