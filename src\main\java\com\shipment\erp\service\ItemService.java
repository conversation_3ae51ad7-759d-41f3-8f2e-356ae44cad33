package com.shipment.erp.service;

import com.shipment.erp.model.Item;
import com.shipment.erp.model.ItemCategory;
import com.shipment.erp.model.UnitOfMeasure;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * خدمة الأصناف
 * Item Service Interface
 */
public interface ItemService extends BaseService<Item, Long> {
    
    /**
     * البحث عن صنف بالكود
     * Find item by code
     */
    Optional<Item> findByCode(String code);
    
    /**
     * البحث عن صنف بالباركود
     * Find item by barcode
     */
    Optional<Item> findByBarcode(String barcode);
    
    /**
     * الحصول على الأصناف حسب المجموعة
     * Get items by category
     */
    List<Item> findByCategory(ItemCategory category);
    
    /**
     * الحصول على جميع الأصناف النشطة
     * Get all active items
     */
    List<Item> findActiveItems();
    
    /**
     * الحصول على الأصناف القابلة للبيع
     * Get sellable items
     */
    List<Item> findSellableItems();
    
    /**
     * الحصول على الأصناف منخفضة المخزون
     * Get low stock items
     */
    List<Item> findLowStockItems();
    
    /**
     * الحصول على الأصناف المنتهية من المخزون
     * Get out of stock items
     */
    List<Item> findOutOfStockItems();
    
    /**
     * البحث في الأصناف
     * Search items
     */
    List<Item> searchItems(String searchText);
    
    /**
     * إنشاء صنف جديد
     * Create new item
     */
    Item createItem(Item item) throws Exception;
    
    /**
     * تحديث صنف
     * Update item
     */
    Item updateItem(Item item) throws Exception;
    
    /**
     * حذف صنف
     * Delete item
     */
    void deleteItem(Long itemId) throws Exception;
    
    /**
     * تفعيل/إلغاء تفعيل صنف
     * Activate/Deactivate item
     */
    void toggleItemStatus(Long itemId) throws Exception;
    
    /**
     * التحقق من صحة بيانات الصنف
     * Validate item data
     */
    void validateItem(Item item) throws Exception;
    
    /**
     * التحقق من إمكانية حذف الصنف
     * Check if item can be deleted
     */
    boolean canDeleteItem(Long itemId);
    
    /**
     * تحديث مخزون الصنف
     * Update item stock
     */
    void updateItemStock(Long itemId, BigDecimal newStock) throws Exception;
    
    /**
     * تحديث سعر الصنف
     * Update item price
     */
    void updateItemPrice(Long itemId, BigDecimal newPrice) throws Exception;
    
    /**
     * استيراد الأصناف من Excel
     * Import items from Excel
     */
    ImportResult importItemsFromExcel(String filePath) throws Exception;
    
    /**
     * تصدير الأصناف إلى Excel
     * Export items to Excel
     */
    void exportItemsToExcel(String filePath, List<Item> items) throws Exception;
    
    /**
     * الحصول على إحصائيات الأصناف
     * Get item statistics
     */
    ItemStatistics getItemStatistics();
    
    /**
     * فئة نتيجة الاستيراد
     * Import result class
     */
    class ImportResult {
        private int totalRows;
        private int successfulImports;
        private int failedImports;
        private List<String> errors;
        
        public ImportResult() {}
        
        public ImportResult(int totalRows, int successfulImports, int failedImports, List<String> errors) {
            this.totalRows = totalRows;
            this.successfulImports = successfulImports;
            this.failedImports = failedImports;
            this.errors = errors;
        }
        
        // Getters and setters
        public int getTotalRows() { return totalRows; }
        public void setTotalRows(int totalRows) { this.totalRows = totalRows; }
        
        public int getSuccessfulImports() { return successfulImports; }
        public void setSuccessfulImports(int successfulImports) { this.successfulImports = successfulImports; }
        
        public int getFailedImports() { return failedImports; }
        public void setFailedImports(int failedImports) { this.failedImports = failedImports; }
        
        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }
    }
    
    /**
     * فئة إحصائيات الأصناف
     * Item statistics class
     */
    class ItemStatistics {
        private long totalItems;
        private long activeItems;
        private long sellableItems;
        private long purchasableItems;
        private long lowStockItems;
        private long outOfStockItems;
        private BigDecimal totalInventoryValue;
        
        public ItemStatistics() {}
        
        // Getters and setters
        public long getTotalItems() { return totalItems; }
        public void setTotalItems(long totalItems) { this.totalItems = totalItems; }
        
        public long getActiveItems() { return activeItems; }
        public void setActiveItems(long activeItems) { this.activeItems = activeItems; }
        
        public long getSellableItems() { return sellableItems; }
        public void setSellableItems(long sellableItems) { this.sellableItems = sellableItems; }
        
        public long getPurchasableItems() { return purchasableItems; }
        public void setPurchasableItems(long purchasableItems) { this.purchasableItems = purchasableItems; }
        
        public long getLowStockItems() { return lowStockItems; }
        public void setLowStockItems(long lowStockItems) { this.lowStockItems = lowStockItems; }
        
        public long getOutOfStockItems() { return outOfStockItems; }
        public void setOutOfStockItems(long outOfStockItems) { this.outOfStockItems = outOfStockItems; }
        
        public BigDecimal getTotalInventoryValue() { return totalInventoryValue; }
        public void setTotalInventoryValue(BigDecimal totalInventoryValue) { this.totalInventoryValue = totalInventoryValue; }
    }
}
