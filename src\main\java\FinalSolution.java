import java.sql.*;
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * الحل النهائي - Package + واجهة Java مباشرة
 */
public class FinalSolution extends JFrame {
    
    private Connection connection;
    private JTextArea logArea;
    private JTextField groupCodeField, groupNameField;
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new FinalSolution().setVisible(true);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, "خطأ في بدء التطبيق: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    public FinalSolution() throws Exception {
        initializeDatabase();
        initializeGUI();
    }
    
    /**
     * تهيئة قاعدة البيانات
     */
    private void initializeDatabase() throws Exception {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        
        connection = DriverManager.getConnection(
            "*************************************", 
            "ship_erp", 
            "ship_erp_password"
        );
        
        log("✅ تم الاتصال بقاعدة البيانات");
        
        // إنشاء Package
        createPackage();
        
        // إنشاء Database Link
        createDatabaseLink();
    }
    
    /**
     * إنشاء Package
     */
    private void createPackage() throws SQLException {
        log("📦 إنشاء Package ERP_ITEM_GROUPS...");
        
        Statement stmt = connection.createStatement();
        
        // حذف Package القديم
        try {
            stmt.execute("DROP PACKAGE ERP_ITEM_GROUPS");
            log("🗑️ تم حذف Package القديم");
        } catch (SQLException e) {
            log("ℹ️ Package غير موجود مسبقاً");
        }
        
        // Package Specification
        String packageSpec = """
            CREATE OR REPLACE PACKAGE ERP_ITEM_GROUPS AS
                FUNCTION get_groups_count RETURN NUMBER;
                FUNCTION add_main_group(p_g_code VARCHAR2, p_g_a_name VARCHAR2, p_g_e_name VARCHAR2) RETURN VARCHAR2;
                FUNCTION import_from_ias RETURN VARCHAR2;
                FUNCTION sync_with_ias RETURN VARCHAR2;
                PROCEDURE log_operation(p_operation VARCHAR2, p_message VARCHAR2);
            END ERP_ITEM_GROUPS;
        """;
        
        stmt.execute(packageSpec);
        log("✅ تم إنشاء Package Specification");
        
        // Package Body
        String packageBody = """
            CREATE OR REPLACE PACKAGE BODY ERP_ITEM_GROUPS AS
                
                FUNCTION get_groups_count RETURN NUMBER IS
                    l_count NUMBER;
                BEGIN
                    SELECT COUNT(*) INTO l_count FROM ERP_GROUP_DETAILS;
                    RETURN l_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        RETURN 0;
                END get_groups_count;
                
                FUNCTION add_main_group(p_g_code VARCHAR2, p_g_a_name VARCHAR2, p_g_e_name VARCHAR2) RETURN VARCHAR2 IS
                    l_count NUMBER;
                BEGIN
                    SELECT COUNT(*) INTO l_count FROM ERP_GROUP_DETAILS WHERE G_CODE = p_g_code;
                    IF l_count > 0 THEN
                        RETURN 'ERROR: كود المجموعة موجود مسبقاً';
                    END IF;
                    
                    INSERT INTO ERP_GROUP_DETAILS (G_CODE, G_A_NAME, G_E_NAME)
                    VALUES (p_g_code, p_g_a_name, p_g_e_name);
                    
                    log_operation('INSERT', 'تم إضافة المجموعة: ' || p_g_code);
                    COMMIT;
                    RETURN 'SUCCESS: تم إضافة المجموعة بنجاح';
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RETURN 'ERROR: ' || SQLERRM;
                END add_main_group;
                
                FUNCTION import_from_ias RETURN VARCHAR2 IS
                    l_imported NUMBER := 0;
                BEGIN
                    FOR rec IN (
                        SELECT G_CODE, G_A_NAME, G_E_NAME
                        FROM GROUP_DETAILS@IAS20251_LINK
                        WHERE G_CODE IS NOT NULL AND ROWNUM <= 50
                    ) LOOP
                        BEGIN
                            INSERT INTO ERP_GROUP_DETAILS (G_CODE, G_A_NAME, G_E_NAME)
                            VALUES (rec.G_CODE, rec.G_A_NAME, rec.G_E_NAME);
                            l_imported := l_imported + 1;
                        EXCEPTION
                            WHEN DUP_VAL_ON_INDEX THEN
                                UPDATE ERP_GROUP_DETAILS SET
                                    G_A_NAME = rec.G_A_NAME, G_E_NAME = rec.G_E_NAME
                                WHERE G_CODE = rec.G_CODE;
                                l_imported := l_imported + 1;
                            WHEN OTHERS THEN NULL;
                        END;
                    END LOOP;
                    
                    log_operation('IMPORT', 'تم استيراد ' || l_imported || ' مجموعة من IAS20251');
                    COMMIT;
                    RETURN 'SUCCESS: تم استيراد ' || l_imported || ' مجموعة';
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RETURN 'ERROR: ' || SQLERRM;
                END import_from_ias;
                
                FUNCTION sync_with_ias RETURN VARCHAR2 IS
                BEGIN
                    RETURN import_from_ias();
                END sync_with_ias;
                
                PROCEDURE log_operation(p_operation VARCHAR2, p_message VARCHAR2) IS
                    PRAGMA AUTONOMOUS_TRANSACTION;
                BEGIN
                    INSERT INTO ERP_OPERATION_LOG (
                        log_id, operation_type, table_name, status,
                        message, records_count, operation_date, username
                    ) VALUES (
                        ERP_LOG_SEQ.NEXTVAL, p_operation, 'ERP_ITEM_GROUPS', 'SUCCESS',
                        p_message, 0, SYSDATE, USER
                    );
                    COMMIT;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        NULL;
                END log_operation;
                
            END ERP_ITEM_GROUPS;
        """;
        
        stmt.execute(packageBody);
        log("✅ تم إنشاء Package Body");
        
        // اختبار Package
        try (CallableStatement cs = connection.prepareCall("{ ? = call ERP_ITEM_GROUPS.get_groups_count }")) {
            cs.registerOutParameter(1, Types.NUMERIC);
            cs.execute();
            int count = cs.getInt(1);
            log("📊 عدد المجموعات الحالي: " + count);
            log("✅ Package يعمل بنجاح!");
        }
    }
    
    /**
     * إنشاء Database Link
     */
    private void createDatabaseLink() throws SQLException {
        log("🔗 إنشاء Database Link...");
        
        Statement stmt = connection.createStatement();
        
        try {
            stmt.execute("DROP DATABASE LINK IAS20251_LINK");
            log("🗑️ تم حذف Database Link القديم");
        } catch (SQLException e) {
            log("ℹ️ Database Link غير موجود مسبقاً");
        }
        
        try {
            String createLinkSQL = """
                CREATE DATABASE LINK IAS20251_LINK
                CONNECT TO ias20251 IDENTIFIED BY ys123
                USING 'localhost:1521/orcl'
            """;
            
            stmt.execute(createLinkSQL);
            log("✅ تم إنشاء Database Link: IAS20251_LINK");
            
            // اختبار Database Link
            try (ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM GROUP_DETAILS@IAS20251_LINK")) {
                if (rs.next()) {
                    int count = rs.getInt(1);
                    log("✅ اختبار Database Link نجح - عدد المجموعات في IAS20251: " + count);
                }
            }
        } catch (SQLException e) {
            log("⚠️ تحذير: مشكلة في Database Link: " + e.getMessage());
        }
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    private void initializeGUI() {
        setTitle("نظام إدارة مجموعات الأصناف - ERP");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(800, 600);
        setLocationRelativeTo(null);
        
        // تخطيط رئيسي
        setLayout(new BorderLayout());
        
        // لوحة الإدخال
        JPanel inputPanel = new JPanel(new GridBagLayout());
        inputPanel.setBorder(BorderFactory.createTitledBorder("إدارة المجموعات"));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // حقول الإدخال
        gbc.gridx = 0; gbc.gridy = 0;
        inputPanel.add(new JLabel("كود المجموعة:"), gbc);
        gbc.gridx = 1;
        groupCodeField = new JTextField(15);
        inputPanel.add(groupCodeField, gbc);
        
        gbc.gridx = 0; gbc.gridy = 1;
        inputPanel.add(new JLabel("اسم المجموعة:"), gbc);
        gbc.gridx = 1;
        groupNameField = new JTextField(15);
        inputPanel.add(groupNameField, gbc);
        
        // الأزرار
        JPanel buttonPanel = new JPanel(new FlowLayout());
        
        JButton addButton = new JButton("إضافة مجموعة");
        addButton.addActionListener(e -> addGroup());
        buttonPanel.add(addButton);
        
        JButton countButton = new JButton("عدد المجموعات");
        countButton.addActionListener(e -> getGroupsCount());
        buttonPanel.add(countButton);
        
        JButton importButton = new JButton("استيراد من IAS20251");
        importButton.addActionListener(e -> importFromIAS());
        buttonPanel.add(importButton);
        
        JButton syncButton = new JButton("مزامنة");
        syncButton.addActionListener(e -> syncWithIAS());
        buttonPanel.add(syncButton);
        
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 2;
        inputPanel.add(buttonPanel, gbc);
        
        add(inputPanel, BorderLayout.NORTH);
        
        // منطقة السجل
        logArea = new JTextArea(20, 60);
        logArea.setEditable(false);
        logArea.setFont(new Font("Arial", Font.PLAIN, 12));
        JScrollPane scrollPane = new JScrollPane(logArea);
        scrollPane.setBorder(BorderFactory.createTitledBorder("سجل العمليات"));
        add(scrollPane, BorderLayout.CENTER);
        
        log("🎉 تم تهيئة النظام بنجاح!");
    }
    
    /**
     * إضافة مجموعة
     */
    private void addGroup() {
        try {
            String code = groupCodeField.getText().trim();
            String name = groupNameField.getText().trim();
            
            if (code.isEmpty() || name.isEmpty()) {
                JOptionPane.showMessageDialog(this, "يرجى إدخال كود واسم المجموعة");
                return;
            }
            
            CallableStatement cs = connection.prepareCall("{ ? = call ERP_ITEM_GROUPS.add_main_group(?, ?, ?) }");
            cs.registerOutParameter(1, Types.VARCHAR);
            cs.setString(2, code);
            cs.setString(3, name);
            cs.setString(4, name + " (English)");
            cs.execute();
            
            String result = cs.getString(1);
            log("📋 " + result);
            
            if (result.startsWith("SUCCESS")) {
                groupCodeField.setText("");
                groupNameField.setText("");
                JOptionPane.showMessageDialog(this, "تم إضافة المجموعة بنجاح");
            } else {
                JOptionPane.showMessageDialog(this, result, "خطأ", JOptionPane.ERROR_MESSAGE);
            }
            
        } catch (Exception e) {
            log("❌ خطأ في إضافة المجموعة: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * الحصول على عدد المجموعات
     */
    private void getGroupsCount() {
        try {
            CallableStatement cs = connection.prepareCall("{ ? = call ERP_ITEM_GROUPS.get_groups_count }");
            cs.registerOutParameter(1, Types.NUMERIC);
            cs.execute();
            
            int count = cs.getInt(1);
            log("📊 عدد المجموعات الحالي: " + count);
            JOptionPane.showMessageDialog(this, "عدد المجموعات: " + count);
            
        } catch (Exception e) {
            log("❌ خطأ في الحصول على عدد المجموعات: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * استيراد من IAS20251
     */
    private void importFromIAS() {
        try {
            log("🔄 بدء الاستيراد من IAS20251...");
            
            CallableStatement cs = connection.prepareCall("{ ? = call ERP_ITEM_GROUPS.import_from_ias }");
            cs.registerOutParameter(1, Types.VARCHAR);
            cs.execute();
            
            String result = cs.getString(1);
            log("📋 " + result);
            JOptionPane.showMessageDialog(this, result);
            
        } catch (Exception e) {
            log("❌ خطأ في الاستيراد: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * مزامنة مع IAS20251
     */
    private void syncWithIAS() {
        try {
            log("🔄 بدء المزامنة مع IAS20251...");
            
            CallableStatement cs = connection.prepareCall("{ ? = call ERP_ITEM_GROUPS.sync_with_ias }");
            cs.registerOutParameter(1, Types.VARCHAR);
            cs.execute();
            
            String result = cs.getString(1);
            log("📋 " + result);
            JOptionPane.showMessageDialog(this, result);
            
        } catch (Exception e) {
            log("❌ خطأ في المزامنة: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * تسجيل رسالة في السجل
     */
    private void log(String message) {
        SwingUtilities.invokeLater(() -> {
            logArea.append(java.time.LocalTime.now() + " - " + message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
        System.out.println(message);
    }
}
