@echo off
echo ========================================
echo اختبار النص العربي
echo Arabic Text Test
echo ========================================

REM إعداد متغيرات البيئة للنص العربي
set JAVA_OPTS=-Dfile.encoding=UTF-8
set JAVA_OPTS=%JAVA_OPTS% -Dsun.jnu.encoding=UTF-8
set JAVA_OPTS=%JAVA_OPTS% -Duser.language=ar
set JAVA_OPTS=%JAVA_OPTS% -Duser.country=SA
set JAVA_OPTS=%JAVA_OPTS% -Dawt.useSystemAAFontSettings=lcd
set JAVA_OPTS=%JAVA_OPTS% -Dswing.aatext=true

REM الانتقال إلى مجلد الكود المصدري
cd /d "%~dp0src\main\java"

echo تجميع ملفات الاختبار...
echo Compiling test files...

REM تجميع مدير النصوص العربية
javac %JAVA_OPTS% ArabicTextManager.java
if errorlevel 1 (
    echo خطأ في تجميع مدير النصوص العربية
    pause
    exit /b 1
)

REM تجميع اختبار النص العربي
javac %JAVA_OPTS% ArabicTextTest.java
if errorlevel 1 (
    echo خطأ في تجميع اختبار النص العربي
    pause
    exit /b 1
)

echo تم التجميع بنجاح!
echo Compilation successful!
echo.

echo تشغيل اختبار النص العربي...
echo Running Arabic text test...
echo.

REM تشغيل اختبار النص العربي
java %JAVA_OPTS% ArabicTextTest

echo.
echo انتهى الاختبار
echo Test completed
pause
