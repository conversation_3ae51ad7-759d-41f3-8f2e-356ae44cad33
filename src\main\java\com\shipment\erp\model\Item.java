package com.shipment.erp.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;

/**
 * نموذج الصنف Item Entity
 */
@Entity
@Table(name = "items")
public class Item extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "code", unique = true, nullable = false, length = 50)
    private String code;

    @Column(name = "barcode", unique = true, length = 50)
    private String barcode;

    @Column(name = "name_ar", nullable = false, length = 200)
    private String nameAr;

    @Column(name = "name_en", length = 200)
    private String nameEn;

    @Column(name = "description", length = 1000)
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", nullable = false)
    private ItemCategory category;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "unit_of_measure_id", nullable = false)
    private UnitOfMeasure unitOfMeasure;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "purchase_unit_id")
    private UnitOfMeasure purchaseUnit;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sales_unit_id")
    private UnitOfMeasure salesUnit;

    @Column(name = "item_type", length = 20)
    @Enumerated(EnumType.STRING)
    private ItemType itemType = ItemType.PRODUCT;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "is_sellable")
    private Boolean isSellable = true;

    @Column(name = "is_purchasable")
    private Boolean isPurchasable = true;

    @Column(name = "track_inventory")
    private Boolean trackInventory = true;

    // Cost Information
    @Column(name = "cost_price", precision = 15, scale = 4)
    private BigDecimal costPrice = BigDecimal.ZERO;

    @Column(name = "standard_cost", precision = 15, scale = 4)
    private BigDecimal standardCost = BigDecimal.ZERO;

    @Column(name = "last_cost", precision = 15, scale = 4)
    private BigDecimal lastCost = BigDecimal.ZERO;

    @Column(name = "average_cost", precision = 15, scale = 4)
    private BigDecimal averageCost = BigDecimal.ZERO;

    // Pricing Information
    @Column(name = "sales_price", precision = 15, scale = 4)
    private BigDecimal salesPrice = BigDecimal.ZERO;

    @Column(name = "min_sales_price", precision = 15, scale = 4)
    private BigDecimal minSalesPrice = BigDecimal.ZERO;

    @Column(name = "wholesale_price", precision = 15, scale = 4)
    private BigDecimal wholesalePrice = BigDecimal.ZERO;

    @Column(name = "retail_price", precision = 15, scale = 4)
    private BigDecimal retailPrice = BigDecimal.ZERO;

    // Inventory Information
    @Column(name = "current_stock", precision = 15, scale = 4)
    private BigDecimal currentStock = BigDecimal.ZERO;

    @Column(name = "min_stock_level", precision = 15, scale = 4)
    private BigDecimal minStockLevel = BigDecimal.ZERO;

    @Column(name = "max_stock_level", precision = 15, scale = 4)
    private BigDecimal maxStockLevel = BigDecimal.ZERO;

    @Column(name = "reorder_point", precision = 15, scale = 4)
    private BigDecimal reorderPoint = BigDecimal.ZERO;

    @Column(name = "reorder_quantity", precision = 15, scale = 4)
    private BigDecimal reorderQuantity = BigDecimal.ZERO;

    // Physical Properties
    @Column(name = "weight", precision = 10, scale = 4)
    private BigDecimal weight;

    @Column(name = "length", precision = 10, scale = 4)
    private BigDecimal length;

    @Column(name = "width", precision = 10, scale = 4)
    private BigDecimal width;

    @Column(name = "height", precision = 10, scale = 4)
    private BigDecimal height;

    @Column(name = "volume", precision = 10, scale = 4)
    private BigDecimal volume;

    // Additional Information
    @Column(name = "manufacturer", length = 100)
    private String manufacturer;

    @Column(name = "brand", length = 100)
    private String brand;

    @Column(name = "model", length = 100)
    private String model;

    @Column(name = "color", length = 50)
    private String color;

    @Column(name = "size", length = 50)
    private String size;

    @Column(name = "warranty_period")
    private Integer warrantyPeriod; // in months

    @Column(name = "expiry_date")
    private LocalDate expiryDate;

    @Column(name = "shelf_life")
    private Integer shelfLife; // in days

    // Tax and Accounting
    @Column(name = "tax_rate", precision = 5, scale = 2)
    private BigDecimal taxRate = BigDecimal.ZERO;

    @Column(name = "tax_exempt")
    private Boolean taxExempt = false;

    @Column(name = "account_code", length = 20)
    private String accountCode;

    // Images and Documents
    @Column(name = "image_path", length = 500)
    private String imagePath;

    @Column(name = "thumbnail_path", length = 500)
    private String thumbnailPath;

    @Column(name = "document_path", length = 500)
    private String documentPath;

    @Column(name = "notes", length = 2000)
    private String notes;

    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    // Constructors
    public Item() {}

    public Item(String code, String nameAr, ItemCategory category, UnitOfMeasure unitOfMeasure) {
        this.code = code;
        this.nameAr = nameAr;
        this.category = category;
        this.unitOfMeasure = unitOfMeasure;
    }

    // Enum for Item Type
    public enum ItemType {
        PRODUCT("منتج"), SERVICE("خدمة"), RAW_MATERIAL("مادة خام"), FINISHED_GOOD(
                "منتج نهائي"), SEMI_FINISHED(
                        "منتج نصف مصنع"), CONSUMABLE("مستهلك"), SPARE_PART("قطعة غيار");

        private final String displayName;

        ItemType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public ItemCategory getCategory() {
        return category;
    }

    public void setCategory(ItemCategory category) {
        this.category = category;
    }

    public UnitOfMeasure getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(UnitOfMeasure unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public UnitOfMeasure getPurchaseUnit() {
        return purchaseUnit;
    }

    public void setPurchaseUnit(UnitOfMeasure purchaseUnit) {
        this.purchaseUnit = purchaseUnit;
    }

    public UnitOfMeasure getSalesUnit() {
        return salesUnit;
    }

    public void setSalesUnit(UnitOfMeasure salesUnit) {
        this.salesUnit = salesUnit;
    }

    public ItemType getItemType() {
        return itemType;
    }

    public void setItemType(ItemType itemType) {
        this.itemType = itemType;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Boolean getIsSellable() {
        return isSellable;
    }

    public void setIsSellable(Boolean isSellable) {
        this.isSellable = isSellable;
    }

    public Boolean getIsPurchasable() {
        return isPurchasable;
    }

    public void setIsPurchasable(Boolean isPurchasable) {
        this.isPurchasable = isPurchasable;
    }

    public Boolean getTrackInventory() {
        return trackInventory;
    }

    public void setTrackInventory(Boolean trackInventory) {
        this.trackInventory = trackInventory;
    }

    // Cost getters and setters
    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public BigDecimal getStandardCost() {
        return standardCost;
    }

    public void setStandardCost(BigDecimal standardCost) {
        this.standardCost = standardCost;
    }

    public BigDecimal getLastCost() {
        return lastCost;
    }

    public void setLastCost(BigDecimal lastCost) {
        this.lastCost = lastCost;
    }

    public BigDecimal getAverageCost() {
        return averageCost;
    }

    public void setAverageCost(BigDecimal averageCost) {
        this.averageCost = averageCost;
    }

    // Price getters and setters
    public BigDecimal getSalesPrice() {
        return salesPrice;
    }

    public void setSalesPrice(BigDecimal salesPrice) {
        this.salesPrice = salesPrice;
    }

    public BigDecimal getMinSalesPrice() {
        return minSalesPrice;
    }

    public void setMinSalesPrice(BigDecimal minSalesPrice) {
        this.minSalesPrice = minSalesPrice;
    }

    public BigDecimal getWholesalePrice() {
        return wholesalePrice;
    }

    public void setWholesalePrice(BigDecimal wholesalePrice) {
        this.wholesalePrice = wholesalePrice;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    // Inventory getters and setters
    public BigDecimal getCurrentStock() {
        return currentStock;
    }

    public void setCurrentStock(BigDecimal currentStock) {
        this.currentStock = currentStock;
    }

    public BigDecimal getMinStockLevel() {
        return minStockLevel;
    }

    public void setMinStockLevel(BigDecimal minStockLevel) {
        this.minStockLevel = minStockLevel;
    }

    public BigDecimal getMaxStockLevel() {
        return maxStockLevel;
    }

    public void setMaxStockLevel(BigDecimal maxStockLevel) {
        this.maxStockLevel = maxStockLevel;
    }

    public BigDecimal getReorderPoint() {
        return reorderPoint;
    }

    public void setReorderPoint(BigDecimal reorderPoint) {
        this.reorderPoint = reorderPoint;
    }

    public BigDecimal getReorderQuantity() {
        return reorderQuantity;
    }

    public void setReorderQuantity(BigDecimal reorderQuantity) {
        this.reorderQuantity = reorderQuantity;
    }

    // Physical properties getters and setters
    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    // Additional information getters and setters
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public Integer getWarrantyPeriod() {
        return warrantyPeriod;
    }

    public void setWarrantyPeriod(Integer warrantyPeriod) {
        this.warrantyPeriod = warrantyPeriod;
    }

    public LocalDate getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(LocalDate expiryDate) {
        this.expiryDate = expiryDate;
    }

    public Integer getShelfLife() {
        return shelfLife;
    }

    public void setShelfLife(Integer shelfLife) {
        this.shelfLife = shelfLife;
    }

    // Tax and accounting getters and setters
    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public Boolean getTaxExempt() {
        return taxExempt;
    }

    public void setTaxExempt(Boolean taxExempt) {
        this.taxExempt = taxExempt;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    // Images and documents getters and setters
    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public String getThumbnailPath() {
        return thumbnailPath;
    }

    public void setThumbnailPath(String thumbnailPath) {
        this.thumbnailPath = thumbnailPath;
    }

    public String getDocumentPath() {
        return documentPath;
    }

    public void setDocumentPath(String documentPath) {
        this.documentPath = documentPath;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    // Helper methods
    public boolean isLowStock() {
        return currentStock != null && minStockLevel != null
                && currentStock.compareTo(minStockLevel) <= 0;
    }

    public boolean isOutOfStock() {
        return currentStock == null || currentStock.compareTo(BigDecimal.ZERO) <= 0;
    }

    public boolean needsReorder() {
        return reorderPoint != null && currentStock != null
                && currentStock.compareTo(reorderPoint) <= 0;
    }

    public BigDecimal getStockValue() {
        if (currentStock != null && averageCost != null) {
            return currentStock.multiply(averageCost);
        }
        return BigDecimal.ZERO;
    }

    @Override
    public String toString() {
        return nameAr + " (" + code + ")";
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null || getClass() != obj.getClass())
            return false;
        Item item = (Item) obj;
        return id != null && id.equals(item.id);
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
