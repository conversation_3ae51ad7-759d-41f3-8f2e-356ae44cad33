/**
 * فئة بيانات مجموعة الأصناف
 * Item Category Data Class
 */
public class ItemCategoryData {
    private String code;
    private String nameAr;
    private String nameEn;
    private String description;
    private String parentCode;
    private int level;
    private boolean isActive;
    private String icon;
    private String color;
    private int sortOrder;
    private String notes;
    
    public ItemCategoryData() {
        this.level = 1;
        this.isActive = true;
        this.icon = "📁";
        this.color = "#007bff";
        this.sortOrder = 0;
    }
    
    public ItemCategoryData(String code, String nameAr, String nameEn, String description, 
                           String parentCode, int level, boolean isActive, String icon) {
        this.code = code;
        this.nameAr = nameAr;
        this.nameEn = nameEn;
        this.description = description;
        this.parentCode = parentCode;
        this.level = level;
        this.isActive = isActive;
        this.icon = icon != null ? icon : "📁";
        this.color = "#007bff";
        this.sortOrder = 0;
    }
    
    // Getters and Setters
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getNameAr() {
        return nameAr;
    }
    
    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }
    
    public String getNameEn() {
        return nameEn;
    }
    
    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getParentCode() {
        return parentCode;
    }
    
    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }
    
    public int getLevel() {
        return level;
    }
    
    public void setLevel(int level) {
        this.level = level;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    public String getIcon() {
        return icon;
    }
    
    public void setIcon(String icon) {
        this.icon = icon;
    }
    
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
    
    public int getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(int sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    @Override
    public String toString() {
        return nameAr + " (" + code + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ItemCategoryData that = (ItemCategoryData) obj;
        return code != null ? code.equals(that.code) : that.code == null;
    }
    
    @Override
    public int hashCode() {
        return code != null ? code.hashCode() : 0;
    }
}
