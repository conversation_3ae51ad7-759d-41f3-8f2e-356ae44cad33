import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * فئة أساسية للاستيراد - تطبق المعايير الموحدة لاستيراد البيانات يجب أن ترث جميع فئات الاستيراد من
 * هذه الفئة
 */
public abstract class BaseDataImporter {

    // معلومات الاتصال
    protected Connection sourceConnection;
    protected Connection targetConnection;
    protected String sourceTableName;
    protected String targetTableName;

    // معلومات التعليقات
    protected Map<String, String> fieldComments;
    protected Map<String, String> fieldMapping;

    // إحصائيات الاستيراد
    protected int totalRecords = 0;
    protected int importedRecords = 0;
    protected int errorRecords = 0;
    protected List<String> errorMessages = new ArrayList<>();

    /**
     * منشئ الفئة الأساسية
     */
    public BaseDataImporter(String sourceTableName, String targetTableName) {
        this.sourceTableName = sourceTableName;
        this.targetTableName = targetTableName;
        this.fieldComments = new HashMap<>();
        this.fieldMapping = new HashMap<>();
    }

    /**
     * تهيئة الاتصالات
     */
    protected void initializeConnections() throws SQLException {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");

            // الاتصال بقاعدة البيانات المصدر
            sourceConnection = DriverManager.getConnection("*************************************",
                    "ias20251", "ys123");

            // الاتصال بقاعدة البيانات الهدف
            targetConnection = DriverManager.getConnection("*************************************",
                    "ship_erp", "ship_erp_password");

            // إعداد الاتصالات
            sourceConnection.setAutoCommit(false);
            targetConnection.setAutoCommit(false);

            System.out.println("✅ تم تهيئة الاتصالات بنجاح");

        } catch (ClassNotFoundException e) {
            throw new SQLException("تعذر العثور على تعريف Oracle JDBC", e);
        }
    }

    /**
     * تحليل تعليقات الجدول المصدر
     */
    protected void analyzeSourceTableComments() {
        System.out.println("🔍 تحليل تعليقات جدول المصدر: " + sourceTableName);

        // تحليل التعليقات باستخدام مدير التعليقات
        CommentsManager.analyzeTableComments(sourceTableName, "ias20251", "ys123");

        // الحصول على التعليقات
        Map<String, CommentsManager.CommentInfo> comments =
                CommentsManager.getTableComments(sourceTableName);

        // تحويل التعليقات إلى خريطة بسيطة
        for (Map.Entry<String, CommentsManager.CommentInfo> entry : comments.entrySet()) {
            fieldComments.put(entry.getKey(), entry.getValue().getCommentText());
        }

        System.out.println("📊 تم تحليل " + fieldComments.size() + " حقل");
    }

    /**
     * إنشاء خريطة ربط الحقول - يجب تنفيذها في الفئات المشتقة
     */
    protected abstract void createFieldMapping();

    /**
     * التحقق من وجود الجدول الهدف
     */
    protected boolean checkTargetTableExists() {
        try {
            Statement stmt = targetConnection.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = '"
                    + targetTableName + "'");
            rs.next();
            boolean exists = rs.getInt(1) > 0;
            rs.close();
            stmt.close();

            if (exists) {
                System.out.println("✅ الجدول الهدف موجود: " + targetTableName);
            } else {
                System.out.println("❌ الجدول الهدف غير موجود: " + targetTableName);
            }

            return exists;

        } catch (SQLException e) {
            System.err.println("❌ خطأ في فحص الجدول الهدف: " + e.getMessage());
            return false;
        }
    }

    /**
     * عد السجلات في الجدول المصدر
     */
    protected void countSourceRecords() {
        try {
            Statement stmt = sourceConnection.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + sourceTableName);
            rs.next();
            totalRecords = rs.getInt(1);
            rs.close();
            stmt.close();

            System.out.println("📊 إجمالي السجلات في الجدول المصدر: " + totalRecords);

        } catch (SQLException e) {
            System.err.println("❌ خطأ في عد السجلات: " + e.getMessage());
            totalRecords = 0;
        }
    }

    /**
     * تنفيذ عملية الاستيراد - يجب تنفيذها في الفئات المشتقة
     */
    protected abstract void performImport() throws SQLException;

    /**
     * التحقق من صحة البيانات قبل الاستيراد - يمكن إعادة تعريفها في الفئات المشتقة
     */
    protected boolean validateData(ResultSet sourceData) throws SQLException {
        // التحقق الأساسي - يمكن توسيعه في الفئات المشتقة
        return true;
    }

    /**
     * معالجة خطأ في الاستيراد
     */
    protected void handleImportError(String errorMessage, Exception e) {
        errorRecords++;
        String fullErrorMessage = "خطأ في الاستيراد: " + errorMessage;
        if (e != null) {
            fullErrorMessage += " - " + e.getMessage();
        }
        errorMessages.add(fullErrorMessage);
        System.err.println("❌ " + fullErrorMessage);
    }

    /**
     * إنشاء تقرير الاستيراد
     */
    protected void generateImportReport() {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("📊 تقرير الاستيراد - " + sourceTableName + " → " + targetTableName);
        System.out.println("=".repeat(80));

        System.out.println("📈 الإحصائيات:");
        System.out.println("  📋 إجمالي السجلات: " + totalRecords);
        System.out.println("  ✅ السجلات المستوردة: " + importedRecords);
        System.out.println("  ❌ السجلات الخاطئة: " + errorRecords);

        double successRate = totalRecords > 0 ? (double) importedRecords / totalRecords * 100 : 0;
        System.out.println("  📊 نسبة النجاح: " + String.format("%.2f%%", successRate));

        System.out.println("\n🔗 خريطة ربط الحقول:");
        for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
            String sourceField = entry.getKey();
            String targetField = entry.getValue();
            String comment = fieldComments.getOrDefault(sourceField, "لا يوجد تعليق");
            System.out.println("  " + sourceField + " → " + targetField + " (" + comment + ")");
        }

        if (!errorMessages.isEmpty()) {
            System.out.println("\n❌ الأخطاء:");
            for (int i = 0; i < Math.min(errorMessages.size(), 10); i++) {
                System.out.println("  " + (i + 1) + ". " + errorMessages.get(i));
            }
            if (errorMessages.size() > 10) {
                System.out.println("  ... و " + (errorMessages.size() - 10) + " خطأ آخر");
            }
        }

        System.out.println("\n💡 التوصيات:");
        if (successRate < 90) {
            System.out.println("  ⚠️ نسبة النجاح منخفضة، يُنصح بمراجعة الأخطاء");
        }
        if (errorRecords > 0) {
            System.out.println("  🔧 راجع خريطة ربط الحقول والتحقق من صحة البيانات");
        }
        if (successRate >= 95) {
            System.out.println("  🎉 عملية استيراد ناجحة!");
        }
    }

    /**
     * تنفيذ عملية الاستيراد الكاملة
     */
    public final void executeImport() {
        try {
            System.out.println(
                    "🚀 بدء عملية الاستيراد: " + sourceTableName + " → " + targetTableName);

            // 1. تهيئة مدير التعليقات
            CommentsManager.initialize();

            // 2. تهيئة الاتصالات
            initializeConnections();

            // 3. تحليل تعليقات الجدول المصدر
            analyzeSourceTableComments();

            // 4. إنشاء خريطة ربط الحقول
            createFieldMapping();

            // 5. التحقق من وجود الجدول الهدف
            if (!checkTargetTableExists()) {
                throw new SQLException("الجدول الهدف غير موجود: " + targetTableName);
            }

            // 6. عد السجلات في الجدول المصدر
            countSourceRecords();

            // 7. تنفيذ عملية الاستيراد
            performImport();

            // 8. إنشاء تقرير الاستيراد
            generateImportReport();

        } catch (Exception e) {
            System.err.println("❌ خطأ في عملية الاستيراد: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // إغلاق الاتصالات
            closeConnections();
        }
    }

    /**
     * إغلاق الاتصالات
     */
    protected void closeConnections() {
        try {
            if (sourceConnection != null)
                sourceConnection.close();
            if (targetConnection != null)
                targetConnection.close();
            System.out.println("✅ تم إغلاق الاتصالات");
        } catch (SQLException e) {
            System.err.println("⚠️ خطأ في إغلاق الاتصالات: " + e.getMessage());
        }
    }

    // Getters للإحصائيات
    public int getTotalRecords() {
        return totalRecords;
    }

    public int getImportedRecords() {
        return importedRecords;
    }

    public int getErrorRecords() {
        return errorRecords;
    }

    public List<String> getErrorMessages() {
        return new ArrayList<>(errorMessages);
    }
}
