package com.shipment.erp.view;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.ComponentOrientation;
import java.awt.Cursor;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import javax.swing.BorderFactory;
import javax.swing.Box;
import javax.swing.BoxLayout;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.JTree;
import javax.swing.SwingConstants;
import javax.swing.UIManager;
import javax.swing.border.EmptyBorder;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeCellRenderer;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreeSelectionModel;
import com.shipment.erp.service.ItemCategoryService;

/**
 * نافذة إدارة مجموعات الأصناف Item Category Management Window
 */
public class ItemCategoryWindow extends JFrame {

    private Font arabicFont;
    private ItemCategoryService categoryService;

    // UI Components
    private JTree categoryTree;
    private DefaultMutableTreeNode rootNode;
    private DefaultTreeModel treeModel;
    private JTextField searchField;
    private JButton addRootButton, addSubButton, editButton, deleteButton, refreshButton;
    private JLabel statusLabel;

    // Split pane for tree and details
    private JSplitPane splitPane;
    private JPanel detailsPanel;

    public ItemCategoryWindow() {
        this.arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        // this.categoryService = categoryService; // سيتم حقنها لاحقاً

        initializeComponents();
        setupLayout();
        setupEventHandlers();
        loadCategoryData();

        setTitle("إدارة مجموعات الأصناف - Item Category Management");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setExtendedState(JFrame.MAXIMIZED_BOTH);
        setMinimumSize(new Dimension(1000, 700));
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }

    private void initializeComponents() {
        // إعداد شجرة المجموعات
        rootNode = new DefaultMutableTreeNode("مجموعات الأصناف");
        treeModel = new DefaultTreeModel(rootNode);
        categoryTree = new JTree(treeModel);
        categoryTree.setFont(arabicFont);
        categoryTree.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        categoryTree.setRootVisible(true);
        categoryTree.setShowsRootHandles(true);
        categoryTree.setRowHeight(25);
        categoryTree.getSelectionModel().setSelectionMode(TreeSelectionModel.SINGLE_TREE_SELECTION);

        // تخصيص مظهر الشجرة
        categoryTree.setCellRenderer(new CategoryTreeCellRenderer());

        // حقل البحث
        searchField = new JTextField();
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.setPreferredSize(new Dimension(200, 30));

        // الأزرار
        addRootButton = createButton("إضافة مجموعة رئيسية", "addRoot", new Color(40, 167, 69));
        addSubButton = createButton("إضافة مجموعة فرعية", "addSub", new Color(23, 162, 184));
        editButton = createButton("تعديل", "edit", new Color(255, 193, 7));
        deleteButton = createButton("حذف", "delete", new Color(220, 53, 69));
        refreshButton = createButton("تحديث", "refresh", new Color(0, 123, 255));

        addSubButton.setEnabled(false);
        editButton.setEnabled(false);
        deleteButton.setEnabled(false);

        // شريط الحالة
        statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);
        statusLabel.setBorder(new EmptyBorder(5, 10, 5, 10));

        // لوحة التفاصيل
        detailsPanel = createDetailsPanel();
    }

    private JButton createButton(String text, String actionCommand, Color backgroundColor) {
        JButton button = new JButton(text);
        button.setFont(arabicFont);
        button.setActionCommand(actionCommand);
        button.setBackground(backgroundColor);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(150, 35));
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        return button;
    }

    private JPanel createDetailsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createTitledBorder("تفاصيل المجموعة"));
        panel.setPreferredSize(new Dimension(400, 0));

        // لوحة المعلومات
        JPanel infoPanel = new JPanel();
        infoPanel.setLayout(new BoxLayout(infoPanel, BoxLayout.Y_AXIS));
        infoPanel.setBorder(new EmptyBorder(10, 10, 10, 10));
        infoPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إضافة رسالة ترحيبية
        JLabel welcomeLabel = new JLabel("<html><div style='text-align: center;'>"
                + "<h2>مرحباً بك في إدارة مجموعات الأصناف</h2>"
                + "<p>اختر مجموعة من الشجرة لعرض تفاصيلها</p>" + "</div></html>");
        welcomeLabel.setFont(arabicFont);
        welcomeLabel.setHorizontalAlignment(SwingConstants.CENTER);
        welcomeLabel.setOpaque(true);
        welcomeLabel.setBackground(new Color(248, 249, 250));
        welcomeLabel.setBorder(new EmptyBorder(20, 20, 20, 20));

        infoPanel.add(welcomeLabel);

        panel.add(infoPanel, BorderLayout.CENTER);

        return panel;
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // اللوحة العلوية - البحث والأزرار
        JPanel topPanel = createTopPanel();
        add(topPanel, BorderLayout.NORTH);

        // اللوحة الوسطى - الشجرة والتفاصيل
        JPanel centerPanel = createCenterPanel();
        add(centerPanel, BorderLayout.CENTER);

        // اللوحة السفلى - شريط الحالة
        JPanel bottomPanel = createBottomPanel();
        add(bottomPanel, BorderLayout.SOUTH);
    }

    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(new EmptyBorder(10, 10, 10, 10));
        panel.setBackground(new Color(248, 249, 250));

        // لوحة البحث
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchPanel.setOpaque(false);

        JLabel searchLabel = new JLabel("البحث:");
        searchLabel.setFont(arabicFont);

        searchPanel.add(searchLabel);
        searchPanel.add(searchField);

        // لوحة الأزرار
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        buttonPanel.setOpaque(false);

        buttonPanel.add(addRootButton);
        buttonPanel.add(addSubButton);
        buttonPanel.add(editButton);
        buttonPanel.add(deleteButton);
        buttonPanel.add(refreshButton);

        panel.add(searchPanel, BorderLayout.EAST);
        panel.add(buttonPanel, BorderLayout.WEST);

        return panel;
    }

    private JPanel createCenterPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(new EmptyBorder(0, 10, 10, 10));

        // الشجرة مع شريط التمرير
        JScrollPane treeScrollPane = new JScrollPane(categoryTree);
        treeScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        treeScrollPane.setBorder(BorderFactory.createTitledBorder("شجرة المجموعات"));
        treeScrollPane.setPreferredSize(new Dimension(400, 0));

        // إعداد اللوحة المقسمة
        splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        splitPane.setRightComponent(treeScrollPane); // الشجرة على اليمين
        splitPane.setLeftComponent(detailsPanel); // التفاصيل على اليسار
        splitPane.setDividerLocation(400);
        splitPane.setResizeWeight(0.4);
        splitPane.setOneTouchExpandable(true);
        splitPane.setContinuousLayout(true);

        panel.add(splitPane, BorderLayout.CENTER);

        return panel;
    }

    private JPanel createBottomPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createMatteBorder(1, 0, 0, 0, Color.LIGHT_GRAY));
        panel.setBackground(new Color(248, 249, 250));

        panel.add(statusLabel, BorderLayout.WEST);

        return panel;
    }

    private void setupEventHandlers() {
        // البحث في الشجرة
        searchField.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyReleased(java.awt.event.KeyEvent e) {
                searchInTree();
            }
        });

        // تحديد عقدة في الشجرة
        categoryTree.addTreeSelectionListener(e -> {
            DefaultMutableTreeNode selectedNode =
                    (DefaultMutableTreeNode) categoryTree.getLastSelectedPathComponent();
            updateButtonStates(selectedNode);
            updateDetailsPanel(selectedNode);
        });

        // النقر المزدوج على الشجرة للتعديل
        categoryTree.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    DefaultMutableTreeNode selectedNode =
                            (DefaultMutableTreeNode) categoryTree.getLastSelectedPathComponent();
                    if (selectedNode != null && !selectedNode.isRoot()) {
                        editCategory();
                    }
                }
            }
        });

        // أحداث الأزرار
        addRootButton.addActionListener(e -> addRootCategory());
        addSubButton.addActionListener(e -> addSubCategory());
        editButton.addActionListener(e -> editCategory());
        deleteButton.addActionListener(e -> deleteCategory());
        refreshButton.addActionListener(e -> loadCategoryData());
    }

    private void updateButtonStates(DefaultMutableTreeNode selectedNode) {
        boolean hasSelection = selectedNode != null;
        boolean isRoot = hasSelection && selectedNode.isRoot();

        addSubButton.setEnabled(hasSelection);
        editButton.setEnabled(hasSelection && !isRoot);
        deleteButton.setEnabled(hasSelection && !isRoot);
    }

    private void updateDetailsPanel(DefaultMutableTreeNode selectedNode) {
        detailsPanel.removeAll();

        if (selectedNode == null || selectedNode.isRoot()) {
            detailsPanel.add(createWelcomePanel(), BorderLayout.CENTER);
        } else {
            detailsPanel.add(createCategoryDetailsPanel(selectedNode), BorderLayout.CENTER);
        }

        detailsPanel.revalidate();
        detailsPanel.repaint();
    }

    private JPanel createWelcomePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel welcomeLabel = new JLabel("<html><div style='text-align: center;'>"
                + "<h2>مرحباً بك في إدارة مجموعات الأصناف</h2>"
                + "<p>اختر مجموعة من الشجرة لعرض تفاصيلها</p>" + "</div></html>");
        welcomeLabel.setFont(arabicFont);
        welcomeLabel.setHorizontalAlignment(SwingConstants.CENTER);
        welcomeLabel.setOpaque(true);
        welcomeLabel.setBackground(new Color(248, 249, 250));
        welcomeLabel.setBorder(new EmptyBorder(20, 20, 20, 20));

        panel.add(welcomeLabel, BorderLayout.CENTER);

        return panel;
    }

    private JPanel createCategoryDetailsPanel(DefaultMutableTreeNode node) {
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        panel.setBorder(new EmptyBorder(15, 15, 15, 15));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // معلومات المجموعة (بيانات تجريبية)
        String categoryName = node.toString();

        panel.add(createInfoRow("اسم المجموعة:", categoryName));
        panel.add(Box.createVerticalStrut(10));

        panel.add(createInfoRow("الكود:", "CAT_" + categoryName.hashCode()));
        panel.add(Box.createVerticalStrut(10));

        panel.add(createInfoRow("المستوى:", String.valueOf(node.getLevel())));
        panel.add(Box.createVerticalStrut(10));

        panel.add(createInfoRow("عدد المجموعات الفرعية:", String.valueOf(node.getChildCount())));
        panel.add(Box.createVerticalStrut(10));

        panel.add(createInfoRow("عدد الأصناف:", "0")); // سيتم تحديثه لاحقاً
        panel.add(Box.createVerticalStrut(10));

        panel.add(createInfoRow("الحالة:", "نشط"));
        panel.add(Box.createVerticalStrut(20));

        // إضافة وصف
        JTextArea descArea = new JTextArea("وصف المجموعة سيظهر هنا...");
        descArea.setFont(arabicFont);
        descArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        descArea.setEditable(false);
        descArea.setOpaque(false);
        descArea.setLineWrap(true);
        descArea.setWrapStyleWord(true);
        descArea.setBorder(BorderFactory.createTitledBorder("الوصف"));

        panel.add(descArea);

        return panel;
    }

    private JPanel createInfoRow(String label, String value) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setMaximumSize(new Dimension(Integer.MAX_VALUE, 25));

        JLabel labelComp = new JLabel(label);
        labelComp.setFont(new Font(arabicFont.getName(), Font.BOLD, arabicFont.getSize()));
        labelComp.setPreferredSize(new Dimension(120, 25));

        JLabel valueComp = new JLabel(value);
        valueComp.setFont(arabicFont);

        panel.add(labelComp, BorderLayout.EAST);
        panel.add(valueComp, BorderLayout.CENTER);

        return panel;
    }

    private void searchInTree() {
        String searchText = searchField.getText().trim().toLowerCase();

        if (searchText.isEmpty()) {
            // إظهار جميع العقد
            expandAllNodes();
            statusLabel.setText("جاهز");
        } else {
            // البحث وإظهار العقد المطابقة
            searchAndHighlight(rootNode, searchText);
            statusLabel.setText("البحث عن: " + searchText);
        }
    }

    private void searchAndHighlight(DefaultMutableTreeNode node, String searchText) {
        // تنفيذ البحث في الشجرة (مبسط)
        expandAllNodes();
    }

    private void expandAllNodes() {
        for (int i = 0; i < categoryTree.getRowCount(); i++) {
            categoryTree.expandRow(i);
        }
    }

    private void loadCategoryData() {
        try {
            // مسح البيانات الحالية
            rootNode.removeAllChildren();

            // إضافة بيانات تجريبية
            addSampleCategories();

            // تحديث الشجرة
            treeModel.reload();
            expandAllNodes();

            statusLabel.setText("تم تحميل البيانات بنجاح");

        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "خطأ في تحميل البيانات: " + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            statusLabel.setText("خطأ في تحميل البيانات");
        }
    }

    private void addSampleCategories() {
        // مجموعات تجريبية
        DefaultMutableTreeNode electronicsNode = new DefaultMutableTreeNode("الإلكترونيات");
        electronicsNode.add(new DefaultMutableTreeNode("الهواتف الذكية"));
        electronicsNode.add(new DefaultMutableTreeNode("أجهزة الكمبيوتر"));
        electronicsNode.add(new DefaultMutableTreeNode("الأجهزة المنزلية"));
        rootNode.add(electronicsNode);

        DefaultMutableTreeNode clothingNode = new DefaultMutableTreeNode("الملابس");
        clothingNode.add(new DefaultMutableTreeNode("ملابس رجالية"));
        clothingNode.add(new DefaultMutableTreeNode("ملابس نسائية"));
        clothingNode.add(new DefaultMutableTreeNode("ملابس أطفال"));
        rootNode.add(clothingNode);

        DefaultMutableTreeNode foodNode = new DefaultMutableTreeNode("المواد الغذائية");
        foodNode.add(new DefaultMutableTreeNode("الخضروات"));
        foodNode.add(new DefaultMutableTreeNode("الفواكه"));
        foodNode.add(new DefaultMutableTreeNode("اللحوم"));
        rootNode.add(foodNode);
    }

    private void addRootCategory() {
        // مؤقتاً سنستخدم نموذج بسيط
        String categoryName = JOptionPane.showInputDialog(this, "اسم المجموعة الجديدة:",
                "إضافة مجموعة رئيسية", JOptionPane.QUESTION_MESSAGE);
        if (categoryName != null && !categoryName.trim().isEmpty()) {
            DefaultMutableTreeNode newNode = new DefaultMutableTreeNode(categoryName.trim());
            rootNode.add(newNode);
            treeModel.reload();
            expandAllNodes();
            statusLabel.setText("تم إضافة المجموعة: " + categoryName);
        }
    }

    private void addSubCategory() {
        DefaultMutableTreeNode selectedNode =
                (DefaultMutableTreeNode) categoryTree.getLastSelectedPathComponent();
        if (selectedNode == null)
            return;

        String categoryName = JOptionPane.showInputDialog(this, "اسم المجموعة الفرعية الجديدة:",
                "إضافة مجموعة فرعية", JOptionPane.QUESTION_MESSAGE);
        if (categoryName != null && !categoryName.trim().isEmpty()) {
            DefaultMutableTreeNode newNode = new DefaultMutableTreeNode(categoryName.trim());
            selectedNode.add(newNode);
            treeModel.reload();
            expandAllNodes();
            statusLabel.setText("تم إضافة المجموعة الفرعية: " + categoryName);
        }
    }

    private void editCategory() {
        DefaultMutableTreeNode selectedNode =
                (DefaultMutableTreeNode) categoryTree.getLastSelectedPathComponent();
        if (selectedNode == null || selectedNode.isRoot())
            return;

        String currentName = selectedNode.toString();
        String newName = JOptionPane.showInputDialog(this, "اسم المجموعة:", "تعديل المجموعة",
                JOptionPane.QUESTION_MESSAGE, null, null, currentName).toString();
        if (newName != null && !newName.trim().isEmpty() && !newName.equals(currentName)) {
            selectedNode.setUserObject(newName.trim());
            treeModel.reload();
            expandAllNodes();
            statusLabel.setText("تم تعديل المجموعة إلى: " + newName);
        }
    }

    private void deleteCategory() {
        DefaultMutableTreeNode selectedNode =
                (DefaultMutableTreeNode) categoryTree.getLastSelectedPathComponent();
        if (selectedNode == null || selectedNode.isRoot())
            return;

        String categoryName = selectedNode.toString();

        int result = JOptionPane.showConfirmDialog(this,
                "هل أنت متأكد من حذف المجموعة: " + categoryName + "؟\n"
                        + "سيتم حذف جميع المجموعات الفرعية أيضاً.\n"
                        + "هذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف", JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            try {
                // حذف المجموعة
                DefaultMutableTreeNode parent = (DefaultMutableTreeNode) selectedNode.getParent();
                parent.remove(selectedNode);
                treeModel.reload();

                statusLabel.setText("تم حذف المجموعة بنجاح");

            } catch (Exception e) {
                JOptionPane.showMessageDialog(this, "خطأ في حذف المجموعة: " + e.getMessage(), "خطأ",
                        JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    // فئة مخصصة لعرض الشجرة
    private class CategoryTreeCellRenderer extends DefaultTreeCellRenderer {
        @Override
        public Component getTreeCellRendererComponent(JTree tree, Object value, boolean sel,
                boolean expanded, boolean leaf, int row, boolean hasFocus) {

            super.getTreeCellRendererComponent(tree, value, sel, expanded, leaf, row, hasFocus);

            setFont(arabicFont);
            setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

            // تخصيص الأيقونات
            if (leaf && !((DefaultMutableTreeNode) value).isRoot()) {
                setIcon(UIManager.getIcon("FileView.fileIcon"));
            } else if (!leaf) {
                if (expanded) {
                    setIcon(UIManager.getIcon("Tree.openIcon"));
                } else {
                    setIcon(UIManager.getIcon("Tree.closedIcon"));
                }
            }

            return this;
        }
    }
}
