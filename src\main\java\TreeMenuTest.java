import javax.swing.*;
import java.awt.*;
import java.util.Locale;

/**
 * اختبار النافذة الرئيسية مع القائمة الشجرية
 * Tree Menu Main Window Test
 */
public class TreeMenuTest {
    
    public static void main(String[] args) {
        // تعيين الترميز والمحلية العربية
        System.setProperty("file.encoding", "UTF-8");
        Locale.setDefault(new Locale("ar", "SA"));
        
        SwingUtilities.invokeLater(() -> {
            try {
                // تطبيق المظهر المحفوظ
                SettingsManager.applyStoredTheme();
                
                // إنشاء النافذة الرئيسية المحسنة
                EnhancedMainWindow mainWindow = new EnhancedMainWindow();
                mainWindow.setVisible(true);
                
                System.out.println("تم تشغيل النافذة الرئيسية مع القائمة الشجرية بنجاح!");
                
            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(null, 
                    "حدث خطأ في تشغيل التطبيق: " + e.getMessage(),
                    "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
