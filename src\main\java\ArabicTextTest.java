import javax.swing.*;
import java.awt.*;

/**
 * اختبار النص العربي
 * Arabic Text Test
 */
public class ArabicTextTest {
    
    public static void main(String[] args) {
        // إعداد خصائص النظام للنص العربي
        System.setProperty("file.encoding", "UTF-8");
        System.setProperty("user.language", "ar");
        System.setProperty("user.country", "SA");
        System.setProperty("awt.useSystemAAFontSettings", "lcd");
        System.setProperty("swing.aatext", "true");
        
        SwingUtilities.invokeLater(() -> {
            createTestWindow();
        });
    }
    
    private static void createTestWindow() {
        // إنشاء النافذة
        JFrame frame = new JFrame("اختبار النص العربي - Arabic Text Test");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(600, 400);
        frame.setLocationRelativeTo(null);
        frame.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // تهيئة مدير النصوص العربية
        ArabicTextManager arabicManager = ArabicTextManager.getInstance();
        
        // إنشاء اللوحة الرئيسية
        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BoxLayout(mainPanel, BoxLayout.Y_AXIS));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // عنوان الاختبار
        JLabel titleLabel = new JLabel("اختبار عرض النص العربي");
        titleLabel.setFont(arabicManager.getArabicFont(Font.BOLD, 18f));
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        arabicManager.setupArabicComponent(titleLabel);
        
        // نص تجريبي
        String[] testTexts = {
            "مرحباً بك في نظام إدارة الشحنات",
            "إدارة الأصناف - وحدات القياس - مجموعات الأصناف",
            "بيانات الأصناف - تقارير الأصناف",
            "النص العربي يجب أن يظهر بوضوح وبدون تشويش",
            "الأرقام: ١٢٣٤٥٦٧٨٩٠",
            "English text should also work fine",
            "Mixed text: النص المختلط English و العربي"
        };
        
        // إضافة النصوص التجريبية
        for (String text : testTexts) {
            JLabel label = new JLabel(text);
            label.setFont(arabicManager.getArabicFont());
            arabicManager.setupArabicComponent(label);
            label.setBorder(BorderFactory.createEmptyBorder(5, 0, 5, 0));
            mainPanel.add(label);
        }
        
        // إضافة مساحة
        mainPanel.add(Box.createVerticalStrut(20));
        
        // معلومات الخط المستخدم
        Font currentFont = arabicManager.getArabicFont();
        JLabel fontInfoLabel = new JLabel(String.format(
            "الخط المستخدم: %s - الحجم: %d", 
            currentFont.getFontName(), 
            currentFont.getSize()
        ));
        fontInfoLabel.setFont(arabicManager.getArabicFont(Font.ITALIC, 12f));
        arabicManager.setupArabicComponent(fontInfoLabel);
        mainPanel.add(fontInfoLabel);
        
        // اختبار عرض النص
        boolean canDisplay = arabicManager.testArabicDisplay();
        JLabel testResultLabel = new JLabel(
            "نتيجة اختبار العرض: " + (canDisplay ? "نجح ✓" : "فشل ✗")
        );
        testResultLabel.setFont(arabicManager.getArabicFont(Font.BOLD, 12f));
        testResultLabel.setForeground(canDisplay ? Color.GREEN : Color.RED);
        arabicManager.setupArabicComponent(testResultLabel);
        mainPanel.add(testResultLabel);
        
        // إضافة مساحة
        mainPanel.add(Box.createVerticalStrut(20));
        
        // زر الإغلاق
        JButton closeButton = new JButton("إغلاق النافذة");
        closeButton.setFont(arabicManager.getArabicFont());
        arabicManager.setupArabicComponent(closeButton);
        closeButton.addActionListener(e -> System.exit(0));
        
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        buttonPanel.add(closeButton);
        mainPanel.add(buttonPanel);
        
        // إضافة اللوحة للنافذة
        frame.add(mainPanel);
        
        // عرض النافذة
        frame.setVisible(true);
        
        // طباعة معلومات الخطوط في وحدة التحكم
        System.out.println("=== معلومات اختبار النص العربي ===");
        arabicManager.printAvailableFonts();
        System.out.println("نتيجة الاختبار: " + (canDisplay ? "نجح" : "فشل"));
    }
}
