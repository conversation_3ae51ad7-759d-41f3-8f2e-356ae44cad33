package com.shipment.erp;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.image.Image;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.io.IOException;
import java.util.Locale;
import java.util.ResourceBundle;

/**
 * التطبيق الرئيسي لنظام إدارة الشحنات
 */
public class ShipERPApplication extends Application {

    private static final Logger logger = LoggerFactory.getLogger(ShipERPApplication.class);
    
    private static ApplicationContext applicationContext;
    private Stage primaryStage;
    private ResourceBundle resourceBundle;

    /**
     * نقطة البداية الرئيسية
     */
    public static void main(String[] args) {
        // تعيين خصائص النظام للعربية
        System.setProperty("user.language", "ar");
        System.setProperty("user.country", "SA");
        System.setProperty("file.encoding", "UTF-8");
        
        logger.info("بدء تشغيل نظام إدارة الشحنات...");
        
        try {
            // تحميل Spring Context
            applicationContext = new ClassPathXmlApplicationContext("applicationContext.xml");
            logger.info("تم تحميل Spring Context بنجاح");
            
            // بدء تطبيق JavaFX
            launch(args);
            
        } catch (Exception e) {
            logger.error("خطأ في بدء التطبيق", e);
            showErrorDialog("خطأ في بدء التطبيق", "حدث خطأ أثناء بدء التطبيق: " + e.getMessage());
            System.exit(1);
        }
    }

    @Override
    public void init() throws Exception {
        super.init();
        
        // تحميل ملف الرسائل العربية
        try {
            Locale arabicLocale = new Locale("ar", "SA");
            resourceBundle = ResourceBundle.getBundle("messages", arabicLocale);
            logger.info("تم تحميل ملف الرسائل العربية بنجاح");
        } catch (Exception e) {
            logger.warn("لم يتم العثور على ملف الرسائل العربية، سيتم استخدام الافتراضي", e);
        }
    }

    @Override
    public void start(Stage primaryStage) {
        this.primaryStage = primaryStage;
        
        try {
            // إعداد النافذة الرئيسية
            setupPrimaryStage();
            
            // عرض شاشة تسجيل الدخول أولاً
            showLoginScreen();
            
        } catch (Exception e) {
            logger.error("خطأ في إعداد الواجهة الرئيسية", e);
            showErrorDialog("خطأ في التطبيق", "حدث خطأ في إعداد الواجهة: " + e.getMessage());
        }
    }

    /**
     * إعداد النافذة الرئيسية
     */
    private void setupPrimaryStage() {
        primaryStage.setTitle(getMessage("app.title", "نظام إدارة الشحنات"));
        
        // تعيين الأيقونة
        try {
            Image icon = new Image(getClass().getResourceAsStream("/images/app-icon.png"));
            primaryStage.getIcons().add(icon);
        } catch (Exception e) {
            logger.warn("لم يتم العثور على أيقونة التطبيق", e);
        }
        
        // إعدادات النافذة
        primaryStage.setMinWidth(1000);
        primaryStage.setMinHeight(700);
        primaryStage.setMaximized(true);
        
        // معالج إغلاق التطبيق
        primaryStage.setOnCloseRequest(event -> {
            event.consume();
            handleApplicationExit();
        });
    }

    /**
     * عرض شاشة تسجيل الدخول
     */
    private void showLoginScreen() {
        try {
            FXMLLoader loader = new FXMLLoader();
            loader.setLocation(getClass().getResource("/fxml/login.fxml"));
            loader.setResources(resourceBundle);
            
            Scene loginScene = new Scene(loader.load());
            
            // تطبيق ملف CSS للعربية
            loginScene.getStylesheets().add(getClass().getResource("/styles/arabic-rtl.css").toExternalForm());
            
            primaryStage.setScene(loginScene);
            primaryStage.show();
            
            logger.info("تم عرض شاشة تسجيل الدخول");
            
        } catch (IOException e) {
            logger.error("خطأ في تحميل شاشة تسجيل الدخول", e);
            showErrorDialog("خطأ", "لم يتم العثور على شاشة تسجيل الدخول");
        }
    }

    /**
     * عرض الواجهة الرئيسية بعد تسجيل الدخول
     */
    public void showMainInterface() {
        try {
            FXMLLoader loader = new FXMLLoader();
            loader.setLocation(getClass().getResource("/fxml/main.fxml"));
            loader.setResources(resourceBundle);
            
            Scene mainScene = new Scene(loader.load());
            
            // تطبيق ملف CSS للعربية
            mainScene.getStylesheets().add(getClass().getResource("/styles/arabic-rtl.css").toExternalForm());
            
            primaryStage.setScene(mainScene);
            primaryStage.setMaximized(true);
            
            logger.info("تم عرض الواجهة الرئيسية");
            
        } catch (IOException e) {
            logger.error("خطأ في تحميل الواجهة الرئيسية", e);
            showErrorDialog("خطأ", "لم يتم العثور على الواجهة الرئيسية");
        }
    }

    /**
     * معالجة إغلاق التطبيق
     */
    private void handleApplicationExit() {
        Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
        confirmDialog.setTitle(getMessage("confirm.exit", "تأكيد الخروج"));
        confirmDialog.setHeaderText(null);
        confirmDialog.setContentText(getMessage("confirm.exit.message", "هل تريد الخروج من البرنامج؟"));
        
        confirmDialog.showAndWait().ifPresent(response -> {
            if (response == javafx.scene.control.ButtonType.OK) {
                logger.info("إغلاق التطبيق...");
                
                // إغلاق Spring Context
                if (applicationContext instanceof ClassPathXmlApplicationContext) {
                    ((ClassPathXmlApplicationContext) applicationContext).close();
                }
                
                Platform.exit();
                System.exit(0);
            }
        });
    }

    /**
     * عرض رسالة خطأ
     */
    private static void showErrorDialog(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    /**
     * الحصول على رسالة من ملف الموارد
     */
    private String getMessage(String key, String defaultValue) {
        if (resourceBundle != null && resourceBundle.containsKey(key)) {
            return resourceBundle.getString(key);
        }
        return defaultValue;
    }

    /**
     * الحصول على Spring Application Context
     */
    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * الحصول على Bean من Spring Context
     */
    public static <T> T getBean(Class<T> beanClass) {
        return applicationContext.getBean(beanClass);
    }

    /**
     * الحصول على Bean من Spring Context بالاسم
     */
    public static Object getBean(String beanName) {
        return applicationContext.getBean(beanName);
    }

    /**
     * الحصول على النافذة الرئيسية
     */
    public Stage getPrimaryStage() {
        return primaryStage;
    }

    /**
     * الحصول على ملف الموارد
     */
    public ResourceBundle getResourceBundle() {
        return resourceBundle;
    }

    @Override
    public void stop() throws Exception {
        super.stop();
        logger.info("تم إيقاف التطبيق");
    }
}
