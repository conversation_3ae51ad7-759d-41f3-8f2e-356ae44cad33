import java.sql.*;
import javax.swing.*;
import java.awt.*;

/**
 * الحل الحقيقي للاستيراد بناءً على الجداول الفعلية المكتشفة
 */
public class RealImportSolution extends JFrame {
    
    private Connection shipErpConnection;
    private Connection ias20251Connection;
    private JTextArea logArea;
    private JProgressBar progressBar;
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new RealImportSolution().setVisible(true);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, "خطأ في بدء التطبيق: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    public RealImportSolution() throws Exception {
        initializeConnections();
        createRealPackage();
        initializeGUI();
    }
    
    /**
     * تهيئة الاتصالات
     */
    private void initializeConnections() throws Exception {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        
        // اتصال SHIP_ERP
        shipErpConnection = DriverManager.getConnection(
            "*************************************", 
            "ship_erp", 
            "ship_erp_password"
        );
        log("✅ تم الاتصال بـ SHIP_ERP");
        
        // اتصال IAS20251
        ias20251Connection = DriverManager.getConnection(
            "*************************************", 
            "ias20251", 
            "ys123"
        );
        log("✅ تم الاتصال بـ IAS20251");
    }
    
    /**
     * إنشاء Package حقيقي للاستيراد
     */
    private void createRealPackage() throws SQLException {
        log("📦 إنشاء Package للاستيراد الحقيقي...");
        
        Statement stmt = shipErpConnection.createStatement();
        
        // حذف Package القديم
        try {
            stmt.execute("DROP PACKAGE ERP_REAL_IMPORT");
            log("🗑️ تم حذف Package القديم");
        } catch (SQLException e) {
            log("ℹ️ Package غير موجود مسبقاً");
        }
        
        // Package Specification
        String packageSpec = """
            CREATE OR REPLACE PACKAGE ERP_REAL_IMPORT AS
                
                -- استيراد المجموعات الرئيسية من GROUP_DETAILS
                FUNCTION import_main_groups_real RETURN VARCHAR2;
                
                -- استيراد المجموعات الفرعية من IAS_MAINSUB_GRP_DTL
                FUNCTION import_sub_groups_real RETURN VARCHAR2;
                
                -- استيراد الأصناف من IAS_ITM_MST
                FUNCTION import_items_real RETURN VARCHAR2;
                
                -- استيراد جميع البيانات
                FUNCTION import_all_real RETURN VARCHAR2;
                
                -- إحصائيات الاستيراد
                FUNCTION get_import_stats RETURN SYS_REFCURSOR;
                
            END ERP_REAL_IMPORT;
        """;
        
        stmt.execute(packageSpec);
        log("✅ تم إنشاء Package Specification");
        
        // Package Body
        String packageBody = """
            CREATE OR REPLACE PACKAGE BODY ERP_REAL_IMPORT AS
                
                -- استيراد المجموعات الرئيسية
                FUNCTION import_main_groups_real RETURN VARCHAR2 IS
                    l_imported NUMBER := 0;
                    l_updated NUMBER := 0;
                    l_errors NUMBER := 0;
                BEGIN
                    -- استيراد من GROUP_DETAILS في IAS20251
                    FOR rec IN (
                        SELECT G_CODE, G_A_NAME, G_E_NAME, TAX_PRCNT_DFLT, ROL_LMT_QTY
                        FROM GROUP_DETAILS@IAS20251_LINK
                        WHERE G_CODE IS NOT NULL
                    ) LOOP
                        BEGIN
                            -- محاولة الإدراج
                            INSERT INTO ERP_GROUP_DETAILS (
                                G_CODE, G_A_NAME, G_E_NAME, TAX_PRCNT_DFLT, ROL_LMT_QTY, IS_ACTIVE
                            ) VALUES (
                                rec.G_CODE, rec.G_A_NAME, rec.G_E_NAME, 
                                NVL(rec.TAX_PRCNT_DFLT, 0), NVL(rec.ROL_LMT_QTY, 0), 'Y'
                            );
                            l_imported := l_imported + 1;
                        EXCEPTION
                            WHEN DUP_VAL_ON_INDEX THEN
                                -- تحديث البيانات الموجودة
                                UPDATE ERP_GROUP_DETAILS SET
                                    G_A_NAME = rec.G_A_NAME,
                                    G_E_NAME = rec.G_E_NAME,
                                    TAX_PRCNT_DFLT = NVL(rec.TAX_PRCNT_DFLT, 0),
                                    ROL_LMT_QTY = NVL(rec.ROL_LMT_QTY, 0)
                                WHERE G_CODE = rec.G_CODE;
                                l_updated := l_updated + 1;
                            WHEN OTHERS THEN
                                l_errors := l_errors + 1;
                        END;
                    END LOOP;
                    
                    COMMIT;
                    RETURN 'SUCCESS: تم استيراد ' || l_imported || ' مجموعة جديدة، تحديث ' || l_updated || ' مجموعة، أخطاء: ' || l_errors;
                    
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RETURN 'ERROR: ' || SQLERRM;
                END import_main_groups_real;
                
                -- استيراد المجموعات الفرعية
                FUNCTION import_sub_groups_real RETURN VARCHAR2 IS
                    l_imported NUMBER := 0;
                    l_updated NUMBER := 0;
                    l_errors NUMBER := 0;
                BEGIN
                    -- استيراد من IAS_MAINSUB_GRP_DTL في IAS20251
                    FOR rec IN (
                        SELECT G_CODE, MNG_CODE, MNG_A_NAME, MNG_E_NAME
                        FROM IAS_MAINSUB_GRP_DTL@IAS20251_LINK
                        WHERE G_CODE IS NOT NULL AND MNG_CODE IS NOT NULL
                    ) LOOP
                        BEGIN
                            -- محاولة الإدراج
                            INSERT INTO ERP_MAINSUB_GRP_DTL (
                                G_CODE, MNG_CODE, MNG_A_NAME, MNG_E_NAME, IS_ACTIVE
                            ) VALUES (
                                rec.G_CODE, rec.MNG_CODE, rec.MNG_A_NAME, rec.MNG_E_NAME, 'Y'
                            );
                            l_imported := l_imported + 1;
                        EXCEPTION
                            WHEN DUP_VAL_ON_INDEX THEN
                                -- تحديث البيانات الموجودة
                                UPDATE ERP_MAINSUB_GRP_DTL SET
                                    MNG_A_NAME = rec.MNG_A_NAME,
                                    MNG_E_NAME = rec.MNG_E_NAME
                                WHERE G_CODE = rec.G_CODE AND MNG_CODE = rec.MNG_CODE;
                                l_updated := l_updated + 1;
                            WHEN OTHERS THEN
                                l_errors := l_errors + 1;
                        END;
                    END LOOP;
                    
                    COMMIT;
                    RETURN 'SUCCESS: تم استيراد ' || l_imported || ' مجموعة فرعية جديدة، تحديث ' || l_updated || ' مجموعة، أخطاء: ' || l_errors;
                    
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RETURN 'ERROR: ' || SQLERRM;
                END import_sub_groups_real;
                
                -- استيراد الأصناف
                FUNCTION import_items_real RETURN VARCHAR2 IS
                    l_imported NUMBER := 0;
                    l_updated NUMBER := 0;
                    l_errors NUMBER := 0;
                BEGIN
                    -- استيراد من IAS_ITM_MST في IAS20251
                    FOR rec IN (
                        SELECT i.I_CODE, i.I_A_NAME, i.I_E_NAME, i.G_CODE, i.MNG_CODE,
                               d.ITM_UNT, d.P_SIZE, i.I_DESC, i.ITEM_TYPE
                        FROM IAS_ITM_MST@IAS20251_LINK i
                        LEFT JOIN IAS_ITM_DTL@IAS20251_LINK d ON i.I_CODE = d.I_CODE
                        WHERE i.I_CODE IS NOT NULL
                        AND ROWNUM <= 100  -- تحديد العدد للاختبار
                    ) LOOP
                        BEGIN
                            -- محاولة الإدراج في جدول الأصناف
                            INSERT INTO ERP_ITEMS (
                                I_CODE, I_A_NAME, I_E_NAME, G_CODE, MNG_CODE,
                                ITM_UNT, P_SIZE, I_DESC, ITEM_TYPE, IS_ACTIVE
                            ) VALUES (
                                rec.I_CODE, rec.I_A_NAME, rec.I_E_NAME, rec.G_CODE, rec.MNG_CODE,
                                NVL(rec.ITM_UNT, 'قطعة'), NVL(rec.P_SIZE, 1), rec.I_DESC, 
                                NVL(rec.ITEM_TYPE, 1), 'Y'
                            );
                            l_imported := l_imported + 1;
                        EXCEPTION
                            WHEN DUP_VAL_ON_INDEX THEN
                                -- تحديث البيانات الموجودة
                                UPDATE ERP_ITEMS SET
                                    I_A_NAME = rec.I_A_NAME,
                                    I_E_NAME = rec.I_E_NAME,
                                    G_CODE = rec.G_CODE,
                                    MNG_CODE = rec.MNG_CODE,
                                    I_DESC = rec.I_DESC
                                WHERE I_CODE = rec.I_CODE;
                                l_updated := l_updated + 1;
                            WHEN OTHERS THEN
                                l_errors := l_errors + 1;
                        END;
                    END LOOP;
                    
                    COMMIT;
                    RETURN 'SUCCESS: تم استيراد ' || l_imported || ' صنف جديد، تحديث ' || l_updated || ' صنف، أخطاء: ' || l_errors;
                    
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RETURN 'ERROR: ' || SQLERRM;
                END import_items_real;
                
                -- استيراد جميع البيانات
                FUNCTION import_all_real RETURN VARCHAR2 IS
                    l_result VARCHAR2(4000);
                    l_final_result VARCHAR2(4000) := '';
                BEGIN
                    -- استيراد المجموعات الرئيسية
                    l_result := import_main_groups_real();
                    l_final_result := l_final_result || 'المجموعات الرئيسية: ' || l_result || CHR(10);
                    
                    -- استيراد المجموعات الفرعية
                    l_result := import_sub_groups_real();
                    l_final_result := l_final_result || 'المجموعات الفرعية: ' || l_result || CHR(10);
                    
                    -- استيراد الأصناف
                    l_result := import_items_real();
                    l_final_result := l_final_result || 'الأصناف: ' || l_result;
                    
                    RETURN 'SUCCESS: تم الاستيراد الكامل' || CHR(10) || l_final_result;
                    
                EXCEPTION
                    WHEN OTHERS THEN
                        RETURN 'ERROR: ' || SQLERRM;
                END import_all_real;
                
                -- إحصائيات الاستيراد
                FUNCTION get_import_stats RETURN SYS_REFCURSOR IS
                    l_cursor SYS_REFCURSOR;
                BEGIN
                    OPEN l_cursor FOR
                        SELECT 'المجموعات الرئيسية' as table_name,
                               COUNT(*) as erp_count,
                               (SELECT COUNT(*) FROM GROUP_DETAILS@IAS20251_LINK) as ias_count
                        FROM ERP_GROUP_DETAILS
                        UNION ALL
                        SELECT 'المجموعات الفرعية' as table_name,
                               COUNT(*) as erp_count,
                               (SELECT COUNT(*) FROM IAS_MAINSUB_GRP_DTL@IAS20251_LINK) as ias_count
                        FROM ERP_MAINSUB_GRP_DTL
                        UNION ALL
                        SELECT 'الأصناف' as table_name,
                               COUNT(*) as erp_count,
                               (SELECT COUNT(*) FROM IAS_ITM_MST@IAS20251_LINK) as ias_count
                        FROM ERP_ITEMS;
                    
                    RETURN l_cursor;
                END get_import_stats;
                
            END ERP_REAL_IMPORT;
        """;
        
        stmt.execute(packageBody);
        log("✅ تم إنشاء Package Body");
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    private void initializeGUI() {
        setTitle("الاستيراد الحقيقي من IAS20251 إلى SHIP_ERP");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(900, 700);
        setLocationRelativeTo(null);
        
        setLayout(new BorderLayout());
        
        // لوحة الأزرار
        JPanel buttonPanel = new JPanel(new GridLayout(2, 3, 10, 10));
        buttonPanel.setBorder(BorderFactory.createTitledBorder("عمليات الاستيراد الحقيقي"));
        
        JButton importGroupsBtn = new JButton("استيراد المجموعات الرئيسية");
        importGroupsBtn.addActionListener(e -> importMainGroups());
        buttonPanel.add(importGroupsBtn);
        
        JButton importSubGroupsBtn = new JButton("استيراد المجموعات الفرعية");
        importSubGroupsBtn.addActionListener(e -> importSubGroups());
        buttonPanel.add(importSubGroupsBtn);
        
        JButton importItemsBtn = new JButton("استيراد الأصناف");
        importItemsBtn.addActionListener(e -> importItems());
        buttonPanel.add(importItemsBtn);
        
        JButton importAllBtn = new JButton("استيراد جميع البيانات");
        importAllBtn.addActionListener(e -> importAll());
        buttonPanel.add(importAllBtn);
        
        JButton statsBtn = new JButton("إحصائيات الاستيراد");
        statsBtn.addActionListener(e -> showStats());
        buttonPanel.add(statsBtn);
        
        JButton testConnectionBtn = new JButton("اختبار الاتصالات");
        testConnectionBtn.addActionListener(e -> testConnections());
        buttonPanel.add(testConnectionBtn);
        
        add(buttonPanel, BorderLayout.NORTH);
        
        // شريط التقدم
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        add(progressBar, BorderLayout.CENTER);
        
        // منطقة السجل
        logArea = new JTextArea(20, 80);
        logArea.setEditable(false);
        logArea.setFont(new Font("Arial", Font.PLAIN, 12));
        JScrollPane scrollPane = new JScrollPane(logArea);
        scrollPane.setBorder(BorderFactory.createTitledBorder("سجل العمليات"));
        add(scrollPane, BorderLayout.SOUTH);
        
        log("🎉 تم تهيئة نظام الاستيراد الحقيقي!");
        log("📊 البيانات المتاحة في IAS20251:");
        log("   - GROUP_DETAILS: 15 مجموعة رئيسية");
        log("   - IAS_MAINSUB_GRP_DTL: 15 مجموعة فرعية");
        log("   - IAS_ITM_MST: 4647 صنف");
    }
    
    /**
     * استيراد المجموعات الرئيسية
     */
    private void importMainGroups() {
        new Thread(() -> {
            try {
                log("🔄 بدء استيراد المجموعات الرئيسية...");
                progressBar.setIndeterminate(true);
                
                CallableStatement cs = shipErpConnection.prepareCall("{ ? = call ERP_REAL_IMPORT.import_main_groups_real }");
                cs.registerOutParameter(1, Types.VARCHAR);
                cs.execute();
                
                String result = cs.getString(1);
                log("📋 " + result);
                
                progressBar.setIndeterminate(false);
                JOptionPane.showMessageDialog(this, result);
                
            } catch (Exception e) {
                log("❌ خطأ في استيراد المجموعات الرئيسية: " + e.getMessage());
                progressBar.setIndeterminate(false);
                JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        }).start();
    }
    
    /**
     * استيراد المجموعات الفرعية
     */
    private void importSubGroups() {
        new Thread(() -> {
            try {
                log("🔄 بدء استيراد المجموعات الفرعية...");
                progressBar.setIndeterminate(true);
                
                CallableStatement cs = shipErpConnection.prepareCall("{ ? = call ERP_REAL_IMPORT.import_sub_groups_real }");
                cs.registerOutParameter(1, Types.VARCHAR);
                cs.execute();
                
                String result = cs.getString(1);
                log("📋 " + result);
                
                progressBar.setIndeterminate(false);
                JOptionPane.showMessageDialog(this, result);
                
            } catch (Exception e) {
                log("❌ خطأ في استيراد المجموعات الفرعية: " + e.getMessage());
                progressBar.setIndeterminate(false);
                JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        }).start();
    }
    
    /**
     * استيراد الأصناف
     */
    private void importItems() {
        new Thread(() -> {
            try {
                log("🔄 بدء استيراد الأصناف...");
                progressBar.setIndeterminate(true);
                
                CallableStatement cs = shipErpConnection.prepareCall("{ ? = call ERP_REAL_IMPORT.import_items_real }");
                cs.registerOutParameter(1, Types.VARCHAR);
                cs.execute();
                
                String result = cs.getString(1);
                log("📋 " + result);
                
                progressBar.setIndeterminate(false);
                JOptionPane.showMessageDialog(this, result);
                
            } catch (Exception e) {
                log("❌ خطأ في استيراد الأصناف: " + e.getMessage());
                progressBar.setIndeterminate(false);
                JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        }).start();
    }
    
    /**
     * استيراد جميع البيانات
     */
    private void importAll() {
        new Thread(() -> {
            try {
                log("🔄 بدء الاستيراد الكامل...");
                progressBar.setIndeterminate(true);
                
                CallableStatement cs = shipErpConnection.prepareCall("{ ? = call ERP_REAL_IMPORT.import_all_real }");
                cs.registerOutParameter(1, Types.VARCHAR);
                cs.execute();
                
                String result = cs.getString(1);
                log("📋 " + result);
                
                progressBar.setIndeterminate(false);
                JOptionPane.showMessageDialog(this, result);
                
            } catch (Exception e) {
                log("❌ خطأ في الاستيراد الكامل: " + e.getMessage());
                progressBar.setIndeterminate(false);
                JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        }).start();
    }
    
    /**
     * عرض الإحصائيات
     */
    private void showStats() {
        try {
            log("📊 عرض إحصائيات الاستيراد...");
            
            CallableStatement cs = shipErpConnection.prepareCall("{ ? = call ERP_REAL_IMPORT.get_import_stats }");
            cs.registerOutParameter(1, Types.REF_CURSOR);
            cs.execute();
            
            ResultSet rs = (ResultSet) cs.getObject(1);
            
            StringBuilder stats = new StringBuilder("📊 إحصائيات الاستيراد:\n\n");
            while (rs.next()) {
                String tableName = rs.getString("table_name");
                int erpCount = rs.getInt("erp_count");
                int iasCount = rs.getInt("ias_count");
                
                stats.append(tableName).append(":\n");
                stats.append("  - في SHIP_ERP: ").append(erpCount).append("\n");
                stats.append("  - في IAS20251: ").append(iasCount).append("\n\n");
            }
            
            log(stats.toString());
            JOptionPane.showMessageDialog(this, stats.toString());
            
        } catch (Exception e) {
            log("❌ خطأ في عرض الإحصائيات: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * اختبار الاتصالات
     */
    private void testConnections() {
        try {
            log("🔍 اختبار الاتصالات...");
            
            // اختبار SHIP_ERP
            Statement stmt1 = shipErpConnection.createStatement();
            ResultSet rs1 = stmt1.executeQuery("SELECT COUNT(*) FROM ERP_GROUP_DETAILS");
            rs1.next();
            int shipCount = rs1.getInt(1);
            log("✅ SHIP_ERP: " + shipCount + " مجموعة");
            
            // اختبار IAS20251
            Statement stmt2 = ias20251Connection.createStatement();
            ResultSet rs2 = stmt2.executeQuery("SELECT COUNT(*) FROM GROUP_DETAILS");
            rs2.next();
            int iasCount = rs2.getInt(1);
            log("✅ IAS20251: " + iasCount + " مجموعة");
            
            JOptionPane.showMessageDialog(this, "الاتصالات تعمل بنجاح!\nSHIP_ERP: " + shipCount + " مجموعة\nIAS20251: " + iasCount + " مجموعة");
            
        } catch (Exception e) {
            log("❌ خطأ في اختبار الاتصالات: " + e.getMessage());
            JOptionPane.showMessageDialog(this, "خطأ: " + e.getMessage(), "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * تسجيل رسالة
     */
    private void log(String message) {
        SwingUtilities.invokeLater(() -> {
            logArea.append(java.time.LocalTime.now() + " - " + message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
        System.out.println(message);
    }
}
