@echo off
title Oracle Connection Settings Report

echo ====================================
echo    Oracle Connection Settings Report
echo    تقرير إعدادات الاتصال بـ Oracle
echo ====================================
echo.

echo CURRENT CONNECTION SETTINGS:
echo ===========================
echo.

echo 1. DEFAULT SETTINGS (DatabaseConfig.java):
echo ------------------------------------------
echo Host: localhost
echo Port: 1521
echo Service Name: XE
echo Username: hr
echo Password: hr
echo.

echo 2. AUTOMATIC TEST SETTINGS (CompleteSystemTest.java):
echo -----------------------------------------------------
echo Host: localhost
echo Port: 1521
echo Service Name: orcl
echo Username: ysdba2
echo Password: ys123
echo.

echo 3. GUI DEFAULT SETTINGS (AdvancedSystemIntegrationWindow.java):
echo --------------------------------------------------------------
echo Host: localhost (default in hostField)
echo Port: 1521 (default in portField)
echo Service Name: orcl (default in serviceNameField)
echo Username: hr (default in usernameField)
echo Password: hr (default in passwordField)
echo.

echo ANALYSIS:
echo =========
echo.
echo The application has MULTIPLE connection configurations:
echo.
echo A. DatabaseConfig Class Defaults:
echo    - Uses hr/hr credentials (Oracle sample schema)
echo    - Connects to XE service (Oracle Express Edition)
echo.
echo B. Automatic Test (CompleteSystemTest):
echo    - Uses ysdba2/ys123 credentials (Custom user)
echo    - Connects to orcl service (Full Oracle Database)
echo.
echo C. GUI Interface Defaults:
echo    - Shows hr/hr in the form fields initially
echo    - User can change to any credentials
echo    - Most likely changed to ysdba2/ys123 during usage
echo.

echo CURRENT ACTIVE CONNECTION:
echo =========================
echo.
echo Based on the automatic test and recent usage:
echo.
echo ✅ Host: localhost
echo ✅ Port: 1521
echo ✅ Service: orcl
echo ✅ Username: ysdba2
echo ✅ Password: ys123
echo ✅ Schema: IAS20251 (for IAS tables)
echo.

echo ORACLE USER DETAILS:
echo ====================
echo.
echo Primary User: ysdba2
echo - This appears to be a custom Oracle user
echo - Has access to localhost:1521:orcl database
echo - Can access IAS20251 schema tables
echo - Used for IAS_ITM_MST and IAS_ITM_DTL tables
echo.
echo Default User: hr
echo - Oracle sample schema user
echo - Typically used for testing/demo purposes
echo - May not have access to IAS tables
echo.

echo RECOMMENDATIONS:
echo ================
echo.
echo 1. Standardize on ysdba2/ys123 for consistency
echo 2. Update DatabaseConfig defaults to match
echo 3. Ensure ysdba2 has proper permissions for IAS20251 schema
echo 4. Consider using connection pooling for production
echo.

echo ====================================
echo    End of Connection Settings Report
echo ====================================
pause
