import java.sql.*;

public class SimpleTestWindow {
    public static void main(String[] args) {
        try {
            System.out.println("🚀 بدء اختبار النافذة...");
            
            // اختبار الاتصال أولاً
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            System.out.println("✅ تم الاتصال بقاعدة البيانات");
            
            // اختبار وجود الجداول
            DatabaseMetaData metaData = conn.getMetaData();
            ResultSet rs = metaData.getTables(null, null, "ERP_GROUP_DETAILS", new String[]{"TABLE"});
            if (rs.next()) {
                System.out.println("✅ الجدول ERP_GROUP_DETAILS موجود");
            } else {
                System.out.println("❌ الجدول ERP_GROUP_DETAILS غير موجود");
            }
            rs.close();
            conn.close();
            
            // اختبار إنشاء النافذة
            System.out.println("🔄 محاولة إنشاء النافذة...");
            ItemGroupsManagementWindow window = new ItemGroupsManagementWindow();
            System.out.println("✅ تم إنشاء النافذة بنجاح");
            
            window.setVisible(true);
            System.out.println("✅ تم عرض النافذة");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
