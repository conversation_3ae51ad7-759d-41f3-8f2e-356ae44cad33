package com.shipment.erp.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * كيان الشركة
 * يمثل بيانات الشركة الأساسية
 */
@Entity
@Table(name = "COMPANIES")
@SequenceGenerator(name = "company_seq", sequenceName = "SEQ_COMPANY", allocationSize = 1)
public class Company extends BaseEntity {

    @Column(name = "NAME", nullable = false, length = 200)
    @NotBlank(message = "اسم الشركة مطلوب")
    @Size(max = 200, message = "اسم الشركة يجب أن يكون أقل من 200 حرف")
    private String name;

    @Column(name = "NAME_EN", length = 200)
    @Size(max = 200, message = "الاسم الإنجليزي يجب أن يكون أقل من 200 حرف")
    private String nameEn;

    @Column(name = "ADDRESS", length = 500)
    @Size(max = 500, message = "العنوان يجب أن يكون أقل من 500 حرف")
    private String address;

    @Column(name = "CITY", length = 100)
    @Size(max = 100, message = "المدينة يجب أن تكون أقل من 100 حرف")
    private String city;

    @Column(name = "COUNTRY", length = 100)
    @Size(max = 100, message = "البلد يجب أن يكون أقل من 100 حرف")
    private String country;

    @Column(name = "PHONE", length = 50)
    @Size(max = 50, message = "رقم الهاتف يجب أن يكون أقل من 50 حرف")
    private String phone;

    @Column(name = "FAX", length = 50)
    @Size(max = 50, message = "رقم الفاكس يجب أن يكون أقل من 50 حرف")
    private String fax;

    @Column(name = "EMAIL", length = 100)
    @Email(message = "البريد الإلكتروني غير صحيح")
    @Size(max = 100, message = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")
    private String email;

    @Column(name = "WEBSITE", length = 200)
    @Size(max = 200, message = "الموقع الإلكتروني يجب أن يكون أقل من 200 حرف")
    private String website;

    @Column(name = "TAX_NUMBER", length = 50)
    @Size(max = 50, message = "الرقم الضريبي يجب أن يكون أقل من 50 حرف")
    private String taxNumber;

    @Column(name = "COMMERCIAL_REGISTER", length = 50)
    @Size(max = 50, message = "السجل التجاري يجب أن يكون أقل من 50 حرف")
    private String commercialRegister;

    @Lob
    @Column(name = "LOGO")
    private byte[] logo;

    @Column(name = "IS_ACTIVE", nullable = false)
    private Boolean isActive = true;

    @OneToMany(mappedBy = "company", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Branch> branches = new ArrayList<>();

    /**
     * Constructor افتراضي
     */
    public Company() {
        super();
    }

    /**
     * Constructor مع الاسم
     */
    public Company(String name) {
        this();
        this.name = name;
    }

    // Getters and Setters

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getCommercialRegister() {
        return commercialRegister;
    }

    public void setCommercialRegister(String commercialRegister) {
        this.commercialRegister = commercialRegister;
    }

    public byte[] getLogo() {
        return logo;
    }

    public void setLogo(byte[] logo) {
        this.logo = logo;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public List<Branch> getBranches() {
        return branches;
    }

    public void setBranches(List<Branch> branches) {
        this.branches = branches;
    }

    /**
     * إضافة فرع جديد
     */
    public void addBranch(Branch branch) {
        branches.add(branch);
        branch.setCompany(this);
    }

    /**
     * إزالة فرع
     */
    public void removeBranch(Branch branch) {
        branches.remove(branch);
        branch.setCompany(null);
    }

    @Override
    public String toString() {
        return "Company{" +
                "id=" + getId() +
                ", name='" + name + '\'' +
                ", nameEn='" + nameEn + '\'' +
                ", city='" + city + '\'' +
                ", country='" + country + '\'' +
                ", isActive=" + isActive +
                '}';
    }
}
