/**
 * اختبار سريع للمكتبات
 * Quick Library Test
 */
public class QuickLibraryTest {
    
    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("   اختبار سريع للمكتبات");
        System.out.println("   Quick Library Test");
        System.out.println("====================================");
        System.out.println();
        
        // فحص classpath
        String classpath = System.getProperty("java.class.path");
        System.out.println("Classpath: " + classpath);
        System.out.println();
        
        // فحص مكتبة Oracle JDBC
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            System.out.println("✅ Oracle JDBC Driver محمل بنجاح");
            
            // إنشاء instance للتأكد
            oracle.jdbc.driver.OracleDriver driver = new oracle.jdbc.driver.OracleDriver();
            System.out.println("✅ تم إنشاء instance من OracleDriver");
            System.out.println("إصدار التعريف: " + driver.getMajorVersion() + "." + driver.getMinorVersion());
            
        } catch (Exception e) {
            System.out.println("❌ فشل في تحميل Oracle JDBC: " + e.getMessage());
        }
        
        // فحص مكتبة orai18n
        if (classpath.contains("orai18n.jar")) {
            System.out.println("✅ مكتبة orai18n.jar موجودة في classpath");
        } else {
            System.out.println("❌ مكتبة orai18n.jar مفقودة من classpath");
        }
        
        // فحص المكتبات الأخرى
        String[] libraries = {
            "commons-dbcp2",
            "commons-pool2", 
            "commons-logging",
            "json"
        };
        
        for (String lib : libraries) {
            if (classpath.contains(lib)) {
                System.out.println("✅ " + lib + " موجود");
            } else {
                System.out.println("⚠️ " + lib + " مفقود (اختياري)");
            }
        }
        
        System.out.println();
        System.out.println("====================================");
        System.out.println("   انتهى الاختبار");
        System.out.println("====================================");
    }
}
