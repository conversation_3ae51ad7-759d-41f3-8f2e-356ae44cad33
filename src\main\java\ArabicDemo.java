import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

/**
 * عرض توضيحي محسن لنظام إدارة الشحنات مع دعم العربية و RTL
 */
public class ArabicDemo {
    
    private JFrame loginFrame;
    private JFrame mainFrame;
    private JTextField usernameField;
    private JPasswordField passwordField;
    private JLabel statusLabel;
    private Timer clockTimer;
    
    // النصوص العربية
    private static final String APP_TITLE = "\u0646\u0638\u0627\u0645 \u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0634\u062D\u0646\u0627\u062A"; // نظام إدارة الشحنات
    private static final String LOGIN_TITLE = "\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644"; // تسجيل الدخول
    private static final String USERNAME = "\u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645:"; // اسم المستخدم:
    private static final String PASSWORD = "\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631:"; // كلمة المرور:
    private static final String LOGIN_BTN = "\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644"; // تسجيل الدخول
    private static final String CANCEL_BTN = "\u0625\u0644\u063A\u0627\u0621"; // إلغاء
    private static final String READY = "\u062C\u0627\u0647\u0632"; // جاهز
    private static final String WELCOME = "\u0645\u0631\u062D\u0628\u0627\u064B \u0628\u0643 \u0641\u064A " + APP_TITLE; // مرحباً بك في نظام إدارة الشحنات
    
    public static void main(String[] args) {
        // تعيين الترميز والمحلية العربية
        System.setProperty("file.encoding", "UTF-8");
        Locale.setDefault(new Locale("ar", "SA"));

        SwingUtilities.invokeLater(() -> {
            // تطبيق المظهر المحفوظ قبل إنشاء أي واجهة
            SettingsManager.applyStoredTheme();

            new ArabicDemo().showLoginScreen();
        });
    }
    
    /**
     * عرض شاشة تسجيل الدخول
     */
    private void showLoginScreen() {
        loginFrame = new JFrame(APP_TITLE + " - " + LOGIN_TITLE);
        loginFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        loginFrame.setSize(450, 350);
        loginFrame.setLocationRelativeTo(null);
        loginFrame.setResizable(false);
        
        // تعيين الخط العربي
        Font arabicFont = new Font("Arial Unicode MS", Font.PLAIN, 14);
        if (!arabicFont.getFamily().equals("Arial Unicode MS")) {
            arabicFont = new Font("Tahoma", Font.PLAIN, 14);
        }
        
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // العنوان
        JLabel titleLabel = new JLabel(APP_TITLE, SwingConstants.CENTER);
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 20));
        titleLabel.setForeground(new Color(0, 123, 255));
        mainPanel.add(titleLabel, BorderLayout.NORTH);
        
        // نموذج تسجيل الدخول
        JPanel formPanel = new JPanel(new GridBagLayout());
        formPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(15, 15, 15, 15);
        
        // اسم المستخدم
        gbc.gridx = 1; gbc.gridy = 0; gbc.anchor = GridBagConstraints.EAST;
        JLabel userLabel = new JLabel(USERNAME);
        userLabel.setFont(arabicFont);
        userLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        formPanel.add(userLabel, gbc);
        
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        usernameField = new JTextField("admin", 15);
        usernameField.setFont(arabicFont);
        usernameField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        formPanel.add(usernameField, gbc);
        
        // كلمة المرور
        gbc.gridx = 1; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE;
        JLabel passLabel = new JLabel(PASSWORD);
        passLabel.setFont(arabicFont);
        passLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        formPanel.add(passLabel, gbc);
        
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        passwordField = new JPasswordField("admin123", 15);
        passwordField.setFont(arabicFont);
        passwordField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        formPanel.add(passwordField, gbc);
        
        // أزرار
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 2; gbc.fill = GridBagConstraints.NONE;
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JButton loginButton = new JButton(LOGIN_BTN);
        loginButton.setFont(arabicFont);
        loginButton.setBackground(new Color(0, 123, 255));
        loginButton.setForeground(Color.WHITE);
        loginButton.setPreferredSize(new Dimension(120, 35));
        loginButton.addActionListener(new LoginActionListener());
        
        JButton cancelButton = new JButton(CANCEL_BTN);
        cancelButton.setFont(arabicFont);
        cancelButton.setPreferredSize(new Dimension(120, 35));
        cancelButton.addActionListener(e -> System.exit(0));
        
        buttonPanel.add(loginButton);
        buttonPanel.add(cancelButton);
        formPanel.add(buttonPanel, gbc);
        
        mainPanel.add(formPanel, BorderLayout.CENTER);
        
        // شريط الحالة
        statusLabel = new JLabel(READY, SwingConstants.CENTER);
        statusLabel.setFont(arabicFont);
        statusLabel.setBorder(BorderFactory.createLoweredBevelBorder());
        statusLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.add(statusLabel, BorderLayout.SOUTH);
        
        loginFrame.add(mainPanel);
        loginFrame.setVisible(true);
        
        // تركيز على حقل اسم المستخدم
        usernameField.requestFocus();
        
        // ربط Enter بتسجيل الدخول
        passwordField.addActionListener(new LoginActionListener());
    }
    
    /**
     * عرض الواجهة الرئيسية
     */
    private void showMainInterface() {
        loginFrame.dispose();

        mainFrame = new JFrame(APP_TITLE + " - " + "\u0627\u0644\u0648\u0627\u062C\u0647\u0629 \u0627\u0644\u0631\u0626\u064A\u0633\u064A\u0629"); // الواجهة الرئيسية
        mainFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);

        // جعل النافذة ملء الشاشة
        mainFrame.setExtendedState(JFrame.MAXIMIZED_BOTH);
        mainFrame.setUndecorated(false); // الاحتفاظ بشريط العنوان

        // تعيين الحد الأدنى لحجم النافذة
        mainFrame.setMinimumSize(new Dimension(1024, 768));
        
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        // تعيين اتجاه RTL للنافذة الرئيسية
        mainFrame.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // شريط القوائم المحسن
        JMenuBar menuBar = createEnhancedMenuBar(arabicFont);
        mainFrame.setJMenuBar(menuBar);
        
        // المحتوى الرئيسي مع شريط أدوات
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط الأدوات
        JToolBar toolBar = createToolBar(arabicFont);
        mainPanel.add(toolBar, BorderLayout.NORTH);

        // لوحة المحتوى الرئيسي
        JPanel contentMainPanel = new JPanel(new BorderLayout());
        contentMainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // الشريط الجانبي المحسن
        JPanel sidePanel = createEnhancedSidePanel(arabicFont);
        
        String[] menuItems = {
            "\u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0639\u0627\u0645\u0629", // الإعدادات العامة
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645\u064A\u0646", // إدارة المستخدمين
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0639\u0645\u0644\u0627\u062A", // إدارة العملات
            "\u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u0634\u0631\u0643\u0629", // بيانات الشركة
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0623\u0635\u0646\u0627\u0641", // إدارة الأصناف
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0645\u0648\u0631\u062F\u064A\u0646", // إدارة الموردين
            "\u0645\u062A\u0627\u0628\u0639\u0629 \u0627\u0644\u0634\u062D\u0646\u0627\u062A", // متابعة الشحنات
            "\u0627\u0644\u0625\u062F\u062E\u0627\u0644\u0627\u062A \u0627\u0644\u062C\u0645\u0631\u0643\u064A\u0629", // الإدخالات الجمركية
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u062A\u0643\u0627\u0644\u064A\u0641", // إدارة التكاليف
            "\u0627\u0644\u062A\u0642\u0627\u0631\u064A\u0631" // التقارير
        };
        
        JList<String> menuList = new JList<>(menuItems);
        menuList.setFont(arabicFont);
        menuList.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        menuList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        menuList.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                String selected = menuList.getSelectedValue();
                if (selected != null) {
                    showFeatureDialog(selected);
                }
            }
        });
        
        JScrollPane scrollPane = new JScrollPane(menuList);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        sidePanel.add(scrollPane, BorderLayout.CENTER);
        
        // منطقة المحتوى المحسنة مع تبويبات
        JTabbedPane tabbedPane = createEnhancedTabbedPane(arabicFont);
        

        
        contentMainPanel.add(sidePanel, BorderLayout.EAST); // الشريط الجانبي على اليمين
        contentMainPanel.add(tabbedPane, BorderLayout.CENTER); // التبويبات في المنتصف

        mainPanel.add(contentMainPanel, BorderLayout.CENTER);

        // شريط الحالة المحسن
        JPanel statusPanel = createEnhancedStatusBar(arabicFont);
        mainPanel.add(statusPanel, BorderLayout.SOUTH);

        mainFrame.add(mainPanel);
        mainFrame.setVisible(true);

        // تشغيل الساعة
        startClock();
    }

    /**
     * إنشاء شريط القوائم المحسن
     */
    private JMenuBar createEnhancedMenuBar(Font arabicFont) {
        JMenuBar menuBar = new JMenuBar();
        menuBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        menuBar.setBackground(new Color(248, 249, 250));
        menuBar.setBorder(BorderFactory.createMatteBorder(0, 0, 1, 0, new Color(222, 226, 230)));

        // قائمة الملف
        JMenu fileMenu = new JMenu("\u0645\u0644\u0641"); // ملف
        fileMenu.setFont(arabicFont);
        fileMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        fileMenu.add(createMenuItem("\u062C\u062F\u064A\u062F", arabicFont, "Ctrl+N")); // جديد
        fileMenu.add(createMenuItem("\u0641\u062A\u062D", arabicFont, "Ctrl+O")); // فتح
        fileMenu.addSeparator();
        fileMenu.add(createMenuItem("\u062D\u0641\u0638", arabicFont, "Ctrl+S")); // حفظ
        fileMenu.add(createMenuItem("\u062D\u0641\u0638 \u0628\u0627\u0633\u0645", arabicFont, "Ctrl+Shift+S")); // حفظ باسم
        fileMenu.addSeparator();
        fileMenu.add(createMenuItem("\u0637\u0628\u0627\u0639\u0629", arabicFont, "Ctrl+P")); // طباعة
        fileMenu.addSeparator();

        JMenuItem exitItem = createMenuItem("\u062E\u0631\u0648\u062C", arabicFont, "Alt+F4"); // خروج
        exitItem.addActionListener(e -> confirmExit());
        fileMenu.add(exitItem);

        // قائمة التحرير
        JMenu editMenu = new JMenu("\u062A\u062D\u0631\u064A\u0631"); // تحرير
        editMenu.setFont(arabicFont);
        editMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        editMenu.add(createMenuItem("\u062A\u0631\u0627\u062C\u0639", arabicFont, "Ctrl+Z")); // تراجع
        editMenu.add(createMenuItem("\u0625\u0639\u0627\u062F\u0629", arabicFont, "Ctrl+Y")); // إعادة
        editMenu.addSeparator();
        editMenu.add(createMenuItem("\u0642\u0635", arabicFont, "Ctrl+X")); // قص
        editMenu.add(createMenuItem("\u0646\u0633\u062E", arabicFont, "Ctrl+C")); // نسخ
        editMenu.add(createMenuItem("\u0644\u0635\u0642", arabicFont, "Ctrl+V")); // لصق

        // قائمة الإعدادات
        JMenu settingsMenu = new JMenu("\u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A"); // الإعدادات
        settingsMenu.setFont(arabicFont);
        settingsMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JMenuItem generalSettingsItem = createMenuItem("\u0627\u0644\u0645\u062A\u063A\u064A\u0631\u0627\u062A \u0627\u0644\u0639\u0627\u0645\u0629", arabicFont, null); // المتغيرات العامة
        generalSettingsItem.addActionListener(e -> {
            GeneralSettingsWindow settingsWindow = new GeneralSettingsWindow(mainFrame);
            settingsWindow.setVisible(true);
        });
        settingsMenu.add(generalSettingsItem);

        JMenuItem usersItem = createMenuItem("\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645\u064A\u0646", arabicFont, null); // إدارة المستخدمين
        usersItem.addActionListener(e -> {
            UserManagementWindow usersWindow = new UserManagementWindow(mainFrame);
            usersWindow.setVisible(true);
        });
        settingsMenu.add(usersItem);

        settingsMenu.add(createMenuItem("\u0627\u0644\u0639\u0645\u0644\u0627\u062A", arabicFont, null)); // العملات
        settingsMenu.add(createMenuItem("\u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u0634\u0631\u0643\u0629", arabicFont, null)); // بيانات الشركة

        // قائمة العرض
        JMenu viewMenu = new JMenu("\u0639\u0631\u0636"); // عرض
        viewMenu.setFont(arabicFont);
        viewMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JCheckBoxMenuItem showToolbarItem = new JCheckBoxMenuItem("\u0634\u0631\u064A\u0637 \u0627\u0644\u0623\u062F\u0648\u0627\u062A", true); // شريط الأدوات
        showToolbarItem.setFont(arabicFont);
        showToolbarItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        viewMenu.add(showToolbarItem);

        JCheckBoxMenuItem showStatusBarItem = new JCheckBoxMenuItem("\u0634\u0631\u064A\u0637 \u0627\u0644\u062D\u0627\u0644\u0629", true); // شريط الحالة
        showStatusBarItem.setFont(arabicFont);
        showStatusBarItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        viewMenu.add(showStatusBarItem);

        // قائمة المساعدة
        JMenu helpMenu = new JMenu("\u0645\u0633\u0627\u0639\u062F\u0629"); // مساعدة
        helpMenu.setFont(arabicFont);
        helpMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        helpMenu.add(createMenuItem("\u062F\u0644\u064A\u0644 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645", arabicFont, "F1")); // دليل المستخدم
        helpMenu.addSeparator();

        JMenuItem aboutItem = createMenuItem("\u062D\u0648\u0644 \u0627\u0644\u0628\u0631\u0646\u0627\u0645\u062C", arabicFont, null); // حول البرنامج
        aboutItem.addActionListener(e -> showAboutDialog());
        helpMenu.add(aboutItem);

        menuBar.add(fileMenu);
        menuBar.add(editMenu);
        menuBar.add(settingsMenu);
        menuBar.add(viewMenu);
        menuBar.add(helpMenu);

        return menuBar;
    }

    /**
     * إنشاء عنصر قائمة مع اختصار لوحة المفاتيح
     */
    private JMenuItem createMenuItem(String text, Font font, String accelerator) {
        JMenuItem item = new JMenuItem(text);
        item.setFont(font);
        item.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        if (accelerator != null) {
            item.setAccelerator(KeyStroke.getKeyStroke(accelerator));
        }

        return item;
    }

    /**
     * إنشاء شريط الأدوات المحسن
     */
    private JToolBar createToolBar(Font arabicFont) {
        JToolBar toolBar = new JToolBar();
        toolBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        toolBar.setFloatable(false);
        toolBar.setBackground(new Color(248, 249, 250));
        toolBar.setBorder(BorderFactory.createMatteBorder(0, 0, 1, 0, new Color(222, 226, 230)));
        toolBar.setPreferredSize(new Dimension(0, 50));

        // مجموعة أزرار الملف
        toolBar.add(createEnhancedToolBarButton("\u062C\u062F\u064A\u062F", "\u0625\u0646\u0634\u0627\u0621 \u0645\u0644\u0641 \u062C\u062F\u064A\u062F", arabicFont, new Color(40, 167, 69))); // جديد
        toolBar.add(createEnhancedToolBarButton("\u0641\u062A\u062D", "\u0641\u062A\u062D \u0645\u0644\u0641 \u0645\u0648\u062C\u0648\u062F", arabicFont, new Color(0, 123, 255))); // فتح
        toolBar.add(createEnhancedToolBarButton("\u062D\u0641\u0638", "\u062D\u0641\u0638 \u0627\u0644\u0645\u0644\u0641 \u0627\u0644\u062D\u0627\u0644\u064A", arabicFont, new Color(255, 193, 7))); // حفظ

        toolBar.addSeparator();

        // مجموعة أزرار الطباعة والتصدير
        toolBar.add(createEnhancedToolBarButton("\u0637\u0628\u0627\u0639\u0629", "\u0637\u0628\u0627\u0639\u0629 \u0627\u0644\u0645\u0633\u062A\u0646\u062F", arabicFont, new Color(111, 66, 193))); // طباعة
        toolBar.add(createEnhancedToolBarButton("\u062A\u0635\u062F\u064A\u0631", "\u062A\u0635\u062F\u064A\u0631 \u0625\u0644\u0649 PDF", arabicFont, new Color(220, 53, 69))); // تصدير

        toolBar.addSeparator();

        // مجموعة أزرار التحرير
        toolBar.add(createEnhancedToolBarButton("\u062A\u0631\u0627\u062C\u0639", "\u062A\u0631\u0627\u062C\u0639 \u0639\u0646 \u0627\u0644\u0639\u0645\u0644\u064A\u0629 \u0627\u0644\u0623\u062E\u064A\u0631\u0629", arabicFont, new Color(108, 117, 125))); // تراجع
        toolBar.add(createEnhancedToolBarButton("\u0625\u0639\u0627\u062F\u0629", "\u0625\u0639\u0627\u062F\u0629 \u0627\u0644\u0639\u0645\u0644\u064A\u0629", arabicFont, new Color(108, 117, 125))); // إعادة

        toolBar.addSeparator();

        // مجموعة أزرار النظام
        toolBar.add(createEnhancedToolBarButton("\u062A\u062D\u062F\u064A\u062B", "\u062A\u062D\u062F\u064A\u062B \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A", arabicFont, new Color(23, 162, 184))); // تحديث
        toolBar.add(createEnhancedToolBarButton("\u0625\u0639\u062F\u0627\u062F\u0627\u062A", "\u0641\u062A\u062D \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A", arabicFont, new Color(108, 117, 125))); // إعدادات

        // إضافة مساحة مرنة
        toolBar.add(Box.createHorizontalGlue());

        // مربع بحث محسن
        JPanel searchPanel = new JPanel(new BorderLayout());
        searchPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchPanel.setBackground(new Color(248, 249, 250));
        searchPanel.setPreferredSize(new Dimension(250, 35));

        JTextField searchField = new JTextField();
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(206, 212, 218), 1),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));
        searchField.setBackground(Color.WHITE);

        // نص توضيحي للبحث
        searchField.setText("\u0628\u062D\u062B \u0641\u064A \u0627\u0644\u0646\u0638\u0627\u0645..."); // بحث في النظام...
        searchField.setForeground(new Color(108, 117, 125));

        // إضافة مستمع للتركيز
        searchField.addFocusListener(new java.awt.event.FocusAdapter() {
            public void focusGained(java.awt.event.FocusEvent evt) {
                if (searchField.getText().equals("\u0628\u062D\u062B \u0641\u064A \u0627\u0644\u0646\u0638\u0627\u0645...")) {
                    searchField.setText("");
                    searchField.setForeground(new Color(73, 80, 87));
                }
            }
            public void focusLost(java.awt.event.FocusEvent evt) {
                if (searchField.getText().isEmpty()) {
                    searchField.setText("\u0628\u062D\u062B \u0641\u064A \u0627\u0644\u0646\u0638\u0627\u0645...");
                    searchField.setForeground(new Color(108, 117, 125));
                }
            }
        });

        searchPanel.add(searchField, BorderLayout.CENTER);
        toolBar.add(searchPanel);

        // زر البحث
        JButton searchButton = createEnhancedToolBarButton("\u0628\u062D\u062B", "\u0628\u062F\u0621 \u0627\u0644\u0628\u062D\u062B", arabicFont, new Color(40, 167, 69)); // بحث
        toolBar.add(searchButton);

        return toolBar;
    }

    /**
     * إنشاء زر شريط الأدوات المحسن
     */
    private JButton createEnhancedToolBarButton(String text, String tooltip, Font font, Color accentColor) {
        JButton button = new JButton(text);
        button.setFont(font);
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setContentAreaFilled(false);
        button.setMargin(new Insets(8, 15, 8, 15));
        button.setToolTipText(tooltip);
        button.setForeground(new Color(73, 80, 87));
        button.setPreferredSize(new Dimension(80, 35));

        // تأثيرات التفاعل المحسنة
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setContentAreaFilled(true);
                button.setBackground(accentColor);
                button.setForeground(Color.WHITE);
                button.setBorderPainted(true);
                button.setBorder(BorderFactory.createLineBorder(accentColor, 1));
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setContentAreaFilled(false);
                button.setBorderPainted(false);
                button.setForeground(new Color(73, 80, 87));
            }
            public void mousePressed(java.awt.event.MouseEvent evt) {
                button.setBackground(accentColor.darker());
            }
            public void mouseReleased(java.awt.event.MouseEvent evt) {
                button.setBackground(accentColor);
            }
        });

        // إضافة وظيفة للنقر
        button.addActionListener(e -> {
            JOptionPane.showMessageDialog(mainFrame,
                "\u062A\u0645 \u0627\u0644\u0646\u0642\u0631 \u0639\u0644\u0649: " + text + "\n" + tooltip, // تم النقر على:
                "\u0639\u0645\u0644\u064A\u0629 " + text, // عملية
                JOptionPane.INFORMATION_MESSAGE);
        });

        return button;
    }

    /**
     * إنشاء الشريط الجانبي المحسن
     */
    private JPanel createEnhancedSidePanel(Font arabicFont) {
        JPanel sidePanel = new JPanel(new BorderLayout());
        sidePanel.setPreferredSize(new Dimension(320, 0));
        sidePanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        sidePanel.setBackground(new Color(248, 249, 250));
        sidePanel.setBorder(BorderFactory.createMatteBorder(0, 1, 0, 0, new Color(222, 226, 230)));

        // عنوان الشريط الجانبي
        JLabel sideTitle = new JLabel("\u0627\u0644\u0642\u0627\u0626\u0645\u0629 \u0627\u0644\u0631\u0626\u064A\u0633\u064A\u0629"); // القائمة الرئيسية
        sideTitle.setFont(new Font("Tahoma", Font.BOLD, 16));
        sideTitle.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        sideTitle.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));
        sideTitle.setForeground(new Color(73, 80, 87));
        sidePanel.add(sideTitle, BorderLayout.NORTH);

        // القائمة الشجرية المحسنة
        String[] menuItems = {
            "\u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0639\u0627\u0645\u0629", // الإعدادات العامة
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645\u064A\u0646", // إدارة المستخدمين
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0639\u0645\u0644\u0627\u062A", // إدارة العملات
            "\u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u0634\u0631\u0643\u0629", // بيانات الشركة
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0623\u0635\u0646\u0627\u0641", // إدارة الأصناف
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0645\u0648\u0631\u062F\u064A\u0646", // إدارة الموردين
            "\u0645\u062A\u0627\u0628\u0639\u0629 \u0627\u0644\u0634\u062D\u0646\u0627\u062A", // متابعة الشحنات
            "\u0627\u0644\u0625\u062F\u062E\u0627\u0644\u0627\u062A \u0627\u0644\u062C\u0645\u0631\u0643\u064A\u0629", // الإدخالات الجمركية
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u062A\u0643\u0627\u0644\u064A\u0641", // إدارة التكاليف
            "\u0627\u0644\u062A\u0642\u0627\u0631\u064A\u0631" // التقارير
        };

        JList<String> menuList = new JList<>(menuItems);
        menuList.setFont(arabicFont);
        menuList.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        menuList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        menuList.setBackground(new Color(248, 249, 250));
        menuList.setBorder(BorderFactory.createEmptyBorder(5, 15, 15, 15));

        // تخصيص عرض العناصر
        menuList.setCellRenderer(new DefaultListCellRenderer() {
            @Override
            public Component getListCellRendererComponent(JList<?> list, Object value, int index,
                    boolean isSelected, boolean cellHasFocus) {
                super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);

                setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                setFont(arabicFont);
                setBorder(BorderFactory.createEmptyBorder(8, 12, 8, 12));

                if (isSelected) {
                    setBackground(new Color(0, 123, 255));
                    setForeground(Color.WHITE);
                } else {
                    setBackground(new Color(248, 249, 250));
                    setForeground(new Color(73, 80, 87));
                }

                return this;
            }
        });

        menuList.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                String selected = menuList.getSelectedValue();
                if (selected != null) {
                    showFeatureDialog(selected);
                }
            }
        });

        JScrollPane scrollPane = new JScrollPane(menuList);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(null);
        scrollPane.setBackground(new Color(248, 249, 250));
        sidePanel.add(scrollPane, BorderLayout.CENTER);

        return sidePanel;
    }

    /**
     * إنشاء التبويبات المحسنة
     */
    private JTabbedPane createEnhancedTabbedPane(Font arabicFont) {
        JTabbedPane tabbedPane = new JTabbedPane();
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        tabbedPane.setFont(arabicFont);
        tabbedPane.setTabPlacement(JTabbedPane.TOP);

        // تبويب الترحيب
        JPanel welcomePanel = createWelcomePanel(arabicFont);
        tabbedPane.addTab("\u0627\u0644\u062A\u0631\u062D\u064A\u0628", welcomePanel); // الترحيب

        // تبويب لوحة التحكم
        JPanel dashboardPanel = createDashboardPanel(arabicFont);
        tabbedPane.addTab("\u0644\u0648\u062D\u0629 \u0627\u0644\u062A\u062D\u0643\u0645", dashboardPanel); // لوحة التحكم

        // تبويب الإحصائيات
        JPanel statsPanel = createStatsPanel(arabicFont);
        tabbedPane.addTab("\u0627\u0644\u0625\u062D\u0635\u0627\u0626\u064A\u0627\u062A", statsPanel); // الإحصائيات

        return tabbedPane;
    }

    /**
     * إنشاء لوحة الترحيب المحسنة
     */
    private JPanel createWelcomePanel(Font arabicFont) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(new Color(248, 249, 250));

        // الجزء العلوي - العنوان والشعار
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        headerPanel.setBackground(new Color(0, 123, 255));
        headerPanel.setBorder(BorderFactory.createEmptyBorder(40, 40, 40, 40));

        // العنوان الرئيسي
        JLabel welcomeLabel = new JLabel(WELCOME);
        welcomeLabel.setFont(new Font("Tahoma", Font.BOLD, 32));
        welcomeLabel.setForeground(Color.WHITE);
        welcomeLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        welcomeLabel.setHorizontalAlignment(SwingConstants.CENTER);

        // العنوان الفرعي
        JLabel subtitleLabel = new JLabel("\u0646\u0638\u0627\u0645 \u0645\u062A\u0643\u0627\u0645\u0644 \u0644\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0634\u062D\u0646 \u0648\u0627\u0644\u0627\u0633\u062A\u064A\u0631\u0627\u062F"); // نظام متكامل لإدارة الشحن والاستيراد
        subtitleLabel.setFont(new Font("Tahoma", Font.PLAIN, 16));
        subtitleLabel.setForeground(new Color(220, 220, 220));
        subtitleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        subtitleLabel.setHorizontalAlignment(SwingConstants.CENTER);

        headerPanel.add(welcomeLabel, BorderLayout.CENTER);
        headerPanel.add(subtitleLabel, BorderLayout.SOUTH);

        panel.add(headerPanel, BorderLayout.NORTH);

        // الجزء الأوسط - بطاقات الميزات
        JPanel featuresPanel = new JPanel(new GridLayout(2, 3, 20, 20));
        featuresPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        featuresPanel.setBackground(new Color(248, 249, 250));
        featuresPanel.setBorder(BorderFactory.createEmptyBorder(40, 40, 20, 40));

        // إضافة بطاقات الميزات
        featuresPanel.add(createFeatureCard("\u0646\u0638\u0627\u0645 \u0622\u0645\u0646", "\u062A\u0633\u062C\u064A\u0644 \u062F\u062E\u0648\u0644 \u0622\u0645\u0646 \u0648\u0625\u062F\u0627\u0631\u0629 \u0635\u0644\u0627\u062D\u064A\u0627\u062A", new Color(40, 167, 69), arabicFont)); // نظام آمن
        featuresPanel.add(createFeatureCard("\u0625\u062F\u0627\u0631\u0629 \u0634\u0627\u0645\u0644\u0629", "\u0625\u062F\u0627\u0631\u0629 \u0643\u0627\u0645\u0644\u0629 \u0644\u0644\u0634\u062D\u0646\u0627\u062A \u0648\u0627\u0644\u0639\u0645\u0644\u0627\u0621", new Color(0, 123, 255), arabicFont)); // إدارة شاملة
        featuresPanel.add(createFeatureCard("\u062A\u0642\u0627\u0631\u064A\u0631 \u0630\u0643\u064A\u0629", "\u062A\u0642\u0627\u0631\u064A\u0631 \u0648\u0625\u062D\u0635\u0627\u0626\u064A\u0627\u062A \u0645\u062A\u0642\u062F\u0645\u0629", new Color(255, 193, 7), arabicFont)); // تقارير ذكية
        featuresPanel.add(createFeatureCard("\u0648\u0627\u062C\u0647\u0629 \u0639\u0631\u0628\u064A\u0629", "\u062F\u0639\u0645 \u0643\u0627\u0645\u0644 \u0644\u0644\u063A\u0629 \u0627\u0644\u0639\u0631\u0628\u064A\u0629 \u0648 RTL", new Color(220, 53, 69), arabicFont)); // واجهة عربية
        featuresPanel.add(createFeatureCard("\u0623\u062F\u0627\u0621 \u0639\u0627\u0644\u064A", "\u0633\u0631\u0639\u0629 \u0648\u0627\u0633\u062A\u0642\u0631\u0627\u0631 \u0641\u064A \u0627\u0644\u0623\u062F\u0627\u0621", new Color(111, 66, 193), arabicFont)); // أداء عالي
        featuresPanel.add(createFeatureCard("\u062F\u0639\u0645 \u0641\u0646\u064A", "\u062F\u0639\u0645 \u0641\u0646\u064A 24/7 \u0648\u062A\u062D\u062F\u064A\u062B\u0627\u062A \u0645\u0633\u062A\u0645\u0631\u0629", new Color(23, 162, 184), arabicFont)); // دعم فني

        panel.add(featuresPanel, BorderLayout.CENTER);

        // الجزء السفلي - معلومات النظام
        JPanel infoPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        infoPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        infoPanel.setBackground(new Color(248, 249, 250));
        infoPanel.setBorder(BorderFactory.createEmptyBorder(20, 40, 40, 40));

        JLabel versionLabel = new JLabel("\u0627\u0644\u0625\u0635\u062F\u0627\u0631: 1.0.0 | \u062A\u0627\u0631\u064A\u062E \u0627\u0644\u0625\u0635\u062F\u0627\u0631: 2025/01/13 | \u062C\u0645\u064A\u0639 \u0627\u0644\u062D\u0642\u0648\u0642 \u0645\u062D\u0641\u0648\u0638\u0629"); // الإصدار: 1.0.0 | تاريخ الإصدار: 2025/01/13 | جميع الحقوق محفوظة
        versionLabel.setFont(new Font("Tahoma", Font.PLAIN, 12));
        versionLabel.setForeground(new Color(108, 117, 125));
        versionLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        infoPanel.add(versionLabel);
        panel.add(infoPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * إنشاء بطاقة ميزة
     */
    private JPanel createFeatureCard(String title, String description, Color accentColor, Font arabicFont) {
        JPanel card = new JPanel(new BorderLayout());
        card.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        card.setBackground(Color.WHITE);
        card.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(230, 230, 230), 1),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));

        // شريط علوي ملون
        JPanel accentPanel = new JPanel();
        accentPanel.setBackground(accentColor);
        accentPanel.setPreferredSize(new Dimension(0, 4));
        card.add(accentPanel, BorderLayout.NORTH);

        // المحتوى
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        contentPanel.setBackground(Color.WHITE);
        contentPanel.setBorder(BorderFactory.createEmptyBorder(15, 0, 0, 0));

        JLabel titleLabel = new JLabel(title);
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 16));
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        titleLabel.setForeground(accentColor);

        JTextArea descArea = new JTextArea(description);
        descArea.setFont(new Font("Tahoma", Font.PLAIN, 12));
        descArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        descArea.setForeground(new Color(73, 80, 87));
        descArea.setEditable(false);
        descArea.setOpaque(false);
        descArea.setLineWrap(true);
        descArea.setWrapStyleWord(true);

        contentPanel.add(titleLabel, BorderLayout.NORTH);
        contentPanel.add(descArea, BorderLayout.CENTER);

        card.add(contentPanel, BorderLayout.CENTER);

        // تأثير التفاعل
        card.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                card.setBackground(new Color(248, 249, 250));
                contentPanel.setBackground(new Color(248, 249, 250));
                card.setCursor(new Cursor(Cursor.HAND_CURSOR));
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                card.setBackground(Color.WHITE);
                contentPanel.setBackground(Color.WHITE);
                card.setCursor(new Cursor(Cursor.DEFAULT_CURSOR));
            }
        });

        return card;
    }

    /**
     * إنشاء لوحة التحكم المحسنة
     */
    private JPanel createDashboardPanel(Font arabicFont) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(new Color(248, 249, 250));

        // العنوان
        JPanel titlePanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        titlePanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        titlePanel.setBackground(new Color(248, 249, 250));
        titlePanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 10, 20));

        JLabel titleLabel = new JLabel("\u0644\u0648\u062D\u0629 \u0627\u0644\u062A\u062D\u0643\u0645 \u0627\u0644\u0631\u0626\u064A\u0633\u064A\u0629"); // لوحة التحكم الرئيسية
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 24));
        titleLabel.setForeground(new Color(0, 123, 255));
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        titlePanel.add(titleLabel);
        panel.add(titlePanel, BorderLayout.NORTH);

        // الجزء الأوسط - الإحصائيات
        JPanel statsPanel = new JPanel(new GridLayout(2, 4, 15, 15));
        statsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statsPanel.setBackground(new Color(248, 249, 250));
        statsPanel.setBorder(BorderFactory.createEmptyBorder(20, 30, 20, 30));

        // بطاقات إحصائية محسنة
        statsPanel.add(createEnhancedStatCard("\u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645\u064A\u0646", "15", "+3 \u0647\u0630\u0627 \u0627\u0644\u0634\u0647\u0631", new Color(40, 167, 69), arabicFont)); // المستخدمين
        statsPanel.add(createEnhancedStatCard("\u0627\u0644\u0634\u062D\u0646\u0627\u062A", "247", "+18 \u0647\u0630\u0627 \u0627\u0644\u0623\u0633\u0628\u0648\u0639", new Color(0, 123, 255), arabicFont)); // الشحنات
        statsPanel.add(createEnhancedStatCard("\u0627\u0644\u0639\u0645\u0644\u0627\u0621", "89", "+7 \u062C\u062F\u064A\u062F", new Color(255, 193, 7), arabicFont)); // العملاء
        statsPanel.add(createEnhancedStatCard("\u0627\u0644\u0625\u064A\u0631\u0627\u062F\u0627\u062A", "2.5M \u0631\u064A\u0627\u0644", "+12% \u0647\u0630\u0627 \u0627\u0644\u0634\u0647\u0631", new Color(220, 53, 69), arabicFont)); // الإيرادات

        statsPanel.add(createEnhancedStatCard("\u0627\u0644\u0639\u0645\u0644\u0627\u062A", "5", "\u0631\u064A\u0627\u0644\u060C \u062F\u0648\u0644\u0627\u0631\u060C \u064A\u0648\u0631\u0648", new Color(111, 66, 193), arabicFont)); // العملات
        statsPanel.add(createEnhancedStatCard("\u0627\u0644\u0645\u0648\u0631\u062F\u064A\u0646", "34", "+2 \u0647\u0630\u0627 \u0627\u0644\u0634\u0647\u0631", new Color(23, 162, 184), arabicFont)); // الموردين
        statsPanel.add(createEnhancedStatCard("\u0627\u0644\u0623\u0635\u0646\u0627\u0641", "1,256", "+45 \u0635\u0646\u0641 \u062C\u062F\u064A\u062F", new Color(108, 117, 125), arabicFont)); // الأصناف
        statsPanel.add(createEnhancedStatCard("\u0627\u0644\u062A\u0642\u0627\u0631\u064A\u0631", "28", "\u062A\u0642\u0631\u064A\u0631 \u0645\u062A\u0627\u062D", new Color(255, 87, 34), arabicFont)); // التقارير

        panel.add(statsPanel, BorderLayout.CENTER);

        // الجزء السفلي - الأنشطة الأخيرة
        JPanel activityPanel = createRecentActivityPanel(arabicFont);
        panel.add(activityPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * إنشاء بطاقة إحصائية محسنة
     */
    private JPanel createEnhancedStatCard(String title, String value, String subtitle, Color accentColor, Font arabicFont) {
        JPanel card = new JPanel(new BorderLayout());
        card.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        card.setBackground(Color.WHITE);
        card.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(230, 230, 230), 1),
            BorderFactory.createEmptyBorder(25, 25, 25, 25)
        ));

        // شريط علوي ملون
        JPanel accentStrip = new JPanel();
        accentStrip.setBackground(accentColor);
        accentStrip.setPreferredSize(new Dimension(0, 5));
        card.add(accentStrip, BorderLayout.NORTH);

        // المحتوى الرئيسي
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        contentPanel.setBackground(Color.WHITE);
        contentPanel.setBorder(BorderFactory.createEmptyBorder(15, 0, 0, 0));

        // العنوان
        JLabel titleLabel = new JLabel(title);
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 14));
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        titleLabel.setForeground(new Color(73, 80, 87));

        // القيمة الرئيسية
        JLabel valueLabel = new JLabel(value);
        valueLabel.setFont(new Font("Tahoma", Font.BOLD, 28));
        valueLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        valueLabel.setForeground(accentColor);

        // العنوان الفرعي
        JLabel subtitleLabel = new JLabel(subtitle);
        subtitleLabel.setFont(new Font("Tahoma", Font.PLAIN, 11));
        subtitleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        subtitleLabel.setForeground(new Color(108, 117, 125));

        contentPanel.add(titleLabel, BorderLayout.NORTH);
        contentPanel.add(valueLabel, BorderLayout.CENTER);
        contentPanel.add(subtitleLabel, BorderLayout.SOUTH);

        card.add(contentPanel, BorderLayout.CENTER);

        // تأثير التفاعل المحسن
        card.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                card.setBackground(new Color(248, 249, 250));
                contentPanel.setBackground(new Color(248, 249, 250));
                card.setCursor(new Cursor(Cursor.HAND_CURSOR));
                // تأثير الظل
                card.setBorder(BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(accentColor, 2),
                    BorderFactory.createEmptyBorder(24, 24, 24, 24)
                ));
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                card.setBackground(Color.WHITE);
                contentPanel.setBackground(Color.WHITE);
                card.setCursor(new Cursor(Cursor.DEFAULT_CURSOR));
                card.setBorder(BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(new Color(230, 230, 230), 1),
                    BorderFactory.createEmptyBorder(25, 25, 25, 25)
                ));
            }
        });

        return card;
    }

    /**
     * إنشاء لوحة الأنشطة الأخيرة
     */
    private JPanel createRecentActivityPanel(Font arabicFont) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(new Color(248, 249, 250));
        panel.setBorder(BorderFactory.createEmptyBorder(20, 30, 30, 30));

        // العنوان
        JLabel titleLabel = new JLabel("\u0627\u0644\u0623\u0646\u0634\u0637\u0629 \u0627\u0644\u0623\u062E\u064A\u0631\u0629"); // الأنشطة الأخيرة
        titleLabel.setFont(new Font("Tahoma", Font.BOLD, 18));
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        titleLabel.setForeground(new Color(73, 80, 87));
        titleLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 15, 0));

        panel.add(titleLabel, BorderLayout.NORTH);

        // قائمة الأنشطة
        JPanel activitiesPanel = new JPanel();
        activitiesPanel.setLayout(new BoxLayout(activitiesPanel, BoxLayout.Y_AXIS));
        activitiesPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        activitiesPanel.setBackground(Color.WHITE);
        activitiesPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(230, 230, 230), 1),
            BorderFactory.createEmptyBorder(15, 15, 15, 15)
        ));

        // إضافة أنشطة تجريبية
        activitiesPanel.add(createActivityItem("\u062A\u0645 \u0625\u0636\u0627\u0641\u0629 \u0634\u062D\u0646\u0629 \u062C\u062F\u064A\u062F\u0629 #SH-2025-001", "\u0642\u0628\u0644 5 \u062F\u0642\u0627\u0626\u0642", new Color(40, 167, 69), arabicFont)); // تم إضافة شحنة جديدة
        activitiesPanel.add(createActivityItem("\u062A\u062D\u062F\u064A\u062B \u062D\u0627\u0644\u0629 \u0627\u0644\u0634\u062D\u0646\u0629 #SH-2025-002", "\u0642\u0628\u0644 15 \u062F\u0642\u064A\u0642\u0629", new Color(0, 123, 255), arabicFont)); // تحديث حالة الشحنة
        activitiesPanel.add(createActivityItem("\u0625\u0636\u0627\u0641\u0629 \u0639\u0645\u064A\u0644 \u062C\u062F\u064A\u062F: \u0634\u0631\u0643\u0629 \u0627\u0644\u062A\u062C\u0627\u0631\u0629 \u0627\u0644\u062D\u062F\u064A\u062B\u0629", "\u0642\u0628\u0644 30 \u062F\u0642\u064A\u0642\u0629", new Color(255, 193, 7), arabicFont)); // إضافة عميل جديد
        activitiesPanel.add(createActivityItem("\u062A\u0648\u0644\u064A\u062F \u062A\u0642\u0631\u064A\u0631 \u0627\u0644\u0634\u062D\u0646\u0627\u062A \u0627\u0644\u0634\u0647\u0631\u064A", "\u0642\u0628\u0644 \u0633\u0627\u0639\u0629", new Color(111, 66, 193), arabicFont)); // توليد تقرير الشحنات الشهري
        activitiesPanel.add(createActivityItem("\u062A\u0633\u062C\u064A\u0644 \u062F\u062E\u0648\u0644 \u0645\u0633\u062A\u062E\u062F\u0645 \u062C\u062F\u064A\u062F: \u0623\u062D\u0645\u062F \u0645\u062D\u0645\u062F", "\u0642\u0628\u0644 \u0633\u0627\u0639\u062A\u064A\u0646", new Color(23, 162, 184), arabicFont)); // تسجيل دخول مستخدم جديد

        JScrollPane scrollPane = new JScrollPane(activitiesPanel);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(null);
        scrollPane.setPreferredSize(new Dimension(0, 150));

        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء عنصر نشاط
     */
    private JPanel createActivityItem(String activity, String time, Color statusColor, Font arabicFont) {
        JPanel item = new JPanel(new BorderLayout());
        item.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        item.setBackground(Color.WHITE);
        item.setBorder(BorderFactory.createEmptyBorder(8, 0, 8, 0));

        // نقطة الحالة
        JPanel statusDot = new JPanel();
        statusDot.setBackground(statusColor);
        statusDot.setPreferredSize(new Dimension(8, 8));
        statusDot.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 10));

        // النص
        JLabel activityLabel = new JLabel(activity);
        activityLabel.setFont(arabicFont);
        activityLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        activityLabel.setForeground(new Color(73, 80, 87));

        // الوقت
        JLabel timeLabel = new JLabel(time);
        timeLabel.setFont(new Font("Tahoma", Font.PLAIN, 11));
        timeLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        timeLabel.setForeground(new Color(108, 117, 125));

        item.add(statusDot, BorderLayout.EAST);
        item.add(activityLabel, BorderLayout.CENTER);
        item.add(timeLabel, BorderLayout.WEST);

        return item;
    }

    /**
     * إنشاء لوحة الإحصائيات
     */
    private JPanel createStatsPanel(Font arabicFont) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBackground(Color.WHITE);

        JLabel statsLabel = new JLabel("\u0627\u0644\u0625\u062D\u0635\u0627\u0626\u064A\u0627\u062A \u0642\u064A\u062F \u0627\u0644\u062A\u0637\u0648\u064A\u0631"); // الإحصائيات قيد التطوير
        statsLabel.setFont(new Font("Tahoma", Font.BOLD, 18));
        statsLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statsLabel.setHorizontalAlignment(SwingConstants.CENTER);
        statsLabel.setForeground(new Color(108, 117, 125));

        panel.add(statsLabel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء شريط الحالة المحسن
     */
    private JPanel createEnhancedStatusBar(Font arabicFont) {
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusPanel.setBackground(new Color(248, 249, 250));
        statusPanel.setBorder(BorderFactory.createMatteBorder(1, 0, 0, 0, new Color(222, 226, 230)));
        statusPanel.setPreferredSize(new Dimension(0, 30));

        // الجانب الأيمن - الحالة
        JPanel rightPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 5));
        rightPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        rightPanel.setOpaque(false);

        JLabel statusLeft = new JLabel(READY);
        statusLeft.setFont(arabicFont);
        statusLeft.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusLeft.setForeground(new Color(108, 117, 125));
        rightPanel.add(statusLeft);

        // الجانب الأيسر - معلومات المستخدم والوقت
        JPanel leftPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 5));
        leftPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        leftPanel.setOpaque(false);

        statusLabel = new JLabel();
        statusLabel.setFont(arabicFont);
        statusLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusLabel.setForeground(new Color(108, 117, 125));
        leftPanel.add(statusLabel);

        statusPanel.add(rightPanel, BorderLayout.EAST);
        statusPanel.add(leftPanel, BorderLayout.WEST);

        return statusPanel;
    }

    /**
     * بدء الساعة الرقمية
     */
    private void startClock() {
        clockTimer = new Timer(1000, e -> {
            LocalDateTime now = LocalDateTime.now();
            String timeText = now.format(DateTimeFormatter.ofPattern("yyyy/MM/dd - HH:mm:ss"));
            statusLabel.setText("\u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645: admin | " + timeText); // المستخدم: admin
        });
        clockTimer.start();
    }

    /**
     * تأكيد الخروج
     */
    private void confirmExit() {
        int result = JOptionPane.showConfirmDialog(
            mainFrame,
            "\u0647\u0644 \u062A\u0631\u064A\u062F \u0627\u0644\u062E\u0631\u0648\u062C \u0645\u0646 \u0627\u0644\u0628\u0631\u0646\u0627\u0645\u062C\u061F", // هل تريد الخروج من البرنامج؟
            "\u062A\u0623\u0643\u064A\u062F \u0627\u0644\u062E\u0631\u0648\u062C", // تأكيد الخروج
            JOptionPane.YES_NO_OPTION,
            JOptionPane.QUESTION_MESSAGE
        );

        if (result == JOptionPane.YES_OPTION) {
            if (clockTimer != null) {
                clockTimer.stop();
            }
            System.exit(0);
        }
    }

    private void showFeatureDialog(String feature) {
        // فتح نافذة الإعدادات العامة الفعلية
        if (feature.equals("\u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0639\u0627\u0645\u0629")) { // الإعدادات العامة
            openGeneralSettings();
        } else {
            // باقي الميزات قيد التطوير
            JOptionPane.showMessageDialog(
                mainFrame,
                "\u0627\u0644\u0645\u064A\u0632\u0629 '" + feature + "' \u0642\u064A\u062F \u0627\u0644\u062A\u0637\u0648\u064A\u0631\n\u0633\u062A\u0643\u0648\u0646 \u0645\u062A\u0627\u062D\u0629 \u0641\u064A \u0627\u0644\u0625\u0635\u062F\u0627\u0631\u0627\u062A \u0627\u0644\u0642\u0627\u062F\u0645\u0629", // الميزة قيد التطوير - ستكون متاحة في الإصدارات القادمة
                "\u0642\u064A\u062F \u0627\u0644\u062A\u0637\u0648\u064A\u0631", // قيد التطوير
                JOptionPane.INFORMATION_MESSAGE
            );
        }
    }

    /**
     * فتح نافذة الإعدادات العامة
     */
    private void openGeneralSettings() {
        try {
            GeneralSettingsWindow settingsWindow = new GeneralSettingsWindow(mainFrame);
            settingsWindow.setVisible(true);
        } catch (Exception e) {
            JOptionPane.showMessageDialog(
                mainFrame,
                "\u062E\u0637\u0623 \u0641\u064A \u0641\u062A\u062D \u0646\u0627\u0641\u0630\u0629 \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A: " + e.getMessage(), // خطأ في فتح نافذة الإعدادات
                "\u062E\u0637\u0623", // خطأ
                JOptionPane.ERROR_MESSAGE
            );
        }
    }
    
    private void showAboutDialog() {
        String aboutText = 
            APP_TITLE + "\n" +
            "Ship ERP System\n\n" +
            "\u0627\u0644\u0625\u0635\u062F\u0627\u0631: 1.0.0\n" + // الإصدار
            "\u0627\u0644\u062A\u0642\u0646\u064A\u0627\u062A: Java + JavaFX + Spring + Oracle\n\n" + // التقنيات
            "\u0646\u0638\u0627\u0645 \u0645\u062A\u0643\u0627\u0645\u0644 \u0644\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0634\u062D\u0646 \u0648\u0627\u0644\u0627\u0633\u062A\u064A\u0631\u0627\u062F\n\n" + // نظام متكامل لإدارة الشحن والاستيراد
            "\u062C\u0645\u064A\u0639 \u0627\u0644\u062D\u0642\u0648\u0642 \u0645\u062D\u0641\u0648\u0638\u0629 \u00A9 2025"; // جميع الحقوق محفوظة
            
        JOptionPane.showMessageDialog(
            mainFrame,
            aboutText,
            "\u062D\u0648\u0644 \u0627\u0644\u0628\u0631\u0646\u0627\u0645\u062C", // حول البرنامج
            JOptionPane.INFORMATION_MESSAGE
        );
    }
    
    /**
     * معالج تسجيل الدخول
     */
    private class LoginActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String username = usernameField.getText().trim();
            String password = new String(passwordField.getPassword());
            
            if (username.isEmpty()) {
                JOptionPane.showMessageDialog(loginFrame, 
                    "\u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645 \u0645\u0637\u0644\u0648\u0628", // اسم المستخدم مطلوب
                    "\u062E\u0637\u0623", // خطأ
                    JOptionPane.ERROR_MESSAGE);
                usernameField.requestFocus();
                return;
            }
            
            if (password.isEmpty()) {
                JOptionPane.showMessageDialog(loginFrame, 
                    "\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631 \u0645\u0637\u0644\u0648\u0628\u0629", // كلمة المرور مطلوبة
                    "\u062E\u0637\u0623", // خطأ
                    JOptionPane.ERROR_MESSAGE);
                passwordField.requestFocus();
                return;
            }
            
            statusLabel.setText("\u062C\u0627\u0631\u064A \u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644..."); // جاري تسجيل الدخول...
            
            // محاكاة تسجيل الدخول
            Timer loginTimer = new Timer(1500, event -> {
                if ("admin".equals(username) && "admin123".equals(password)) {
                    showMainInterface();
                } else {
                    statusLabel.setText(READY);
                    JOptionPane.showMessageDialog(
                        loginFrame, 
                        "\u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645 \u0623\u0648 \u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631 \u063A\u064A\u0631 \u0635\u062D\u064A\u062D\u0629", // اسم المستخدم أو كلمة المرور غير صحيحة
                        "\u062E\u0637\u0623 \u0641\u064A \u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644", // خطأ في تسجيل الدخول
                        JOptionPane.ERROR_MESSAGE
                    );
                    passwordField.selectAll();
                    passwordField.requestFocus();
                }
            });
            loginTimer.setRepeats(false);
            loginTimer.start();
        }
    }
}
