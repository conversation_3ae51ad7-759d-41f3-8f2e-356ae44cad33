import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.Frame;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JComponent;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JTextField;

/**
 * نافذة إضافة مجموعة تحت فرعية محسنة Improved Add Sub Group Dialog
 */
public class ImprovedAddSubGroupDialog extends JDialog {

    private Connection connection;
    private boolean saved = false;

    // مكونات الواجهة
    private JComboBox<String> mainGroupCombo;
    private JComboBox<String> mainSubGroupCombo;
    private JTextField subCodeField;
    private JTextField arabicNameField;
    private JTextField englishNameField;
    private JTextField imageCodeField;
    private JTextField orderField;
    private JCheckBox webSyncCheckBox;
    private JCheckBox activeCheckBox;

    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 14);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 14);

    public ImprovedAddSubGroupDialog(Frame parent, Connection connection) {
        super(parent, "إضافة مجموعة تحت فرعية جديدة", true);
        this.connection = connection;

        initializeComponents();
        setupLayout();
        loadMainGroups();
        setDefaultValues();

        setSize(750, 600);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }

    private void initializeComponents() {
        // إنشاء الحقول مع أحجام محسنة
        mainGroupCombo = createComboBox();
        mainSubGroupCombo = createComboBox();
        subCodeField = createTextField(true); // قابل للتعديل (إدخال يدوي)
        arabicNameField = createTextField(true);
        englishNameField = createTextField(true);
        imageCodeField = createTextField(true);
        orderField = createTextField(true);

        // إنشاء صناديق الاختيار
        webSyncCheckBox = createCheckBox("مزامنة الويب");
        activeCheckBox = createCheckBox("نشط");

        // إضافة مستمعين
        mainGroupCombo.addActionListener(e -> loadMainSubGroups());
        mainSubGroupCombo.addActionListener(e -> suggestSubCode());
    }

    private JComboBox<String> createComboBox() {
        JComboBox<String> combo = new JComboBox<>();
        combo.setFont(arabicFont);
        combo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        combo.setPreferredSize(new Dimension(300, 35));
        return combo;
    }

    private JTextField createTextField(boolean enabled) {
        JTextField field = new JTextField();
        field.setFont(arabicFont);
        field.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        field.setPreferredSize(new Dimension(300, 35));
        field.setEnabled(enabled);
        field.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(enabled ? Color.GRAY : Color.LIGHT_GRAY, 1),
                BorderFactory.createEmptyBorder(8, 12, 8, 12)));
        if (!enabled) {
            field.setBackground(new Color(245, 245, 245));
        }
        return field;
    }

    private JCheckBox createCheckBox(String text) {
        JCheckBox checkBox = new JCheckBox(text);
        checkBox.setFont(arabicFont);
        checkBox.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        checkBox.setPreferredSize(new Dimension(350, 30));
        return checkBox;
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(12, 12, 12, 12);
        gbc.anchor = GridBagConstraints.EAST;
        gbc.fill = GridBagConstraints.HORIZONTAL;

        int row = 0;

        // إضافة الحقول
        addFieldRow(mainPanel, gbc, row++, "المجموعة الرئيسية *:", mainGroupCombo);
        addFieldRow(mainPanel, gbc, row++, "المجموعة الفرعية *:", mainSubGroupCombo);
        addFieldRow(mainPanel, gbc, row++, "كود المجموعة تحت فرعية *:", subCodeField);
        addFieldRow(mainPanel, gbc, row++, "الاسم العربي *:", arabicNameField);
        addFieldRow(mainPanel, gbc, row++, "الاسم الإنجليزي:", englishNameField);
        addFieldRow(mainPanel, gbc, row++, "كود الصورة:", imageCodeField);
        addFieldRow(mainPanel, gbc, row++, "الترتيب:", orderField);

        // إضافة صناديق الاختيار
        gbc.gridx = 0;
        gbc.gridy = row++;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        mainPanel.add(webSyncCheckBox, gbc);

        gbc.gridy = row++;
        mainPanel.add(activeCheckBox, gbc);

        add(mainPanel, BorderLayout.CENTER);
        add(createButtonPanel(), BorderLayout.SOUTH);
    }

    private void addFieldRow(JPanel panel, GridBagConstraints gbc, int row, String labelText,
            JComponent field) {
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.3;
        gbc.gridwidth = 1;
        panel.add(createLabel(labelText), gbc);

        gbc.gridx = 1;
        gbc.weightx = 0.7;
        panel.add(field, gbc);
    }

    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicBoldFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        label.setPreferredSize(new Dimension(220, 35));
        return label;
    }

    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER, 20, 15));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));

        JButton saveButton = new JButton("💾 حفظ");
        saveButton.setFont(arabicBoldFont);
        saveButton.setBackground(new Color(39, 174, 96));
        saveButton.setForeground(Color.WHITE);
        saveButton.setPreferredSize(new Dimension(120, 40));
        saveButton.setFocusPainted(false);
        saveButton.addActionListener(e -> saveSubGroup());

        JButton cancelButton = new JButton("❌ إلغاء");
        cancelButton.setFont(arabicBoldFont);
        cancelButton.setBackground(new Color(231, 76, 60));
        cancelButton.setForeground(Color.WHITE);
        cancelButton.setPreferredSize(new Dimension(120, 40));
        cancelButton.setFocusPainted(false);
        cancelButton.addActionListener(e -> dispose());

        panel.add(saveButton);
        panel.add(cancelButton);

        return panel;
    }

    private void loadMainGroups() {
        try {
            String sql =
                    "SELECT G_CODE, G_A_NAME FROM ERP_GROUP_DETAILS WHERE IS_ACTIVE = 1 ORDER BY G_CODE";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            mainGroupCombo.removeAllItems();
            mainGroupCombo.addItem("-- اختر المجموعة الرئيسية --");

            while (rs.next()) {
                String code = rs.getString("G_CODE");
                String name = rs.getString("G_A_NAME");
                mainGroupCombo.addItem(code + " - " + name);
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات الرئيسية: " + e.getMessage());
        }
    }

    private void loadMainSubGroups() {
        mainSubGroupCombo.removeAllItems();
        mainSubGroupCombo.addItem("-- اختر المجموعة الفرعية --");

        if (mainGroupCombo.getSelectedIndex() <= 0) {
            return;
        }

        try {
            String selectedItem = (String) mainGroupCombo.getSelectedItem();
            String mainGroupCode = selectedItem.split(" - ")[0];

            String sql =
                    "SELECT MNG_CODE, MNG_A_NAME FROM ERP_MAINSUB_GRP_DTL WHERE G_CODE = ? AND IS_ACTIVE = 1 ORDER BY MNG_CODE";
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, mainGroupCode);
            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                String code = rs.getString("MNG_CODE");
                String name = rs.getString("MNG_A_NAME");
                mainSubGroupCombo.addItem(code + " - " + name);
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات الفرعية: " + e.getMessage());
        }
    }

    private void suggestSubCode() {
        if (mainSubGroupCombo.getSelectedIndex() <= 0) {
            return;
        }

        try {
            String selectedItem = (String) mainSubGroupCombo.getSelectedItem();
            String mainSubGroupCode = selectedItem.split(" - ")[0];

            String sql =
                    """
                                SELECT ? || LPAD(NVL(MAX(TO_NUMBER(SUBSTR(SUBG_CODE, LENGTH(?) + 1))), 0) + 1, 2, '0')
                                FROM ERP_SUB_GRP_DTL
                                WHERE MNG_CODE = ? AND SUBG_CODE LIKE ? || '%'
                            """;

            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, mainSubGroupCode);
            pstmt.setString(2, mainSubGroupCode);
            pstmt.setString(3, mainSubGroupCode);
            pstmt.setString(4, mainSubGroupCode);

            ResultSet rs = pstmt.executeQuery();

            if (rs.next()) {
                String nextCode = rs.getString(1);
                // اقتراح الكود فقط إذا كان الحقل فارغاً
                if (subCodeField.getText().trim().isEmpty()) {
                    subCodeField.setText(nextCode);
                }
            } else {
                if (subCodeField.getText().trim().isEmpty()) {
                    subCodeField.setText(mainSubGroupCode + "01");
                }
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في اقتراح كود المجموعة تحت فرعية: " + e.getMessage());
        }
    }

    private void setDefaultValues() {
        orderField.setText("1");
        activeCheckBox.setSelected(true);
        webSyncCheckBox.setSelected(false);
    }

    private void saveSubGroup() {
        if (!validateInput()) {
            return;
        }

        try {
            String mainGroupItem = (String) mainGroupCombo.getSelectedItem();
            String mainGroupCode = mainGroupItem.split(" - ")[0];

            String mainSubGroupItem = (String) mainSubGroupCombo.getSelectedItem();
            String mainSubGroupCode = mainSubGroupItem.split(" - ")[0];

            String sql = """
                        INSERT INTO ERP_SUB_GRP_DTL
                        (G_CODE, MNG_CODE, SUBG_CODE, SUBG_A_NAME, SUBG_E_NAME, SUBG_I_CODE,
                         SYNCHRNZ_TO_WEB_FLG, SUBG_ORDR, IS_ACTIVE, CREATED_BY, CREATED_DATE)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'USER', SYSDATE)
                    """;

            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, mainGroupCode);
            pstmt.setString(2, mainSubGroupCode);
            pstmt.setString(3, subCodeField.getText().trim());
            pstmt.setString(4, arabicNameField.getText().trim());
            pstmt.setString(5, englishNameField.getText().trim());
            pstmt.setString(6, imageCodeField.getText().trim());
            pstmt.setInt(7, webSyncCheckBox.isSelected() ? 1 : 0);

            String orderText = orderField.getText().trim();
            if (orderText.isEmpty()) {
                pstmt.setNull(8, Types.INTEGER);
            } else {
                pstmt.setInt(8, Integer.parseInt(orderText));
            }

            pstmt.setInt(9, activeCheckBox.isSelected() ? 1 : 0);

            pstmt.executeUpdate();
            connection.commit();

            saved = true;
            JOptionPane.showMessageDialog(this, "تم حفظ المجموعة تحت فرعية بنجاح!", "نجح الحفظ",
                    JOptionPane.INFORMATION_MESSAGE);

            dispose();

        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }

            JOptionPane.showMessageDialog(this, "خطأ في حفظ المجموعة تحت فرعية:\n" + e.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    private boolean validateInput() {
        if (mainGroupCombo.getSelectedIndex() <= 0) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار المجموعة الرئيسية", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        if (mainSubGroupCombo.getSelectedIndex() <= 0) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار المجموعة الفرعية", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        if (subCodeField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "كود المجموعة تحت فرعية مطلوب", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            subCodeField.requestFocus();
            return false;
        }

        if (arabicNameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "الاسم العربي مطلوب", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            arabicNameField.requestFocus();
            return false;
        }

        // التحقق من عدم تكرار الكود
        try {
            String sql = "SELECT COUNT(*) FROM ERP_SUB_GRP_DTL WHERE SUBG_CODE = ?";
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, subCodeField.getText().trim());
            ResultSet rs = pstmt.executeQuery();

            if (rs.next() && rs.getInt(1) > 0) {
                JOptionPane.showMessageDialog(this, "كود المجموعة تحت فرعية موجود مسبقاً", "خطأ",
                        JOptionPane.ERROR_MESSAGE);
                subCodeField.requestFocus();
                return false;
            }

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في التحقق من الكود", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        return true;
    }

    public boolean isSaved() {
        return saved;
    }
}
