@echo off
echo 🚀 تشغيل النظام الكامل...
echo.

cd /d "e:\ship_erp\java\src\main\java"

echo 📋 التحقق من ملفات المكتبات...
if not exist "lib\ojdbc11.jar" (
    echo ❌ ملف ojdbc11.jar غير موجود
    pause
    exit /b 1
)

if not exist "lib\orai18n.jar" (
    echo ❌ ملف orai18n.jar غير موجود
    pause
    exit /b 1
)

if not exist "lib\h2-2.2.224.jar" (
    echo ❌ ملف h2-2.2.224.jar غير موجود
    pause
    exit /b 1
)

echo ✅ جميع المكتبات موجودة
echo.

echo 🔄 تشغيل النظام الكامل...
java "-Duser.language=en" "-Duser.country=US" "-Duser.region=US" "-Dfile.encoding=UTF-8" "-Djava.locale.providers=COMPAT,SPI" -cp "lib/ojdbc11.jar;lib/orai18n.jar;lib/h2-2.2.224.jar;." CompleteSystemTest

echo.
echo 📋 انتهى التشغيل
pause
