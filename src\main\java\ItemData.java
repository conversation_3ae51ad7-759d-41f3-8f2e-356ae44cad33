/**
 * فئة بيانات الصنف
 * Item Data Class
 */
public class ItemData {
    private String code;
    private String nameAr;
    private String nameEn;
    private String description;
    private String categoryCode;
    private String unitCode;
    private double salesPrice;
    private double costPrice;
    private double currentStock;
    private double minStockLevel;
    private boolean isActive;
    private boolean isSellable;
    private boolean isPurchasable;
    private boolean trackInventory;
    
    public ItemData() {
        this.isActive = true;
        this.isSellable = true;
        this.isPurchasable = true;
        this.trackInventory = true;
    }
    
    public ItemData(String code, String nameAr, String nameEn, String description,
                   String categoryCode, String unitCode, double salesPrice, double costPrice,
                   double currentStock, double minStockLevel, boolean isActive,
                   boolean isSellable, boolean isPurchasable, boolean trackInventory) {
        this.code = code;
        this.nameAr = nameAr;
        this.nameEn = nameEn;
        this.description = description;
        this.categoryCode = categoryCode;
        this.unitCode = unitCode;
        this.salesPrice = salesPrice;
        this.costPrice = costPrice;
        this.currentStock = currentStock;
        this.minStockLevel = minStockLevel;
        this.isActive = isActive;
        this.isSellable = isSellable;
        this.isPurchasable = isPurchasable;
        this.trackInventory = trackInventory;
    }
    
    // Getters and Setters
    public String getCode() { 
        return code; 
    }
    
    public void setCode(String code) { 
        this.code = code; 
    }
    
    public String getNameAr() { 
        return nameAr; 
    }
    
    public void setNameAr(String nameAr) { 
        this.nameAr = nameAr; 
    }
    
    public String getNameEn() { 
        return nameEn; 
    }
    
    public void setNameEn(String nameEn) { 
        this.nameEn = nameEn; 
    }
    
    public String getDescription() { 
        return description; 
    }
    
    public void setDescription(String description) { 
        this.description = description; 
    }
    
    public String getCategoryCode() { 
        return categoryCode; 
    }
    
    public void setCategoryCode(String categoryCode) { 
        this.categoryCode = categoryCode; 
    }
    
    public String getUnitCode() { 
        return unitCode; 
    }
    
    public void setUnitCode(String unitCode) { 
        this.unitCode = unitCode; 
    }
    
    public double getSalesPrice() { 
        return salesPrice; 
    }
    
    public void setSalesPrice(double salesPrice) { 
        this.salesPrice = salesPrice; 
    }
    
    public double getCostPrice() { 
        return costPrice; 
    }
    
    public void setCostPrice(double costPrice) { 
        this.costPrice = costPrice; 
    }
    
    public double getCurrentStock() { 
        return currentStock; 
    }
    
    public void setCurrentStock(double currentStock) { 
        this.currentStock = currentStock; 
    }
    
    public double getMinStockLevel() { 
        return minStockLevel; 
    }
    
    public void setMinStockLevel(double minStockLevel) { 
        this.minStockLevel = minStockLevel; 
    }
    
    public boolean isActive() { 
        return isActive; 
    }
    
    public void setActive(boolean active) { 
        isActive = active; 
    }
    
    public boolean isSellable() { 
        return isSellable; 
    }
    
    public void setSellable(boolean sellable) { 
        isSellable = sellable; 
    }
    
    public boolean isPurchasable() { 
        return isPurchasable; 
    }
    
    public void setPurchasable(boolean purchasable) { 
        isPurchasable = purchasable; 
    }
    
    public boolean isTrackInventory() { 
        return trackInventory; 
    }
    
    public void setTrackInventory(boolean trackInventory) { 
        this.trackInventory = trackInventory; 
    }
    
    @Override
    public String toString() {
        return nameAr + " (" + code + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ItemData that = (ItemData) obj;
        return code != null ? code.equals(that.code) : that.code == null;
    }
    
    @Override
    public int hashCode() {
        return code != null ? code.hashCode() : 0;
    }
}
