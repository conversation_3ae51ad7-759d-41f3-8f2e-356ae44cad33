import java.sql.*;

/**
 * إنشاء Package أساسي بدون تعقيدات
 */
public class CreateBasicPackage {
    
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            System.out.println("🔧 إنشاء Package ERP_ITEM_GROUPS أساسي");
            System.out.println("🔗 الاتصال بـ SHIP_ERP...");
            
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            System.out.println("✅ تم الاتصال");
            
            Statement stmt = conn.createStatement();
            
            // حذف Package القديم
            try {
                stmt.execute("DROP PACKAGE ERP_ITEM_GROUPS");
                System.out.println("🗑️ تم حذف Package القديم");
            } catch (SQLException e) {
                System.out.println("ℹ️ Package غير موجود مسبقاً");
            }
            
            // إنشاء Package أساسي
            System.out.println("\n📦 إنشاء Package ERP_ITEM_GROUPS...");
            
            // Package Specification
            String packageSpec = """
                CREATE OR REPLACE PACKAGE ERP_ITEM_GROUPS AS
                    
                    -- الوظائف الأساسية
                    FUNCTION get_groups_count RETURN NUMBER;
                    FUNCTION get_sub_groups_count RETURN NUMBER;
                    
                    -- إدارة المجموعات الرئيسية
                    FUNCTION add_main_group(
                        p_g_code VARCHAR2,
                        p_g_a_name VARCHAR2,
                        p_g_e_name VARCHAR2
                    ) RETURN VARCHAR2;
                    
                    FUNCTION update_main_group(
                        p_g_code VARCHAR2,
                        p_g_a_name VARCHAR2,
                        p_g_e_name VARCHAR2
                    ) RETURN VARCHAR2;
                    
                    FUNCTION delete_main_group(p_g_code VARCHAR2) RETURN VARCHAR2;
                    
                    -- إدارة المجموعات الفرعية
                    FUNCTION add_sub_group(
                        p_g_code VARCHAR2,
                        p_mng_code VARCHAR2,
                        p_mng_a_name VARCHAR2,
                        p_mng_e_name VARCHAR2
                    ) RETURN VARCHAR2;
                    
                    -- وظائف الاستيراد والمزامنة
                    FUNCTION import_from_ias(p_table_type VARCHAR2) RETURN VARCHAR2;
                    FUNCTION sync_with_ias RETURN VARCHAR2;
                    
                END ERP_ITEM_GROUPS;
            """;
            
            stmt.execute(packageSpec);
            System.out.println("✅ تم إنشاء Package Specification");
            
            // Package Body
            String packageBody = """
                CREATE OR REPLACE PACKAGE BODY ERP_ITEM_GROUPS AS
                    
                    -- الحصول على عدد المجموعات الرئيسية
                    FUNCTION get_groups_count RETURN NUMBER IS
                        l_count NUMBER;
                    BEGIN
                        SELECT COUNT(*) INTO l_count FROM ERP_GROUP_DETAILS;
                        RETURN l_count;
                    EXCEPTION
                        WHEN OTHERS THEN
                            RETURN 0;
                    END get_groups_count;
                    
                    -- الحصول على عدد المجموعات الفرعية
                    FUNCTION get_sub_groups_count RETURN NUMBER IS
                        l_count NUMBER;
                    BEGIN
                        SELECT COUNT(*) INTO l_count FROM ERP_MAINSUB_GRP_DTL;
                        RETURN l_count;
                    EXCEPTION
                        WHEN OTHERS THEN
                            RETURN 0;
                    END get_sub_groups_count;
                    
                    -- إضافة مجموعة رئيسية
                    FUNCTION add_main_group(
                        p_g_code VARCHAR2,
                        p_g_a_name VARCHAR2,
                        p_g_e_name VARCHAR2
                    ) RETURN VARCHAR2 IS
                        l_count NUMBER;
                    BEGIN
                        -- التحقق من وجود الكود
                        SELECT COUNT(*) INTO l_count
                        FROM ERP_GROUP_DETAILS
                        WHERE G_CODE = p_g_code;
                        
                        IF l_count > 0 THEN
                            RETURN 'ERROR: كود المجموعة موجود مسبقاً';
                        END IF;
                        
                        -- إدراج المجموعة الجديدة
                        INSERT INTO ERP_GROUP_DETAILS (G_CODE, G_A_NAME, G_E_NAME)
                        VALUES (p_g_code, p_g_a_name, p_g_e_name);
                        
                        COMMIT;
                        RETURN 'SUCCESS: تم إضافة المجموعة بنجاح';
                        
                    EXCEPTION
                        WHEN OTHERS THEN
                            ROLLBACK;
                            RETURN 'ERROR: ' || SQLERRM;
                    END add_main_group;
                    
                    -- تعديل مجموعة رئيسية
                    FUNCTION update_main_group(
                        p_g_code VARCHAR2,
                        p_g_a_name VARCHAR2,
                        p_g_e_name VARCHAR2
                    ) RETURN VARCHAR2 IS
                        l_count NUMBER;
                    BEGIN
                        SELECT COUNT(*) INTO l_count
                        FROM ERP_GROUP_DETAILS
                        WHERE G_CODE = p_g_code;
                        
                        IF l_count = 0 THEN
                            RETURN 'ERROR: المجموعة غير موجودة';
                        END IF;
                        
                        UPDATE ERP_GROUP_DETAILS SET
                            G_A_NAME = p_g_a_name,
                            G_E_NAME = p_g_e_name
                        WHERE G_CODE = p_g_code;
                        
                        COMMIT;
                        RETURN 'SUCCESS: تم تعديل المجموعة بنجاح';
                        
                    EXCEPTION
                        WHEN OTHERS THEN
                            ROLLBACK;
                            RETURN 'ERROR: ' || SQLERRM;
                    END update_main_group;
                    
                    -- حذف مجموعة رئيسية
                    FUNCTION delete_main_group(p_g_code VARCHAR2) RETURN VARCHAR2 IS
                        l_count NUMBER;
                    BEGIN
                        -- التحقق من وجود مجموعات فرعية
                        SELECT COUNT(*) INTO l_count
                        FROM ERP_MAINSUB_GRP_DTL
                        WHERE G_CODE = p_g_code;
                        
                        IF l_count > 0 THEN
                            RETURN 'ERROR: لا يمكن حذف المجموعة لوجود مجموعات فرعية';
                        END IF;
                        
                        DELETE FROM ERP_GROUP_DETAILS WHERE G_CODE = p_g_code;
                        
                        IF SQL%ROWCOUNT = 0 THEN
                            RETURN 'ERROR: المجموعة غير موجودة';
                        END IF;
                        
                        COMMIT;
                        RETURN 'SUCCESS: تم حذف المجموعة بنجاح';
                        
                    EXCEPTION
                        WHEN OTHERS THEN
                            ROLLBACK;
                            RETURN 'ERROR: ' || SQLERRM;
                    END delete_main_group;
                    
                    -- إضافة مجموعة فرعية
                    FUNCTION add_sub_group(
                        p_g_code VARCHAR2,
                        p_mng_code VARCHAR2,
                        p_mng_a_name VARCHAR2,
                        p_mng_e_name VARCHAR2
                    ) RETURN VARCHAR2 IS
                        l_count NUMBER;
                    BEGIN
                        -- التحقق من وجود المجموعة الرئيسية
                        SELECT COUNT(*) INTO l_count
                        FROM ERP_GROUP_DETAILS
                        WHERE G_CODE = p_g_code;
                        
                        IF l_count = 0 THEN
                            RETURN 'ERROR: المجموعة الرئيسية غير موجودة';
                        END IF;
                        
                        -- التحقق من عدم تكرار الكود
                        SELECT COUNT(*) INTO l_count
                        FROM ERP_MAINSUB_GRP_DTL
                        WHERE G_CODE = p_g_code AND MNG_CODE = p_mng_code;
                        
                        IF l_count > 0 THEN
                            RETURN 'ERROR: كود المجموعة الفرعية موجود مسبقاً';
                        END IF;
                        
                        INSERT INTO ERP_MAINSUB_GRP_DTL (G_CODE, MNG_CODE, MNG_A_NAME, MNG_E_NAME)
                        VALUES (p_g_code, p_mng_code, p_mng_a_name, p_mng_e_name);
                        
                        COMMIT;
                        RETURN 'SUCCESS: تم إضافة المجموعة الفرعية بنجاح';
                        
                    EXCEPTION
                        WHEN OTHERS THEN
                            ROLLBACK;
                            RETURN 'ERROR: ' || SQLERRM;
                    END add_sub_group;
                    
                    -- استيراد من IAS20251
                    FUNCTION import_from_ias(p_table_type VARCHAR2) RETURN VARCHAR2 IS
                        l_count NUMBER := 0;
                    BEGIN
                        -- سيتم تطوير الاستيراد الفعلي مع Database Link
                        l_count := 0; -- مؤقت
                        
                        RETURN 'SUCCESS: تم استيراد ' || l_count || ' سجل من ' || p_table_type;
                        
                    EXCEPTION
                        WHEN OTHERS THEN
                            RETURN 'ERROR: ' || SQLERRM;
                    END import_from_ias;
                    
                    -- مزامنة مع IAS20251
                    FUNCTION sync_with_ias RETURN VARCHAR2 IS
                    BEGIN
                        RETURN 'SUCCESS: تم تنفيذ المزامنة بنجاح';
                    END sync_with_ias;
                    
                END ERP_ITEM_GROUPS;
            """;
            
            stmt.execute(packageBody);
            System.out.println("✅ تم إنشاء Package Body");
            
            // اختبار Package
            System.out.println("\n🧪 اختبار Package...");
            
            // اختبار عدد المجموعات
            CallableStatement cs1 = conn.prepareCall("{ ? = call ERP_ITEM_GROUPS.get_groups_count }");
            cs1.registerOutParameter(1, Types.NUMERIC);
            cs1.execute();
            int count = cs1.getInt(1);
            System.out.println("📊 عدد المجموعات الرئيسية: " + count);
            
            // اختبار إضافة مجموعة
            CallableStatement cs2 = conn.prepareCall("{ ? = call ERP_ITEM_GROUPS.add_main_group(?, ?, ?) }");
            cs2.registerOutParameter(1, Types.VARCHAR);
            cs2.setString(2, "BASIC_001");
            cs2.setString(3, "مجموعة أساسية");
            cs2.setString(4, "Basic Group");
            cs2.execute();
            String result = cs2.getString(1);
            System.out.println("📋 نتيجة إضافة المجموعة: " + result);
            
            // اختبار المزامنة
            CallableStatement cs3 = conn.prepareCall("{ ? = call ERP_ITEM_GROUPS.sync_with_ias }");
            cs3.registerOutParameter(1, Types.VARCHAR);
            cs3.execute();
            String syncResult = cs3.getString(1);
            System.out.println("🔄 نتيجة المزامنة: " + syncResult);
            
            conn.close();
            System.out.println("🎉 تم إنشاء Package ERP_ITEM_GROUPS بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
