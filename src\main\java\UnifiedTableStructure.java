import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * نظام توحيد بنية الجداول Unified Table Structure System
 */
public class UnifiedTableStructure {

    /**
     * البنية الموحدة للأصناف
     */
    public static class UnifiedItemStructure {
        // الحقول الأساسية
        public String itemCode; // كود الصنف
        public String itemName; // اسم الصنف
        public String itemDesc; // وصف الصنف
        public String categoryCode; // كود الفئة
        public String unitCode; // كود الوحدة
        public boolean isActive; // نشط/غير نشط
        public java.sql.Date createdDate; // تاريخ الإنشاء
        public java.sql.Date modifiedDate; // تاريخ التعديل

        // الحقول المالية
        public Double costPrice; // سعر التكلفة
        public Double sellPrice; // سعر البيع
        public Double stockQty; // الكمية المتاحة
        public Double minStock; // الحد الأدنى
        public Double maxStock; // الحد الأقصى
        public Double reorderLevel; // نقطة إعادة الطلب

        // حقول إضافية
        public String locationCode; // كود الموقع
        public String supplierCode; // كود المورد
        public java.sql.Date lastPurchaseDate; // آخر تاريخ شراء
        public java.sql.Date lastSaleDate; // آخر تاريخ بيع
    }

    /**
     * خريطة تحويل الحقول من IAS إلى البنية الموحدة
     */
    public static class IASFieldMapping {
        public static final Map<String, String> FIELD_MAP = new HashMap<>();

        static {
            // الحقول الأساسية
            FIELD_MAP.put("itemCode", "m.I_CODE");
            FIELD_MAP.put("itemName", "m.I_NAME");
            FIELD_MAP.put("itemDesc", "m.I_DESC");
            FIELD_MAP.put("categoryCode", "m.G_CODE");
            FIELD_MAP.put("unitCode", "m.ITM_UNT");
            FIELD_MAP.put("isActive", "CASE WHEN m.INACTIVE = 0 THEN 1 ELSE 0 END");
            FIELD_MAP.put("createdDate", "m.AD_DATE");
            FIELD_MAP.put("modifiedDate", "m.UP_DATE");

            // الحقول المالية
            FIELD_MAP.put("costPrice", "d.PRIMARY_COST");
            FIELD_MAP.put("sellPrice", "m.I_CWTAVG");
            FIELD_MAP.put("stockQty", "d.P_SIZE");
            FIELD_MAP.put("minStock", "m.ITM_MIN_LMT_QTY");
            FIELD_MAP.put("maxStock", "m.ITM_MAX_LMT_QTY");
            FIELD_MAP.put("reorderLevel", "m.ITM_ROL_LMT_QTY");

            // حقول إضافية
            FIELD_MAP.put("locationCode", "m.ASSISTANT_NO");
            FIELD_MAP.put("supplierCode", "m.V_CODE");
            FIELD_MAP.put("lastPurchaseDate", "m.INCOME_DATE");
            FIELD_MAP.put("lastSaleDate", "d.UP_DATE");
        }
    }

    /**
     * إنشاء استعلام موحد للاستيراد من IAS
     */
    public static String createUnifiedQuery() {
        return """
                    SELECT
                        I_CODE as itemCode,
                        I_NAME as itemName,
                        COALESCE(I_DESC, I_NAME) as itemDesc,
                        COALESCE(G_CODE, '001') as categoryCode,
                        'PCS' as unitCode,
                        CASE WHEN INACTIVE = 0 THEN 1 ELSE 0 END as isActive,
                        COALESCE(AD_DATE, SYSDATE) as createdDate,
                        COALESCE(UP_DATE, SYSDATE) as modifiedDate,

                        COALESCE(I_CWTAVG, 0) as costPrice,
                        COALESCE(I_CWTAVG, 0) as sellPrice,
                        1 as stockQty,
                        0 as minStock,
                        999999 as maxStock,
                        10 as reorderLevel,

                        COALESCE(ASSISTANT_NO, 'MAIN') as locationCode,
                        COALESCE(V_CODE, 'DEFAULT') as supplierCode,
                        COALESCE(INCOME_DATE, AD_DATE) as lastPurchaseDate,
                        COALESCE(UP_DATE, AD_DATE) as lastSaleDate
                    FROM IAS20251.IAS_ITM_MST
                    WHERE INACTIVE = 0
                    ORDER BY I_CODE
                """;
    }

    /**
     * تحويل ResultSet إلى البنية الموحدة
     */
    public static UnifiedItemStructure convertFromResultSet(ResultSet rs) throws SQLException {
        UnifiedItemStructure item = new UnifiedItemStructure();

        try {
            item.itemCode = rs.getString("itemCode");
            item.itemName = rs.getString("itemName");
            item.itemDesc = rs.getString("itemDesc");
            item.categoryCode = rs.getString("categoryCode");
            item.unitCode = rs.getString("unitCode");
            item.isActive = rs.getInt("isActive") == 1;
            item.createdDate = rs.getDate("createdDate");
            item.modifiedDate = rs.getDate("modifiedDate");

            item.costPrice = getDoubleOrNull(rs, "costPrice");
            item.sellPrice = getDoubleOrNull(rs, "sellPrice");
            item.stockQty = getDoubleOrNull(rs, "stockQty");
            item.minStock = getDoubleOrNull(rs, "minStock");
            item.maxStock = getDoubleOrNull(rs, "maxStock");
            item.reorderLevel = getDoubleOrNull(rs, "reorderLevel");

            item.locationCode = rs.getString("locationCode");
            item.supplierCode = rs.getString("supplierCode");
            item.lastPurchaseDate = rs.getDate("lastPurchaseDate");
            item.lastSaleDate = rs.getDate("lastSaleDate");

        } catch (SQLException e) {
            System.err.println("Error converting ResultSet: " + e.getMessage());
            throw e;
        }

        return item;
    }

    /**
     * مساعد للحصول على قيمة Double أو null
     */
    private static Double getDoubleOrNull(ResultSet rs, String columnName) throws SQLException {
        double value = rs.getDouble(columnName);
        return rs.wasNull() ? null : value;
    }

    /**
     * إنشاء جداول موحدة في النظام الحالي
     */
    public static String createUnifiedTableSQL() {
        return """
                    CREATE TABLE UNIFIED_ITEMS (
                        ITEM_CODE VARCHAR2(30) PRIMARY KEY,
                        ITEM_NAME VARCHAR2(100) NOT NULL,
                        ITEM_DESC VARCHAR2(2000),
                        CATEGORY_CODE VARCHAR2(10),
                        UNIT_CODE VARCHAR2(10),
                        IS_ACTIVE NUMBER(1) DEFAULT 1,
                        CREATED_DATE DATE DEFAULT SYSDATE,
                        MODIFIED_DATE DATE DEFAULT SYSDATE,

                        COST_PRICE NUMBER(15,3),
                        SELL_PRICE NUMBER(15,3),
                        STOCK_QTY NUMBER(15,3),
                        MIN_STOCK NUMBER(15,3),
                        MAX_STOCK NUMBER(15,3),
                        REORDER_LEVEL NUMBER(15,3),

                        LOCATION_CODE VARCHAR2(20),
                        SUPPLIER_CODE VARCHAR2(20),
                        LAST_PURCHASE_DATE DATE,
                        LAST_SALE_DATE DATE,

                        IMPORT_SOURCE VARCHAR2(20) DEFAULT 'IAS',
                        IMPORT_DATE DATE DEFAULT SYSDATE
                    )
                """;
    }

    /**
     * اختبار النظام الموحد
     */
    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("   Unified Table Structure System");
        System.out.println("====================================");
        System.out.println();

        System.out.println("Generated Unified Query:");
        System.out.println("========================");
        System.out.println(createUnifiedQuery());
        System.out.println();

        System.out.println("Unified Table Creation SQL:");
        System.out.println("===========================");
        System.out.println(createUnifiedTableSQL());
        System.out.println();

        System.out.println("Field Mappings:");
        System.out.println("===============");
        for (Map.Entry<String, String> entry : IASFieldMapping.FIELD_MAP.entrySet()) {
            System.out.printf("%-20s -> %s%n", entry.getKey(), entry.getValue());
        }
    }
}
