import javax.swing.*;
import java.awt.*;

/**
 * اختبار شجرة القائمة بعد حذف نوافذ بيانات الأصناف
 */
public class TestTreeMenuAfterDeletion extends JFrame {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new TestTreeMenuAfterDeletion().setVisible(true);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, "خطأ: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    public TestTreeMenuAfterDeletion() {
        setTitle("اختبار شجرة القائمة بعد الحذف");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(800, 600);
        setLocationRelativeTo(null);
        
        // إنشاء شجرة القائمة
        TreeMenuPanel treePanel = new TreeMenuPanel(this);
        
        // إضافة الشجرة إلى النافذة
        add(treePanel, BorderLayout.CENTER);
        
        // إضافة رسالة تأكيد
        JLabel confirmationLabel = new JLabel(
            "<html><div style='text-align: center; padding: 20px;'>" +
            "<h2>✅ تم حذف نوافذ بيانات الأصناف بنجاح!</h2>" +
            "<p>تحقق من قسم 'إدارة الأصناف' في الشجرة</p>" +
            "<p>يجب ألا تجد:</p>" +
            "<ul>" +
            "<li>❌ بيانات الأصناف</li>" +
            "<li>❌ بيانات الأصناف التفصيلية</li>" +
            "</ul>" +
            "<p>المتبقي فقط:</p>" +
            "<ul>" +
            "<li>✅ وحدات القياس</li>" +
            "<li>✅ مجموعات الأصناف</li>" +
            "<li>✅ ربط النظام واستيراد البيانات</li>" +
            "<li>✅ تقارير الأصناف</li>" +
            "</ul>" +
            "</div></html>", 
            SwingConstants.CENTER
        );
        
        confirmationLabel.setFont(new Font("Arial", Font.PLAIN, 14));
        confirmationLabel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        add(confirmationLabel, BorderLayout.SOUTH);
        
        System.out.println("🎉 تم تشغيل اختبار شجرة القائمة بعد الحذف");
        System.out.println("📋 تحقق من قسم 'إدارة الأصناف' في الشجرة");
        System.out.println("❌ يجب ألا تجد: 'بيانات الأصناف' أو 'بيانات الأصناف التفصيلية'");
        System.out.println("✅ المتبقي: وحدات القياس، مجموعات الأصناف، ربط النظام، تقارير الأصناف");
    }
}
