@echo off
echo ==========================================
echo    Ship ERP - Comprehensive Items System
echo    with IAS_ITM_MST and IAS_ITM_DTL Integration
echo ==========================================
echo.
echo Starting comprehensive item data system...
echo.
echo System Features:
echo - Complete understanding of IAS_ITM_MST and IAS_ITM_DTL structure
echo - Integrated queries between main and detail tables
echo - Main items list with primary units from IAS_ITM_DTL
echo - Dedicated units details tab showing all units
echo - Search functionality for specific item units
echo - Arabic field labels from comments system
echo - Restructured Basic Data tab with all groups fields
echo - New Main Data section with real database fields
echo.
echo Database Tables:
echo - IAS_ITM_MST: 4647 items (4630 active) - 229 columns
echo - IAS_ITM_DTL: 9108 unit details - 32 columns
echo - Relationship: One-to-Many via I_CODE
echo.
java "-Duser.language=en" "-Duser.country=US" "-Dfile.encoding=UTF-8" -cp "lib/*;." ShipERPMain
if %ERRORLEVEL% neq 0 (
    echo ERROR: System failed to start
    pause
)
