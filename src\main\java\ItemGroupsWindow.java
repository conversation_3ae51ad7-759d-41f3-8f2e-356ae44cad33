import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.DefaultTableCellRenderer;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.*;
import java.util.Vector;

/**
 * نافذة مجموعات الأصناف الجديدة
 * الأبعاد: 1377×782 بكسل
 * نوع: تبويبات حسب جداول IAS20251
 */
public class ItemGroupsWindow extends JFrame {
    
    private Connection shipErpConnection;
    private Connection ias20251Connection;
    private JTabbedPane tabbedPane;
    
    // نماذج الجداول
    private DefaultTableModel mainGroupModel;
    private DefaultTableModel mainSubGroupModel;
    private DefaultTableModel subGroupModel;
    private DefaultTableModel assistantGroupModel;
    private DefaultTableModel detailGroupModel;
    
    // الجداول
    private JTable mainGroupTable;
    private JTable mainSubGroupTable;
    private JTable subGroupTable;
    private JTable assistantGroupTable;
    private JTable detailGroupTable;
    
    public ItemGroupsWindow() {
        initializeConnections();
        initializeUI();
        loadAllData();
    }
    
    /**
     * تهيئة الاتصالات بقواعد البيانات
     */
    private void initializeConnections() {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            // الاتصال بـ SHIP_ERP
            shipErpConnection = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            System.out.println("✅ تم الاتصال بقاعدة البيانات SHIP_ERP");
            
            // الاتصال بـ IAS20251
            ias20251Connection = DriverManager.getConnection(
                "*************************************", 
                "ias20251", 
                "ys123"
            );
            System.out.println("✅ تم الاتصال بقاعدة البيانات IAS20251");
            
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, 
                "خطأ في الاتصال بقاعدة البيانات: " + e.getMessage(),
                "خطأ", 
                JOptionPane.ERROR_MESSAGE);
            e.printStackTrace();
        }
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    private void initializeUI() {
        setTitle("مجموعات الأصناف");
        setSize(1377, 782);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);
        
        // تطبيق اللغة العربية
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // إنشاء التبويبات
        tabbedPane = new JTabbedPane();
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        tabbedPane.setFont(new Font("Tahoma", Font.BOLD, 12));
        
        // إضافة التبويبات
        createMainGroupTab();
        createMainSubGroupTab();
        createSubGroupTab();
        createAssistantGroupTab();
        createDetailGroupTab();
        
        add(tabbedPane, BorderLayout.CENTER);
        
        // شريط الحالة
        JPanel statusPanel = createStatusPanel();
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * إنشاء تبويب المجموعات الرئيسية
     * بنية جدول GROUP_DETAILS من IAS20251
     */
    private void createMainGroupTab() {
        // أعمدة جدول GROUP_DETAILS
        String[] columns = {
            "كود المجموعة", "الاسم العربي", "الاسم الإنجليزي", "نسبة الضريبة الافتراضية", 
            "كمية الحد الأدنى", "كود المجموعة الداخلي", "مزامنة مع الويب", 
            "استخدام سعر البيع كسعر شراء", "السماح بالخصم", 
            "السماح بخصم فاتورة الشراء", "ترتيب المجموعة", "نشط"
        };
        
        mainGroupModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        mainGroupTable = new JTable(mainGroupModel);
        setupTableProperties(mainGroupTable);
        
        JPanel panel = createTabPanel("المجموعات الرئيسية", mainGroupTable, "main");
        tabbedPane.addTab("المجموعات الرئيسية", panel);
    }
    
    /**
     * إنشاء تبويب المجموعات الفرعية
     * بنية جدول IAS_MAINSUB_GRP_DTL من IAS20251
     */
    private void createMainSubGroupTab() {
        String[] columns = {
            "كود المجموعة الرئيسية", "كود المجموعة الفرعية", "الاسم العربي", "الاسم الإنجليزي", 
            "كود المجموعة الداخلي", "مزامنة مع الويب", "ترتيب المجموعة", "نشط"
        };
        
        mainSubGroupModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        mainSubGroupTable = new JTable(mainSubGroupModel);
        setupTableProperties(mainSubGroupTable);
        
        JPanel panel = createTabPanel("المجموعات الفرعية", mainSubGroupTable, "mainsub");
        tabbedPane.addTab("المجموعات الفرعية", panel);
    }
    
    /**
     * إنشاء تبويب المجموعات تحت فرعية
     * بنية جدول IAS_SUB_GRP_DTL من IAS20251
     */
    private void createSubGroupTab() {
        String[] columns = {
            "كود المجموعة الرئيسية", "كود المجموعة الفرعية", "كود المجموعة تحت فرعية", 
            "الاسم العربي", "الاسم الإنجليزي", "كود المجموعة الداخلي", 
            "مزامنة مع الويب", "ترتيب المجموعة", "نشط"
        };
        
        subGroupModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        subGroupTable = new JTable(subGroupModel);
        setupTableProperties(subGroupTable);
        
        JPanel panel = createTabPanel("المجموعات تحت فرعية", subGroupTable, "sub");
        tabbedPane.addTab("المجموعات تحت فرعية", panel);
    }
    
    /**
     * إنشاء تبويب المجموعات المساعدة
     * بنية جدول IAS_ASSISTANT_GROUP من IAS20251
     */
    private void createAssistantGroupTab() {
        String[] columns = {
            "كود المجموعة الرئيسية", "كود المجموعة الفرعية", "كود المجموعة تحت فرعية", 
            "رقم المجموعة المساعدة", "الاسم العربي", "الاسم الإنجليزي", 
            "كود المجموعة الداخلي", "مزامنة مع الويب", "ترتيب المجموعة", "نشط"
        };
        
        assistantGroupModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        assistantGroupTable = new JTable(assistantGroupModel);
        setupTableProperties(assistantGroupTable);
        
        JPanel panel = createTabPanel("المجموعات المساعدة", assistantGroupTable, "assistant");
        tabbedPane.addTab("المجموعات المساعدة", panel);
    }
    
    /**
     * إنشاء تبويب المجموعات التفصيلية
     * بنية جدول IAS_DETAIL_GROUP من IAS20251
     */
    private void createDetailGroupTab() {
        String[] columns = {
            "كود المجموعة الرئيسية", "كود المجموعة الفرعية", "كود المجموعة تحت فرعية", 
            "رقم المجموعة المساعدة", "رقم المجموعة التفصيلية", "الاسم العربي", 
            "الاسم الإنجليزي", "كود المجموعة الداخلي", "مزامنة مع الويب", 
            "ترتيب المجموعة", "نشط"
        };
        
        detailGroupModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        detailGroupTable = new JTable(detailGroupModel);
        setupTableProperties(detailGroupTable);
        
        JPanel panel = createTabPanel("المجموعات التفصيلية", detailGroupTable, "detail");
        tabbedPane.addTab("المجموعات التفصيلية", panel);
    }
    
    /**
     * إعداد خصائص الجدول
     */
    private void setupTableProperties(JTable table) {
        table.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        table.setAutoResizeMode(JTable.AUTO_RESIZE_ALL_COLUMNS);
        table.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        table.setRowHeight(25);
        
        // تنسيق الخط
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 11);
        table.setFont(arabicFont);
        table.getTableHeader().setFont(new Font("Tahoma", Font.BOLD, 11));
        
        // محاذاة النص
        DefaultTableCellRenderer rightRenderer = new DefaultTableCellRenderer();
        rightRenderer.setHorizontalAlignment(SwingConstants.RIGHT);
        rightRenderer.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        for (int i = 0; i < table.getColumnCount(); i++) {
            table.getColumnModel().getColumn(i).setCellRenderer(rightRenderer);
        }
        
        // ألوان متناوبة للصفوف
        table.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, 
                    boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                
                if (!isSelected) {
                    if (row % 2 == 0) {
                        c.setBackground(Color.WHITE);
                    } else {
                        c.setBackground(new Color(245, 245, 245));
                    }
                }
                
                setHorizontalAlignment(SwingConstants.RIGHT);
                setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                return c;
            }
        });
    }
    
    /**
     * إنشاء لوحة التبويب
     */
    private JPanel createTabPanel(String title, JTable table, String type) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // شريط الأدوات
        JPanel toolbarPanel = createToolbar(type);
        panel.add(toolbarPanel, BorderLayout.NORTH);
        
        // الجدول مع شريط التمرير
        JScrollPane scrollPane = new JScrollPane(table);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * إنشاء شريط الأدوات
     */
    private JPanel createToolbar(String type) {
        JPanel toolbar = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        toolbar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        toolbar.setBorder(BorderFactory.createEtchedBorder());
        
        // أزرار الأدوات
        JButton addBtn = createStyledButton("إضافة", new Color(46, 125, 50));
        JButton editBtn = createStyledButton("تعديل", new Color(25, 118, 210));
        JButton deleteBtn = createStyledButton("حذف", new Color(211, 47, 47));
        JButton refreshBtn = createStyledButton("تحديث", new Color(123, 31, 162));
        JButton importBtn = createStyledButton("استيراد", new Color(255, 152, 0));
        
        // إضافة الأحداث
        addBtn.addActionListener(e -> addRecord(type));
        editBtn.addActionListener(e -> editRecord(type));
        deleteBtn.addActionListener(e -> deleteRecord(type));
        refreshBtn.addActionListener(e -> refreshData(type));
        importBtn.addActionListener(e -> importData(type));
        
        toolbar.add(addBtn);
        toolbar.add(editBtn);
        toolbar.add(deleteBtn);
        toolbar.add(new JSeparator(SwingConstants.VERTICAL));
        toolbar.add(refreshBtn);
        toolbar.add(importBtn);
        
        return toolbar;
    }
    
    /**
     * إنشاء زر منسق
     */
    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setFont(new Font("Tahoma", Font.BOLD, 11));
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(80, 30));
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        
        // تأثير hover
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(color.brighter());
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(color);
            }
        });
        
        return button;
    }
    
    /**
     * إنشاء شريط الحالة
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        JLabel statusLabel = new JLabel("جاهز");
        statusLabel.setFont(new Font("Tahoma", Font.PLAIN, 11));
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        panel.add(statusLabel, BorderLayout.WEST);
        
        return panel;
    }
    
    /**
     * تحميل جميع البيانات
     */
    private void loadAllData() {
        loadMainGroupData();
        loadMainSubGroupData();
        loadSubGroupData();
        loadAssistantGroupData();
        loadDetailGroupData();
    }
