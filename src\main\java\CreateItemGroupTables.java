import java.sql.*;

/**
 * إنشاء جداول مجموعات الأصناف في قاعدة البيانات SHIP_ERP
 * Create Item Group Tables in SHIP_ERP Database
 */
public class CreateItemGroupTables {
    
    public static void main(String[] args) {
        try {
            // تحميل Oracle JDBC driver
            Class.forName("oracle.jdbc.OracleDriver");
            System.out.println("✅ تم تحميل Oracle JDBC driver بنجاح");
            
            // الاتصال بقاعدة البيانات SHIP_ERP
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            conn.setAutoCommit(false);
            
            System.out.println("✅ متصل بقاعدة البيانات SHIP_ERP");
            
            // إنشاء الجداول بالترتيب الصحيح (حسب العلاقات)
            createMainGroupTable(conn);           // 1. المجموعات الرئيسية
            createMainSubGroupTable(conn);        // 2. المجموعات الفرعية
            createSubGroupTable(conn);            // 3. المجموعات تحت فرعية
            createAssistantGroupTable(conn);      // 4. المجموعات المساعدة
            createDetailGroupTable(conn);         // 5. المجموعات التفصيلية
            
            // إنشاء العلاقات والقيود
            createConstraints(conn);
            
            // إنشاء الفهارس
            createIndexes(conn);
            
            // إنشاء التسلسلات
            createSequences(conn);
            
            conn.commit();
            System.out.println("🎉 تم إنشاء جميع جداول مجموعات الأصناف بنجاح!");
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * إنشاء جدول المجموعات الرئيسية
     */
    private static void createMainGroupTable(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء جدول المجموعات الرئيسية...");
        
        String sql = """
            CREATE TABLE ERP_GROUP_DETAILS (
                G_CODE VARCHAR2(10) NOT NULL,
                G_A_NAME VARCHAR2(100) NOT NULL,
                G_E_NAME VARCHAR2(100),
                TAX_PRCNT_DFLT NUMBER,
                ROL_LMT_QTY NUMBER,
                G_I_CODE VARCHAR2(6),
                SYNCHRNZ_TO_WEB_FLG NUMBER(1) DEFAULT 0,
                USE_SAL_PRICE_AS_PUR_PRICE NUMBER(1) DEFAULT 0,
                AD_U_ID NUMBER(5),
                AD_DATE DATE,
                UP_U_ID NUMBER(5),
                UP_DATE DATE,
                UP_CNT NUMBER(10),
                PR_REP NUMBER(10),
                AD_TRMNL_NM VARCHAR2(50),
                UP_TRMNL_NM VARCHAR2(50),
                ALLOW_DISC_FLG NUMBER(1) DEFAULT 0,
                ALLOW_DISC_PI_FLG NUMBER(1) DEFAULT 0,
                G_ORDR NUMBER,
                LOW_SAL_PRICE_ALLW_TYP NUMBER(1),
                LOW_SAL_PRICE_ALLW_SGN CHAR(1),
                LOW_SAL_PRICE_ALLW_VAL_TYP NUMBER(1),
                LOW_SAL_PRICE_ALLW_VAL NUMBER,
                IS_ACTIVE NUMBER(1) DEFAULT 1,
                CREATED_BY VARCHAR2(50) DEFAULT 'SYSTEM',
                CREATED_DATE DATE DEFAULT SYSDATE,
                UPDATED_BY VARCHAR2(50),
                UPDATED_DATE DATE
            )
        """;
        
        executeSQL(conn, sql, "جدول المجموعات الرئيسية");
    }
    
    /**
     * إنشاء جدول المجموعات الفرعية
     */
    private static void createMainSubGroupTable(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء جدول المجموعات الفرعية...");
        
        String sql = """
            CREATE TABLE ERP_MAINSUB_GRP_DTL (
                G_CODE VARCHAR2(10) NOT NULL,
                MNG_CODE VARCHAR2(10) NOT NULL,
                MNG_A_NAME VARCHAR2(100) NOT NULL,
                MNG_E_NAME VARCHAR2(100),
                MNG_I_CODE VARCHAR2(6),
                SYNCHRNZ_TO_WEB_FLG NUMBER(1) DEFAULT 0,
                MNG_ORDR NUMBER(15),
                AD_U_ID NUMBER(5),
                AD_DATE DATE,
                UP_U_ID NUMBER(5),
                UP_DATE DATE,
                UP_CNT NUMBER(10),
                PR_REP NUMBER(6),
                AD_TRMNL_NM VARCHAR2(50),
                UP_TRMNL_NM VARCHAR2(50),
                IS_ACTIVE NUMBER(1) DEFAULT 1,
                CREATED_BY VARCHAR2(50) DEFAULT 'SYSTEM',
                CREATED_DATE DATE DEFAULT SYSDATE,
                UPDATED_BY VARCHAR2(50),
                UPDATED_DATE DATE
            )
        """;
        
        executeSQL(conn, sql, "جدول المجموعات الفرعية");
    }
    
    /**
     * إنشاء جدول المجموعات تحت فرعية
     */
    private static void createSubGroupTable(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء جدول المجموعات تحت فرعية...");
        
        String sql = """
            CREATE TABLE ERP_SUB_GRP_DTL (
                G_CODE VARCHAR2(10),
                MNG_CODE VARCHAR2(10),
                SUBG_CODE VARCHAR2(10) NOT NULL,
                SUBG_A_NAME VARCHAR2(100) NOT NULL,
                SUBG_E_NAME VARCHAR2(100),
                SUBG_I_CODE VARCHAR2(6),
                SYNCHRNZ_TO_WEB_FLG NUMBER(1) DEFAULT 0,
                AD_U_ID NUMBER(5),
                AD_DATE DATE,
                UP_U_ID NUMBER(5),
                UP_DATE DATE,
                UP_CNT NUMBER(10),
                PR_REP NUMBER(6),
                AD_TRMNL_NM VARCHAR2(50),
                UP_TRMNL_NM VARCHAR2(50),
                SUBG_ORDR NUMBER,
                IS_ACTIVE NUMBER(1) DEFAULT 1,
                CREATED_BY VARCHAR2(50) DEFAULT 'SYSTEM',
                CREATED_DATE DATE DEFAULT SYSDATE,
                UPDATED_BY VARCHAR2(50),
                UPDATED_DATE DATE
            )
        """;
        
        executeSQL(conn, sql, "جدول المجموعات تحت فرعية");
    }
    
    /**
     * إنشاء جدول المجموعات المساعدة
     */
    private static void createAssistantGroupTable(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء جدول المجموعات المساعدة...");
        
        String sql = """
            CREATE TABLE ERP_ASSISTANT_GROUP (
                G_CODE VARCHAR2(10),
                MNG_CODE VARCHAR2(10),
                SUBG_CODE VARCHAR2(10),
                ASSISTANT_NO VARCHAR2(10) NOT NULL,
                ASSIST_I_CODE VARCHAR2(6),
                ASSISTANT_A_NAME VARCHAR2(100) NOT NULL,
                ASSISTANT_E_NAME VARCHAR2(100),
                SYNCHRNZ_TO_WEB_FLG NUMBER(1) DEFAULT 0,
                AD_U_ID NUMBER(5),
                AD_DATE DATE,
                UP_U_ID NUMBER(5),
                UP_DATE DATE,
                UP_CNT NUMBER(10),
                PR_REP NUMBER(10),
                AD_TRMNL_NM VARCHAR2(50),
                UP_TRMNL_NM VARCHAR2(50),
                IS_ACTIVE NUMBER(1) DEFAULT 1,
                CREATED_BY VARCHAR2(50) DEFAULT 'SYSTEM',
                CREATED_DATE DATE DEFAULT SYSDATE,
                UPDATED_BY VARCHAR2(50),
                UPDATED_DATE DATE
            )
        """;
        
        executeSQL(conn, sql, "جدول المجموعات المساعدة");
    }
    
    /**
     * إنشاء جدول المجموعات التفصيلية
     */
    private static void createDetailGroupTable(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء جدول المجموعات التفصيلية...");
        
        String sql = """
            CREATE TABLE ERP_DETAIL_GROUP (
                G_CODE VARCHAR2(10),
                MNG_CODE VARCHAR2(10),
                SUBG_CODE VARCHAR2(10),
                ASSISTANT_NO VARCHAR2(10),
                DETAIL_NO VARCHAR2(10) NOT NULL,
                DET_I_CODE VARCHAR2(6),
                DETAIL_A_NAME VARCHAR2(100) NOT NULL,
                DETAIL_E_NAME VARCHAR2(100),
                SYNCHRNZ_TO_WEB_FLG NUMBER(1) DEFAULT 0,
                AD_TRMNL_NM VARCHAR2(50),
                UP_TRMNL_NM VARCHAR2(50),
                AD_U_ID NUMBER(5),
                AD_DATE DATE,
                UP_U_ID NUMBER(5),
                UP_DATE DATE,
                UP_CNT NUMBER(10),
                PR_REP NUMBER(10),
                IS_ACTIVE NUMBER(1) DEFAULT 1,
                CREATED_BY VARCHAR2(50) DEFAULT 'SYSTEM',
                CREATED_DATE DATE DEFAULT SYSDATE,
                UPDATED_BY VARCHAR2(50),
                UPDATED_DATE DATE
            )
        """;
        
        executeSQL(conn, sql, "جدول المجموعات التفصيلية");
    }
    
    /**
     * إنشاء القيود والعلاقات
     */
    private static void createConstraints(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء القيود والعلاقات...");
        
        // Primary Keys
        String[] primaryKeys = {
            "ALTER TABLE ERP_GROUP_DETAILS ADD CONSTRAINT PK_ERP_GROUP_DETAILS PRIMARY KEY (G_CODE)",
            "ALTER TABLE ERP_MAINSUB_GRP_DTL ADD CONSTRAINT PK_ERP_MAINSUB_GRP_DTL PRIMARY KEY (G_CODE, MNG_CODE)",
            "ALTER TABLE ERP_SUB_GRP_DTL ADD CONSTRAINT PK_ERP_SUB_GRP_DTL PRIMARY KEY (SUBG_CODE)",
            "ALTER TABLE ERP_ASSISTANT_GROUP ADD CONSTRAINT PK_ERP_ASSISTANT_GROUP PRIMARY KEY (ASSISTANT_NO)",
            "ALTER TABLE ERP_DETAIL_GROUP ADD CONSTRAINT PK_ERP_DETAIL_GROUP PRIMARY KEY (DETAIL_NO)"
        };
        
        for (String pk : primaryKeys) {
            executeSQL(conn, pk, "Primary Key");
        }
        
        // Foreign Keys
        String[] foreignKeys = {
            "ALTER TABLE ERP_MAINSUB_GRP_DTL ADD CONSTRAINT FK_MAINSUB_GROUP FOREIGN KEY (G_CODE) REFERENCES ERP_GROUP_DETAILS(G_CODE)"
        };
        
        for (String fk : foreignKeys) {
            executeSQL(conn, fk, "Foreign Key");
        }
        
        // Check Constraints
        String[] checkConstraints = {
            "ALTER TABLE ERP_GROUP_DETAILS ADD CONSTRAINT CHK_GROUP_NAME CHECK (G_A_NAME IS NOT NULL)",
            "ALTER TABLE ERP_MAINSUB_GRP_DTL ADD CONSTRAINT CHK_MAINSUB_NAME CHECK (MNG_A_NAME IS NOT NULL)",
            "ALTER TABLE ERP_SUB_GRP_DTL ADD CONSTRAINT CHK_SUB_NAME CHECK (SUBG_A_NAME IS NOT NULL)",
            "ALTER TABLE ERP_ASSISTANT_GROUP ADD CONSTRAINT CHK_ASSISTANT_NAME CHECK (ASSISTANT_A_NAME IS NOT NULL)",
            "ALTER TABLE ERP_DETAIL_GROUP ADD CONSTRAINT CHK_DETAIL_NAME CHECK (DETAIL_A_NAME IS NOT NULL)"
        };
        
        for (String chk : checkConstraints) {
            executeSQL(conn, chk, "Check Constraint");
        }
    }
    
    /**
     * إنشاء الفهارس
     */
    private static void createIndexes(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء الفهارس...");
        
        String[] indexes = {
            "CREATE INDEX IDX_GROUP_NAME ON ERP_GROUP_DETAILS(G_A_NAME)",
            "CREATE INDEX IDX_MAINSUB_NAME ON ERP_MAINSUB_GRP_DTL(MNG_A_NAME)",
            "CREATE INDEX IDX_SUB_NAME ON ERP_SUB_GRP_DTL(SUBG_A_NAME)",
            "CREATE INDEX IDX_ASSISTANT_NAME ON ERP_ASSISTANT_GROUP(ASSISTANT_A_NAME)",
            "CREATE INDEX IDX_DETAIL_NAME ON ERP_DETAIL_GROUP(DETAIL_A_NAME)",
            "CREATE INDEX IDX_MAINSUB_GCODE ON ERP_MAINSUB_GRP_DTL(G_CODE)"
        };
        
        for (String idx : indexes) {
            executeSQL(conn, idx, "Index");
        }
    }
    
    /**
     * إنشاء التسلسلات
     */
    private static void createSequences(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء التسلسلات...");
        
        String[] sequences = {
            "CREATE SEQUENCE SEQ_GROUP_DETAILS START WITH 1 INCREMENT BY 1",
            "CREATE SEQUENCE SEQ_MAINSUB_GRP START WITH 1 INCREMENT BY 1",
            "CREATE SEQUENCE SEQ_SUB_GRP START WITH 1 INCREMENT BY 1",
            "CREATE SEQUENCE SEQ_ASSISTANT_GRP START WITH 1 INCREMENT BY 1",
            "CREATE SEQUENCE SEQ_DETAIL_GRP START WITH 1 INCREMENT BY 1"
        };
        
        for (String seq : sequences) {
            executeSQL(conn, seq, "Sequence");
        }
    }
    
    /**
     * تنفيذ SQL مع معالجة الأخطاء
     */
    private static void executeSQL(Connection conn, String sql, String description) throws SQLException {
        try {
            Statement stmt = conn.createStatement();
            stmt.execute(sql);
            System.out.println("✅ تم إنشاء " + description);
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) { // ORA-00955: name is already used
                System.out.println("⚠️ " + description + " موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إنشاء " + description + ": " + e.getMessage());
                throw e;
            }
        }
    }
}
