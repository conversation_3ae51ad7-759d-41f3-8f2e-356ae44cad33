package com.shipment.erp.util;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.Locale;

/**
 * أداة للتعامل مع التواريخ والأوقات
 */
public class DateUtil {

    // تنسيقات التاريخ المختلفة
    public static final DateTimeFormatter DATE_FORMAT_ARABIC = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    public static final DateTimeFormatter DATE_FORMAT_ISO = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter DATETIME_FORMAT_ARABIC = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
    public static final DateTimeFormatter DATETIME_FORMAT_ISO = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter TIME_FORMAT = DateTimeFormatter.ofPattern("HH:mm:ss");
    public static final DateTimeFormatter TIME_FORMAT_SHORT = DateTimeFormatter.ofPattern("HH:mm");

    // المنطقة الزمنية الافتراضية (السعودية)
    public static final ZoneId DEFAULT_ZONE = ZoneId.of("Asia/Riyadh");

    /**
     * الحصول على التاريخ الحالي
     */
    public static LocalDate getCurrentDate() {
        return LocalDate.now(DEFAULT_ZONE);
    }

    /**
     * الحصول على التاريخ والوقت الحالي
     */
    public static LocalDateTime getCurrentDateTime() {
        return LocalDateTime.now(DEFAULT_ZONE);
    }

    /**
     * تنسيق التاريخ بالتنسيق العربي
     */
    public static String formatDateArabic(LocalDate date) {
        return date != null ? date.format(DATE_FORMAT_ARABIC) : "";
    }

    /**
     * تنسيق التاريخ والوقت بالتنسيق العربي
     */
    public static String formatDateTimeArabic(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATETIME_FORMAT_ARABIC) : "";
    }

    /**
     * تنسيق التاريخ بتنسيق ISO
     */
    public static String formatDateISO(LocalDate date) {
        return date != null ? date.format(DATE_FORMAT_ISO) : "";
    }

    /**
     * تنسيق التاريخ والوقت بتنسيق ISO
     */
    public static String formatDateTimeISO(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATETIME_FORMAT_ISO) : "";
    }

    /**
     * تنسيق الوقت
     */
    public static String formatTime(LocalTime time) {
        return time != null ? time.format(TIME_FORMAT) : "";
    }

    /**
     * تنسيق الوقت (مختصر)
     */
    public static String formatTimeShort(LocalTime time) {
        return time != null ? time.format(TIME_FORMAT_SHORT) : "";
    }

    /**
     * تحويل النص إلى تاريخ
     */
    public static LocalDate parseDate(String dateString) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null;
        }

        try {
            // محاولة التنسيق العربي أولاً
            return LocalDate.parse(dateString.trim(), DATE_FORMAT_ARABIC);
        } catch (DateTimeParseException e1) {
            try {
                // محاولة تنسيق ISO
                return LocalDate.parse(dateString.trim(), DATE_FORMAT_ISO);
            } catch (DateTimeParseException e2) {
                throw new IllegalArgumentException("تنسيق التاريخ غير صحيح: " + dateString);
            }
        }
    }

    /**
     * تحويل النص إلى تاريخ ووقت
     */
    public static LocalDateTime parseDateTime(String dateTimeString) {
        if (dateTimeString == null || dateTimeString.trim().isEmpty()) {
            return null;
        }

        try {
            // محاولة التنسيق العربي أولاً
            return LocalDateTime.parse(dateTimeString.trim(), DATETIME_FORMAT_ARABIC);
        } catch (DateTimeParseException e1) {
            try {
                // محاولة تنسيق ISO
                return LocalDateTime.parse(dateTimeString.trim(), DATETIME_FORMAT_ISO);
            } catch (DateTimeParseException e2) {
                throw new IllegalArgumentException("تنسيق التاريخ والوقت غير صحيح: " + dateTimeString);
            }
        }
    }

    /**
     * تحويل النص إلى وقت
     */
    public static LocalTime parseTime(String timeString) {
        if (timeString == null || timeString.trim().isEmpty()) {
            return null;
        }

        try {
            return LocalTime.parse(timeString.trim(), TIME_FORMAT);
        } catch (DateTimeParseException e1) {
            try {
                return LocalTime.parse(timeString.trim(), TIME_FORMAT_SHORT);
            } catch (DateTimeParseException e2) {
                throw new IllegalArgumentException("تنسيق الوقت غير صحيح: " + timeString);
            }
        }
    }

    /**
     * حساب الفرق بين تاريخين بالأيام
     */
    public static long daysBetween(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            return 0;
        }
        return ChronoUnit.DAYS.between(startDate, endDate);
    }

    /**
     * حساب الفرق بين تاريخين بالأشهر
     */
    public static long monthsBetween(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            return 0;
        }
        return ChronoUnit.MONTHS.between(startDate, endDate);
    }

    /**
     * حساب الفرق بين تاريخين بالسنوات
     */
    public static long yearsBetween(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            return 0;
        }
        return ChronoUnit.YEARS.between(startDate, endDate);
    }

    /**
     * إضافة أيام إلى التاريخ
     */
    public static LocalDate addDays(LocalDate date, long days) {
        return date != null ? date.plusDays(days) : null;
    }

    /**
     * إضافة أشهر إلى التاريخ
     */
    public static LocalDate addMonths(LocalDate date, long months) {
        return date != null ? date.plusMonths(months) : null;
    }

    /**
     * إضافة سنوات إلى التاريخ
     */
    public static LocalDate addYears(LocalDate date, long years) {
        return date != null ? date.plusYears(years) : null;
    }

    /**
     * الحصول على بداية الشهر
     */
    public static LocalDate getStartOfMonth(LocalDate date) {
        return date != null ? date.withDayOfMonth(1) : null;
    }

    /**
     * الحصول على نهاية الشهر
     */
    public static LocalDate getEndOfMonth(LocalDate date) {
        return date != null ? date.withDayOfMonth(date.lengthOfMonth()) : null;
    }

    /**
     * الحصول على بداية السنة
     */
    public static LocalDate getStartOfYear(LocalDate date) {
        return date != null ? date.withDayOfYear(1) : null;
    }

    /**
     * الحصول على نهاية السنة
     */
    public static LocalDate getEndOfYear(LocalDate date) {
        return date != null ? date.withDayOfYear(date.lengthOfYear()) : null;
    }

    /**
     * التحقق من كون التاريخ في الماضي
     */
    public static boolean isPast(LocalDate date) {
        return date != null && date.isBefore(getCurrentDate());
    }

    /**
     * التحقق من كون التاريخ في المستقبل
     */
    public static boolean isFuture(LocalDate date) {
        return date != null && date.isAfter(getCurrentDate());
    }

    /**
     * التحقق من كون التاريخ اليوم
     */
    public static boolean isToday(LocalDate date) {
        return date != null && date.equals(getCurrentDate());
    }

    /**
     * التحقق من كون التاريخ في نطاق معين
     */
    public static boolean isInRange(LocalDate date, LocalDate startDate, LocalDate endDate) {
        if (date == null || startDate == null || endDate == null) {
            return false;
        }
        return !date.isBefore(startDate) && !date.isAfter(endDate);
    }

    /**
     * الحصول على عمر بالسنوات
     */
    public static int getAge(LocalDate birthDate) {
        if (birthDate == null) {
            return 0;
        }
        return (int) yearsBetween(birthDate, getCurrentDate());
    }

    /**
     * تحويل التاريخ إلى التقويم الهجري (تقريبي)
     */
    public static String toHijriDate(LocalDate gregorianDate) {
        if (gregorianDate == null) {
            return "";
        }
        
        // تحويل تقريبي - يحتاج لمكتبة متخصصة للدقة
        long daysSinceEpoch = gregorianDate.toEpochDay();
        long hijriDays = daysSinceEpoch - 227015; // تقريب للفرق بين التقويمين
        long hijriYear = 1 + (hijriDays / 354);
        long remainingDays = hijriDays % 354;
        long hijriMonth = 1 + (remainingDays / 30);
        long hijriDay = 1 + (remainingDays % 30);
        
        return String.format("%02d/%02d/%04d هـ", hijriDay, hijriMonth, hijriYear);
    }

    /**
     * الحصول على اسم اليوم بالعربية
     */
    public static String getDayNameArabic(LocalDate date) {
        if (date == null) {
            return "";
        }
        
        switch (date.getDayOfWeek()) {
            case SUNDAY: return "الأحد";
            case MONDAY: return "الاثنين";
            case TUESDAY: return "الثلاثاء";
            case WEDNESDAY: return "الأربعاء";
            case THURSDAY: return "الخميس";
            case FRIDAY: return "الجمعة";
            case SATURDAY: return "السبت";
            default: return "";
        }
    }

    /**
     * الحصول على اسم الشهر بالعربية
     */
    public static String getMonthNameArabic(LocalDate date) {
        if (date == null) {
            return "";
        }
        
        switch (date.getMonth()) {
            case JANUARY: return "يناير";
            case FEBRUARY: return "فبراير";
            case MARCH: return "مارس";
            case APRIL: return "أبريل";
            case MAY: return "مايو";
            case JUNE: return "يونيو";
            case JULY: return "يوليو";
            case AUGUST: return "أغسطس";
            case SEPTEMBER: return "سبتمبر";
            case OCTOBER: return "أكتوبر";
            case NOVEMBER: return "نوفمبر";
            case DECEMBER: return "ديسمبر";
            default: return "";
        }
    }

    /**
     * تنسيق التاريخ بالعربية الكاملة
     */
    public static String formatDateArabicFull(LocalDate date) {
        if (date == null) {
            return "";
        }
        
        return String.format("%s، %d %s %d",
            getDayNameArabic(date),
            date.getDayOfMonth(),
            getMonthNameArabic(date),
            date.getYear()
        );
    }

    /**
     * التحقق من صحة التاريخ
     */
    public static boolean isValidDate(String dateString) {
        try {
            parseDate(dateString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * التحقق من صحة التاريخ والوقت
     */
    public static boolean isValidDateTime(String dateTimeString) {
        try {
            parseDateTime(dateTimeString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
