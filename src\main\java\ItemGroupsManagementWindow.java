import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.ComponentOrientation;
import java.awt.Cursor;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.Vector;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSeparator;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.ListSelectionModel;
import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة إدارة مجموعات الأصناف الأبعاد: 1377×782 بكسل نوع: تبويبات حسب جداول IAS20251
 */
public class ItemGroupsManagementWindow extends JFrame {

    private Connection shipErpConnection;
    private Connection ias20251Connection;
    private JTabbedPane tabbedPane;

    // نماذج الجداول
    private DefaultTableModel mainGroupModel;
    private DefaultTableModel mainSubGroupModel;
    private DefaultTableModel subGroupModel;
    private DefaultTableModel assistantGroupModel;
    private DefaultTableModel detailGroupModel;

    // الجداول
    private JTable mainGroupTable;
    private JTable mainSubGroupTable;
    private JTable subGroupTable;
    private JTable assistantGroupTable;
    private JTable detailGroupTable;

    public ItemGroupsManagementWindow() {
        try {
            initializeConnections();
            verifyTablesExist();
            initializeUI();
            loadAllData();
        } catch (Exception e) {
            handleError("خطأ في تهيئة النافذة", e);
        }
    }

    /**
     * تهيئة الاتصالات بقواعد البيانات
     */
    private void initializeConnections() throws SQLException, ClassNotFoundException {
        Class.forName("oracle.jdbc.driver.OracleDriver");

        // الاتصال بـ SHIP_ERP
        shipErpConnection = DriverManager.getConnection("*************************************",
                "ship_erp", "ship_erp_password");
        System.out.println("✅ تم الاتصال بقاعدة البيانات SHIP_ERP");

        // الاتصال بـ IAS20251
        ias20251Connection = DriverManager.getConnection("*************************************",
                "ias20251", "ys123");
        System.out.println("✅ تم الاتصال بقاعدة البيانات IAS20251");
    }

    /**
     * التحقق من وجود الجداول وإنشاؤها إذا لم تكن موجودة
     */
    private void verifyTablesExist() throws SQLException {
        String[] requiredTables = {"ERP_GROUP_DETAILS", "ERP_MAINSUB_GRP_DTL", "ERP_SUB_GRP_DTL",
                "ERP_ASSISTANT_GROUP", "ERP_DETAIL_GROUP"};

        DatabaseMetaData metaData = shipErpConnection.getMetaData();

        for (String tableName : requiredTables) {
            ResultSet rs = metaData.getTables(null, null, tableName, new String[] {"TABLE"});
            if (!rs.next()) {
                throw new SQLException("الجدول المطلوب غير موجود: " + tableName
                        + ". يرجى تشغيل CreateItemGroupTablesFromIAS أولاً.");
            }
            rs.close();
        }

        System.out.println("✅ تم التحقق من وجود جميع الجداول المطلوبة");
    }

    /**
     * تهيئة واجهة المستخدم
     */
    private void initializeUI() {
        setTitle("إدارة مجموعات الأصناف");
        setSize(1377, 782);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setLocationRelativeTo(null);

        // تطبيق اللغة العربية
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // إنشاء التبويبات
        tabbedPane = new JTabbedPane();
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        tabbedPane.setFont(new Font("Tahoma", Font.BOLD, 12));

        // إضافة التبويبات
        createMainGroupTab();
        createMainSubGroupTab();
        createSubGroupTab();
        createAssistantGroupTab();
        createDetailGroupTab();

        add(tabbedPane, BorderLayout.CENTER);

        // شريط الحالة
        JPanel statusPanel = createStatusPanel();
        add(statusPanel, BorderLayout.SOUTH);
    }

    /**
     * إنشاء تبويب المجموعات الرئيسية بنية جدول GROUP_DETAILS من IAS20251
     */
    private void createMainGroupTab() {
        try {
            // الحصول على بنية الجدول من IAS20251
            String[] columns = getTableColumns("GROUP_DETAILS", ias20251Connection);

            mainGroupModel = new DefaultTableModel(columns, 0) {
                @Override
                public boolean isCellEditable(int row, int column) {
                    return false;
                }
            };

            mainGroupTable = new JTable(mainGroupModel);
            setupTableProperties(mainGroupTable);

            JPanel panel = createTabPanel("المجموعات الرئيسية", mainGroupTable, "main");
            tabbedPane.addTab("المجموعات الرئيسية", panel);

        } catch (SQLException e) {
            handleError("خطأ في إنشاء تبويب المجموعات الرئيسية", e);
        }
    }

    /**
     * إنشاء تبويب المجموعات الفرعية بنية جدول IAS_MAINSUB_GRP_DTL من IAS20251
     */
    private void createMainSubGroupTab() {
        try {
            String[] columns = getTableColumns("IAS_MAINSUB_GRP_DTL", ias20251Connection);

            mainSubGroupModel = new DefaultTableModel(columns, 0) {
                @Override
                public boolean isCellEditable(int row, int column) {
                    return false;
                }
            };

            mainSubGroupTable = new JTable(mainSubGroupModel);
            setupTableProperties(mainSubGroupTable);

            JPanel panel = createTabPanel("المجموعات الفرعية", mainSubGroupTable, "mainsub");
            tabbedPane.addTab("المجموعات الفرعية", panel);

        } catch (SQLException e) {
            handleError("خطأ في إنشاء تبويب المجموعات الفرعية", e);
        }
    }

    /**
     * إنشاء تبويب المجموعات تحت فرعية بنية جدول IAS_SUB_GRP_DTL من IAS20251
     */
    private void createSubGroupTab() {
        try {
            String[] columns = getTableColumns("IAS_SUB_GRP_DTL", ias20251Connection);

            subGroupModel = new DefaultTableModel(columns, 0) {
                @Override
                public boolean isCellEditable(int row, int column) {
                    return false;
                }
            };

            subGroupTable = new JTable(subGroupModel);
            setupTableProperties(subGroupTable);

            JPanel panel = createTabPanel("المجموعات تحت فرعية", subGroupTable, "sub");
            tabbedPane.addTab("المجموعات تحت فرعية", panel);

        } catch (SQLException e) {
            handleError("خطأ في إنشاء تبويب المجموعات تحت فرعية", e);
        }
    }

    /**
     * إنشاء تبويب المجموعات المساعدة بنية جدول IAS_ASSISTANT_GROUP من IAS20251
     */
    private void createAssistantGroupTab() {
        try {
            String[] columns = getTableColumns("IAS_ASSISTANT_GROUP", ias20251Connection);

            assistantGroupModel = new DefaultTableModel(columns, 0) {
                @Override
                public boolean isCellEditable(int row, int column) {
                    return false;
                }
            };

            assistantGroupTable = new JTable(assistantGroupModel);
            setupTableProperties(assistantGroupTable);

            JPanel panel = createTabPanel("المجموعات المساعدة", assistantGroupTable, "assistant");
            tabbedPane.addTab("المجموعات المساعدة", panel);

        } catch (SQLException e) {
            handleError("خطأ في إنشاء تبويب المجموعات المساعدة", e);
        }
    }

    /**
     * إنشاء تبويب المجموعات التفصيلية بنية جدول IAS_DETAIL_GROUP من IAS20251
     */
    private void createDetailGroupTab() {
        try {
            String[] columns = getTableColumns("IAS_DETAIL_GROUP", ias20251Connection);

            detailGroupModel = new DefaultTableModel(columns, 0) {
                @Override
                public boolean isCellEditable(int row, int column) {
                    return false;
                }
            };

            detailGroupTable = new JTable(detailGroupModel);
            setupTableProperties(detailGroupTable);

            JPanel panel = createTabPanel("المجموعات التفصيلية", detailGroupTable, "detail");
            tabbedPane.addTab("المجموعات التفصيلية", panel);

        } catch (SQLException e) {
            handleError("خطأ في إنشاء تبويب المجموعات التفصيلية", e);
        }
    }

    /**
     * الحصول على أعمدة الجدول من قاعدة البيانات
     */
    private String[] getTableColumns(String tableName, Connection connection) throws SQLException {
        Vector<String> columns = new Vector<>();

        DatabaseMetaData metaData = connection.getMetaData();
        ResultSet rs = metaData.getColumns(null, null, tableName, null);

        while (rs.next()) {
            String columnName = rs.getString("COLUMN_NAME");
            // تجاهل أعمدة النظام
            if (!columnName.equals("CREATED_BY") && !columnName.equals("CREATED_DATE")
                    && !columnName.equals("UPDATED_BY") && !columnName.equals("UPDATED_DATE")) {
                columns.add(columnName);
            }
        }
        rs.close();

        if (columns.isEmpty()) {
            throw new SQLException("لم يتم العثور على أعمدة للجدول: " + tableName);
        }

        return columns.toArray(new String[0]);
    }

    /**
     * إعداد خصائص الجدول
     */
    private void setupTableProperties(JTable table) {
        table.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        table.setAutoResizeMode(JTable.AUTO_RESIZE_ALL_COLUMNS);
        table.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        table.setRowHeight(25);

        // تنسيق الخط
        Font arabicFont = new Font("Tahoma", Font.PLAIN, 11);
        table.setFont(arabicFont);
        table.getTableHeader().setFont(new Font("Tahoma", Font.BOLD, 11));

        // محاذاة النص
        DefaultTableCellRenderer rightRenderer = new DefaultTableCellRenderer();
        rightRenderer.setHorizontalAlignment(SwingConstants.RIGHT);
        rightRenderer.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        for (int i = 0; i < table.getColumnCount(); i++) {
            table.getColumnModel().getColumn(i).setCellRenderer(rightRenderer);
        }

        // ألوان متناوبة للصفوف
        table.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value,
                    boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected,
                        hasFocus, row, column);

                if (!isSelected) {
                    if (row % 2 == 0) {
                        c.setBackground(Color.WHITE);
                    } else {
                        c.setBackground(new Color(245, 245, 245));
                    }
                }

                setHorizontalAlignment(SwingConstants.RIGHT);
                setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
                return c;
            }
        });
    }

    /**
     * إنشاء لوحة التبويب
     */
    private JPanel createTabPanel(String title, JTable table, String type) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط الأدوات
        JPanel toolbarPanel = createToolbar(type);
        panel.add(toolbarPanel, BorderLayout.NORTH);

        // الجدول مع شريط التمرير
        JScrollPane scrollPane = new JScrollPane(table);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء شريط الأدوات
     */
    private JPanel createToolbar(String type) {
        JPanel toolbar = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        toolbar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        toolbar.setBorder(BorderFactory.createEtchedBorder());

        // أزرار الأدوات
        JButton addBtn = createStyledButton("إضافة", new Color(46, 125, 50));
        JButton editBtn = createStyledButton("تعديل", new Color(25, 118, 210));
        JButton deleteBtn = createStyledButton("حذف", new Color(211, 47, 47));
        JButton refreshBtn = createStyledButton("تحديث", new Color(123, 31, 162));
        JButton importBtn = createStyledButton("استيراد", new Color(255, 152, 0));

        // إضافة الأحداث
        addBtn.addActionListener(e -> addRecord(type));
        editBtn.addActionListener(e -> editRecord(type));
        deleteBtn.addActionListener(e -> deleteRecord(type));
        refreshBtn.addActionListener(e -> refreshData(type));
        importBtn.addActionListener(e -> importData(type));

        toolbar.add(addBtn);
        toolbar.add(editBtn);
        toolbar.add(deleteBtn);
        toolbar.add(new JSeparator(SwingConstants.VERTICAL));
        toolbar.add(refreshBtn);
        toolbar.add(importBtn);

        return toolbar;
    }

    /**
     * إنشاء زر منسق
     */
    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setFont(new Font("Tahoma", Font.BOLD, 11));
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(80, 30));
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));

        // تأثير hover
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(color.brighter());
            }

            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(color);
            }
        });

        return button;
    }

    /**
     * إنشاء شريط الحالة
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createLoweredBevelBorder());

        JLabel statusLabel = new JLabel("جاهز - تم تحميل البيانات من قاعدة بيانات SHIP_ERP");
        statusLabel.setFont(new Font("Tahoma", Font.PLAIN, 11));
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        panel.add(statusLabel, BorderLayout.WEST);

        return panel;
    }

    /**
     * تحميل جميع البيانات
     */
    private void loadAllData() {
        try {
            loadMainGroupData();
            loadMainSubGroupData();
            loadSubGroupData();
            loadAssistantGroupData();
            loadDetailGroupData();
            System.out.println("✅ تم تحميل جميع البيانات بنجاح");
        } catch (Exception e) {
            handleError("خطأ في تحميل البيانات", e);
        }
    }

    /**
     * تحميل بيانات المجموعات الرئيسية
     */
    private void loadMainGroupData() throws SQLException {
        String sql = "SELECT * FROM ERP_GROUP_DETAILS ORDER BY G_CODE";
        loadTableData(sql, mainGroupModel, shipErpConnection);
    }

    /**
     * تحميل بيانات المجموعات الفرعية
     */
    private void loadMainSubGroupData() throws SQLException {
        String sql = "SELECT * FROM ERP_MAINSUB_GRP_DTL ORDER BY G_CODE, MNG_CODE";
        loadTableData(sql, mainSubGroupModel, shipErpConnection);
    }

    /**
     * تحميل بيانات المجموعات تحت فرعية
     */
    private void loadSubGroupData() throws SQLException {
        String sql = "SELECT * FROM ERP_SUB_GRP_DTL ORDER BY G_CODE, MNG_CODE, SUBG_CODE";
        loadTableData(sql, subGroupModel, shipErpConnection);
    }

    /**
     * تحميل بيانات المجموعات المساعدة
     */
    private void loadAssistantGroupData() throws SQLException {
        String sql =
                "SELECT * FROM ERP_ASSISTANT_GROUP ORDER BY G_CODE, MNG_CODE, SUBG_CODE, ASSISTANT_NO";
        loadTableData(sql, assistantGroupModel, shipErpConnection);
    }

    /**
     * تحميل بيانات المجموعات التفصيلية
     */
    private void loadDetailGroupData() throws SQLException {
        String sql =
                "SELECT * FROM ERP_DETAIL_GROUP ORDER BY G_CODE, MNG_CODE, SUBG_CODE, ASSISTANT_NO, DETAIL_NO";
        loadTableData(sql, detailGroupModel, shipErpConnection);
    }

    /**
     * تحميل بيانات الجدول
     */
    private void loadTableData(String sql, DefaultTableModel model, Connection connection)
            throws SQLException {
        // مسح البيانات الحالية
        model.setRowCount(0);

        try (PreparedStatement stmt = connection.prepareStatement(sql);
                ResultSet rs = stmt.executeQuery()) {

            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                Vector<Object> row = new Vector<>();
                for (int i = 1; i <= columnCount; i++) {
                    // تجاهل أعمدة النظام
                    String columnName = metaData.getColumnName(i);
                    if (!columnName.equals("CREATED_BY") && !columnName.equals("CREATED_DATE")
                            && !columnName.equals("UPDATED_BY")
                            && !columnName.equals("UPDATED_DATE")) {
                        row.add(rs.getObject(i));
                    }
                }
                if (!row.isEmpty()) {
                    model.addRow(row);
                }
            }
        }
    }

    /**
     * إضافة سجل جديد
     */
    private void addRecord(String type) {
        JOptionPane.showMessageDialog(this, "سيتم إضافة سجل جديد في " + getTableDisplayName(type),
                "إضافة سجل", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * تعديل سجل
     */
    private void editRecord(String type) {
        JTable currentTable = getCurrentTable(type);
        int selectedRow = currentTable.getSelectedRow();

        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار سجل للتعديل", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        JOptionPane.showMessageDialog(this,
                "سيتم تعديل السجل المحدد في " + getTableDisplayName(type), "تعديل سجل",
                JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * حذف سجل
     */
    private void deleteRecord(String type) {
        JTable currentTable = getCurrentTable(type);
        int selectedRow = currentTable.getSelectedRow();

        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار سجل للحذف", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        int result = JOptionPane.showConfirmDialog(this, "هل أنت متأكد من حذف السجل المحدد؟",
                "تأكيد الحذف", JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            JOptionPane.showMessageDialog(this, "سيتم حذف السجل من " + getTableDisplayName(type),
                    "حذف سجل", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * تحديث البيانات
     */
    private void refreshData(String type) {
        try {
            switch (type) {
                case "main":
                    loadMainGroupData();
                    break;
                case "mainsub":
                    loadMainSubGroupData();
                    break;
                case "sub":
                    loadSubGroupData();
                    break;
                case "assistant":
                    loadAssistantGroupData();
                    break;
                case "detail":
                    loadDetailGroupData();
                    break;
            }
            JOptionPane.showMessageDialog(this,
                    "تم تحديث بيانات " + getTableDisplayName(type) + " بنجاح", "تحديث البيانات",
                    JOptionPane.INFORMATION_MESSAGE);
        } catch (Exception e) {
            handleError("خطأ في تحديث البيانات", e);
        }
    }

    /**
     * استيراد البيانات من IAS20251
     */
    private void importData(String type) {
        int result = JOptionPane.showConfirmDialog(this,
                "هل تريد استيراد البيانات من قاعدة بيانات IAS20251؟\n"
                        + "سيتم استبدال البيانات الحالية.",
                "استيراد البيانات", JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            try {
                performDataImport(type);
                refreshData(type);
                JOptionPane.showMessageDialog(this, "تم استيراد البيانات من IAS20251 بنجاح",
                        "استيراد البيانات", JOptionPane.INFORMATION_MESSAGE);
            } catch (Exception e) {
                handleError("خطأ في استيراد البيانات", e);
            }
        }
    }

    /**
     * تنفيذ استيراد البيانات
     */
    private void performDataImport(String type) throws SQLException {
        String sourceTable = "";
        String targetTable = "";

        switch (type) {
            case "main":
                sourceTable = "GROUP_DETAILS";
                targetTable = "ERP_GROUP_DETAILS";
                break;
            case "mainsub":
                sourceTable = "IAS_MAINSUB_GRP_DTL";
                targetTable = "ERP_MAINSUB_GRP_DTL";
                break;
            case "sub":
                sourceTable = "IAS_SUB_GRP_DTL";
                targetTable = "ERP_SUB_GRP_DTL";
                break;
            case "assistant":
                sourceTable = "IAS_ASSISTANT_GROUP";
                targetTable = "ERP_ASSISTANT_GROUP";
                break;
            case "detail":
                sourceTable = "IAS_DETAIL_GROUP";
                targetTable = "ERP_DETAIL_GROUP";
                break;
        }

        // حذف البيانات الحالية
        String deleteSQL = "DELETE FROM " + targetTable;
        try (PreparedStatement deleteStmt = shipErpConnection.prepareStatement(deleteSQL)) {
            deleteStmt.executeUpdate();
        }

        // استيراد البيانات الجديدة
        String selectSQL = "SELECT * FROM " + sourceTable;
        String insertSQL = buildInsertSQL(targetTable, sourceTable);

        try (PreparedStatement selectStmt = ias20251Connection.prepareStatement(selectSQL);
                PreparedStatement insertStmt = shipErpConnection.prepareStatement(insertSQL);
                ResultSet rs = selectStmt.executeQuery()) {

            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                for (int i = 1; i <= columnCount; i++) {
                    insertStmt.setObject(i, rs.getObject(i));
                }
                insertStmt.addBatch();
            }

            insertStmt.executeBatch();
        }
    }

    /**
     * بناء استعلام الإدراج
     */
    private String buildInsertSQL(String targetTable, String sourceTable) throws SQLException {
        Vector<String> columns = new Vector<>();

        DatabaseMetaData metaData = ias20251Connection.getMetaData();
        ResultSet rs = metaData.getColumns(null, null, sourceTable, null);

        while (rs.next()) {
            columns.add(rs.getString("COLUMN_NAME"));
        }
        rs.close();

        StringBuilder sql = new StringBuilder("INSERT INTO " + targetTable + " (");
        StringBuilder values = new StringBuilder(" VALUES (");

        for (int i = 0; i < columns.size(); i++) {
            if (i > 0) {
                sql.append(", ");
                values.append(", ");
            }
            sql.append(columns.get(i));
            values.append("?");
        }

        sql.append(")");
        values.append(")");
        sql.append(values);

        return sql.toString();
    }

    /**
     * الحصول على الجدول الحالي
     */
    private JTable getCurrentTable(String type) {
        switch (type) {
            case "main":
                return mainGroupTable;
            case "mainsub":
                return mainSubGroupTable;
            case "sub":
                return subGroupTable;
            case "assistant":
                return assistantGroupTable;
            case "detail":
                return detailGroupTable;
            default:
                return mainGroupTable;
        }
    }

    /**
     * الحصول على اسم الجدول للعرض
     */
    private String getTableDisplayName(String type) {
        switch (type) {
            case "main":
                return "المجموعات الرئيسية";
            case "mainsub":
                return "المجموعات الفرعية";
            case "sub":
                return "المجموعات تحت فرعية";
            case "assistant":
                return "المجموعات المساعدة";
            case "detail":
                return "المجموعات التفصيلية";
            default:
                return "غير محدد";
        }
    }

    /**
     * معالجة الأخطاء
     */
    private void handleError(String message, Exception e) {
        String fullMessage = message + "\n\nتفاصيل الخطأ:\n" + e.getMessage();

        JOptionPane.showMessageDialog(this, fullMessage, "خطأ", JOptionPane.ERROR_MESSAGE);

        System.err.println("❌ " + message);
        e.printStackTrace();
    }

    /**
     * إغلاق الاتصالات عند إغلاق النافذة
     */
    @Override
    public void dispose() {
        try {
            if (shipErpConnection != null && !shipErpConnection.isClosed()) {
                shipErpConnection.close();
            }
            if (ias20251Connection != null && !ias20251Connection.isClosed()) {
                ias20251Connection.close();
            }
        } catch (SQLException e) {
            System.err.println("خطأ في إغلاق الاتصالات: " + e.getMessage());
        }
        super.dispose();
    }
}
