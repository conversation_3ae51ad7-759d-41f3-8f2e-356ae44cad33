@echo off
echo 🚀 تشغيل نافذة وحدات القياس...
echo.

cd /d "e:\ship_erp\java\src\main\java"

echo 📋 التحقق من ملفات المكتبات...
if not exist "lib\ojdbc11.jar" (
    echo ❌ ملف ojdbc11.jar غير موجود
    pause
    exit /b 1
)

if not exist "lib\orai18n.jar" (
    echo ❌ ملف orai18n.jar غير موجود
    pause
    exit /b 1
)

echo ✅ جميع المكتبات موجودة
echo.

echo 🔄 تشغيل نافذة وحدات القياس...
java "-Duser.language=en" "-Duser.country=US" "-Dfile.encoding=UTF-8" -cp "lib/ojdbc11.jar;lib/orai18n.jar;lib/h2-2.2.224.jar;." OriginalMeasurementUnitsWindow

echo.
echo 📋 انتهى التشغيل
pause
