package com.shipment.erp.service;

import com.shipment.erp.model.BaseEntity;
import java.util.List;
import java.util.Optional;

/**
 * Service أساسي لجميع الكيانات
 * يحتوي على العمليات الأساسية لمنطق الأعمال
 */
public interface BaseService<T extends BaseEntity> {

    /**
     * حفظ كيان جديد أو تحديث موجود
     */
    T save(T entity);

    /**
     * حفظ قائمة من الكيانات
     */
    List<T> saveAll(List<T> entities);

    /**
     * البحث عن كيان بالمعرف
     */
    Optional<T> findById(Long id);

    /**
     * الحصول على كيان بالمعرف مع رمي استثناء إذا لم يوجد
     */
    T getById(Long id);

    /**
     * التحقق من وجود كيان بالمعرف
     */
    boolean existsById(Long id);

    /**
     * الحصول على جميع الكيانات
     */
    List<T> findAll();

    /**
     * الحصول على جميع الكيانات مع ترقيم الصفحات
     */
    List<T> findAll(int page, int size);

    /**
     * الحصول على عدد الكيانات
     */
    long count();

    /**
     * حذف كيان بالمعرف
     */
    void deleteById(Long id);

    /**
     * حذف كيان
     */
    void delete(T entity);

    /**
     * حذف قائمة من الكيانات
     */
    void deleteAll(List<T> entities);

    /**
     * البحث عن الكيانات النشطة
     */
    List<T> findActive();

    /**
     * البحث عن الكيانات غير النشطة
     */
    List<T> findInactive();

    /**
     * البحث بالنص
     */
    List<T> search(String searchText);

    /**
     * البحث بالنص مع ترقيم الصفحات
     */
    List<T> search(String searchText, int page, int size);

    /**
     * عدد نتائج البحث
     */
    long countSearch(String searchText);

    /**
     * تحديث حالة النشاط
     */
    void updateActiveStatus(Long id, boolean isActive);

    /**
     * تفعيل كيان
     */
    void activate(Long id);

    /**
     * إلغاء تفعيل كيان
     */
    void deactivate(Long id);

    /**
     * الحصول على آخر الكيانات المضافة
     */
    List<T> findLatest(int limit);

    /**
     * التحقق من صحة الكيان قبل الحفظ
     */
    void validate(T entity);

    /**
     * التحقق من صحة الكيان قبل التحديث
     */
    void validateForUpdate(T entity);

    /**
     * التحقق من إمكانية حذف الكيان
     */
    void validateForDelete(Long id);

    /**
     * تحديث الكيان في الجلسة
     */
    void refresh(T entity);

    /**
     * فصل الكيان من الجلسة
     */
    void detach(T entity);

    /**
     * تصدير البيانات
     */
    byte[] exportData(String format);

    /**
     * استيراد البيانات
     */
    void importData(byte[] data, String format);
}
