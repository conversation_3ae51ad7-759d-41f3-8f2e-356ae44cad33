@echo off
chcp 65001 > nul
echo ========================================
echo    Update Dependencies and Libraries
echo ========================================
echo.

echo [INFO] Starting dependency update...
echo.

REM Check Java installation
echo [1/5] Checking Java...
java -version > nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java not installed or not in PATH
    pause
    exit /b 1
)
echo [OK] Java available

REM Check Maven installation
echo [2/5] Checking Maven...
mvn -version > nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Maven not installed or not in PATH
    echo [INFO] Skipping Maven operations...
    goto :skip_maven
)
echo [OK] Maven available

REM Copy settings file
echo [3/5] Setting up Maven settings...
if exist "settings.xml" (
    if not exist "%USERPROFILE%\.m2" mkdir "%USERPROFILE%\.m2"
    copy "settings.xml" "%USERPROFILE%\.m2\settings.xml" > nul
    echo [OK] Settings file copied
) else (
    echo [WARNING] settings.xml file not found
)

REM Clean project
echo [4/5] Cleaning project...
call mvn clean -q
if %errorlevel% neq 0 (
    echo [ERROR] Failed to clean project
    goto :skip_maven
)
echo [OK] Project cleaned

REM Update dependencies
echo [5/5] Updating dependencies...
echo.
echo [INFO] Downloading dependencies... (may take time)
call mvn dependency:resolve -U
if %errorlevel% neq 0 (
    echo [WARNING] Some dependencies may not be available
    echo [INFO] Continuing with available dependencies...
)

echo.
echo [INFO] Updating plugins...
call mvn dependency:resolve-sources -q
call mvn dependency:copy-dependencies -q

echo.
echo ========================================
echo           Dependency Report
echo ========================================
echo.

echo [INFO] Showing dependency tree...
call mvn dependency:tree -q

:skip_maven
echo.
echo ========================================
echo    Dependency Update Complete
echo ========================================
echo.

echo [SUCCESS] Dependencies ready for use!
echo [INFO] You can now run the application using run-enhanced-erp.bat

pause
