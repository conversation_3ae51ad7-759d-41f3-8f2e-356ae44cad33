@echo off
chcp 65001 > nul
echo.
echo ========================================
echo    اختبار عرض القائمة المضغوط النهائي
echo ========================================
echo.

cd /d "e:\ship_erp\java\src\main\java"

echo 📋 التعديلات المطبقة على عرض القائمة:
echo ==========================================
echo.
echo ✅ TreeMenuPanel.java:
echo    • العرض المفضل: 150 بكسل (كان 180)
echo    • الحد الأدنى: 140 بكسل (كان 160)
echo.
echo ✅ EnhancedMainWindow.java:
echo    • العرض الافتراضي: 150 بكسل (كان 200)
echo    • الحد الأدنى المحفوظ: 140 بكسل (كان 180)
echo    • عرض التبديل: 150 بكسل (كان 200)
echo.
echo 📊 النتيجة: توفير 25-30%% من عرض القائمة
echo 🎯 الهدف: مساحة أكبر للمحتوى مع الحفاظ على وضوح شجرة الأنظمة
echo.

echo 🚀 تشغيل النافذة الرئيسية للاختبار...
echo.
echo 📝 ملاحظات للاختبار:
echo • تحقق من أن القائمة أصبحت أضيق
echo • تأكد من أن أسماء الأنظمة واضحة ومقروءة
echo • جرب توسيع وطي القائمة
echo • تحقق من حفظ الإعدادات
echo.

java -cp "lib/*;." EnhancedMainWindow

echo.
echo ========================================
echo    انتهى الاختبار
echo ========================================
echo.
echo ✅ إذا كانت القائمة أضيق وواضحة = نجح التعديل
echo ❌ إذا كانت القائمة عريضة أو غير واضحة = فشل التعديل
echo.
pause
