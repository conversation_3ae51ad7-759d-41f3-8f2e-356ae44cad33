package com.shipment.erp.repository;

import com.shipment.erp.model.UnitOfMeasure;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * تنفيذ مستودع وحدات القياس
 * Unit of Measure Repository Implementation
 */
@Repository
public class UnitOfMeasureRepositoryImpl extends BaseRepositoryImpl<UnitOfMeasure, Long> 
        implements UnitOfMeasureRepository {
    
    @Autowired
    private SessionFactory sessionFactory;
    
    public UnitOfMeasureRepositoryImpl() {
        super(UnitOfMeasure.class);
    }
    
    @Override
    public Optional<UnitOfMeasure> findByCode(String code) {
        Session session = sessionFactory.getCurrentSession();
        Query<UnitOfMeasure> query = session.createQuery(
            "FROM UnitOfMeasure u WHERE u.code = :code", UnitOfMeasure.class);
        query.setParameter("code", code);
        return query.uniqueResultOptional();
    }
    
    @Override
    public Optional<UnitOfMeasure> findByNameAr(String nameAr) {
        Session session = sessionFactory.getCurrentSession();
        Query<UnitOfMeasure> query = session.createQuery(
            "FROM UnitOfMeasure u WHERE u.nameAr = :nameAr", UnitOfMeasure.class);
        query.setParameter("nameAr", nameAr);
        return query.uniqueResultOptional();
    }
    
    @Override
    public Optional<UnitOfMeasure> findBySymbol(String symbol) {
        Session session = sessionFactory.getCurrentSession();
        Query<UnitOfMeasure> query = session.createQuery(
            "FROM UnitOfMeasure u WHERE u.symbol = :symbol", UnitOfMeasure.class);
        query.setParameter("symbol", symbol);
        return query.uniqueResultOptional();
    }
    
    @Override
    public List<UnitOfMeasure> findByIsActiveTrue() {
        Session session = sessionFactory.getCurrentSession();
        Query<UnitOfMeasure> query = session.createQuery(
            "FROM UnitOfMeasure u WHERE u.isActive = true ORDER BY u.sortOrder, u.nameAr", 
            UnitOfMeasure.class);
        return query.getResultList();
    }
    
    @Override
    public List<UnitOfMeasure> findByIsBaseUnitTrue() {
        Session session = sessionFactory.getCurrentSession();
        Query<UnitOfMeasure> query = session.createQuery(
            "FROM UnitOfMeasure u WHERE u.isBaseUnit = true AND u.isActive = true ORDER BY u.nameAr", 
            UnitOfMeasure.class);
        return query.getResultList();
    }
    
    @Override
    public List<UnitOfMeasure> findByBaseUnit(UnitOfMeasure baseUnit) {
        Session session = sessionFactory.getCurrentSession();
        Query<UnitOfMeasure> query = session.createQuery(
            "FROM UnitOfMeasure u WHERE u.baseUnit = :baseUnit AND u.isActive = true ORDER BY u.conversionFactor", 
            UnitOfMeasure.class);
        query.setParameter("baseUnit", baseUnit);
        return query.getResultList();
    }
    
    @Override
    public List<UnitOfMeasure> searchByText(String searchText) {
        Session session = sessionFactory.getCurrentSession();
        String searchPattern = "%" + searchText.toLowerCase() + "%";
        Query<UnitOfMeasure> query = session.createQuery(
            "FROM UnitOfMeasure u WHERE " +
            "(LOWER(u.nameAr) LIKE :searchText OR " +
            "LOWER(u.nameEn) LIKE :searchText OR " +
            "LOWER(u.code) LIKE :searchText OR " +
            "LOWER(u.symbol) LIKE :searchText) " +
            "AND u.isActive = true " +
            "ORDER BY u.sortOrder, u.nameAr", 
            UnitOfMeasure.class);
        query.setParameter("searchText", searchPattern);
        return query.getResultList();
    }
    
    @Override
    public List<UnitOfMeasure> findAllOrderBySortOrder() {
        Session session = sessionFactory.getCurrentSession();
        Query<UnitOfMeasure> query = session.createQuery(
            "FROM UnitOfMeasure u ORDER BY u.sortOrder, u.nameAr", UnitOfMeasure.class);
        return query.getResultList();
    }
    
    @Override
    public boolean existsByCode(String code) {
        Session session = sessionFactory.getCurrentSession();
        Query<Long> query = session.createQuery(
            "SELECT COUNT(u) FROM UnitOfMeasure u WHERE u.code = :code", Long.class);
        query.setParameter("code", code);
        return query.uniqueResult() > 0;
    }
    
    @Override
    public boolean existsByNameAr(String nameAr) {
        Session session = sessionFactory.getCurrentSession();
        Query<Long> query = session.createQuery(
            "SELECT COUNT(u) FROM UnitOfMeasure u WHERE u.nameAr = :nameAr", Long.class);
        query.setParameter("nameAr", nameAr);
        return query.uniqueResult() > 0;
    }
    
    @Override
    public long countByIsActiveTrue() {
        Session session = sessionFactory.getCurrentSession();
        Query<Long> query = session.createQuery(
            "SELECT COUNT(u) FROM UnitOfMeasure u WHERE u.isActive = true", Long.class);
        return query.uniqueResult();
    }
}
