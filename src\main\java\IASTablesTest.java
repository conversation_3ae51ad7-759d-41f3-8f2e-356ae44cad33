import java.sql.Connection;
import java.sql.Date;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * اختبار الجداول المحددة IAS_ITM_MST و IAS_ITM_DTL Test for specific tables IAS_ITM_MST and
 * IAS_ITM_DTL
 */
public class IASTablesTest {

    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("   اختبار جداول IAS_ITM_MST و IAS_ITM_DTL");
        System.out.println("   IAS Tables Test");
        System.out.println("====================================");
        System.out.println();

        IASTablesTest test = new IASTablesTest();
        test.runTest();
    }

    /**
     * تشغيل اختبار الجداول
     */
    public void runTest() {
        // بيانات الاتصال
        String url = "*************************************";
        String username = "ysdba2";
        String password = "ys123";

        Connection connection = null;

        try {
            System.out.println("🔄 الاتصال بقاعدة البيانات...");
            connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ تم الاتصال بنجاح!");
            System.out.println();

            // فحص وجود الجداول
            checkTablesExist(connection);

            // فحص هيكل الجداول
            checkTableStructure(connection);

            // فحص البيانات
            checkTableData(connection);

            // اختبار الاستعلام المدمج
            testJoinQuery(connection);

        } catch (SQLException e) {
            System.err.println("❌ خطأ في الاتصال: " + e.getMessage());
            e.printStackTrace();

        } finally {
            if (connection != null) {
                try {
                    connection.close();
                    System.out.println("🔒 تم إغلاق الاتصال");
                } catch (SQLException e) {
                    System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
                }
            }
        }
    }

    /**
     * فحص وجود الجداول
     */
    private void checkTablesExist(Connection connection) throws SQLException {
        System.out.println("🔍 فحص وجود الجداول...");

        String query = """
                    SELECT table_name, num_rows, last_analyzed
                    FROM user_tables
                    WHERE table_name IN ('IAS_ITM_MST', 'IAS_ITM_DTL')
                    ORDER BY table_name
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            boolean foundMST = false;
            boolean foundDTL = false;

            while (rs.next()) {
                String tableName = rs.getString("table_name");
                Object numRows = rs.getObject("num_rows");
                Date lastAnalyzed = rs.getDate("last_analyzed");

                System.out.printf("✅ %s موجود", tableName);
                if (numRows != null) {
                    System.out.printf(" (%s سجل)", numRows);
                }
                if (lastAnalyzed != null) {
                    System.out.printf(" - آخر تحليل: %s", lastAnalyzed);
                }
                System.out.println();

                if ("IAS_ITM_MST".equals(tableName))
                    foundMST = true;
                if ("IAS_ITM_DTL".equals(tableName))
                    foundDTL = true;
            }

            if (!foundMST) {
                System.out.println("❌ جدول IAS_ITM_MST غير موجود!");
            }
            if (!foundDTL) {
                System.out.println("❌ جدول IAS_ITM_DTL غير موجود!");
            }

            if (foundMST && foundDTL) {
                System.out.println("✅ جميع الجداول المطلوبة موجودة");
            }
        }

        System.out.println();
    }

    /**
     * فحص هيكل الجداول
     */
    private void checkTableStructure(Connection connection) throws SQLException {
        System.out.println("📋 فحص هيكل الجداول...");

        String[] tables = {"IAS_ITM_MST", "IAS_ITM_DTL"};

        for (String tableName : tables) {
            System.out.println("\n🔍 هيكل جدول " + tableName + ":");

            String query = """
                        SELECT column_name, data_type, data_length, nullable, data_default
                        FROM user_tab_columns
                        WHERE table_name = ?
                        ORDER BY column_id
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(query)) {
                stmt.setString(1, tableName);

                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        String columnName = rs.getString("column_name");
                        String dataType = rs.getString("data_type");
                        int dataLength = rs.getInt("data_length");
                        String nullable = rs.getString("nullable");
                        String defaultValue = rs.getString("data_default");

                        System.out.printf("  📌 %s: %s(%d) %s", columnName, dataType, dataLength,
                                "Y".equals(nullable) ? "NULL" : "NOT NULL");

                        if (defaultValue != null) {
                            System.out.printf(" DEFAULT %s", defaultValue.trim());
                        }
                        System.out.println();
                    }
                }
            }
        }

        System.out.println();
    }

    /**
     * فحص البيانات
     */
    private void checkTableData(Connection connection) throws SQLException {
        System.out.println("📊 فحص البيانات...");

        // فحص IAS_ITM_MST
        String mstQuery = """
                    SELECT
                        COUNT(*) as total_items,
                        COUNT(CASE WHEN INACTIVE = 0 THEN 1 END) as active_items,
                        COUNT(CASE WHEN I_NAME IS NOT NULL THEN 1 END) as items_with_name,
                        MIN(AD_DATE) as oldest_item,
                        MAX(AD_DATE) as newest_item
                    FROM IAS20251.IAS_ITM_MST
                """;

        try (PreparedStatement stmt = connection.prepareStatement(mstQuery);
                ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                System.out.println("📋 جدول IAS_ITM_MST:");
                System.out.println("  إجمالي الأصناف: " + rs.getInt("total_items"));
                System.out.println("  الأصناف النشطة: " + rs.getInt("active_items"));
                System.out.println("  أصناف بأسماء: " + rs.getInt("items_with_name"));

                Date oldest = rs.getDate("oldest_item");
                Date newest = rs.getDate("newest_item");
                if (oldest != null)
                    System.out.println("  أقدم صنف: " + oldest);
                if (newest != null)
                    System.out.println("  أحدث صنف: " + newest);
            }
        }

        // فحص IAS_ITM_DTL
        String dtlQuery = """
                    SELECT
                        COUNT(*) as total_details,
                        COUNT(CASE WHEN P_SIZE > 0 THEN 1 END) as items_with_cost,
                        COUNT(CASE WHEN MAIN_UNIT = 1 THEN 1 END) as items_with_price,
                        COUNT(CASE WHEN P_SIZE > 0 THEN 1 END) as items_with_stock,
                        AVG(P_SIZE) as avg_cost,
                        AVG(LVL_UNIT) as avg_price
                    FROM IAS20251.IAS_ITM_DTL
                """;

        try (PreparedStatement stmt = connection.prepareStatement(dtlQuery);
                ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                System.out.println("\n📋 جدول IAS_ITM_DTL:");
                System.out.println("  إجمالي التفاصيل: " + rs.getInt("total_details"));
                System.out.println("  أصناف بأسعار تكلفة: " + rs.getInt("items_with_cost"));
                System.out.println("  أصناف بأسعار بيع: " + rs.getInt("items_with_price"));
                System.out.println("  أصناف بمخزون: " + rs.getInt("items_with_stock"));

                double avgCost = rs.getDouble("avg_cost");
                double avgPrice = rs.getDouble("avg_price");
                if (avgCost > 0)
                    System.out.printf("  متوسط سعر التكلفة: %.2f%n", avgCost);
                if (avgPrice > 0)
                    System.out.printf("  متوسط سعر البيع: %.2f%n", avgPrice);
            }
        }

        System.out.println();
    }

    /**
     * اختبار الاستعلام المدمج
     */
    private void testJoinQuery(Connection connection) throws SQLException {
        System.out.println("🔗 اختبار الاستعلام المدمج...");

        String query = """
                    SELECT
                        m.I_CODE as ITM_CODE,
                        m.I_NAME as ITM_NAME,
                        m.I_DESC as ITM_DESC,
                        m.PRIMARY_COST as COST_PRICE,
                        m.I_CWTAVG as SELL_PRICE,
                        d.P_SIZE as STOCK_QTY
                    FROM IAS20251.IAS_ITM_MST m
                    LEFT JOIN IAS20251.IAS_ITM_DTL d ON m.I_CODE = d.I_CODE AND d.MAIN_UNIT = 1
                    WHERE m.INACTIVE = 0
                    AND ROWNUM <= 5
                    ORDER BY m.I_CODE
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            System.out.println("📋 عينة من البيانات المدمجة:");
            System.out.println("الكود\t\tالاسم\t\tالوصف\t\tسعر التكلفة\tسعر البيع\tالكمية");
            System.out.println(
                    "--------------------------------------------------------------------");

            while (rs.next()) {
                String code = rs.getString("ITM_CODE");
                String name = rs.getString("ITM_NAME");
                String desc = rs.getString("ITM_DESC");
                Object costPrice = rs.getObject("COST_PRICE");
                Object sellPrice = rs.getObject("SELL_PRICE");
                Object stockQty = rs.getObject("STOCK_QTY");

                System.out.printf("%s\t%s\t%s\t%s\t\t%s\t\t%s%n", code != null ? code : "NULL",
                        name != null ? (name.length() > 10 ? name.substring(0, 10) + "..." : name)
                                : "NULL",
                        desc != null ? (desc.length() > 10 ? desc.substring(0, 10) + "..." : desc)
                                : "NULL",
                        costPrice != null ? costPrice : "NULL",
                        sellPrice != null ? sellPrice : "NULL",
                        stockQty != null ? stockQty : "NULL");
            }

            System.out.println("✅ الاستعلام المدمج يعمل بنجاح!");
        }

        System.out.println();
    }
}
