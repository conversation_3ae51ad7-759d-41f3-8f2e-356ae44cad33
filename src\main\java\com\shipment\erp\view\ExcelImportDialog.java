package com.shipment.erp.view;

import com.shipment.erp.util.ExcelImportUtil;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;

/**
 * نافذة استيراد البيانات من Excel
 * Excel Import Dialog
 */
public class ExcelImportDialog extends JDialog {
    
    private Font arabicFont;
    private boolean importCompleted = false;
    
    // UI Components
    private JTextField filePathField;
    private JButton browseButton, downloadTemplateButton, importButton, cancelButton;
    private JProgressBar progressBar;
    private JTextArea logArea;
    private JLabel statusLabel;
    
    // Import settings
    private JCheckBox skipFirstRowCheck, validateDataCheck, updateExistingCheck;
    private JComboBox<String> sheetCombo;
    
    public ExcelImportDialog(Frame parent) {
        super(parent, "استيراد الأصناف من Excel", true);
        this.arabicFont = new Font("Tahoma", Font.PLAIN, 12);
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        setSize(600, 500);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        setResizable(false);
    }
    
    private void initializeComponents() {
        // حقل مسار الملف
        filePathField = new JTextField();
        filePathField.setFont(arabicFont);
        filePathField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        filePathField.setPreferredSize(new Dimension(300, 30));
        filePathField.setEditable(false);
        
        // الأزرار
        browseButton = createButton("استعراض", new Color(0, 123, 255));
        downloadTemplateButton = createButton("تحميل القالب", new Color(40, 167, 69));
        importButton = createButton("بدء الاستيراد", new Color(255, 193, 7));
        cancelButton = createButton("إلغاء", new Color(108, 117, 125));
        
        importButton.setEnabled(false);
        
        // شريط التقدم
        progressBar = new JProgressBar(0, 100);
        progressBar.setStringPainted(true);
        progressBar.setString("جاهز للاستيراد");
        progressBar.setFont(arabicFont);
        progressBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // منطقة السجل
        logArea = new JTextArea(8, 40);
        logArea.setFont(arabicFont);
        logArea.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        logArea.setEditable(false);
        logArea.setBackground(new Color(248, 249, 250));
        logArea.setText("مرحباً بك في أداة استيراد الأصناف من Excel\n" +
                       "الرجاء اختيار ملف Excel للبدء في عملية الاستيراد\n");
        
        // تسمية الحالة
        statusLabel = new JLabel("جاهز");
        statusLabel.setFont(arabicFont);
        
        // إعدادات الاستيراد
        skipFirstRowCheck = new JCheckBox("تجاهل الصف الأول (العناوين)");
        skipFirstRowCheck.setFont(arabicFont);
        skipFirstRowCheck.setSelected(true);
        
        validateDataCheck = new JCheckBox("التحقق من صحة البيانات");
        validateDataCheck.setFont(arabicFont);
        validateDataCheck.setSelected(true);
        
        updateExistingCheck = new JCheckBox("تحديث الأصناف الموجودة");
        updateExistingCheck.setFont(arabicFont);
        updateExistingCheck.setSelected(false);
        
        // قائمة الأوراق
        sheetCombo = new JComboBox<>();
        sheetCombo.setFont(arabicFont);
        sheetCombo.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        sheetCombo.addItem("الورقة الأولى");
    }
    
    private JButton createButton(String text, Color backgroundColor) {
        JButton button = new JButton(text);
        button.setFont(arabicFont);
        button.setBackground(backgroundColor);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(100, 35));
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        return button;
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BoxLayout(mainPanel, BoxLayout.Y_AXIS));
        mainPanel.setBorder(new EmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // قسم اختيار الملف
        mainPanel.add(createFileSelectionPanel());
        mainPanel.add(Box.createVerticalStrut(15));
        
        // قسم إعدادات الاستيراد
        mainPanel.add(createSettingsPanel());
        mainPanel.add(Box.createVerticalStrut(15));
        
        // قسم التقدم والسجل
        mainPanel.add(createProgressPanel());
        
        add(mainPanel, BorderLayout.CENTER);
        
        // لوحة الأزرار
        add(createButtonPanel(), BorderLayout.SOUTH);
    }
    
    private JPanel createFileSelectionPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createTitledBorder("اختيار ملف Excel"));
        
        JPanel filePanel = new JPanel(new BorderLayout(10, 0));
        filePanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        filePanel.setBorder(new EmptyBorder(10, 10, 10, 10));
        
        JLabel fileLabel = new JLabel("مسار الملف:");
        fileLabel.setFont(arabicFont);
        fileLabel.setPreferredSize(new Dimension(80, 30));
        
        filePanel.add(fileLabel, BorderLayout.EAST);
        filePanel.add(filePathField, BorderLayout.CENTER);
        filePanel.add(browseButton, BorderLayout.WEST);
        
        panel.add(filePanel, BorderLayout.CENTER);
        
        // لوحة القالب
        JPanel templatePanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        templatePanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        templatePanel.setBorder(new EmptyBorder(0, 10, 10, 10));
        
        JLabel templateLabel = new JLabel("ليس لديك ملف؟");
        templateLabel.setFont(arabicFont);
        
        templatePanel.add(templateLabel);
        templatePanel.add(downloadTemplateButton);
        
        panel.add(templatePanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private JPanel createSettingsPanel() {
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createTitledBorder("إعدادات الاستيراد"));
        
        JPanel innerPanel = new JPanel(new GridLayout(2, 2, 10, 10));
        innerPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        innerPanel.setBorder(new EmptyBorder(10, 10, 10, 10));
        
        innerPanel.add(skipFirstRowCheck);
        innerPanel.add(validateDataCheck);
        innerPanel.add(updateExistingCheck);
        
        // لوحة اختيار الورقة
        JPanel sheetPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        sheetPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        JLabel sheetLabel = new JLabel("الورقة:");
        sheetLabel.setFont(arabicFont);
        
        sheetPanel.add(sheetLabel);
        sheetPanel.add(sheetCombo);
        
        innerPanel.add(sheetPanel);
        
        panel.add(innerPanel);
        
        return panel;
    }
    
    private JPanel createProgressPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createTitledBorder("تقدم العملية"));
        
        JPanel progressPanel = new JPanel(new BorderLayout(0, 10));
        progressPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        progressPanel.setBorder(new EmptyBorder(10, 10, 10, 10));
        
        progressPanel.add(progressBar, BorderLayout.NORTH);
        progressPanel.add(statusLabel, BorderLayout.CENTER);
        
        panel.add(progressPanel, BorderLayout.NORTH);
        
        // منطقة السجل
        JScrollPane logScrollPane = new JScrollPane(logArea);
        logScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        logScrollPane.setBorder(BorderFactory.createTitledBorder("سجل العمليات"));
        
        panel.add(logScrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 15));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createMatteBorder(1, 0, 0, 0, Color.LIGHT_GRAY));
        panel.setBackground(new Color(248, 249, 250));
        
        panel.add(importButton);
        panel.add(cancelButton);
        
        return panel;
    }
    
    private void setupEventHandlers() {
        browseButton.addActionListener(e -> browseFile());
        downloadTemplateButton.addActionListener(e -> downloadTemplate());
        importButton.addActionListener(e -> startImport());
        cancelButton.addActionListener(e -> dispose());
        
        // تحديث حالة زر الاستيراد عند تغيير مسار الملف
        filePathField.addPropertyChangeListener("text", e -> updateImportButtonState());
    }
    
    private void browseFile() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter(
            "Excel Files (*.xlsx, *.xls)", "xlsx", "xls"));
        fileChooser.setDialogTitle("اختر ملف Excel");
        
        if (fileChooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            File selectedFile = fileChooser.getSelectedFile();
            filePathField.setText(selectedFile.getAbsolutePath());
            
            // التحقق من صحة الملف
            if (ExcelImportUtil.validateExcelFormat(selectedFile.getAbsolutePath())) {
                logArea.append("تم اختيار الملف: " + selectedFile.getName() + "\n");
                statusLabel.setText("ملف صالح - جاهز للاستيراد");
                updateImportButtonState();
                
                // تحميل معلومات الملف
                loadFileInfo(selectedFile.getAbsolutePath());
            } else {
                logArea.append("خطأ: الملف المختار غير صالح\n");
                statusLabel.setText("ملف غير صالح");
                filePathField.setText("");
                updateImportButtonState();
            }
        }
    }
    
    private void loadFileInfo(String filePath) {
        try {
            ExcelImportUtil.ExcelFileInfo info = ExcelImportUtil.getExcelFileInfo(filePath);
            logArea.append("معلومات الملف:\n");
            logArea.append("- عدد الصفوف: " + info.getRowCount() + "\n");
            logArea.append("- عدد الأعمدة: " + info.getColumnCount() + "\n");
            logArea.append("- حجم الملف: " + (info.getFileSize() / 1024) + " KB\n");
        } catch (Exception e) {
            logArea.append("خطأ في قراءة معلومات الملف: " + e.getMessage() + "\n");
        }
    }
    
    private void downloadTemplate() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("Excel Files", "xlsx"));
        fileChooser.setDialogTitle("حفظ قالب الاستيراد");
        fileChooser.setSelectedFile(new File("قالب_استيراد_الأصناف.xlsx"));
        
        if (fileChooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
            String filePath = fileChooser.getSelectedFile().getAbsolutePath();
            if (!filePath.endsWith(".xlsx")) {
                filePath += ".xlsx";
            }
            
            try {
                ExcelImportUtil.createImportTemplate(filePath);
                logArea.append("تم إنشاء قالب الاستيراد: " + filePath + "\n");
                
                JOptionPane.showMessageDialog(this,
                    "تم إنشاء قالب الاستيراد بنجاح\n" + filePath,
                    "نجح الإنشاء",
                    JOptionPane.INFORMATION_MESSAGE);
                    
            } catch (Exception e) {
                logArea.append("خطأ في إنشاء القالب: " + e.getMessage() + "\n");
                JOptionPane.showMessageDialog(this,
                    "خطأ في إنشاء القالب: " + e.getMessage(),
                    "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    private void startImport() {
        String filePath = filePathField.getText();
        if (filePath.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "الرجاء اختيار ملف Excel أولاً",
                "تنبيه",
                JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        // تعطيل الأزرار أثناء الاستيراد
        importButton.setEnabled(false);
        browseButton.setEnabled(false);
        
        // بدء عملية الاستيراد في خيط منفصل
        SwingWorker<ExcelImportUtil.ImportResult, String> worker = new SwingWorker<ExcelImportUtil.ImportResult, String>() {
            @Override
            protected ExcelImportUtil.ImportResult doInBackground() throws Exception {
                publish("بدء عملية الاستيراد...");
                progressBar.setIndeterminate(true);
                
                ExcelImportUtil.ImportResult result = ExcelImportUtil.importItemsFromExcel(filePath);
                
                return result;
            }
            
            @Override
            protected void process(java.util.List<String> chunks) {
                for (String message : chunks) {
                    logArea.append(message + "\n");
                    statusLabel.setText(message);
                }
            }
            
            @Override
            protected void done() {
                try {
                    ExcelImportUtil.ImportResult result = get();
                    
                    progressBar.setIndeterminate(false);
                    progressBar.setValue(100);
                    progressBar.setString("اكتمل الاستيراد");
                    
                    logArea.append("\n=== نتائج الاستيراد ===\n");
                    logArea.append("إجمالي الصفوف: " + result.getTotalRows() + "\n");
                    logArea.append("نجح الاستيراد: " + result.getSuccessfulImports() + "\n");
                    logArea.append("فشل الاستيراد: " + result.getFailedImports() + "\n");
                    
                    if (!result.getErrors().isEmpty()) {
                        logArea.append("\nالأخطاء:\n");
                        for (String error : result.getErrors()) {
                            logArea.append("- " + error + "\n");
                        }
                    }
                    
                    statusLabel.setText("اكتمل الاستيراد - " + result.getSuccessfulImports() + " صنف");
                    importCompleted = true;
                    
                    // إظهار نتائج الاستيراد
                    showImportResults(result);
                    
                } catch (Exception e) {
                    logArea.append("خطأ في الاستيراد: " + e.getMessage() + "\n");
                    statusLabel.setText("فشل الاستيراد");
                    progressBar.setIndeterminate(false);
                    progressBar.setValue(0);
                    progressBar.setString("فشل الاستيراد");
                } finally {
                    // إعادة تفعيل الأزرار
                    importButton.setEnabled(true);
                    browseButton.setEnabled(true);
                }
            }
        };
        
        worker.execute();
    }
    
    private void showImportResults(ExcelImportUtil.ImportResult result) {
        String message = "تم الانتهاء من عملية الاستيراد\n\n" +
                        "إجمالي الصفوف: " + result.getTotalRows() + "\n" +
                        "نجح الاستيراد: " + result.getSuccessfulImports() + "\n" +
                        "فشل الاستيراد: " + result.getFailedImports();
        
        if (result.getFailedImports() > 0) {
            message += "\n\nيمكنك مراجعة سجل العمليات لمعرفة تفاصيل الأخطاء";
        }
        
        JOptionPane.showMessageDialog(this,
            message,
            "نتائج الاستيراد",
            result.getFailedImports() > 0 ? JOptionPane.WARNING_MESSAGE : JOptionPane.INFORMATION_MESSAGE);
    }
    
    private void updateImportButtonState() {
        boolean hasFile = !filePathField.getText().trim().isEmpty();
        importButton.setEnabled(hasFile);
    }
    
    public boolean isImportCompleted() {
        return importCompleted;
    }
}
