import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;

/**
 * نافذة إدارة وحدات القياس مع قاعدة البيانات المدمجة Local Measurement Units Window with Embedded
 * Database
 */
public class LocalMeasurementUnitsWindow extends JFrame {

    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 12);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 12);

    // مكونات الواجهة
    private JTextField codeField;
    private JTextField nameField;
    private JTextField nameEnField;
    private JTextField symbolField;
    private JTextArea descriptionArea;
    private JComboBox<String> typeComboBox;
    private JCheckBox activeCheckBox;

    private JTable unitsTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;

    // قاعدة البيانات المدمجة
    private Connection connection;

    // الألوان
    private Color primaryColor = new Color(52, 152, 219);
    private Color successColor = new Color(46, 204, 113);
    private Color warningColor = new Color(241, 196, 15);
    private Color dangerColor = new Color(231, 76, 60);

    public LocalMeasurementUnitsWindow() {
        initializeDatabase();
        initializeComponents();
        loadUnitsData();
    }

    /**
     * تهيئة قاعدة البيانات المدمجة H2
     */
    private void initializeDatabase() {
        try {
            // تحميل H2 driver
            Class.forName("org.h2.Driver");
            System.out.println("✅ تم تحميل H2 Database driver بنجاح");

            // إنشاء قاعدة بيانات مدمجة
            String url = "jdbc:h2:./data/measurement_units;AUTO_SERVER=TRUE;DB_CLOSE_DELAY=-1";
            connection = DriverManager.getConnection(url, "sa", "");
            System.out.println("✅ تم إنشاء قاعدة البيانات المدمجة بنجاح");

            // إنشاء الجدول
            createTable();

            // إدراج بيانات تجريبية
            insertSampleData();

        } catch (Exception e) {
            System.err.println("❌ خطأ في تهيئة قاعدة البيانات: " + e.getMessage());
            e.printStackTrace();

            JOptionPane.showMessageDialog(this, "خطأ في تهيئة قاعدة البيانات:\n" + e.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * إنشاء جدول وحدات القياس
     */
    private void createTable() throws SQLException {
        String createTableSQL = """
                    CREATE TABLE IF NOT EXISTS MEASUREMENT_UNITS (
                        MEASURE_CODE VARCHAR(10) PRIMARY KEY,
                        MEASURE_NAME VARCHAR(100) NOT NULL,
                        MEASURE_NAME_EN VARCHAR(100),
                        SYMBOL VARCHAR(10),
                        DESCRIPTION VARCHAR(500),
                        MEASURE_TYPE INT DEFAULT 1,
                        IS_ACTIVE BOOLEAN DEFAULT TRUE,
                        CREATED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.execute(createTableSQL);
            System.out.println("✅ تم إنشاء جدول وحدات القياس");
        }
    }

    /**
     * إدراج بيانات تجريبية
     */
    private void insertSampleData() throws SQLException {
        // فحص وجود البيانات
        String checkQuery = "SELECT COUNT(*) FROM MEASUREMENT_UNITS";
        try (Statement stmt = connection.createStatement();
                ResultSet rs = stmt.executeQuery(checkQuery)) {

            if (rs.next() && rs.getInt(1) > 0) {
                System.out.println("ℹ️ البيانات التجريبية موجودة مسبقاً");
                return;
            }
        }

        // إدراج البيانات التجريبية
        String insertSQL =
                """
                            INSERT INTO MEASUREMENT_UNITS (MEASURE_CODE, MEASURE_NAME, MEASURE_NAME_EN, SYMBOL, DESCRIPTION, MEASURE_TYPE) VALUES
                            ('PIECE', 'قطعة', 'Piece', 'قطعة', 'وحدة العد الأساسية', 1),
                            ('KG', 'كيلوجرام', 'Kilogram', 'كجم', 'وحدة الوزن الأساسية', 2),
                            ('LITER', 'لتر', 'Liter', 'لتر', 'وحدة الحجم الأساسية', 3),
                            ('METER', 'متر', 'Meter', 'متر', 'وحدة الطول الأساسية', 4),
                            ('SQM', 'متر مربع', 'Square Meter', 'م²', 'وحدة المساحة الأساسية', 5),
                            ('GRAM', 'جرام', 'Gram', 'جم', 'وحدة وزن صغيرة', 2),
                            ('CM', 'سنتيمتر', 'Centimeter', 'سم', 'وحدة طول صغيرة', 4),
                            ('BOX', 'صندوق', 'Box', 'صندوق', 'وحدة تعبئة', 1),
                            ('DOZEN', 'دزينة', 'Dozen', 'دزينة', '12 قطعة', 1),
                            ('TON', 'طن', 'Ton', 'طن', 'وحدة وزن كبيرة', 2)
                        """;

        try (Statement stmt = connection.createStatement()) {
            stmt.execute(insertSQL);
            System.out.println("✅ تم إدراج البيانات التجريبية (10 وحدات قياس)");
        }
    }

    /**
     * تهيئة مكونات الواجهة
     */
    private void initializeComponents() {
        setTitle("إدارة وحدات القياس - قاعدة البيانات المدمجة");
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // لوحة الأزرار العلوية
        JPanel topPanel = createTopPanel();
        mainPanel.add(topPanel, BorderLayout.NORTH);

        // اللوحة الوسطى - تقسيم أفقي
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(400);

        // اللوحة اليمنى - نموذج الإدخال
        JPanel formPanel = createFormPanel();
        splitPane.setRightComponent(formPanel);

        // اللوحة اليسرى - جدول البيانات
        JPanel tablePanel = createTablePanel();
        splitPane.setLeftComponent(tablePanel);

        mainPanel.add(splitPane, BorderLayout.CENTER);

        // شريط الحالة
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);

        add(mainPanel);
    }

    /**
     * إنشاء لوحة الأزرار العلوية
     */
    private JPanel createTopPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 5));

        JButton newBtn = createStyledButton("🆕 جديد", successColor);
        newBtn.addActionListener(e -> clearForm());

        JButton saveBtn = createStyledButton("💾 حفظ", primaryColor);
        saveBtn.addActionListener(e -> saveUnit());

        JButton deleteBtn = createStyledButton("🗑️ حذف", dangerColor);
        deleteBtn.addActionListener(e -> deleteUnit());

        JButton refreshBtn = createStyledButton("🔄 تحديث", new Color(52, 73, 94));
        refreshBtn.addActionListener(e -> loadUnitsData());

        JButton exportBtn = createStyledButton("📤 تصدير", warningColor);
        exportBtn.addActionListener(e -> exportData());

        panel.add(newBtn);
        panel.add(saveBtn);
        panel.add(deleteBtn);
        panel.add(refreshBtn);
        panel.add(exportBtn);

        return panel;
    }

    /**
     * إنشاء نموذج الإدخال
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder(BorderFactory.createEtchedBorder(),
                "بيانات وحدة القياس", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        // كود وحدة القياس
        gbc.gridx = 0;
        gbc.gridy = 0;
        panel.add(new JLabel("كود وحدة القياس:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        codeField = new JTextField(15);
        codeField.setFont(arabicFont);
        panel.add(codeField, gbc);

        // اسم وحدة القياس
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("اسم وحدة القياس:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        nameField = new JTextField(15);
        nameField.setFont(arabicFont);
        panel.add(nameField, gbc);

        // الاسم بالإنجليزية
        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("الاسم بالإنجليزية:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        nameEnField = new JTextField(15);
        nameEnField.setFont(arabicFont);
        panel.add(nameEnField, gbc);

        // الرمز
        gbc.gridx = 0;
        gbc.gridy = 3;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("الرمز:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        symbolField = new JTextField(15);
        symbolField.setFont(arabicFont);
        panel.add(symbolField, gbc);

        // نوع وحدة القياس
        gbc.gridx = 0;
        gbc.gridy = 4;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("النوع:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        typeComboBox = new JComboBox<>(new String[] {"عادي", "وزن", "حجم", "طول", "مساحة"});
        typeComboBox.setFont(arabicFont);
        panel.add(typeComboBox, gbc);

        // حالة النشاط
        gbc.gridx = 0;
        gbc.gridy = 5;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("نشط:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        activeCheckBox = new JCheckBox();
        activeCheckBox.setSelected(true);
        panel.add(activeCheckBox, gbc);

        // الوصف
        gbc.gridx = 0;
        gbc.gridy = 6;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("الوصف:", JLabel.RIGHT), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.BOTH;
        gbc.weighty = 1.0;
        descriptionArea = new JTextArea(4, 15);
        descriptionArea.setFont(arabicFont);
        descriptionArea.setLineWrap(true);
        descriptionArea.setWrapStyleWord(true);
        JScrollPane descScrollPane = new JScrollPane(descriptionArea);
        panel.add(descScrollPane, gbc);

        return panel;
    }

    /**
     * إنشاء لوحة الجدول
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout(5, 5));
        panel.setBorder(BorderFactory.createTitledBorder(BorderFactory.createEtchedBorder(),
                "قائمة وحدات القياس", TitledBorder.RIGHT, TitledBorder.TOP, arabicBoldFont));

        // لوحة البحث
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchPanel.add(new JLabel("البحث:", JLabel.RIGHT));
        searchField = new JTextField(20);
        searchField.setFont(arabicFont);
        searchField.addActionListener(e -> searchUnits());
        searchPanel.add(searchField);

        JButton searchBtn = createStyledButton("🔍", primaryColor);
        searchBtn.addActionListener(e -> searchUnits());
        searchPanel.add(searchBtn);

        panel.add(searchPanel, BorderLayout.NORTH);

        // الجدول
        String[] columnNames = {"الكود", "الاسم", "الاسم الإنجليزي", "الرمز", "النوع", "نشط"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        unitsTable = new JTable(tableModel);
        unitsTable.setFont(arabicFont);
        unitsTable.getTableHeader().setFont(arabicBoldFont);
        unitsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        unitsTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedUnit();
            }
        });

        JScrollPane tableScrollPane = new JScrollPane(unitsTable);
        panel.add(tableScrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء شريط الحالة
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEtchedBorder());

        JLabel statusLabel = new JLabel("جاهز - قاعدة البيانات المدمجة H2");
        statusLabel.setFont(arabicFont);
        panel.add(statusLabel, BorderLayout.WEST);

        JLabel dbLabel = new JLabel("قاعدة البيانات: H2 Database (مدمجة)");
        dbLabel.setFont(arabicFont);
        dbLabel.setForeground(successColor);
        panel.add(dbLabel, BorderLayout.EAST);

        return panel;
    }

    /**
     * إنشاء زر منسق
     */
    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setFont(arabicBoldFont);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setOpaque(true);
        return button;
    }

    /**
     * تحميل بيانات وحدات القياس
     */
    private void loadUnitsData() {
        try {
            String query = "SELECT * FROM MEASUREMENT_UNITS ORDER BY MEASURE_NAME";

            // مسح البيانات الحالية
            tableModel.setRowCount(0);

            try (Statement stmt = connection.createStatement();
                    ResultSet rs = stmt.executeQuery(query)) {

                while (rs.next()) {
                    Object[] row = {rs.getString("MEASURE_CODE"), rs.getString("MEASURE_NAME"),
                            rs.getString("MEASURE_NAME_EN"), rs.getString("SYMBOL"),
                            getTypeText(rs.getInt("MEASURE_TYPE")),
                            rs.getBoolean("IS_ACTIVE") ? "نعم" : "لا"};
                    tableModel.addRow(row);
                }
            }

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تحميل البيانات:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * تحميل الوحدة المحددة في النموذج
     */
    private void loadSelectedUnit() {
        int selectedRow = unitsTable.getSelectedRow();
        if (selectedRow >= 0) {
            String code = (String) tableModel.getValueAt(selectedRow, 0);

            try {
                String query = "SELECT * FROM MEASUREMENT_UNITS WHERE MEASURE_CODE = ?";
                try (PreparedStatement stmt = connection.prepareStatement(query)) {
                    stmt.setString(1, code);

                    try (ResultSet rs = stmt.executeQuery()) {
                        if (rs.next()) {
                            populateForm(rs);
                        }
                    }
                }
            } catch (SQLException e) {
                JOptionPane.showMessageDialog(this,
                        "خطأ في تحميل بيانات الوحدة:\n" + e.getMessage(), "خطأ",
                        JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * ملء النموذج ببيانات الوحدة
     */
    private void populateForm(ResultSet rs) throws SQLException {
        codeField.setText(rs.getString("MEASURE_CODE"));
        nameField.setText(rs.getString("MEASURE_NAME"));
        nameEnField.setText(rs.getString("MEASURE_NAME_EN"));
        symbolField.setText(rs.getString("SYMBOL"));
        descriptionArea.setText(rs.getString("DESCRIPTION"));

        int type = rs.getInt("MEASURE_TYPE");
        if (type >= 1 && type <= 5) {
            typeComboBox.setSelectedIndex(type - 1);
        }

        activeCheckBox.setSelected(rs.getBoolean("IS_ACTIVE"));
    }

    /**
     * مسح النموذج
     */
    private void clearForm() {
        codeField.setText("");
        nameField.setText("");
        nameEnField.setText("");
        symbolField.setText("");
        descriptionArea.setText("");
        typeComboBox.setSelectedIndex(0);
        activeCheckBox.setSelected(true);

        unitsTable.clearSelection();
    }

    /**
     * حفظ وحدة القياس
     */
    private void saveUnit() {
        try {
            // التحقق من صحة البيانات
            if (!validateForm()) {
                return;
            }

            String code = codeField.getText().trim();
            String name = nameField.getText().trim();
            String nameEn = nameEnField.getText().trim();
            String symbol = symbolField.getText().trim();
            String description = descriptionArea.getText().trim();
            int type = typeComboBox.getSelectedIndex() + 1;
            boolean active = activeCheckBox.isSelected();

            // التحقق من وجود الوحدة
            String checkQuery = "SELECT COUNT(*) FROM MEASUREMENT_UNITS WHERE MEASURE_CODE = ?";
            boolean exists = false;

            try (PreparedStatement checkStmt = connection.prepareStatement(checkQuery)) {
                checkStmt.setString(1, code);
                try (ResultSet rs = checkStmt.executeQuery()) {
                    if (rs.next()) {
                        exists = rs.getInt(1) > 0;
                    }
                }
            }

            if (exists) {
                // تحديث
                String updateSQL = """
                            UPDATE MEASUREMENT_UNITS SET
                            MEASURE_NAME = ?, MEASURE_NAME_EN = ?, SYMBOL = ?,
                            DESCRIPTION = ?, MEASURE_TYPE = ?, IS_ACTIVE = ?
                            WHERE MEASURE_CODE = ?
                        """;

                try (PreparedStatement stmt = connection.prepareStatement(updateSQL)) {
                    stmt.setString(1, name);
                    stmt.setString(2, nameEn);
                    stmt.setString(3, symbol);
                    stmt.setString(4, description);
                    stmt.setInt(5, type);
                    stmt.setBoolean(6, active);
                    stmt.setString(7, code);

                    stmt.executeUpdate();
                }

                JOptionPane.showMessageDialog(this, "تم تحديث وحدة القياس بنجاح", "نجح",
                        JOptionPane.INFORMATION_MESSAGE);
            } else {
                // إضافة جديدة
                String insertSQL =
                        """
                                    INSERT INTO MEASUREMENT_UNITS
                                    (MEASURE_CODE, MEASURE_NAME, MEASURE_NAME_EN, SYMBOL, DESCRIPTION, MEASURE_TYPE, IS_ACTIVE)
                                    VALUES (?, ?, ?, ?, ?, ?, ?)
                                """;

                try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
                    stmt.setString(1, code);
                    stmt.setString(2, name);
                    stmt.setString(3, nameEn);
                    stmt.setString(4, symbol);
                    stmt.setString(5, description);
                    stmt.setInt(6, type);
                    stmt.setBoolean(7, active);

                    stmt.executeUpdate();
                }

                JOptionPane.showMessageDialog(this, "تم إضافة وحدة القياس بنجاح", "نجح",
                        JOptionPane.INFORMATION_MESSAGE);
            }

            // تحديث الجدول
            loadUnitsData();
            clearForm();

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في حفظ البيانات:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * حذف وحدة القياس
     */
    private void deleteUnit() {
        int selectedRow = unitsTable.getSelectedRow();
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار وحدة قياس للحذف", "تنبيه",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        String code = (String) tableModel.getValueAt(selectedRow, 0);
        String name = (String) tableModel.getValueAt(selectedRow, 1);

        int result = JOptionPane.showConfirmDialog(this,
                "هل أنت متأكد من حذف وحدة القياس:\n" + name + " (" + code + ")?", "تأكيد الحذف",
                JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            try {
                String deleteSQL = "DELETE FROM MEASUREMENT_UNITS WHERE MEASURE_CODE = ?";
                try (PreparedStatement stmt = connection.prepareStatement(deleteSQL)) {
                    stmt.setString(1, code);
                    stmt.executeUpdate();
                }

                JOptionPane.showMessageDialog(this, "تم حذف وحدة القياس بنجاح", "نجح",
                        JOptionPane.INFORMATION_MESSAGE);

                loadUnitsData();
                clearForm();

            } catch (SQLException e) {
                JOptionPane.showMessageDialog(this, "خطأ في حذف وحدة القياس:\n" + e.getMessage(),
                        "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * البحث في وحدات القياس
     */
    private void searchUnits() {
        String searchTerm = searchField.getText().trim();

        try {
            String query;
            PreparedStatement stmt;

            if (searchTerm.isEmpty()) {
                query = "SELECT * FROM MEASUREMENT_UNITS ORDER BY MEASURE_NAME";
                stmt = connection.prepareStatement(query);
            } else {
                query = """
                            SELECT * FROM MEASUREMENT_UNITS
                            WHERE UPPER(MEASURE_NAME) LIKE UPPER(?)
                               OR UPPER(MEASURE_CODE) LIKE UPPER(?)
                               OR UPPER(MEASURE_NAME_EN) LIKE UPPER(?)
                            ORDER BY MEASURE_NAME
                        """;
                stmt = connection.prepareStatement(query);
                String searchPattern = "%" + searchTerm + "%";
                stmt.setString(1, searchPattern);
                stmt.setString(2, searchPattern);
                stmt.setString(3, searchPattern);
            }

            // مسح البيانات الحالية
            tableModel.setRowCount(0);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    Object[] row = {rs.getString("MEASURE_CODE"), rs.getString("MEASURE_NAME"),
                            rs.getString("MEASURE_NAME_EN"), rs.getString("SYMBOL"),
                            getTypeText(rs.getInt("MEASURE_TYPE")),
                            rs.getBoolean("IS_ACTIVE") ? "نعم" : "لا"};
                    tableModel.addRow(row);
                }
            }

            stmt.close();

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في البحث:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * تصدير البيانات
     */
    private void exportData() {
        try {
            String query = "SELECT * FROM MEASUREMENT_UNITS ORDER BY MEASURE_NAME";
            StringBuilder export = new StringBuilder();
            export.append("كود الوحدة\tاسم الوحدة\tالاسم الإنجليزي\tالرمز\tالنوع\tالوصف\tنشط\n");

            try (Statement stmt = connection.createStatement();
                    ResultSet rs = stmt.executeQuery(query)) {

                while (rs.next()) {
                    export.append(rs.getString("MEASURE_CODE")).append("\t");
                    export.append(rs.getString("MEASURE_NAME")).append("\t");
                    export.append(rs.getString("MEASURE_NAME_EN")).append("\t");
                    export.append(rs.getString("SYMBOL")).append("\t");
                    export.append(getTypeText(rs.getInt("MEASURE_TYPE"))).append("\t");
                    export.append(rs.getString("DESCRIPTION")).append("\t");
                    export.append(rs.getBoolean("IS_ACTIVE") ? "نعم" : "لا").append("\n");
                }
            }

            // عرض البيانات في نافذة
            JTextArea textArea = new JTextArea(export.toString());
            textArea.setFont(arabicFont);
            textArea.setEditable(false);

            JScrollPane scrollPane = new JScrollPane(textArea);
            scrollPane.setPreferredSize(new Dimension(800, 600));

            JOptionPane.showMessageDialog(this, scrollPane, "تصدير البيانات",
                    JOptionPane.INFORMATION_MESSAGE);

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "خطأ في تصدير البيانات:\n" + e.getMessage(), "خطأ",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * التحقق من صحة النموذج
     */
    private boolean validateForm() {
        if (codeField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال كود وحدة القياس", "خطأ في البيانات",
                    JOptionPane.ERROR_MESSAGE);
            codeField.requestFocus();
            return false;
        }

        if (nameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال اسم وحدة القياس", "خطأ في البيانات",
                    JOptionPane.ERROR_MESSAGE);
            nameField.requestFocus();
            return false;
        }

        return true;
    }

    /**
     * الحصول على نص النوع
     */
    private String getTypeText(int type) {
        switch (type) {
            case 1:
                return "عادي";
            case 2:
                return "وزن";
            case 3:
                return "حجم";
            case 4:
                return "طول";
            case 5:
                return "مساحة";
            default:
                return "غير محدد";
        }
    }

    @Override
    public void dispose() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        super.dispose();
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new LocalMeasurementUnitsWindow().setVisible(true);
        });
    }
}
