@echo off
chcp 65001 > nul
echo ========================================
echo    نظام إدارة الشحنات المتكامل
echo    Ship ERP System v1.0.0
echo ========================================
echo.

REM التحقق من وجود Java
echo [INFO] التحقق من تثبيت Java...
java -version > nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java غير مثبت أو غير موجود في PATH
    echo [INFO] يرجى تثبيت Java JDK 17 أو أحدث من:
    echo https://adoptium.net/
    pause
    exit /b 1
)

REM التحقق من وجود Maven
echo [INFO] التحقق من تثبيت Maven...
mvn -version > nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Maven غير مثبت أو غير موجود في PATH
    echo [INFO] يرجى تثبيت Apache Maven من:
    echo https://maven.apache.org/download.cgi
    pause
    exit /b 1
)

REM إنشاء مجلد السجلات إذا لم يكن موجوداً
if not exist "logs" (
    echo [INFO] إنشاء مجلد السجلات...
    mkdir logs
)

REM إنشاء مجلد التقارير إذا لم يكن موجوداً
if not exist "reports" (
    echo [INFO] إنشاء مجلد التقارير...
    mkdir reports
)

REM إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
if not exist "backup" (
    echo [INFO] إنشاء مجلد النسخ الاحتياطي...
    mkdir backup
)

REM التحقق من وجود ملف pom.xml
if not exist "pom.xml" (
    echo [ERROR] ملف pom.xml غير موجود
    echo [INFO] تأكد من تشغيل الملف من مجلد المشروع الصحيح
    pause
    exit /b 1
)

echo [INFO] بدء تشغيل نظام إدارة الشحنات...
echo.

REM تشغيل التطبيق باستخدام Maven
echo [INFO] تشغيل التطبيق...
mvn javafx:run

REM في حالة فشل Maven، محاولة تشغيل JAR مباشرة
if %errorlevel% neq 0 (
    echo.
    echo [WARN] فشل في تشغيل التطبيق باستخدام Maven
    echo [INFO] محاولة البناء والتشغيل...
    
    echo [INFO] بناء المشروع...
    mvn clean package -DskipTests
    
    if %errorlevel% equ 0 (
        echo [INFO] تشغيل JAR...
        java -jar target\ship-erp-system-1.0.0.jar
    ) else (
        echo [ERROR] فشل في بناء المشروع
        echo [INFO] تحقق من السجلات للحصول على تفاصيل الخطأ
    )
)

echo.
echo [INFO] انتهى تشغيل التطبيق
pause
