import java.sql.*;

public class TestIASConnection {
    public static void main(String[] args) {
        String[] passwords = {"ys123", "ias123", "oracle", "123", "ias20251"};
        
        for (String password : passwords) {
            try {
                System.out.println("🔗 محاولة الاتصال بـ IAS20251 بكلمة المرور: " + password);
                Class.forName("oracle.jdbc.driver.OracleDriver");
                Connection conn = DriverManager.getConnection(
                    "*************************************", 
                    "ias20251", 
                    password
                );
                
                System.out.println("✅ نجح الاتصال بكلمة المرور: " + password);
                
                // اختبار استعلام بسيط
                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM USER_TABLES");
                if (rs.next()) {
                    System.out.println("📊 عدد الجداول: " + rs.getInt(1));
                }
                
                conn.close();
                break;
                
            } catch (Exception e) {
                System.out.println("❌ فشل الاتصال بكلمة المرور: " + password + " - " + e.getMessage());
            }
        }
    }
}
