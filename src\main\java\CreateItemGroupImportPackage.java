import java.sql.*;

/**
 * إنشاء Package خاص بمجموعات الأصناف للاستيراد
 * Create Item Group Import Package
 */
public class CreateItemGroupImportPackage {
    
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.OracleDriver");
            System.out.println("✅ تم تحميل Oracle JDBC driver بنجاح");
            
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            conn.setAutoCommit(false);
            
            System.out.println("✅ متصل بقاعدة البيانات SHIP_ERP");
            
            // إنشاء Package مجموعات الأصناف
            createItemGroupImportPackage(conn);
            
            conn.commit();
            System.out.println("🎉 تم إنشاء Package مجموعات الأصناف بنجاح!");
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * إنشاء Package استيراد مجموعات الأصناف
     */
    private static void createItemGroupImportPackage(Connection conn) throws SQLException {
        System.out.println("🔄 إنشاء Package استيراد مجموعات الأصناف...");
        
        // Package Specification
        String packageSpec = """
            CREATE OR REPLACE PACKAGE PKG_ITEM_GROUP_IMPORT AS
                -- استيراد مجموعات الأصناف من النظام الأصلي
                
                -- استيراد المجموعات الرئيسية
                FUNCTION IMPORT_MAIN_GROUPS RETURN NUMBER;
                
                -- استيراد المجموعات الفرعية
                FUNCTION IMPORT_MAIN_SUB_GROUPS RETURN NUMBER;
                
                -- استيراد المجموعات تحت فرعية
                FUNCTION IMPORT_SUB_GROUPS RETURN NUMBER;
                
                -- استيراد المجموعات المساعدة
                FUNCTION IMPORT_ASSISTANT_GROUPS RETURN NUMBER;
                
                -- استيراد المجموعات التفصيلية
                FUNCTION IMPORT_DETAIL_GROUPS RETURN NUMBER;
                
                -- استيراد جميع المجموعات
                FUNCTION IMPORT_ALL_GROUPS RETURN NUMBER;
                
                -- التحقق من صحة البيانات
                FUNCTION VALIDATE_GROUP_DATA(p_group_type VARCHAR2) RETURN VARCHAR2;
                
                -- الحصول على إحصائيات الاستيراد
                FUNCTION GET_IMPORT_STATISTICS RETURN SYS_REFCURSOR;
                
            END PKG_ITEM_GROUP_IMPORT;
        """;
        
        executeSQL(conn, packageSpec, "Package Specification - استيراد مجموعات الأصناف");
        
        // Package Body
        String packageBody = """
            CREATE OR REPLACE PACKAGE BODY PKG_ITEM_GROUP_IMPORT AS
                
                FUNCTION IMPORT_MAIN_GROUPS RETURN NUMBER IS
                    v_count NUMBER := 0;
                    v_import_id NUMBER;
                BEGIN
                    -- تسجيل بداية الاستيراد
                    v_import_id := PKG_IMPORT_EXECUTION.LOG_IMPORT_START(
                        (SELECT MAPPING_ID FROM ERP_TABLE_MAPPING WHERE SOURCE_TABLE_NAME = 'GROUP_DETAILS'),
                        'MANUAL', 'SYSTEM'
                    );
                    
                    -- محاكاة استيراد المجموعات الرئيسية
                    -- في التطبيق الحقيقي سيتم الاتصال بقاعدة البيانات الأصلية
                    
                    -- إدراج بيانات تجريبية
                    INSERT INTO ERP_GROUP_DETAILS (
                        G_CODE, G_A_NAME, G_E_NAME, IS_ACTIVE, CREATED_BY, CREATED_DATE
                    ) 
                    SELECT 'G' || LPAD(LEVEL, 3, '0'), 
                           'مجموعة رئيسية ' || LEVEL,
                           'Main Group ' || LEVEL,
                           1, 'IMPORT', SYSDATE
                    FROM DUAL 
                    CONNECT BY LEVEL <= 5
                    WHERE NOT EXISTS (
                        SELECT 1 FROM ERP_GROUP_DETAILS 
                        WHERE G_CODE = 'G' || LPAD(LEVEL, 3, '0')
                    );
                    
                    v_count := SQL%ROWCOUNT;
                    
                    -- تسجيل انتهاء الاستيراد
                    PKG_IMPORT_EXECUTION.LOG_IMPORT_END(
                        v_import_id, v_count, v_count, 0, 'SUCCESS', NULL
                    );
                    
                    COMMIT;
                    RETURN v_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        PKG_IMPORT_EXECUTION.LOG_IMPORT_END(
                            v_import_id, 0, 0, 0, 'FAILED', SQLERRM
                        );
                        RAISE_APPLICATION_ERROR(-20401, 'خطأ في استيراد المجموعات الرئيسية: ' || SQLERRM);
                END IMPORT_MAIN_GROUPS;
                
                FUNCTION IMPORT_MAIN_SUB_GROUPS RETURN NUMBER IS
                    v_count NUMBER := 0;
                    v_import_id NUMBER;
                BEGIN
                    -- تسجيل بداية الاستيراد
                    v_import_id := PKG_IMPORT_EXECUTION.LOG_IMPORT_START(
                        (SELECT MAPPING_ID FROM ERP_TABLE_MAPPING WHERE SOURCE_TABLE_NAME = 'IAS_MAINSUB_GRP_DTL'),
                        'MANUAL', 'SYSTEM'
                    );
                    
                    -- إدراج بيانات تجريبية للمجموعات الفرعية
                    INSERT INTO ERP_MAINSUB_GRP_DTL (
                        G_CODE, MNG_CODE, MNG_A_NAME, MNG_E_NAME, IS_ACTIVE, CREATED_BY, CREATED_DATE
                    )
                    SELECT g.G_CODE,
                           g.G_CODE || LPAD(LEVEL, 2, '0'),
                           'مجموعة فرعية ' || LEVEL || ' للمجموعة ' || g.G_A_NAME,
                           'Sub Group ' || LEVEL || ' for ' || g.G_E_NAME,
                           1, 'IMPORT', SYSDATE
                    FROM ERP_GROUP_DETAILS g
                    CROSS JOIN (SELECT LEVEL FROM DUAL CONNECT BY LEVEL <= 3)
                    WHERE NOT EXISTS (
                        SELECT 1 FROM ERP_MAINSUB_GRP_DTL 
                        WHERE G_CODE = g.G_CODE AND MNG_CODE = g.G_CODE || LPAD(LEVEL, 2, '0')
                    );
                    
                    v_count := SQL%ROWCOUNT;
                    
                    -- تسجيل انتهاء الاستيراد
                    PKG_IMPORT_EXECUTION.LOG_IMPORT_END(
                        v_import_id, v_count, v_count, 0, 'SUCCESS', NULL
                    );
                    
                    COMMIT;
                    RETURN v_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        PKG_IMPORT_EXECUTION.LOG_IMPORT_END(
                            v_import_id, 0, 0, 0, 'FAILED', SQLERRM
                        );
                        RAISE_APPLICATION_ERROR(-20402, 'خطأ في استيراد المجموعات الفرعية: ' || SQLERRM);
                END IMPORT_MAIN_SUB_GROUPS;
                
                FUNCTION IMPORT_SUB_GROUPS RETURN NUMBER IS
                    v_count NUMBER := 0;
                    v_import_id NUMBER;
                BEGIN
                    -- تسجيل بداية الاستيراد
                    v_import_id := PKG_IMPORT_EXECUTION.LOG_IMPORT_START(
                        (SELECT MAPPING_ID FROM ERP_TABLE_MAPPING WHERE SOURCE_TABLE_NAME = 'IAS_SUB_GRP_DTL'),
                        'MANUAL', 'SYSTEM'
                    );
                    
                    -- إدراج بيانات تجريبية للمجموعات تحت فرعية
                    INSERT INTO ERP_SUB_GRP_DTL (
                        G_CODE, MNG_CODE, SUBG_CODE, SUBG_A_NAME, SUBG_E_NAME, IS_ACTIVE, CREATED_BY, CREATED_DATE
                    )
                    SELECT ms.G_CODE, ms.MNG_CODE,
                           ms.MNG_CODE || LPAD(LEVEL, 2, '0'),
                           'مجموعة تحت فرعية ' || LEVEL || ' للمجموعة ' || ms.MNG_A_NAME,
                           'Sub Sub Group ' || LEVEL || ' for ' || ms.MNG_E_NAME,
                           1, 'IMPORT', SYSDATE
                    FROM ERP_MAINSUB_GRP_DTL ms
                    CROSS JOIN (SELECT LEVEL FROM DUAL CONNECT BY LEVEL <= 2)
                    WHERE NOT EXISTS (
                        SELECT 1 FROM ERP_SUB_GRP_DTL 
                        WHERE SUBG_CODE = ms.MNG_CODE || LPAD(LEVEL, 2, '0')
                    );
                    
                    v_count := SQL%ROWCOUNT;
                    
                    -- تسجيل انتهاء الاستيراد
                    PKG_IMPORT_EXECUTION.LOG_IMPORT_END(
                        v_import_id, v_count, v_count, 0, 'SUCCESS', NULL
                    );
                    
                    COMMIT;
                    RETURN v_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        PKG_IMPORT_EXECUTION.LOG_IMPORT_END(
                            v_import_id, 0, 0, 0, 'FAILED', SQLERRM
                        );
                        RAISE_APPLICATION_ERROR(-20403, 'خطأ في استيراد المجموعات تحت فرعية: ' || SQLERRM);
                END IMPORT_SUB_GROUPS;
                
                FUNCTION IMPORT_ASSISTANT_GROUPS RETURN NUMBER IS
                    v_count NUMBER := 0;
                    v_import_id NUMBER;
                BEGIN
                    -- تسجيل بداية الاستيراد
                    v_import_id := PKG_IMPORT_EXECUTION.LOG_IMPORT_START(
                        (SELECT MAPPING_ID FROM ERP_TABLE_MAPPING WHERE SOURCE_TABLE_NAME = 'IAS_ASSISTANT_GROUP'),
                        'MANUAL', 'SYSTEM'
                    );
                    
                    -- إدراج بيانات تجريبية للمجموعات المساعدة
                    INSERT INTO ERP_ASSISTANT_GROUP (
                        G_CODE, MNG_CODE, SUBG_CODE, ASSISTANT_NO, ASSISTANT_A_NAME, ASSISTANT_E_NAME, 
                        IS_ACTIVE, CREATED_BY, CREATED_DATE
                    )
                    SELECT s.G_CODE, s.MNG_CODE, s.SUBG_CODE,
                           s.SUBG_CODE || LPAD(LEVEL, 2, '0'),
                           'مجموعة مساعدة ' || LEVEL || ' للمجموعة ' || s.SUBG_A_NAME,
                           'Assistant Group ' || LEVEL || ' for ' || s.SUBG_E_NAME,
                           1, 'IMPORT', SYSDATE
                    FROM ERP_SUB_GRP_DTL s
                    CROSS JOIN (SELECT LEVEL FROM DUAL CONNECT BY LEVEL <= 2)
                    WHERE NOT EXISTS (
                        SELECT 1 FROM ERP_ASSISTANT_GROUP 
                        WHERE ASSISTANT_NO = s.SUBG_CODE || LPAD(LEVEL, 2, '0')
                    );
                    
                    v_count := SQL%ROWCOUNT;
                    
                    -- تسجيل انتهاء الاستيراد
                    PKG_IMPORT_EXECUTION.LOG_IMPORT_END(
                        v_import_id, v_count, v_count, 0, 'SUCCESS', NULL
                    );
                    
                    COMMIT;
                    RETURN v_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        PKG_IMPORT_EXECUTION.LOG_IMPORT_END(
                            v_import_id, 0, 0, 0, 'FAILED', SQLERRM
                        );
                        RAISE_APPLICATION_ERROR(-20404, 'خطأ في استيراد المجموعات المساعدة: ' || SQLERRM);
                END IMPORT_ASSISTANT_GROUPS;
                
                FUNCTION IMPORT_DETAIL_GROUPS RETURN NUMBER IS
                    v_count NUMBER := 0;
                    v_import_id NUMBER;
                BEGIN
                    -- تسجيل بداية الاستيراد
                    v_import_id := PKG_IMPORT_EXECUTION.LOG_IMPORT_START(
                        (SELECT MAPPING_ID FROM ERP_TABLE_MAPPING WHERE SOURCE_TABLE_NAME = 'IAS_DETAIL_GROUP'),
                        'MANUAL', 'SYSTEM'
                    );
                    
                    -- إدراج بيانات تجريبية للمجموعات التفصيلية
                    INSERT INTO ERP_DETAIL_GROUP (
                        G_CODE, MNG_CODE, SUBG_CODE, ASSISTANT_NO, DETAIL_NO, DETAIL_A_NAME, DETAIL_E_NAME,
                        IS_ACTIVE, CREATED_BY, CREATED_DATE
                    )
                    SELECT a.G_CODE, a.MNG_CODE, a.SUBG_CODE, a.ASSISTANT_NO,
                           a.ASSISTANT_NO || LPAD(LEVEL, 2, '0'),
                           'مجموعة تفصيلية ' || LEVEL || ' للمجموعة ' || a.ASSISTANT_A_NAME,
                           'Detail Group ' || LEVEL || ' for ' || a.ASSISTANT_E_NAME,
                           1, 'IMPORT', SYSDATE
                    FROM ERP_ASSISTANT_GROUP a
                    CROSS JOIN (SELECT LEVEL FROM DUAL CONNECT BY LEVEL <= 2)
                    WHERE NOT EXISTS (
                        SELECT 1 FROM ERP_DETAIL_GROUP 
                        WHERE DETAIL_NO = a.ASSISTANT_NO || LPAD(LEVEL, 2, '0')
                    );
                    
                    v_count := SQL%ROWCOUNT;
                    
                    -- تسجيل انتهاء الاستيراد
                    PKG_IMPORT_EXECUTION.LOG_IMPORT_END(
                        v_import_id, v_count, v_count, 0, 'SUCCESS', NULL
                    );
                    
                    COMMIT;
                    RETURN v_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        PKG_IMPORT_EXECUTION.LOG_IMPORT_END(
                            v_import_id, 0, 0, 0, 'FAILED', SQLERRM
                        );
                        RAISE_APPLICATION_ERROR(-20405, 'خطأ في استيراد المجموعات التفصيلية: ' || SQLERRM);
                END IMPORT_DETAIL_GROUPS;
                
                FUNCTION IMPORT_ALL_GROUPS RETURN NUMBER IS
                    v_total_count NUMBER := 0;
                BEGIN
                    v_total_count := v_total_count + IMPORT_MAIN_GROUPS();
                    v_total_count := v_total_count + IMPORT_MAIN_SUB_GROUPS();
                    v_total_count := v_total_count + IMPORT_SUB_GROUPS();
                    v_total_count := v_total_count + IMPORT_ASSISTANT_GROUPS();
                    v_total_count := v_total_count + IMPORT_DETAIL_GROUPS();
                    
                    RETURN v_total_count;
                END IMPORT_ALL_GROUPS;
                
                FUNCTION VALIDATE_GROUP_DATA(p_group_type VARCHAR2) RETURN VARCHAR2 IS
                    v_result VARCHAR2(4000);
                    v_count NUMBER;
                BEGIN
                    CASE p_group_type
                        WHEN 'main' THEN
                            SELECT COUNT(*) INTO v_count FROM ERP_GROUP_DETAILS;
                            v_result := 'المجموعات الرئيسية: ' || v_count || ' سجل';
                        WHEN 'mainsub' THEN
                            SELECT COUNT(*) INTO v_count FROM ERP_MAINSUB_GRP_DTL;
                            v_result := 'المجموعات الفرعية: ' || v_count || ' سجل';
                        WHEN 'sub' THEN
                            SELECT COUNT(*) INTO v_count FROM ERP_SUB_GRP_DTL;
                            v_result := 'المجموعات تحت فرعية: ' || v_count || ' سجل';
                        WHEN 'assistant' THEN
                            SELECT COUNT(*) INTO v_count FROM ERP_ASSISTANT_GROUP;
                            v_result := 'المجموعات المساعدة: ' || v_count || ' سجل';
                        WHEN 'detail' THEN
                            SELECT COUNT(*) INTO v_count FROM ERP_DETAIL_GROUP;
                            v_result := 'المجموعات التفصيلية: ' || v_count || ' سجل';
                        ELSE
                            v_result := 'نوع مجموعة غير صحيح';
                    END CASE;
                    
                    RETURN v_result;
                EXCEPTION
                    WHEN OTHERS THEN
                        RETURN 'خطأ في التحقق: ' || SQLERRM;
                END VALIDATE_GROUP_DATA;
                
                FUNCTION GET_IMPORT_STATISTICS RETURN SYS_REFCURSOR IS
                    v_cursor SYS_REFCURSOR;
                BEGIN
                    OPEN v_cursor FOR
                        SELECT 'المجموعات الرئيسية' as GROUP_TYPE, COUNT(*) as RECORD_COUNT
                        FROM ERP_GROUP_DETAILS
                        UNION ALL
                        SELECT 'المجموعات الفرعية', COUNT(*)
                        FROM ERP_MAINSUB_GRP_DTL
                        UNION ALL
                        SELECT 'المجموعات تحت فرعية', COUNT(*)
                        FROM ERP_SUB_GRP_DTL
                        UNION ALL
                        SELECT 'المجموعات المساعدة', COUNT(*)
                        FROM ERP_ASSISTANT_GROUP
                        UNION ALL
                        SELECT 'المجموعات التفصيلية', COUNT(*)
                        FROM ERP_DETAIL_GROUP;
                    
                    RETURN v_cursor;
                END GET_IMPORT_STATISTICS;
                
            END PKG_ITEM_GROUP_IMPORT;
        """;
        
        executeSQL(conn, packageBody, "Package Body - استيراد مجموعات الأصناف");
    }
    
    /**
     * تنفيذ SQL مع معالجة الأخطاء
     */
    private static void executeSQL(Connection conn, String sql, String description) throws SQLException {
        try {
            Statement stmt = conn.createStatement();
            stmt.execute(sql);
            System.out.println("✅ تم إنشاء " + description);
        } catch (SQLException e) {
            if (e.getErrorCode() == 955) { // ORA-00955: name is already used
                System.out.println("⚠️ " + description + " موجود مسبقاً");
            } else {
                System.err.println("❌ خطأ في إنشاء " + description + ": " + e.getMessage());
                throw e;
            }
        }
    }
}
