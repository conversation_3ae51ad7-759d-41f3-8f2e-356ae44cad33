import java.io.FileWriter;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * أداة التحليل الشامل لقاعدة البيانات IAS20251 Complete Database Analyzer for IAS20251
 */
public class CompleteDatabaseAnalyzer {

    private Connection connection;
    private PrintWriter reportWriter;

    public static void main(String[] args) {
        CompleteDatabaseAnalyzer analyzer = new CompleteDatabaseAnalyzer();
        analyzer.performCompleteAnalysis();
    }

    /**
     * تنفيذ التحليل الشامل
     */
    public void performCompleteAnalysis() {
        try {
            // إنشاء ملف التقرير
            reportWriter = new PrintWriter(new FileWriter("IAS20251_COMPLETE_ANALYSIS.md"), true);

            System.out.println("🔄 بدء التحليل الشامل لقاعدة البيانات IAS20251...");
            writeHeader();

            // الاتصال بقاعدة البيانات
            if (!connectToDatabase()) {
                return;
            }

            // 1. تحليل جميع الجداول
            analyzeAllTables();

            // 2. تحليل العلاقات والمفاتيح الخارجية
            analyzeForeignKeys();

            // 3. تحليل الفهارس
            analyzeIndexes();

            // 4. تحليل المشاهد (Views)
            analyzeViews();

            // 5. تحليل المتسلسلات (Sequences)
            analyzeSequences();

            // 6. تحليل الإجراءات المخزنة
            analyzeStoredProcedures();

            // 7. تحليل البيانات والإحصائيات
            analyzeDataStatistics();

            // 8. تحليل أنماط التسمية
            analyzeNamingPatterns();

            // 9. إنشاء خريطة قاعدة البيانات
            createDatabaseMap();

            // 10. توصيات للنظام الجديد
            generateRecommendations();

            System.out.println("✅ تم إكمال التحليل الشامل!");
            System.out.println("📄 تقرير مفصل محفوظ في: IAS20251_COMPLETE_ANALYSIS.md");

        } catch (Exception e) {
            System.err.println("❌ خطأ في التحليل: " + e.getMessage());
            e.printStackTrace();
        } finally {
            cleanup();
        }
    }

    /**
     * الاتصال بقاعدة البيانات
     */
    private boolean connectToDatabase() {
        try {
            String url = "*************************************";
            String username = "ias20251";
            String password = "ys123";

            connection = DriverManager.getConnection(url, username, password);

            System.out.println("✅ تم الاتصال بقاعدة البيانات بنجاح");
            writeToReport("✅ تم الاتصال بقاعدة البيانات بنجاح\n");
            return true;

        } catch (SQLException e) {
            System.err.println("❌ فشل في الاتصال: " + e.getMessage());
            writeToReport("❌ فشل في الاتصال: " + e.getMessage() + "\n");
            return false;
        }
    }

    /**
     * تحليل جميع الجداول
     */
    private void analyzeAllTables() throws SQLException {
        System.out.println("📋 تحليل جميع الجداول...");
        writeToReport("\n## 📋 تحليل جميع الجداول\n\n");

        String query = """
                    SELECT
                        table_name,
                        num_rows,
                        blocks,
                        avg_row_len,
                        last_analyzed
                    FROM all_tables
                    WHERE owner = 'IAS20251'
                    ORDER BY table_name
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            int tableCount = 0;
            writeToReport("| اسم الجدول | عدد الصفوف | الكتل | متوسط طول الصف | آخر تحليل |\n");
            writeToReport("|------------|------------|-------|----------------|----------|\n");

            while (rs.next()) {
                String tableName = rs.getString("table_name");
                String numRows = rs.getString("num_rows");
                String blocks = rs.getString("blocks");
                String avgRowLen = rs.getString("avg_row_len");
                String lastAnalyzed = rs.getString("last_analyzed");

                writeToReport(String.format("| %s | %s | %s | %s | %s |\n", tableName,
                        numRows != null ? numRows : "غير محدد",
                        blocks != null ? blocks : "غير محدد",
                        avgRowLen != null ? avgRowLen : "غير محدد",
                        lastAnalyzed != null ? lastAnalyzed : "غير محدد"));

                tableCount++;

                // تحليل تفصيلي لكل جدول
                analyzeTableStructure(tableName);
            }

            writeToReport(String.format("\n**إجمالي الجداول: %d**\n", tableCount));
            System.out.println("📊 تم العثور على " + tableCount + " جدول");
        }
    }

    /**
     * تحليل بنية جدول محدد
     */
    private void analyzeTableStructure(String tableName) throws SQLException {
        writeToReport(String.format("\n### 🔍 تفاصيل جدول %s:\n\n", tableName));

        String query = """
                    SELECT
                        column_name,
                        data_type,
                        data_length,
                        data_precision,
                        data_scale,
                        nullable,
                        data_default,
                        column_id
                    FROM all_tab_columns
                    WHERE owner = 'IAS20251' AND table_name = ?
                    ORDER BY column_id
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, tableName);

            try (ResultSet rs = stmt.executeQuery()) {
                writeToReport(
                        "| العمود | النوع | الطول | دقة | مقياس | يقبل NULL | القيمة الافتراضية |\n");
                writeToReport(
                        "|--------|-------|-------|-----|-------|-----------|------------------|\n");

                int columnCount = 0;
                while (rs.next()) {
                    String columnName = rs.getString("column_name");
                    String dataType = rs.getString("data_type");
                    String dataLength = rs.getString("data_length");
                    String dataPrecision = rs.getString("data_precision");
                    String dataScale = rs.getString("data_scale");
                    String nullable = rs.getString("nullable");
                    String dataDefault = rs.getString("data_default");

                    String typeInfo = dataType;
                    if (dataPrecision != null) {
                        typeInfo += "(" + dataPrecision;
                        if (dataScale != null && !dataScale.equals("0")) {
                            typeInfo += "," + dataScale;
                        }
                        typeInfo += ")";
                    } else if (dataLength != null && !dataType.equals("DATE")) {
                        typeInfo += "(" + dataLength + ")";
                    }

                    writeToReport(String.format("| %s | %s | %s | %s | %s | %s | %s |\n",
                            columnName, typeInfo, dataLength != null ? dataLength : "-",
                            dataPrecision != null ? dataPrecision : "-",
                            dataScale != null ? dataScale : "-",
                            "Y".equals(nullable) ? "نعم" : "لا",
                            dataDefault != null
                                    ? dataDefault.substring(0, Math.min(dataDefault.length(), 20))
                                    : "-"));

                    columnCount++;
                }

                writeToReport(String.format("\n**عدد الأعمدة: %d**\n", columnCount));
            }
        }

        // تحليل عينة من البيانات
        analyzeSampleData(tableName);
    }

    /**
     * تحليل عينة من البيانات
     */
    private void analyzeSampleData(String tableName) throws SQLException {
        writeToReport(String.format("\n#### 📊 عينة من بيانات %s:\n\n", tableName));

        String query = String.format("SELECT * FROM IAS20251.%s WHERE ROWNUM <= 3", tableName);

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            // عرض أسماء الأعمدة
            writeToReport("| ");
            for (int i = 1; i <= Math.min(columnCount, 5); i++) {
                writeToReport(metaData.getColumnName(i) + " | ");
            }
            if (columnCount > 5) {
                writeToReport("... |");
            }
            writeToReport("\n");

            // خط الفصل
            writeToReport("| ");
            for (int i = 1; i <= Math.min(columnCount, 5); i++) {
                writeToReport("--- | ");
            }
            if (columnCount > 5) {
                writeToReport("--- |");
            }
            writeToReport("\n");

            // عرض البيانات
            int rowCount = 0;
            while (rs.next() && rowCount < 3) {
                writeToReport("| ");
                for (int i = 1; i <= Math.min(columnCount, 5); i++) {
                    Object value = rs.getObject(i);
                    String displayValue = value != null ? value.toString() : "NULL";
                    if (displayValue.length() > 15) {
                        displayValue = displayValue.substring(0, 15) + "...";
                    }
                    writeToReport(displayValue + " | ");
                }
                if (columnCount > 5) {
                    writeToReport("... |");
                }
                writeToReport("\n");
                rowCount++;
            }

            if (rowCount == 0) {
                writeToReport("*الجدول فارغ*\n");
            }

        } catch (SQLException e) {
            writeToReport("*خطأ في قراءة البيانات: " + e.getMessage() + "*\n");
        }

        writeToReport("\n---\n");
    }

    /**
     * تحليل المفاتيح الخارجية
     */
    private void analyzeForeignKeys() throws SQLException {
        System.out.println("🔗 تحليل المفاتيح الخارجية...");
        writeToReport("\n## 🔗 تحليل المفاتيح الخارجية والعلاقات\n\n");

        String query =
                """
                            SELECT
                                a.constraint_name,
                                a.table_name,
                                a.column_name,
                                c_pk.table_name r_table_name,
                                c_pk.column_name r_column_name
                            FROM all_cons_columns a
                            JOIN all_constraints c ON a.owner = c.owner AND a.constraint_name = c.constraint_name
                            JOIN all_constraints c_pk ON c.r_owner = c_pk.owner AND c.r_constraint_name = c_pk.constraint_name
                            JOIN all_cons_columns c_pk ON c_pk.owner = c_pk.owner AND c_pk.constraint_name = c_pk.constraint_name
                            WHERE c.constraint_type = 'R'
                            AND a.owner = 'IAS20251'
                            ORDER BY a.table_name, a.constraint_name
                        """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            writeToReport("| الجدول | العمود | يشير إلى جدول | يشير إلى عمود | اسم القيد |\n");
            writeToReport("|--------|-------|---------------|---------------|----------|\n");

            int fkCount = 0;
            while (rs.next()) {
                writeToReport(
                        String.format("| %s | %s | %s | %s | %s |\n", rs.getString("table_name"),
                                rs.getString("column_name"), rs.getString("r_table_name"),
                                rs.getString("r_column_name"), rs.getString("constraint_name")));
                fkCount++;
            }

            writeToReport(String.format("\n**إجمالي المفاتيح الخارجية: %d**\n", fkCount));
        }
    }

    /**
     * كتابة رأس التقرير
     */
    private void writeHeader() {
        writeToReport("# 🎯 التحليل الشامل لقاعدة البيانات IAS20251\n");
        writeToReport("## Complete Analysis of IAS20251 Database\n\n");
        writeToReport("**تاريخ التحليل:** " + new Date() + "\n");
        writeToReport("**الهدف:** تحليل شامل لبنية قاعدة البيانات لبناء نظام ERP جديد\n\n");
        writeToReport("---\n\n");
    }

    /**
     * كتابة في ملف التقرير
     */
    private void writeToReport(String text) {
        if (reportWriter != null) {
            reportWriter.print(text);
            reportWriter.flush();
        }
    }

    /**
     * تحليل الفهارس
     */
    private void analyzeIndexes() throws SQLException {
        System.out.println("📇 تحليل الفهارس...");
        writeToReport("\n## 📇 تحليل الفهارس\n\n");

        String query = """
                    SELECT
                        index_name,
                        table_name,
                        uniqueness,
                        index_type,
                        status
                    FROM all_indexes
                    WHERE owner = 'IAS20251'
                    ORDER BY table_name, index_name
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            writeToReport("| اسم الفهرس | الجدول | فريد | النوع | الحالة |\n");
            writeToReport("|------------|--------|------|-------|-------|\n");

            int indexCount = 0;
            while (rs.next()) {
                writeToReport(
                        String.format("| %s | %s | %s | %s | %s |\n", rs.getString("index_name"),
                                rs.getString("table_name"), rs.getString("uniqueness"),
                                rs.getString("index_type"), rs.getString("status")));
                indexCount++;
            }

            writeToReport(String.format("\n**إجمالي الفهارس: %d**\n", indexCount));
        }
    }

    /**
     * تحليل المشاهد
     */
    private void analyzeViews() throws SQLException {
        System.out.println("👁️ تحليل المشاهد...");
        writeToReport("\n## 👁️ تحليل المشاهد (Views)\n\n");

        String query = """
                    SELECT
                        view_name,
                        text_length,
                        read_only
                    FROM all_views
                    WHERE owner = 'IAS20251'
                    ORDER BY view_name
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            writeToReport("| اسم المشهد | طول النص | للقراءة فقط |\n");
            writeToReport("|------------|-----------|-------------|\n");

            int viewCount = 0;
            while (rs.next()) {
                writeToReport(String.format("| %s | %s | %s |\n", rs.getString("view_name"),
                        rs.getString("text_length"), rs.getString("read_only")));
                viewCount++;
            }

            writeToReport(String.format("\n**إجمالي المشاهد: %d**\n", viewCount));
        }
    }

    /**
     * تحليل المتسلسلات
     */
    private void analyzeSequences() throws SQLException {
        System.out.println("🔢 تحليل المتسلسلات...");
        writeToReport("\n## 🔢 تحليل المتسلسلات (Sequences)\n\n");

        String query = """
                    SELECT
                        sequence_name,
                        min_value,
                        max_value,
                        increment_by,
                        last_number
                    FROM all_sequences
                    WHERE sequence_owner = 'IAS20251'
                    ORDER BY sequence_name
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            writeToReport("| اسم المتسلسل | القيمة الدنيا | القيمة العليا | الزيادة | آخر رقم |\n");
            writeToReport(
                    "|--------------|---------------|---------------|---------|----------|\n");

            int seqCount = 0;
            while (rs.next()) {
                writeToReport(
                        String.format("| %s | %s | %s | %s | %s |\n", rs.getString("sequence_name"),
                                rs.getString("min_value"), rs.getString("max_value"),
                                rs.getString("increment_by"), rs.getString("last_number")));
                seqCount++;
            }

            writeToReport(String.format("\n**إجمالي المتسلسلات: %d**\n", seqCount));
        }
    }

    /**
     * تحليل الإجراءات المخزنة
     */
    private void analyzeStoredProcedures() throws SQLException {
        System.out.println("⚙️ تحليل الإجراءات المخزنة...");
        writeToReport("\n## ⚙️ تحليل الإجراءات المخزنة\n\n");

        String query = """
                    SELECT
                        object_name,
                        object_type,
                        status,
                        created,
                        last_ddl_time
                    FROM all_objects
                    WHERE owner = 'IAS20251'
                    AND object_type IN ('PROCEDURE', 'FUNCTION', 'PACKAGE', 'TRIGGER')
                    ORDER BY object_type, object_name
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            writeToReport("| الاسم | النوع | الحالة | تاريخ الإنشاء | آخر تعديل |\n");
            writeToReport("|-------|------|-------|-------------|----------|\n");

            int objCount = 0;
            while (rs.next()) {
                writeToReport(
                        String.format("| %s | %s | %s | %s | %s |\n", rs.getString("object_name"),
                                rs.getString("object_type"), rs.getString("status"),
                                rs.getString("created"), rs.getString("last_ddl_time")));
                objCount++;
            }

            writeToReport(String.format("\n**إجمالي الكائنات: %d**\n", objCount));
        }
    }

    /**
     * تحليل البيانات والإحصائيات
     */
    private void analyzeDataStatistics() throws SQLException {
        System.out.println("📊 تحليل إحصائيات البيانات...");
        writeToReport("\n## 📊 إحصائيات البيانات\n\n");

        // إحصائيات عامة
        writeToReport("### إحصائيات عامة:\n\n");

        String[] importantTables =
                {"IAS_ITM_MST", "IAS_ITM_DTL", "IAS_CUSTOMER", "IAS_VENDOR", "IAS_INVOICE"};

        for (String tableName : importantTables) {
            analyzeTableStatistics(tableName);
        }
    }

    /**
     * تحليل إحصائيات جدول محدد
     */
    private void analyzeTableStatistics(String tableName) throws SQLException {
        String query = String.format("SELECT COUNT(*) as total_rows FROM IAS20251.%s", tableName);

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                int totalRows = rs.getInt("total_rows");
                writeToReport(String.format("- **%s**: %,d صف\n", tableName, totalRows));
            }
        } catch (SQLException e) {
            writeToReport(
                    String.format("- **%s**: غير متاح (خطأ: %s)\n", tableName, e.getMessage()));
        }
    }

    /**
     * تحليل أنماط التسمية
     */
    private void analyzeNamingPatterns() throws SQLException {
        System.out.println("🏷️ تحليل أنماط التسمية...");
        writeToReport("\n## 🏷️ تحليل أنماط التسمية\n\n");

        // تحليل بادئات الجداول
        Map<String, Integer> prefixes = new HashMap<>();

        String query = "SELECT table_name FROM all_tables WHERE owner = 'IAS20251'";

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                String tableName = rs.getString("table_name");
                String prefix = tableName.contains("_") ? tableName.split("_")[0] : tableName;
                prefixes.put(prefix, prefixes.getOrDefault(prefix, 0) + 1);
            }
        }

        writeToReport("### بادئات الجداول:\n\n");
        writeToReport("| البادئة | عدد الجداول |\n");
        writeToReport("|---------|-------------|\n");

        prefixes.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry -> writeToReport(
                        String.format("| %s | %d |\n", entry.getKey(), entry.getValue())));
    }

    /**
     * إنشاء خريطة قاعدة البيانات
     */
    private void createDatabaseMap() throws SQLException {
        System.out.println("🗺️ إنشاء خريطة قاعدة البيانات...");
        writeToReport("\n## 🗺️ خريطة قاعدة البيانات\n\n");

        writeToReport("```mermaid\n");
        writeToReport("erDiagram\n");

        // إضافة الجداول الرئيسية والعلاقات
        String query =
                """
                            SELECT DISTINCT
                                a.table_name,
                                c_pk.table_name r_table_name
                            FROM all_cons_columns a
                            JOIN all_constraints c ON a.owner = c.owner AND a.constraint_name = c.constraint_name
                            JOIN all_constraints c_pk ON c.r_owner = c_pk.owner AND c.r_constraint_name = c_pk.constraint_name
                            WHERE c.constraint_type = 'R'
                            AND a.owner = 'IAS20251'
                            ORDER BY a.table_name
                        """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                String tableName = rs.getString("table_name");
                String refTableName = rs.getString("r_table_name");
                writeToReport(
                        String.format("    %s ||--o{ %s : references\n", refTableName, tableName));
            }
        }

        writeToReport("```\n\n");
    }

    /**
     * إنشاء التوصيات
     */
    private void generateRecommendations() throws SQLException {
        System.out.println("💡 إنشاء التوصيات...");
        writeToReport("\n## 💡 التوصيات لبناء النظام الجديد\n\n");

        writeToReport("### 🎯 التوصيات الرئيسية:\n\n");
        writeToReport("1. **الجداول الأساسية للنسخ:**\n");
        writeToReport("   - IAS_ITM_MST (الأصناف الرئيسي)\n");
        writeToReport("   - IAS_ITM_DTL (تفاصيل الأصناف)\n");
        writeToReport("   - جداول العملاء والموردين\n");
        writeToReport("   - جداول الفواتير والمعاملات\n\n");

        writeToReport("2. **التحسينات المقترحة:**\n");
        writeToReport("   - توحيد أنماط التسمية\n");
        writeToReport("   - إضافة قيود مرجعية محسنة\n");
        writeToReport("   - تحسين الفهارس للأداء\n");
        writeToReport("   - إضافة جداول التدقيق\n\n");

        writeToReport("3. **الخطة المقترحة:**\n");
        writeToReport("   - المرحلة 1: نسخ الجداول الأساسية\n");
        writeToReport("   - المرحلة 2: تحسين البنية\n");
        writeToReport("   - المرحلة 3: إضافة الوظائف الجديدة\n");
        writeToReport("   - المرحلة 4: أدوات الاستيراد والمزامنة\n\n");
    }

    /**
     * تنظيف الموارد
     */
    private void cleanup() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
            if (reportWriter != null) {
                reportWriter.close();
            }
        } catch (SQLException e) {
            System.err.println("خطأ في إغلاق الاتصال: " + e.getMessage());
        }
    }
}
