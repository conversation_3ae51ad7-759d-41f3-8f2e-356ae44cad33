package com.shipment.erp.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * كيان الفرع
 * يمثل فروع الشركة
 */
@Entity
@Table(name = "BRANCHES", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"COMPANY_ID", "CODE"})
})
@SequenceGenerator(name = "branch_seq", sequenceName = "SEQ_BRANCH", allocationSize = 1)
public class Branch extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "COMPANY_ID", nullable = false)
    @NotNull(message = "الشركة مطلوبة")
    private Company company;

    @Column(name = "CODE", nullable = false, length = 20)
    @NotBlank(message = "رمز الفرع مطلوب")
    @Size(max = 20, message = "رمز الفرع يجب أن يكون أقل من 20 حرف")
    private String code;

    @Column(name = "NAME", nullable = false, length = 200)
    @NotBlank(message = "اسم الفرع مطلوب")
    @Size(max = 200, message = "اسم الفرع يجب أن يكون أقل من 200 حرف")
    private String name;

    @Column(name = "NAME_EN", length = 200)
    @Size(max = 200, message = "الاسم الإنجليزي يجب أن يكون أقل من 200 حرف")
    private String nameEn;

    @Column(name = "ADDRESS", length = 500)
    @Size(max = 500, message = "العنوان يجب أن يكون أقل من 500 حرف")
    private String address;

    @Column(name = "MANAGER_NAME", length = 100)
    @Size(max = 100, message = "اسم المدير يجب أن يكون أقل من 100 حرف")
    private String managerName;

    @Column(name = "PHONE", length = 50)
    @Size(max = 50, message = "رقم الهاتف يجب أن يكون أقل من 50 حرف")
    private String phone;

    @Column(name = "EMAIL", length = 100)
    @Email(message = "البريد الإلكتروني غير صحيح")
    @Size(max = 100, message = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")
    private String email;

    @Column(name = "IS_ACTIVE", nullable = false)
    private Boolean isActive = true;

    @OneToMany(mappedBy = "branch", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<User> users = new ArrayList<>();

    /**
     * Constructor افتراضي
     */
    public Branch() {
        super();
    }

    /**
     * Constructor مع الرمز والاسم
     */
    public Branch(String code, String name) {
        this();
        this.code = code;
        this.name = name;
    }

    /**
     * Constructor مع الشركة والرمز والاسم
     */
    public Branch(Company company, String code, String name) {
        this(code, name);
        this.company = company;
    }

    // Getters and Setters

    public Company getCompany() {
        return company;
    }

    public void setCompany(Company company) {
        this.company = company;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public List<User> getUsers() {
        return users;
    }

    public void setUsers(List<User> users) {
        this.users = users;
    }

    /**
     * إضافة مستخدم جديد
     */
    public void addUser(User user) {
        users.add(user);
        user.setBranch(this);
    }

    /**
     * إزالة مستخدم
     */
    public void removeUser(User user) {
        users.remove(user);
        user.setBranch(null);
    }

    /**
     * الحصول على الاسم الكامل للفرع (رمز - اسم)
     */
    public String getFullName() {
        return code + " - " + name;
    }

    @Override
    public String toString() {
        return "Branch{" +
                "id=" + getId() +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", managerName='" + managerName + '\'' +
                ", isActive=" + isActive +
                '}';
    }
}
