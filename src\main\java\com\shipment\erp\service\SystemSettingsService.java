package com.shipment.erp.service;

import com.shipment.erp.model.SystemSettings;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service لإعدادات النظام
 */
public interface SystemSettingsService extends BaseService<SystemSettings> {

    /**
     * البحث عن إعداد بالمفتاح
     */
    Optional<SystemSettings> findBySettingKey(String settingKey);

    /**
     * الحصول على قيمة إعداد
     */
    Optional<String> getSettingValue(String settingKey);

    /**
     * الحصول على قيمة إعداد مع قيمة افتراضية
     */
    String getSettingValue(String settingKey, String defaultValue);

    /**
     * الحصول على قيمة إعداد كـ Integer
     */
    Optional<Integer> getIntegerSetting(String settingKey);

    /**
     * الحصول على قيمة إعداد كـ Integer مع قيمة افتراضية
     */
    Integer getIntegerSetting(String settingKey, Integer defaultValue);

    /**
     * الحصول على قيمة إعداد كـ Double
     */
    Optional<Double> getDoubleSetting(String settingKey);

    /**
     * الحصول على قيمة إعداد كـ Double مع قيمة افتراضية
     */
    Double getDoubleSetting(String settingKey, Double defaultValue);

    /**
     * الحصول على قيمة إعداد كـ Boolean
     */
    Optional<Boolean> getBooleanSetting(String settingKey);

    /**
     * الحصول على قيمة إعداد كـ Boolean مع قيمة افتراضية
     */
    Boolean getBooleanSetting(String settingKey, Boolean defaultValue);

    /**
     * تحديث قيمة إعداد
     */
    void updateSettingValue(String settingKey, String settingValue);

    /**
     * تحديث قيمة إعداد Integer
     */
    void updateIntegerSetting(String settingKey, Integer value);

    /**
     * تحديث قيمة إعداد Double
     */
    void updateDoubleSetting(String settingKey, Double value);

    /**
     * تحديث قيمة إعداد Boolean
     */
    void updateBooleanSetting(String settingKey, Boolean value);

    /**
     * إنشاء أو تحديث إعداد
     */
    SystemSettings createOrUpdateSetting(String settingKey, String settingValue, 
                                        String settingType, String description);

    /**
     * إنشاء أو تحديث إعداد مع النوع
     */
    SystemSettings createOrUpdateSetting(String settingKey, String settingValue, 
                                        SystemSettings.SettingType settingType, String description);

    /**
     * حذف إعداد بالمفتاح
     */
    void deleteBySettingKey(String settingKey);

    /**
     * البحث عن الإعدادات بالنوع
     */
    List<SystemSettings> findBySettingType(String settingType);

    /**
     * البحث عن الإعدادات بالنوع
     */
    List<SystemSettings> findBySettingType(SystemSettings.SettingType settingType);

    /**
     * البحث عن إعدادات النظام
     */
    List<SystemSettings> findSystemSettings();

    /**
     * البحث عن إعدادات المستخدم
     */
    List<SystemSettings> findUserSettings();

    /**
     * الحصول على جميع الإعدادات كـ Map
     */
    Map<String, String> getAllSettingsAsMap();

    /**
     * الحصول على إعدادات النظام كـ Map
     */
    Map<String, String> getSystemSettingsAsMap();

    /**
     * الحصول على إعدادات المستخدم كـ Map
     */
    Map<String, String> getUserSettingsAsMap();

    /**
     * البحث المتقدم في الإعدادات
     */
    List<SystemSettings> advancedSearch(String settingKey, String settingType, 
                                       Boolean isSystem, String description);

    /**
     * التحقق من اكتمال الإعدادات المطلوبة
     */
    boolean areRequiredSettingsComplete();

    /**
     * الحصول على الإعدادات المفقودة
     */
    List<String> getMissingRequiredSettings();

    /**
     * إنشاء الإعدادات الافتراضية
     */
    void createDefaultSettings();

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     */
    void resetToDefaults();

    /**
     * إعادة تعيين إعداد معين للقيمة الافتراضية
     */
    void resetSettingToDefault(String settingKey);

    /**
     * تصدير الإعدادات
     */
    byte[] exportSettings(String format, List<String> settingKeys);

    /**
     * استيراد الإعدادات
     */
    void importSettings(byte[] data, String format, boolean overwriteExisting);

    /**
     * نسخ احتياطي للإعدادات
     */
    byte[] backupSettings();

    /**
     * استعادة الإعدادات من نسخة احتياطية
     */
    void restoreSettings(byte[] backupData);

    /**
     * التحقق من صحة قيمة الإعداد
     */
    boolean isValidSettingValue(String settingKey, String settingValue);

    /**
     * الحصول على الإعدادات المطلوبة للتطبيق
     */
    List<SystemSettings> getRequiredSettings();

    /**
     * تحديث إعدادات متعددة
     */
    void updateMultipleSettings(Map<String, String> settings);

    /**
     * الحصول على إحصائيات الإعدادات
     */
    SettingsStatistics getSettingsStatistics();

    /**
     * إحصائيات الإعدادات
     */
    class SettingsStatistics {
        private long totalSettings;
        private long systemSettings;
        private long userSettings;
        private long stringSettings;
        private long integerSettings;
        private long booleanSettings;
        private long decimalSettings;

        public SettingsStatistics() {}

        public SettingsStatistics(long totalSettings, long systemSettings, long userSettings,
                                long stringSettings, long integerSettings, long booleanSettings, long decimalSettings) {
            this.totalSettings = totalSettings;
            this.systemSettings = systemSettings;
            this.userSettings = userSettings;
            this.stringSettings = stringSettings;
            this.integerSettings = integerSettings;
            this.booleanSettings = booleanSettings;
            this.decimalSettings = decimalSettings;
        }

        // Getters and setters
        public long getTotalSettings() { return totalSettings; }
        public void setTotalSettings(long totalSettings) { this.totalSettings = totalSettings; }

        public long getSystemSettings() { return systemSettings; }
        public void setSystemSettings(long systemSettings) { this.systemSettings = systemSettings; }

        public long getUserSettings() { return userSettings; }
        public void setUserSettings(long userSettings) { this.userSettings = userSettings; }

        public long getStringSettings() { return stringSettings; }
        public void setStringSettings(long stringSettings) { this.stringSettings = stringSettings; }

        public long getIntegerSettings() { return integerSettings; }
        public void setIntegerSettings(long integerSettings) { this.integerSettings = integerSettings; }

        public long getBooleanSettings() { return booleanSettings; }
        public void setBooleanSettings(long booleanSettings) { this.booleanSettings = booleanSettings; }

        public long getDecimalSettings() { return decimalSettings; }
        public void setDecimalSettings(long decimalSettings) { this.decimalSettings = decimalSettings; }
    }
}
