package com.shipment.erp.service;

import com.shipment.erp.model.Company;
import java.util.List;
import java.util.Optional;

/**
 * Service للشركات
 */
public interface CompanyService extends BaseService<Company> {

    /**
     * البحث عن شركة بالاسم
     */
    Optional<Company> findByName(String name);

    /**
     * البحث عن شركة بالاسم الإنجليزي
     */
    Optional<Company> findByNameEn(String nameEn);

    /**
     * البحث عن شركة بالرقم الضريبي
     */
    Optional<Company> findByTaxNumber(String taxNumber);

    /**
     * البحث عن شركة بالسجل التجاري
     */
    Optional<Company> findByCommercialRegister(String commercialRegister);

    /**
     * البحث عن الشركات بالمدينة
     */
    List<Company> findByCity(String city);

    /**
     * البحث عن الشركات بالبلد
     */
    List<Company> findByCountry(String country);

    /**
     * البحث عن الشركات بالاسم (جزئي)
     */
    List<Company> findByNameContaining(String name);

    /**
     * البحث عن الشركات النشطة
     */
    List<Company> findActiveCompanies();

    /**
     * البحث عن الشركات غير النشطة
     */
    List<Company> findInactiveCompanies();

    /**
     * التحقق من وجود شركة بالاسم
     */
    boolean existsByName(String name);

    /**
     * التحقق من وجود شركة بالرقم الضريبي
     */
    boolean existsByTaxNumber(String taxNumber);

    /**
     * التحقق من وجود شركة بالسجل التجاري
     */
    boolean existsByCommercialRegister(String commercialRegister);

    /**
     * الحصول على عدد الشركات النشطة
     */
    long countActiveCompanies();

    /**
     * الحصول على عدد الشركات غير النشطة
     */
    long countInactiveCompanies();

    /**
     * البحث المتقدم في الشركات
     */
    List<Company> advancedSearch(String name, String city, String country, Boolean isActive);

    /**
     * الحصول على الشركات مع الفروع
     */
    List<Company> findCompaniesWithBranches();

    /**
     * الحصول على الشركات بدون فروع
     */
    List<Company> findCompaniesWithoutBranches();

    /**
     * إنشاء شركة جديدة مع فرع رئيسي
     */
    Company createCompanyWithMainBranch(Company company, String branchCode, String branchName);

    /**
     * تحديث شعار الشركة
     */
    void updateLogo(Long companyId, byte[] logo);

    /**
     * حذف شعار الشركة
     */
    void deleteLogo(Long companyId);

    /**
     * التحقق من إمكانية حذف الشركة
     */
    boolean canDeleteCompany(Long companyId);

    /**
     * نسخ بيانات الشركة
     */
    Company duplicateCompany(Long companyId, String newName);

    /**
     * تصدير بيانات الشركات
     */
    byte[] exportCompanies(String format, List<Long> companyIds);

    /**
     * استيراد بيانات الشركات
     */
    void importCompanies(byte[] data, String format, boolean updateExisting);

    /**
     * الحصول على إحصائيات الشركات
     */
    CompanyStatistics getCompanyStatistics();

    /**
     * كلاس إحصائيات الشركات
     */
    class CompanyStatistics {
        private long totalCompanies;
        private long activeCompanies;
        private long inactiveCompanies;
        private long companiesWithBranches;
        private long companiesWithoutBranches;

        // Constructors, getters and setters
        public CompanyStatistics() {}

        public CompanyStatistics(long totalCompanies, long activeCompanies, long inactiveCompanies,
                               long companiesWithBranches, long companiesWithoutBranches) {
            this.totalCompanies = totalCompanies;
            this.activeCompanies = activeCompanies;
            this.inactiveCompanies = inactiveCompanies;
            this.companiesWithBranches = companiesWithBranches;
            this.companiesWithoutBranches = companiesWithoutBranches;
        }

        public long getTotalCompanies() { return totalCompanies; }
        public void setTotalCompanies(long totalCompanies) { this.totalCompanies = totalCompanies; }

        public long getActiveCompanies() { return activeCompanies; }
        public void setActiveCompanies(long activeCompanies) { this.activeCompanies = activeCompanies; }

        public long getInactiveCompanies() { return inactiveCompanies; }
        public void setInactiveCompanies(long inactiveCompanies) { this.inactiveCompanies = inactiveCompanies; }

        public long getCompaniesWithBranches() { return companiesWithBranches; }
        public void setCompaniesWithBranches(long companiesWithBranches) { this.companiesWithBranches = companiesWithBranches; }

        public long getCompaniesWithoutBranches() { return companiesWithoutBranches; }
        public void setCompaniesWithoutBranches(long companiesWithoutBranches) { this.companiesWithoutBranches = companiesWithoutBranches; }
    }
}
