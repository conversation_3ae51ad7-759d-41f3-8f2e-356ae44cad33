package com.shipment.erp.controller;

import com.shipment.erp.ShipERPApplication;
import com.shipment.erp.util.DateUtil;
import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.util.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.time.LocalDateTime;
import java.util.ResourceBundle;

/**
 * Controller للواجهة الرئيسية
 */
public class MainController implements Initializable {

    private static final Logger logger = LoggerFactory.getLogger(MainController.class);

    @FXML private TreeView<String> navigationTree;
    @FXML private TabPane contentTabPane;
    @FXML private Label statusLabel;
    @FXML private Label userLabel;
    @FXML private Label dateTimeLabel;

    private ResourceBundle resources;
    private Timeline clockTimeline;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        this.resources = resources;
        
        try {
            setupUI();
            setupNavigationTree();
            startClock();
            
            logger.info("تم تهيئة الواجهة الرئيسية بنجاح");
            
        } catch (Exception e) {
            logger.error("خطأ في تهيئة الواجهة الرئيسية", e);
            showError("خطأ في تهيئة الواجهة: " + e.getMessage());
        }
    }

    /**
     * إعداد واجهة المستخدم
     */
    private void setupUI() {
        // عرض معلومات المستخدم الحالي
        LoginController.SessionManager sessionManager = LoginController.SessionManager.getInstance();
        if (sessionManager.isLoggedIn()) {
            userLabel.setText(getMessage("user.welcome", "مرحباً") + ": " + 
                            sessionManager.getCurrentUser().getFullName());
        }
        
        // تعيين النص الافتراضي لشريط الحالة
        statusLabel.setText(getMessage("status.ready", "جاهز"));
    }

    /**
     * إعداد القائمة الشجرية للتنقل
     */
    private void setupNavigationTree() {
        // معالج النقر على عناصر القائمة
        navigationTree.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2) {
                TreeItem<String> selectedItem = navigationTree.getSelectionModel().getSelectedItem();
                if (selectedItem != null && selectedItem.isLeaf()) {
                    handleNavigationSelection(selectedItem.getValue());
                }
            }
        });
        
        // توسيع العقد الرئيسية
        expandMainNodes();
    }

    /**
     * توسيع العقد الرئيسية في القائمة الشجرية
     */
    private void expandMainNodes() {
        TreeItem<String> root = navigationTree.getRoot();
        if (root != null) {
            root.setExpanded(true);
            for (TreeItem<String> child : root.getChildren()) {
                child.setExpanded(true);
            }
        }
    }

    /**
     * معالجة اختيار عنصر من القائمة الشجرية
     */
    private void handleNavigationSelection(String itemValue) {
        logger.debug("تم اختيار عنصر القائمة: {}", itemValue);
        
        try {
            // فتح النافذة المناسبة حسب الاختيار
            if (itemValue.equals(getMessage("settings.general", "المتغيرات العامة"))) {
                openGeneralSettings();
            } else if (itemValue.equals(getMessage("settings.fiscal.year", "السنة المالية"))) {
                openFiscalYearSettings();
            } else if (itemValue.equals(getMessage("settings.currencies", "العملات"))) {
                openCurrencySettings();
            } else if (itemValue.equals(getMessage("settings.company", "بيانات الشركة"))) {
                openCompanySettings();
            } else if (itemValue.equals(getMessage("settings.users", "المستخدمين"))) {
                openUserManagement();
            } else if (itemValue.equals(getMessage("settings.permissions", "الصلاحيات"))) {
                openPermissionSettings();
            } else {
                // عرض رسالة "قيد التطوير" للعناصر الأخرى
                showUnderDevelopmentMessage(itemValue);
            }
            
        } catch (Exception e) {
            logger.error("خطأ في فتح النافذة: " + itemValue, e);
            showError("خطأ في فتح النافذة: " + e.getMessage());
        }
    }

    /**
     * بدء ساعة الوقت الحقيقي
     */
    private void startClock() {
        clockTimeline = new Timeline(new KeyFrame(Duration.seconds(1), e -> updateDateTime()));
        clockTimeline.setCycleCount(Timeline.INDEFINITE);
        clockTimeline.play();
        updateDateTime(); // تحديث فوري
    }

    /**
     * تحديث التاريخ والوقت
     */
    private void updateDateTime() {
        LocalDateTime now = DateUtil.getCurrentDateTime();
        String dateTimeText = DateUtil.formatDateTimeArabic(now);
        dateTimeLabel.setText(dateTimeText);
    }

    // معالجات القوائم
    @FXML private void handleNewFile() { showUnderDevelopmentMessage("ملف جديد"); }
    @FXML private void handleOpenFile() { showUnderDevelopmentMessage("فتح ملف"); }
    @FXML private void handleSaveFile() { showUnderDevelopmentMessage("حفظ ملف"); }
    @FXML private void handleSaveAsFile() { showUnderDevelopmentMessage("حفظ باسم"); }
    @FXML private void handlePrint() { showUnderDevelopmentMessage("طباعة"); }
    @FXML private void handleExit() { handleApplicationExit(); }

    @FXML private void handleUndo() { showUnderDevelopmentMessage("تراجع"); }
    @FXML private void handleRedo() { showUnderDevelopmentMessage("إعادة"); }
    @FXML private void handleCut() { showUnderDevelopmentMessage("قص"); }
    @FXML private void handleCopy() { showUnderDevelopmentMessage("نسخ"); }
    @FXML private void handlePaste() { showUnderDevelopmentMessage("لصق"); }

    @FXML private void handleGeneralSettings() { openGeneralSettings(); }
    @FXML private void handleFiscalYear() { openFiscalYearSettings(); }
    @FXML private void handleCurrencies() { openCurrencySettings(); }
    @FXML private void handleCompanyData() { openCompanySettings(); }
    @FXML private void handleUsers() { openUserManagement(); }
    @FXML private void handlePermissions() { openPermissionSettings(); }

    @FXML private void handleUserGuide() { showUnderDevelopmentMessage("دليل المستخدم"); }
    @FXML private void handleAbout() { showAboutDialog(); }

    @FXML private void handleRefresh() { 
        statusLabel.setText(getMessage("status.refreshing", "جاري التحديث..."));
        // تحديث البيانات
        Platform.runLater(() -> statusLabel.setText(getMessage("status.ready", "جاهز")));
    }

    // معالجات البطاقات السريعة
    @FXML private void handleQuickSettings() { openGeneralSettings(); }
    @FXML private void handleQuickUsers() { openUserManagement(); }
    @FXML private void handleQuickReports() { showUnderDevelopmentMessage("التقارير"); }

    /**
     * فتح نافذة الإعدادات العامة
     */
    private void openGeneralSettings() {
        openTabIfNotExists("settings.general", "المتغيرات العامة", "/fxml/settings/general-settings.fxml");
    }

    /**
     * فتح نافذة إعدادات السنة المالية
     */
    private void openFiscalYearSettings() {
        openTabIfNotExists("settings.fiscal.year", "السنة المالية", "/fxml/settings/fiscal-year-settings.fxml");
    }

    /**
     * فتح نافذة إعدادات العملات
     */
    private void openCurrencySettings() {
        openTabIfNotExists("settings.currencies", "العملات", "/fxml/settings/currency-settings.fxml");
    }

    /**
     * فتح نافذة بيانات الشركة
     */
    private void openCompanySettings() {
        openTabIfNotExists("settings.company", "بيانات الشركة", "/fxml/settings/company-settings.fxml");
    }

    /**
     * فتح نافذة إدارة المستخدمين
     */
    private void openUserManagement() {
        openTabIfNotExists("settings.users", "المستخدمين", "/fxml/settings/user-management.fxml");
    }

    /**
     * فتح نافذة إعدادات الصلاحيات
     */
    private void openPermissionSettings() {
        openTabIfNotExists("settings.permissions", "الصلاحيات", "/fxml/settings/permission-settings.fxml");
    }

    /**
     * فتح تبويب جديد إذا لم يكن موجوداً
     */
    private void openTabIfNotExists(String tabId, String defaultTitle, String fxmlPath) {
        // البحث عن التبويب الموجود
        for (Tab tab : contentTabPane.getTabs()) {
            if (tabId.equals(tab.getId())) {
                contentTabPane.getSelectionModel().select(tab);
                return;
            }
        }

        // إنشاء تبويب جديد
        try {
            Tab newTab = new Tab();
            newTab.setId(tabId);
            newTab.setText(getMessage(tabId, defaultTitle));
            newTab.setClosable(true);

            // تحميل المحتوى (مؤقتاً نعرض رسالة قيد التطوير)
            Label content = new Label(getMessage("under.development", "هذه الميزة قيد التطوير"));
            content.setStyle("-fx-font-size: 16px; -fx-text-alignment: center;");
            newTab.setContent(content);

            contentTabPane.getTabs().add(newTab);
            contentTabPane.getSelectionModel().select(newTab);

            logger.info("تم فتح تبويب جديد: {}", defaultTitle);

        } catch (Exception e) {
            logger.error("خطأ في فتح التبويب: " + defaultTitle, e);
            showError("خطأ في فتح النافذة: " + e.getMessage());
        }
    }

    /**
     * معالجة إغلاق التطبيق
     */
    private void handleApplicationExit() {
        Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
        confirmDialog.setTitle(getMessage("confirm.exit", "تأكيد الخروج"));
        confirmDialog.setHeaderText(null);
        confirmDialog.setContentText(getMessage("confirm.exit.message", "هل تريد الخروج من البرنامج؟"));

        confirmDialog.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                logger.info("إغلاق التطبيق من الواجهة الرئيسية");

                // تنظيف الموارد
                cleanup();

                Platform.exit();
                System.exit(0);
            }
        });
    }

    /**
     * عرض نافذة حول البرنامج
     */
    private void showAboutDialog() {
        Alert aboutDialog = new Alert(Alert.AlertType.INFORMATION);
        aboutDialog.setTitle(getMessage("about.title", "حول البرنامج"));
        aboutDialog.setHeaderText(getMessage("app.title", "نظام إدارة الشحنات"));

        String aboutText = String.format(
            "%s\n\n%s: %s\n%s: Java 17 + JavaFX + Spring + Hibernate + Oracle\n\n%s",
            getMessage("about.description", "نظام إدارة شحنات متكامل وشامل ومتقدم"),
            getMessage("about.version", "الإصدار"),
            getMessage("app.version", "1.0.0"),
            getMessage("about.technology", "التقنيات المستخدمة"),
            getMessage("about.copyright", "جميع الحقوق محفوظة © 2025")
        );

        aboutDialog.setContentText(aboutText);
        aboutDialog.showAndWait();
    }

    /**
     * عرض رسالة "قيد التطوير"
     */
    private void showUnderDevelopmentMessage(String feature) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(getMessage("under.development.title", "قيد التطوير"));
        alert.setHeaderText(null);
        alert.setContentText(String.format(
            getMessage("under.development.message", "الميزة '%s' قيد التطوير وستكون متاحة في الإصدارات القادمة"),
            feature
        ));
        alert.showAndWait();
    }

    /**
     * عرض رسالة خطأ
     */
    private void showError(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(getMessage("error.title", "خطأ"));
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * الحصول على رسالة من ملف الموارد
     */
    private String getMessage(String key, String defaultValue) {
        if (resources != null && resources.containsKey(key)) {
            return resources.getString(key);
        }
        return defaultValue;
    }

    /**
     * تنظيف الموارد عند إغلاق الواجهة
     */
    public void cleanup() {
        if (clockTimeline != null) {
            clockTimeline.stop();
        }
    }
}
