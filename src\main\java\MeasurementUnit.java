import java.util.Date;

/**
 * كلاس وحدة القياس
 * Measurement Unit Entity
 */
public class MeasurementUnit {
    
    // الحقول الأساسية
    private String measureCode;        // كود وحدة القياس (مفتاح أساسي)
    private String measureName;        // اسم وحدة القياس بالعربية
    private String measureNameEn;      // اسم وحدة القياس بالإنجليزية
    private String measureCodeGlobal; // الكود العالمي
    private Integer measureType;       // نوع وحدة القياس
    private Integer measureWeightType; // نوع الوزن
    private Integer measureWeightConn; // ربط الوزن
    private Double defaultSize;        // الحجم الافتراضي
    private Integer allowUpdate;       // السماح بالتحديث
    private Integer unitSaleType;      // نوع وحدة البيع
    
    // حقول التدقيق
    private Integer addUserId;         // معرف المستخدم المضيف
    private Date addDate;              // تاريخ الإضافة
    private Integer updateUserId;      // معرف المستخدم المحدث
    private Date updateDate;           // تاريخ التحديث
    private Integer updateCount;       // عدد مرات التحديث
    private String addTerminalName;    // اسم الجهاز المضيف
    private String updateTerminalName; // اسم الجهاز المحدث
    
    // حقول إضافية للنظام الجديد
    private boolean isActive;          // حالة النشاط
    private String description;        // وصف وحدة القياس
    private String symbol;             // رمز وحدة القياس
    private Double conversionFactor;   // معامل التحويل للوحدة الأساسية
    
    // Constructors
    public MeasurementUnit() {
        this.isActive = true;
        this.allowUpdate = 1;
        this.measureType = 1;
        this.measureWeightConn = 0;
        this.unitSaleType = 3;
        this.addDate = new Date();
    }
    
    public MeasurementUnit(String measureCode, String measureName) {
        this();
        this.measureCode = measureCode;
        this.measureName = measureName;
    }
    
    // Getters and Setters
    public String getMeasureCode() {
        return measureCode;
    }
    
    public void setMeasureCode(String measureCode) {
        this.measureCode = measureCode;
    }
    
    public String getMeasureName() {
        return measureName;
    }
    
    public void setMeasureName(String measureName) {
        this.measureName = measureName;
    }
    
    public String getMeasureNameEn() {
        return measureNameEn;
    }
    
    public void setMeasureNameEn(String measureNameEn) {
        this.measureNameEn = measureNameEn;
    }
    
    public String getMeasureCodeGlobal() {
        return measureCodeGlobal;
    }
    
    public void setMeasureCodeGlobal(String measureCodeGlobal) {
        this.measureCodeGlobal = measureCodeGlobal;
    }
    
    public Integer getMeasureType() {
        return measureType;
    }
    
    public void setMeasureType(Integer measureType) {
        this.measureType = measureType;
    }
    
    public Integer getMeasureWeightType() {
        return measureWeightType;
    }
    
    public void setMeasureWeightType(Integer measureWeightType) {
        this.measureWeightType = measureWeightType;
    }
    
    public Integer getMeasureWeightConn() {
        return measureWeightConn;
    }
    
    public void setMeasureWeightConn(Integer measureWeightConn) {
        this.measureWeightConn = measureWeightConn;
    }
    
    public Double getDefaultSize() {
        return defaultSize;
    }
    
    public void setDefaultSize(Double defaultSize) {
        this.defaultSize = defaultSize;
    }
    
    public Integer getAllowUpdate() {
        return allowUpdate;
    }
    
    public void setAllowUpdate(Integer allowUpdate) {
        this.allowUpdate = allowUpdate;
    }
    
    public Integer getUnitSaleType() {
        return unitSaleType;
    }
    
    public void setUnitSaleType(Integer unitSaleType) {
        this.unitSaleType = unitSaleType;
    }
    
    public Integer getAddUserId() {
        return addUserId;
    }
    
    public void setAddUserId(Integer addUserId) {
        this.addUserId = addUserId;
    }
    
    public Date getAddDate() {
        return addDate;
    }
    
    public void setAddDate(Date addDate) {
        this.addDate = addDate;
    }
    
    public Integer getUpdateUserId() {
        return updateUserId;
    }
    
    public void setUpdateUserId(Integer updateUserId) {
        this.updateUserId = updateUserId;
    }
    
    public Date getUpdateDate() {
        return updateDate;
    }
    
    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    
    public Integer getUpdateCount() {
        return updateCount;
    }
    
    public void setUpdateCount(Integer updateCount) {
        this.updateCount = updateCount;
    }
    
    public String getAddTerminalName() {
        return addTerminalName;
    }
    
    public void setAddTerminalName(String addTerminalName) {
        this.addTerminalName = addTerminalName;
    }
    
    public String getUpdateTerminalName() {
        return updateTerminalName;
    }
    
    public void setUpdateTerminalName(String updateTerminalName) {
        this.updateTerminalName = updateTerminalName;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getSymbol() {
        return symbol;
    }
    
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
    
    public Double getConversionFactor() {
        return conversionFactor;
    }
    
    public void setConversionFactor(Double conversionFactor) {
        this.conversionFactor = conversionFactor;
    }
    
    @Override
    public String toString() {
        return measureName != null ? measureName : measureCode;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        MeasurementUnit that = (MeasurementUnit) obj;
        return measureCode != null ? measureCode.equals(that.measureCode) : that.measureCode == null;
    }
    
    @Override
    public int hashCode() {
        return measureCode != null ? measureCode.hashCode() : 0;
    }
}
