@echo off
chcp 65001 > nul
echo ========================================
echo    إعداد قاعدة بيانات نظام الشحنات
echo    Ship ERP Database Setup
echo ========================================
echo.

REM التحقق من وجود Oracle SQL*Plus
echo [INFO] التحقق من تثبيت Oracle...
sqlplus -v > nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Oracle SQL*Plus غير مثبت أو غير موجود في PATH
    echo [INFO] يرجى تثبيت Oracle Database أو Oracle Client
    echo [INFO] وإضافة مجلد bin إلى متغير PATH
    pause
    exit /b 1
)

echo [INFO] تم العثور على Oracle SQL*Plus
echo.

REM طلب معلومات الاتصال
echo [INFO] يرجى إدخال معلومات الاتصال بقاعدة البيانات:
echo.

set /p DB_HOST="عنوان الخادم (افتراضي: localhost): "
if "%DB_HOST%"=="" set DB_HOST=localhost

set /p DB_PORT="رقم المنفذ (افتراضي: 1521): "
if "%DB_PORT%"=="" set DB_PORT=1521

set /p DB_SID="اسم قاعدة البيانات (افتراضي: orcl): "
if "%DB_SID%"=="" set DB_SID=orcl

set /p DBA_USER="مستخدم DBA (افتراضي: system): "
if "%DBA_USER%"=="" set DBA_USER=system

echo.
echo [INFO] سيتم الاتصال بـ: %DBA_USER%@%DB_HOST%:%DB_PORT%/%DB_SID%
echo.

REM التحقق من وجود ملف السكريبت
if not exist "scripts\setup-database.sql" (
    echo [ERROR] ملف السكريبت غير موجود: scripts\setup-database.sql
    echo [INFO] تأكد من وجود الملف في المجلد الصحيح
    pause
    exit /b 1
)

echo [INFO] بدء تشغيل سكريبت إعداد قاعدة البيانات...
echo [WARN] سيتم إنشاء مستخدم جديد: ship_erp
echo [WARN] كلمة المرور: ship_erp_password
echo.

set /p CONFIRM="هل تريد المتابعة؟ (y/n): "
if /i not "%CONFIRM%"=="y" (
    echo [INFO] تم إلغاء العملية
    pause
    exit /b 0
)

echo.
echo [INFO] تشغيل السكريبت...
echo [INFO] قد تستغرق هذه العملية عدة دقائق...
echo.

REM تشغيل السكريبت
sqlplus %DBA_USER%@%DB_HOST%:%DB_PORT%/%DB_SID% @scripts\setup-database.sql

if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] تم إعداد قاعدة البيانات بنجاح!
    echo.
    echo [INFO] معلومات الاتصال الجديدة:
    echo   المستخدم: ship_erp
    echo   كلمة المرور: ship_erp_password
    echo   قاعدة البيانات: %DB_HOST%:%DB_PORT%/%DB_SID%
    echo.
    echo [INFO] يمكنك الآن تشغيل التطبيق باستخدام run-ship-erp.bat
) else (
    echo.
    echo [ERROR] فشل في إعداد قاعدة البيانات
    echo [INFO] تحقق من رسائل الخطأ أعلاه
    echo [INFO] تأكد من:
    echo   - صحة معلومات الاتصال
    echo   - وجود صلاحيات DBA للمستخدم
    echo   - تشغيل خدمة Oracle Database
)

echo.
pause
