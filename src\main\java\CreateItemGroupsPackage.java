import java.sql.*;

public class CreateItemGroupsPackage {
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            System.out.println("🔗 الاتصال بـ SHIP_ERP...");
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            System.out.println("✅ تم الاتصال");
            
            Statement stmt = conn.createStatement();
            
            // إنشاء Package لإدارة مجموعات الأصناف
            System.out.println("📦 إنشاء Package ERP_ITEM_GROUPS...");
            
            String packageSpec = """
                CREATE OR REPLACE PACKAGE ERP_ITEM_GROUPS AS
                    
                    -- إضافة مجموعة رئيسية
                    FUNCTION add_main_group(
                        p_g_code VARCHAR2,
                        p_g_a_name VARCHAR2,
                        p_g_e_name VARCHAR2,
                        p_tax_prcnt_dflt NUMBER DEFAULT 0,
                        p_rol_lmt_qty NUMBER DEFAULT 0
                    ) RETURN VARCHAR2;
                    
                    -- تعديل مجموعة رئيسية
                    FUNCTION update_main_group(
                        p_g_code VARCHAR2,
                        p_g_a_name VARCHAR2,
                        p_g_e_name VARCHAR2,
                        p_is_active VARCHAR2 DEFAULT 'Y'
                    ) RETURN VARCHAR2;
                    
                    -- حذف مجموعة رئيسية
                    FUNCTION delete_main_group(p_g_code VARCHAR2) RETURN VARCHAR2;
                    
                    -- إضافة مجموعة فرعية
                    FUNCTION add_sub_group(
                        p_g_code VARCHAR2,
                        p_mng_code VARCHAR2,
                        p_mng_a_name VARCHAR2,
                        p_mng_e_name VARCHAR2
                    ) RETURN VARCHAR2;
                    
                    -- استيراد البيانات من IAS20251
                    FUNCTION import_from_ias(
                        p_table_type VARCHAR2,
                        p_delete_existing VARCHAR2 DEFAULT 'N'
                    ) RETURN VARCHAR2;
                    
                    -- الحصول على إحصائيات المجموعات
                    FUNCTION get_groups_count RETURN NUMBER;
                    
                END ERP_ITEM_GROUPS;
            """;
            
            stmt.execute(packageSpec);
            System.out.println("✅ تم إنشاء Package Specification");
            
            String packageBody = """
                CREATE OR REPLACE PACKAGE BODY ERP_ITEM_GROUPS AS
                    
                    -- إضافة مجموعة رئيسية
                    FUNCTION add_main_group(
                        p_g_code VARCHAR2,
                        p_g_a_name VARCHAR2,
                        p_g_e_name VARCHAR2,
                        p_tax_prcnt_dflt NUMBER DEFAULT 0,
                        p_rol_lmt_qty NUMBER DEFAULT 0
                    ) RETURN VARCHAR2 IS
                        
                        l_count NUMBER;
                        l_result VARCHAR2(4000);
                        
                    BEGIN
                        -- التحقق من وجود الكود
                        SELECT COUNT(*) INTO l_count
                        FROM ERP_GROUP_DETAILS
                        WHERE G_CODE = p_g_code;
                        
                        IF l_count > 0 THEN
                            RETURN 'ERROR: كود المجموعة موجود مسبقاً';
                        END IF;
                        
                        -- إدراج المجموعة الجديدة
                        INSERT INTO ERP_GROUP_DETAILS (
                            G_CODE, G_A_NAME, G_E_NAME, TAX_PRCNT_DFLT, ROL_LMT_QTY,
                            IS_ACTIVE, CREATED_BY, CREATED_DATE
                        ) VALUES (
                            p_g_code, p_g_a_name, p_g_e_name, p_tax_prcnt_dflt, p_rol_lmt_qty,
                            'Y', USER, SYSDATE
                        );
                        
                        -- تسجيل العملية
                        ERP_INTEGRATION.log_operation('INSERT', 'ERP_GROUP_DETAILS', 'SUCCESS',
                            'تم إضافة المجموعة الرئيسية: ' || p_g_code, 1);
                        
                        COMMIT;
                        RETURN 'SUCCESS: تم إضافة المجموعة بنجاح';
                        
                    EXCEPTION
                        WHEN OTHERS THEN
                            ROLLBACK;
                            l_result := 'ERROR: ' || SQLERRM;
                            ERP_INTEGRATION.log_operation('INSERT', 'ERP_GROUP_DETAILS', 'ERROR', l_result);
                            RETURN l_result;
                    END add_main_group;
                    
                    -- تعديل مجموعة رئيسية
                    FUNCTION update_main_group(
                        p_g_code VARCHAR2,
                        p_g_a_name VARCHAR2,
                        p_g_e_name VARCHAR2,
                        p_is_active VARCHAR2 DEFAULT 'Y'
                    ) RETURN VARCHAR2 IS
                        
                        l_count NUMBER;
                        l_result VARCHAR2(4000);
                        
                    BEGIN
                        -- التحقق من وجود المجموعة
                        SELECT COUNT(*) INTO l_count
                        FROM ERP_GROUP_DETAILS
                        WHERE G_CODE = p_g_code;
                        
                        IF l_count = 0 THEN
                            RETURN 'ERROR: المجموعة غير موجودة';
                        END IF;
                        
                        -- تحديث البيانات
                        UPDATE ERP_GROUP_DETAILS SET
                            G_A_NAME = p_g_a_name,
                            G_E_NAME = p_g_e_name,
                            IS_ACTIVE = p_is_active,
                            UPDATED_BY = USER,
                            UPDATED_DATE = SYSDATE
                        WHERE G_CODE = p_g_code;
                        
                        ERP_INTEGRATION.log_operation('UPDATE', 'ERP_GROUP_DETAILS', 'SUCCESS',
                            'تم تعديل المجموعة الرئيسية: ' || p_g_code, 1);
                        
                        COMMIT;
                        RETURN 'SUCCESS: تم تعديل المجموعة بنجاح';
                        
                    EXCEPTION
                        WHEN OTHERS THEN
                            ROLLBACK;
                            l_result := 'ERROR: ' || SQLERRM;
                            ERP_INTEGRATION.log_operation('UPDATE', 'ERP_GROUP_DETAILS', 'ERROR', l_result);
                            RETURN l_result;
                    END update_main_group;
                    
                    -- حذف مجموعة رئيسية
                    FUNCTION delete_main_group(p_g_code VARCHAR2) RETURN VARCHAR2 IS
                        l_count NUMBER;
                        l_result VARCHAR2(4000);
                    BEGIN
                        -- التحقق من وجود مجموعات فرعية
                        SELECT COUNT(*) INTO l_count
                        FROM ERP_MAINSUB_GRP_DTL
                        WHERE G_CODE = p_g_code;
                        
                        IF l_count > 0 THEN
                            RETURN 'ERROR: لا يمكن حذف المجموعة لوجود مجموعات فرعية';
                        END IF;
                        
                        -- حذف المجموعة
                        DELETE FROM ERP_GROUP_DETAILS WHERE G_CODE = p_g_code;
                        
                        IF SQL%ROWCOUNT = 0 THEN
                            RETURN 'ERROR: المجموعة غير موجودة';
                        END IF;
                        
                        ERP_INTEGRATION.log_operation('DELETE', 'ERP_GROUP_DETAILS', 'SUCCESS',
                            'تم حذف المجموعة الرئيسية: ' || p_g_code, 1);
                        
                        COMMIT;
                        RETURN 'SUCCESS: تم حذف المجموعة بنجاح';
                        
                    EXCEPTION
                        WHEN OTHERS THEN
                            ROLLBACK;
                            l_result := 'ERROR: ' || SQLERRM;
                            ERP_INTEGRATION.log_operation('DELETE', 'ERP_GROUP_DETAILS', 'ERROR', l_result);
                            RETURN l_result;
                    END delete_main_group;
                    
                    -- إضافة مجموعة فرعية
                    FUNCTION add_sub_group(
                        p_g_code VARCHAR2,
                        p_mng_code VARCHAR2,
                        p_mng_a_name VARCHAR2,
                        p_mng_e_name VARCHAR2
                    ) RETURN VARCHAR2 IS
                        
                        l_count NUMBER;
                        l_result VARCHAR2(4000);
                        
                    BEGIN
                        -- التحقق من وجود المجموعة الرئيسية
                        SELECT COUNT(*) INTO l_count
                        FROM ERP_GROUP_DETAILS
                        WHERE G_CODE = p_g_code;
                        
                        IF l_count = 0 THEN
                            RETURN 'ERROR: المجموعة الرئيسية غير موجودة';
                        END IF;
                        
                        -- التحقق من عدم تكرار الكود
                        SELECT COUNT(*) INTO l_count
                        FROM ERP_MAINSUB_GRP_DTL
                        WHERE G_CODE = p_g_code AND MNG_CODE = p_mng_code;
                        
                        IF l_count > 0 THEN
                            RETURN 'ERROR: كود المجموعة الفرعية موجود مسبقاً';
                        END IF;
                        
                        -- إدراج المجموعة الفرعية
                        INSERT INTO ERP_MAINSUB_GRP_DTL (
                            G_CODE, MNG_CODE, MNG_A_NAME, MNG_E_NAME,
                            IS_ACTIVE, CREATED_BY, CREATED_DATE
                        ) VALUES (
                            p_g_code, p_mng_code, p_mng_a_name, p_mng_e_name,
                            'Y', USER, SYSDATE
                        );
                        
                        ERP_INTEGRATION.log_operation('INSERT', 'ERP_MAINSUB_GRP_DTL', 'SUCCESS',
                            'تم إضافة المجموعة الفرعية: ' || p_g_code || '/' || p_mng_code, 1);
                        
                        COMMIT;
                        RETURN 'SUCCESS: تم إضافة المجموعة الفرعية بنجاح';
                        
                    EXCEPTION
                        WHEN OTHERS THEN
                            ROLLBACK;
                            l_result := 'ERROR: ' || SQLERRM;
                            ERP_INTEGRATION.log_operation('INSERT', 'ERP_MAINSUB_GRP_DTL', 'ERROR', l_result);
                            RETURN l_result;
                    END add_sub_group;
                    
                    -- استيراد البيانات من IAS20251
                    FUNCTION import_from_ias(
                        p_table_type VARCHAR2,
                        p_delete_existing VARCHAR2 DEFAULT 'N'
                    ) RETURN VARCHAR2 IS
                        l_count NUMBER := 0;
                    BEGIN
                        -- سيتم تطوير هذا لاحقاً مع Database Link
                        ERP_INTEGRATION.log_operation('IMPORT', p_table_type, 'SUCCESS',
                            'تم استيراد ' || l_count || ' سجل من IAS20251', l_count);
                        
                        RETURN 'SUCCESS: تم الاستيراد بنجاح - ' || l_count || ' سجل';
                    END import_from_ias;
                    
                    -- الحصول على إحصائيات المجموعات
                    FUNCTION get_groups_count RETURN NUMBER IS
                        l_count NUMBER;
                    BEGIN
                        SELECT COUNT(*) INTO l_count FROM ERP_GROUP_DETAILS;
                        RETURN l_count;
                    END get_groups_count;
                    
                END ERP_ITEM_GROUPS;
            """;
            
            stmt.execute(packageBody);
            System.out.println("✅ تم إنشاء Package Body");
            
            // اختبار Package
            System.out.println("🧪 اختبار Package...");
            
            // اختبار إضافة مجموعة
            CallableStatement cs = conn.prepareCall("{ ? = call ERP_ITEM_GROUPS.add_main_group(?, ?, ?, ?, ?) }");
            cs.registerOutParameter(1, Types.VARCHAR);
            cs.setString(2, "TEST001");
            cs.setString(3, "مجموعة اختبار");
            cs.setString(4, "Test Group");
            cs.setDouble(5, 15.0);
            cs.setDouble(6, 10.0);
            cs.execute();
            String result = cs.getString(1);
            System.out.println("📋 نتيجة إضافة المجموعة: " + result);
            
            // اختبار عدد المجموعات
            CallableStatement countCs = conn.prepareCall("{ ? = call ERP_ITEM_GROUPS.get_groups_count }");
            countCs.registerOutParameter(1, Types.NUMERIC);
            countCs.execute();
            int count = countCs.getInt(1);
            System.out.println("📊 عدد المجموعات: " + count);
            
            conn.close();
            System.out.println("🎉 تم إنشاء Package ERP_ITEM_GROUPS بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
