package com.shipment.erp.service;

import com.shipment.erp.model.UnitOfMeasure;
import com.shipment.erp.repository.UnitOfMeasureRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * تنفيذ خدمة وحدات القياس
 * Unit of Measure Service Implementation
 */
@Service
@Transactional
public class UnitOfMeasureServiceImpl extends BaseServiceImpl<UnitOfMeasure, Long> 
        implements UnitOfMeasureService {
    
    @Autowired
    private UnitOfMeasureRepository unitRepository;
    
    public UnitOfMeasureServiceImpl() {
        super();
    }
    
    @Override
    public Optional<UnitOfMeasure> findByCode(String code) {
        return unitRepository.findByCode(code);
    }
    
    @Override
    public Optional<UnitOfMeasure> findByNameAr(String nameAr) {
        return unitRepository.findByNameAr(nameAr);
    }
    
    @Override
    public List<UnitOfMeasure> findActiveUnits() {
        return unitRepository.findByIsActiveTrue();
    }
    
    @Override
    public List<UnitOfMeasure> findBaseUnits() {
        return unitRepository.findByIsBaseUnitTrue();
    }
    
    @Override
    public List<UnitOfMeasure> findRelatedUnits(UnitOfMeasure baseUnit) {
        return unitRepository.findByBaseUnit(baseUnit);
    }
    
    @Override
    public List<UnitOfMeasure> searchUnits(String searchText) {
        return unitRepository.searchByText(searchText);
    }
    
    @Override
    public UnitOfMeasure createUnit(UnitOfMeasure unit) throws Exception {
        validateUnit(unit);
        
        // التحقق من عدم تكرار الكود
        if (unitRepository.existsByCode(unit.getCode())) {
            throw new Exception("كود وحدة القياس موجود مسبقاً: " + unit.getCode());
        }
        
        // التحقق من عدم تكرار الاسم العربي
        if (unitRepository.existsByNameAr(unit.getNameAr())) {
            throw new Exception("اسم وحدة القياس موجود مسبقاً: " + unit.getNameAr());
        }
        
        // تعيين القيم الافتراضية
        if (unit.getIsActive() == null) {
            unit.setIsActive(true);
        }
        
        if (unit.getIsBaseUnit() == null) {
            unit.setIsBaseUnit(false);
        }
        
        if (unit.getConversionFactor() == null) {
            unit.setConversionFactor(1.0);
        }
        
        if (unit.getSortOrder() == null) {
            unit.setSortOrder(0);
        }
        
        return unitRepository.save(unit);
    }
    
    @Override
    public UnitOfMeasure updateUnit(UnitOfMeasure unit) throws Exception {
        validateUnit(unit);
        
        // التحقق من وجود الوحدة
        UnitOfMeasure existingUnit = unitRepository.findById(unit.getId())
                .orElseThrow(() -> new Exception("وحدة القياس غير موجودة"));
        
        // التحقق من عدم تكرار الكود (إذا تم تغييره)
        if (!existingUnit.getCode().equals(unit.getCode()) && 
            unitRepository.existsByCode(unit.getCode())) {
            throw new Exception("كود وحدة القياس موجود مسبقاً: " + unit.getCode());
        }
        
        // التحقق من عدم تكرار الاسم العربي (إذا تم تغييره)
        if (!existingUnit.getNameAr().equals(unit.getNameAr()) && 
            unitRepository.existsByNameAr(unit.getNameAr())) {
            throw new Exception("اسم وحدة القياس موجود مسبقاً: " + unit.getNameAr());
        }
        
        return unitRepository.save(unit);
    }
    
    @Override
    public void deleteUnit(Long unitId) throws Exception {
        if (!canDeleteUnit(unitId)) {
            throw new Exception("لا يمكن حذف وحدة القياس لأنها مستخدمة في أصناف أخرى");
        }
        
        unitRepository.deleteById(unitId);
    }
    
    @Override
    public void toggleUnitStatus(Long unitId) throws Exception {
        UnitOfMeasure unit = unitRepository.findById(unitId)
                .orElseThrow(() -> new Exception("وحدة القياس غير موجودة"));
        
        unit.setIsActive(!unit.getIsActive());
        unitRepository.save(unit);
    }
    
    @Override
    public void validateUnit(UnitOfMeasure unit) throws Exception {
        if (unit == null) {
            throw new Exception("بيانات وحدة القياس مطلوبة");
        }
        
        if (unit.getCode() == null || unit.getCode().trim().isEmpty()) {
            throw new Exception("كود وحدة القياس مطلوب");
        }
        
        if (unit.getNameAr() == null || unit.getNameAr().trim().isEmpty()) {
            throw new Exception("اسم وحدة القياس باللغة العربية مطلوب");
        }
        
        if (unit.getCode().length() > 20) {
            throw new Exception("كود وحدة القياس يجب أن يكون أقل من 20 حرف");
        }
        
        if (unit.getNameAr().length() > 100) {
            throw new Exception("اسم وحدة القياس يجب أن يكون أقل من 100 حرف");
        }
        
        // التحقق من معامل التحويل
        if (unit.getConversionFactor() != null && unit.getConversionFactor() <= 0) {
            throw new Exception("معامل التحويل يجب أن يكون أكبر من الصفر");
        }
        
        // التحقق من الوحدة الأساسية
        if (!unit.getIsBaseUnit() && unit.getBaseUnit() == null) {
            throw new Exception("يجب تحديد الوحدة الأساسية للوحدات المشتقة");
        }
        
        if (unit.getIsBaseUnit() && unit.getBaseUnit() != null) {
            throw new Exception("الوحدة الأساسية لا يمكن أن تكون مرتبطة بوحدة أساسية أخرى");
        }
    }
    
    @Override
    public boolean canDeleteUnit(Long unitId) {
        // التحقق من عدم استخدام الوحدة في أصناف
        // يمكن إضافة المزيد من التحققات حسب الحاجة
        return true; // مؤقتاً
    }
    
    @Override
    public Double convertQuantity(Double quantity, UnitOfMeasure fromUnit, UnitOfMeasure toUnit) throws Exception {
        if (quantity == null || fromUnit == null || toUnit == null) {
            throw new Exception("جميع المعاملات مطلوبة للتحويل");
        }
        
        if (fromUnit.equals(toUnit)) {
            return quantity;
        }
        
        // التحقق من أن الوحدتين من نفس النوع الأساسي
        UnitOfMeasure fromBase = fromUnit.getIsBaseUnit() ? fromUnit : fromUnit.getBaseUnit();
        UnitOfMeasure toBase = toUnit.getIsBaseUnit() ? toUnit : toUnit.getBaseUnit();
        
        if (!fromBase.equals(toBase)) {
            throw new Exception("لا يمكن التحويل بين وحدات من أنواع مختلفة");
        }
        
        // تحويل إلى الوحدة الأساسية أولاً
        Double baseQuantity = quantity * fromUnit.getConversionFactor();
        
        // ثم تحويل من الوحدة الأساسية إلى الوحدة المطلوبة
        return baseQuantity / toUnit.getConversionFactor();
    }
    
    @Override
    public UnitStatistics getUnitStatistics() {
        long totalUnits = unitRepository.count();
        long activeUnits = unitRepository.countByIsActiveTrue();
        long baseUnits = unitRepository.findByIsBaseUnitTrue().size();
        long derivedUnits = totalUnits - baseUnits;
        
        return new UnitStatistics(totalUnits, activeUnits, baseUnits, derivedUnits);
    }
}
