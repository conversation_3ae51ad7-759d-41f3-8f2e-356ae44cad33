import com.formdev.flatlaf.FlatDarkLaf;
import com.formdev.flatlaf.FlatLightLaf;
import com.formdev.flatlaf.extras.FlatAnimatedLafChange;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartPanel;
import org.jfree.chart.JFreeChart;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

/**
 * نظام إدارة الشحنات المحسن مع مكتبات متقدمة
 * Enhanced Ship ERP System with Advanced Libraries
 */
public class EnhancedShipERP {

    private JFrame loginFrame;
    private JFrame mainFrame;
    private JTextField usernameField;
    private JPasswordField passwordField;
    private JLabel statusLabel;
    private Timer clockTimer;
    private boolean isDarkMode = false;

    // النصوص العربية
    private static final String APP_TITLE = "\u0646\u0638\u0627\u0645 \u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0634\u062D\u0646\u0627\u062A \u0627\u0644\u0645\u062D\u0633\u0646"; // نظام إدارة الشحنات المحسن
    private static final String LOGIN_TITLE = "\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644"; // تسجيل الدخول
    private static final String USERNAME = "\u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645:"; // اسم المستخدم:
    private static final String PASSWORD = "\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631:"; // كلمة المرور:
    private static final String LOGIN_BTN = "\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644"; // تسجيل الدخول
    private static final String CANCEL_BTN = "\u0625\u0644\u063A\u0627\u0621"; // إلغاء
    private static final String READY = "\u062C\u0627\u0647\u0632"; // جاهز
    private static final String WELCOME = "\u0645\u0631\u062D\u0628\u0627\u064B \u0628\u0643 \u0641\u064A " + APP_TITLE; // مرحباً بك في نظام إدارة الشحنات المحسن

    public static void main(String[] args) {
        // تعيين الترميز والمحلية العربية
        System.setProperty("file.encoding", "UTF-8");
        Locale.setDefault(new Locale("ar", "SA"));

        SwingUtilities.invokeLater(() -> {
            new EnhancedShipERP().initializeApplication();
        });
    }

    /**
     * تهيئة التطبيق مع المكتبات المحسنة
     */
    private void initializeApplication() {
        try {
            // تطبيق FlatLaf Look and Feel
            FlatLightLaf.setup();

            // تفعيل الرسوم المتحركة
            FlatAnimatedLafChange.showSnapshot();

            // إعدادات إضافية للـ Look and Feel
            UIManager.put("Button.arc", 8);
            UIManager.put("Component.arc", 8);
            UIManager.put("ProgressBar.arc", 8);
            UIManager.put("TextComponent.arc", 8);

            // تحسين الخطوط
            UIManager.put("defaultFont", new Font("Segoe UI", Font.PLAIN, 13));

            showLoginScreen();

        } catch (Exception e) {
            e.printStackTrace();
            // العودة للـ Look and Feel الافتراضي في حالة الخطأ
            try {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
                showLoginScreen();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    /**
     * عرض شاشة تسجيل الدخول المحسنة
     */
    private void showLoginScreen() {
        loginFrame = new JFrame(APP_TITLE + " - " + LOGIN_TITLE);
        loginFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        loginFrame.setSize(500, 400);
        loginFrame.setLocationRelativeTo(null);
        loginFrame.setResizable(false);

        // تعيين الخط العربي
        Font arabicFont = new Font("Segoe UI", Font.PLAIN, 14);

        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // العنوان مع أيقونة
        JPanel titlePanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        titlePanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel titleLabel = new JLabel(APP_TITLE);
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 24));
        titleLabel.setForeground(new Color(0, 123, 255));
        titlePanel.add(titleLabel);

        mainPanel.add(titlePanel, BorderLayout.NORTH);

        // نموذج تسجيل الدخول المحسن
        JPanel formPanel = createEnhancedLoginForm(arabicFont);
        mainPanel.add(formPanel, BorderLayout.CENTER);

        // شريط الحالة
        statusLabel = new JLabel(READY, SwingConstants.CENTER);
        statusLabel.setFont(arabicFont);
        statusLabel.setBorder(BorderFactory.createEmptyBorder(10, 0, 0, 0));
        statusLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        mainPanel.add(statusLabel, BorderLayout.SOUTH);

        loginFrame.add(mainPanel);
        loginFrame.setVisible(true);

        // تركيز على حقل اسم المستخدم
        usernameField.requestFocus();
    }

    /**
     * إنشاء نموذج تسجيل الدخول المحسن
     */
    private JPanel createEnhancedLoginForm(Font arabicFont) {
        JPanel formPanel = new JPanel();
        formPanel.setLayout(new BoxLayout(formPanel, BoxLayout.Y_AXIS));
        formPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        formPanel.setBorder(BorderFactory.createEmptyBorder(20, 40, 20, 40));

        // اسم المستخدم
        JPanel userPanel = new JPanel(new BorderLayout(10, 5));
        userPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel userLabel = new JLabel(USERNAME);
        userLabel.setFont(arabicFont);
        userLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        usernameField = new JTextField("admin");
        usernameField.setFont(arabicFont);
        usernameField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        usernameField.setPreferredSize(new Dimension(0, 35));

        userPanel.add(userLabel, BorderLayout.NORTH);
        userPanel.add(usernameField, BorderLayout.CENTER);

        // كلمة المرور
        JPanel passPanel = new JPanel(new BorderLayout(10, 5));
        passPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel passLabel = new JLabel(PASSWORD);
        passLabel.setFont(arabicFont);
        passLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        passwordField = new JPasswordField("admin123");
        passwordField.setFont(arabicFont);
        passwordField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        passwordField.setPreferredSize(new Dimension(0, 35));

        passPanel.add(passLabel, BorderLayout.NORTH);
        passPanel.add(passwordField, BorderLayout.CENTER);

        // أزرار محسنة
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 0));
        buttonPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JButton loginButton = createStyledButton(LOGIN_BTN, arabicFont, true);
        loginButton.addActionListener(new LoginActionListener());

        JButton cancelButton = createStyledButton(CANCEL_BTN, arabicFont, false);
        cancelButton.addActionListener(e -> System.exit(0));

        buttonPanel.add(loginButton);
        buttonPanel.add(cancelButton);

        // تجميع المكونات
        formPanel.add(userPanel);
        formPanel.add(Box.createVerticalStrut(15));
        formPanel.add(passPanel);
        formPanel.add(Box.createVerticalStrut(25));
        formPanel.add(buttonPanel);

        // ربط Enter بتسجيل الدخول
        passwordField.addActionListener(new LoginActionListener());

        return formPanel;
    }

    /**
     * إنشاء زر مُنسق
     */
    private JButton createStyledButton(String text, Font font, boolean isPrimary) {
        JButton button = new JButton(text);
        button.setFont(font);
        button.setPreferredSize(new Dimension(140, 40));
        button.setFocusPainted(false);

        if (isPrimary) {
            button.setBackground(new Color(0, 123, 255));
            button.setForeground(Color.WHITE);
        } else {
            button.setBackground(new Color(108, 117, 125));
            button.setForeground(Color.WHITE);
        }

        // تأثيرات التفاعل
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            Color originalColor = button.getBackground();

            public void mouseEntered(java.awt.event.MouseEvent evt) {
                if (isPrimary) {
                    button.setBackground(new Color(0, 86, 179));
                } else {
                    button.setBackground(new Color(90, 98, 104));
                }
            }

            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(originalColor);
            }
        });

        return button;
    }

    /**
     * عرض الواجهة الرئيسية المحسنة
     */
    private void showEnhancedMainInterface() {
        loginFrame.dispose();

        mainFrame = new JFrame(APP_TITLE);
        mainFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        mainFrame.setExtendedState(JFrame.MAXIMIZED_BOTH);
        mainFrame.setMinimumSize(new Dimension(1200, 800));
        mainFrame.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        Font arabicFont = new Font("Segoe UI", Font.PLAIN, 12);

        // إنشاء المحتوى الرئيسي
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // شريط القوائم المحسن
        JMenuBar menuBar = createEnhancedMenuBar(arabicFont);
        mainFrame.setJMenuBar(menuBar);

        // شريط الأدوات المحسن
        JToolBar toolBar = createEnhancedToolBar(arabicFont);
        mainPanel.add(toolBar, BorderLayout.NORTH);

        // المحتوى الرئيسي مع تبويبات محسنة
        JTabbedPane tabbedPane = createEnhancedTabbedPane(arabicFont);

        // الشريط الجانبي المحسن
        JPanel sidePanel = createEnhancedSidePanel(arabicFont);

        // تجميع المحتوى
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        contentPanel.add(sidePanel, BorderLayout.EAST);
        contentPanel.add(tabbedPane, BorderLayout.CENTER);

        mainPanel.add(contentPanel, BorderLayout.CENTER);

        // شريط الحالة المحسن
        JPanel statusPanel = createEnhancedStatusBar(arabicFont);
        mainPanel.add(statusPanel, BorderLayout.SOUTH);

        mainFrame.add(mainPanel);
        mainFrame.setVisible(true);

        // بدء الساعة
        startClock();
    }

    /**
     * إنشاء شريط القوائم المحسن
     */
    private JMenuBar createEnhancedMenuBar(Font arabicFont) {
        JMenuBar menuBar = new JMenuBar();
        menuBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // قائمة الملف
        JMenu fileMenu = new JMenu("\u0645\u0644\u0641"); // ملف
        fileMenu.setFont(arabicFont);
        fileMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // قائمة العرض مع تبديل الوضع المظلم
        JMenu viewMenu = new JMenu("\u0639\u0631\u0636"); // عرض
        viewMenu.setFont(arabicFont);
        viewMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JMenuItem darkModeItem = new JMenuItem("\u0627\u0644\u0648\u0636\u0639 \u0627\u0644\u0645\u0638\u0644\u0645"); // الوضع المظلم
        darkModeItem.setFont(arabicFont);
        darkModeItem.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        darkModeItem.addActionListener(e -> toggleDarkMode());
        viewMenu.add(darkModeItem);

        // قائمة الإعدادات
        JMenu settingsMenu = new JMenu("\u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A"); // الإعدادات
        settingsMenu.setFont(arabicFont);
        settingsMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // قائمة المساعدة
        JMenu helpMenu = new JMenu("\u0645\u0633\u0627\u0639\u062F\u0629"); // مساعدة
        helpMenu.setFont(arabicFont);
        helpMenu.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        menuBar.add(fileMenu);
        menuBar.add(viewMenu);
        menuBar.add(settingsMenu);
        menuBar.add(helpMenu);

        return menuBar;
    }

    /**
     * إنشاء شريط الأدوات المحسن
     */
    private JToolBar createEnhancedToolBar(Font arabicFont) {
        JToolBar toolBar = new JToolBar();
        toolBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        toolBar.setFloatable(false);

        // أزرار شريط الأدوات
        toolBar.add(createToolBarButton("\u062C\u062F\u064A\u062F", arabicFont)); // جديد
        toolBar.add(createToolBarButton("\u062D\u0641\u0638", arabicFont)); // حفظ
        toolBar.addSeparator();
        toolBar.add(createToolBarButton("\u0637\u0628\u0627\u0639\u0629", arabicFont)); // طباعة
        toolBar.addSeparator();
        toolBar.add(createToolBarButton("\u062A\u062D\u062F\u064A\u062B", arabicFont)); // تحديث

        // مساحة مرنة
        toolBar.add(Box.createHorizontalGlue());

        // مربع بحث محسن
        JTextField searchField = new JTextField(20);
        searchField.setFont(arabicFont);
        searchField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        searchField.setToolTipText("\u0628\u062D\u062B \u0641\u064A \u0627\u0644\u0646\u0638\u0627\u0645"); // بحث في النظام
        toolBar.add(searchField);

        return toolBar;
    }

    /**
     * إنشاء زر شريط الأدوات
     */
    private JButton createToolBarButton(String text, Font font) {
        JButton button = new JButton(text);
        button.setFont(font);
        button.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        button.setFocusPainted(false);
        return button;
    }

    /**
     * إنشاء التبويبات المحسنة
     */
    private JTabbedPane createEnhancedTabbedPane(Font arabicFont) {
        JTabbedPane tabbedPane = new JTabbedPane();
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        tabbedPane.setFont(arabicFont);

        // تبويب لوحة التحكم مع رسوم بيانية
        JPanel dashboardPanel = createDashboardWithCharts(arabicFont);
        tabbedPane.addTab("\u0644\u0648\u062D\u0629 \u0627\u0644\u062A\u062D\u0643\u0645", dashboardPanel); // لوحة التحكم

        // تبويب الإحصائيات
        JPanel statsPanel = createStatsPanel(arabicFont);
        tabbedPane.addTab("\u0627\u0644\u0625\u062D\u0635\u0627\u0626\u064A\u0627\u062A", statsPanel); // الإحصائيات

        // تبويب التقارير
        JPanel reportsPanel = createReportsPanel(arabicFont);
        tabbedPane.addTab("\u0627\u0644\u062A\u0642\u0627\u0631\u064A\u0631", reportsPanel); // التقارير

        return tabbedPane;
    }

    /**
     * إنشاء لوحة التحكم مع الرسوم البيانية
     */
    private JPanel createDashboardWithCharts(Font arabicFont) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        // الجزء العلوي - بطاقات سريعة
        JPanel cardsPanel = new JPanel(new GridLayout(1, 4, 15, 15));
        cardsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        cardsPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 10, 20));

        cardsPanel.add(createStatCard("\u0627\u0644\u0634\u062D\u0646\u0627\u062A", "156", "+12%", arabicFont)); // الشحنات
        cardsPanel.add(createStatCard("\u0627\u0644\u0639\u0645\u0644\u0627\u0621", "89", "+5%", arabicFont)); // العملاء
        cardsPanel.add(createStatCard("\u0627\u0644\u0625\u064A\u0631\u0627\u062F\u0627\u062A", "2.5M", "+18%", arabicFont)); // الإيرادات
        cardsPanel.add(createStatCard("\u0627\u0644\u0623\u0631\u0628\u0627\u062D", "450K", "+8%", arabicFont)); // الأرباح

        panel.add(cardsPanel, BorderLayout.NORTH);

        // الجزء الأوسط - الرسوم البيانية
        JPanel chartsPanel = new JPanel(new GridLayout(1, 2, 15, 15));
        chartsPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        chartsPanel.setBorder(BorderFactory.createEmptyBorder(10, 20, 20, 20));

        // رسم بياني خطي للشحنات
        JPanel lineChartPanel = createLineChart();
        chartsPanel.add(lineChartPanel);

        // رسم بياني دائري للحالات
        JPanel pieChartPanel = createPieChart();
        chartsPanel.add(pieChartPanel);

        panel.add(chartsPanel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء بطاقة إحصائية
     */
    private JPanel createStatCard(String title, String value, String change, Font arabicFont) {
        JPanel card = new JPanel(new BorderLayout());
        card.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        card.setBackground(Color.WHITE);
        card.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(230, 230, 230), 1),
            BorderFactory.createEmptyBorder(20, 20, 20, 20)
        ));

        JLabel titleLabel = new JLabel(title);
        titleLabel.setFont(arabicFont);
        titleLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        titleLabel.setForeground(new Color(108, 117, 125));

        JLabel valueLabel = new JLabel(value);
        valueLabel.setFont(new Font("Segoe UI", Font.BOLD, 28));
        valueLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        valueLabel.setForeground(new Color(0, 123, 255));

        JLabel changeLabel = new JLabel(change);
        changeLabel.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        changeLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        changeLabel.setForeground(new Color(40, 167, 69));

        card.add(titleLabel, BorderLayout.NORTH);
        card.add(valueLabel, BorderLayout.CENTER);
        card.add(changeLabel, BorderLayout.SOUTH);

        return card;
    }

    /**
     * إنشاء رسم بياني خطي
     */
    private JPanel createLineChart() {
        DefaultCategoryDataset dataset = new DefaultCategoryDataset();
        dataset.addValue(120, "\u0627\u0644\u0634\u062D\u0646\u0627\u062A", "\u064A\u0646\u0627\u064A\u0631"); // الشحنات، يناير
        dataset.addValue(150, "\u0627\u0644\u0634\u062D\u0646\u0627\u062A", "\u0641\u0628\u0631\u0627\u064A\u0631"); // فبراير
        dataset.addValue(180, "\u0627\u0644\u0634\u062D\u0646\u0627\u062A", "\u0645\u0627\u0631\u0633"); // مارس
        dataset.addValue(200, "\u0627\u0644\u0634\u062D\u0646\u0627\u062A", "\u0623\u0628\u0631\u064A\u0644"); // أبريل
        dataset.addValue(170, "\u0627\u0644\u0634\u062D\u0646\u0627\u062A", "\u0645\u0627\u064A\u0648"); // مايو
        dataset.addValue(220, "\u0627\u0644\u0634\u062D\u0646\u0627\u062A", "\u064A\u0648\u0646\u064A\u0648"); // يونيو

        JFreeChart chart = ChartFactory.createLineChart(
            "\u0625\u062D\u0635\u0627\u0626\u064A\u0627\u062A \u0627\u0644\u0634\u062D\u0646\u0627\u062A \u0627\u0644\u0634\u0647\u0631\u064A\u0629", // إحصائيات الشحنات الشهرية
            "\u0627\u0644\u0634\u0647\u0631", // الشهر
            "\u0639\u062F\u062F \u0627\u0644\u0634\u062D\u0646\u0627\u062A", // عدد الشحنات
            dataset
        );

        ChartPanel chartPanel = new ChartPanel(chart);
        chartPanel.setPreferredSize(new Dimension(400, 300));

        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("\u0627\u0644\u0634\u062D\u0646\u0627\u062A \u0627\u0644\u0634\u0647\u0631\u064A\u0629")); // الشحنات الشهرية
        panel.add(chartPanel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء رسم بياني دائري
     */
    private JPanel createPieChart() {
        DefaultPieDataset dataset = new DefaultPieDataset();
        dataset.setValue("\u0645\u0643\u062A\u0645\u0644\u0629", 65); // مكتملة
        dataset.setValue("\u0642\u064A\u062F \u0627\u0644\u062A\u0646\u0641\u064A\u0630", 25); // قيد التنفيذ
        dataset.setValue("\u0645\u0624\u062C\u0644\u0629", 7); // مؤجلة
        dataset.setValue("\u0645\u0644\u063A\u0627\u0629", 3); // ملغاة

        JFreeChart chart = ChartFactory.createPieChart(
            "\u062D\u0627\u0644\u0629 \u0627\u0644\u0634\u062D\u0646\u0627\u062A", // حالة الشحنات
            dataset,
            true,
            true,
            false
        );

        ChartPanel chartPanel = new ChartPanel(chart);
        chartPanel.setPreferredSize(new Dimension(400, 300));

        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("\u062D\u0627\u0644\u0629 \u0627\u0644\u0634\u062D\u0646\u0627\u062A")); // حالة الشحنات
        panel.add(chartPanel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء لوحة الإحصائيات
     */
    private JPanel createStatsPanel(Font arabicFont) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel label = new JLabel("\u0644\u0648\u062D\u0629 \u0627\u0644\u0625\u062D\u0635\u0627\u0626\u064A\u0627\u062A \u0627\u0644\u0645\u062A\u0642\u062F\u0645\u0629"); // لوحة الإحصائيات المتقدمة
        label.setFont(new Font("Segoe UI", Font.BOLD, 18));
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        label.setHorizontalAlignment(SwingConstants.CENTER);

        panel.add(label, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء لوحة التقارير
     */
    private JPanel createReportsPanel(Font arabicFont) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        JLabel label = new JLabel("\u0646\u0638\u0627\u0645 \u0627\u0644\u062A\u0642\u0627\u0631\u064A\u0631 \u0627\u0644\u0630\u0643\u064A"); // نظام التقارير الذكي
        label.setFont(new Font("Segoe UI", Font.BOLD, 18));
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        label.setHorizontalAlignment(SwingConstants.CENTER);

        panel.add(label, BorderLayout.CENTER);

        return panel;
    }

    /**
     * إنشاء الشريط الجانبي المحسن
     */
    private JPanel createEnhancedSidePanel(Font arabicFont) {
        JPanel sidePanel = new JPanel(new BorderLayout());
        sidePanel.setPreferredSize(new Dimension(300, 0));
        sidePanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        sidePanel.setBackground(new Color(248, 249, 250));

        // عنوان الشريط الجانبي
        JLabel sideTitle = new JLabel("\u0627\u0644\u0642\u0627\u0626\u0645\u0629 \u0627\u0644\u0631\u0626\u064A\u0633\u064A\u0629"); // القائمة الرئيسية
        sideTitle.setFont(new Font("Segoe UI", Font.BOLD, 16));
        sideTitle.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        sideTitle.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        sidePanel.add(sideTitle, BorderLayout.NORTH);

        // قائمة الوحدات
        String[] modules = {
            "\u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u0639\u0627\u0645\u0629", // الإعدادات العامة
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645\u064A\u0646", // إدارة المستخدمين
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0639\u0645\u0644\u0627\u062A", // إدارة العملات
            "\u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u0634\u0631\u0643\u0629", // بيانات الشركة
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0623\u0635\u0646\u0627\u0641", // إدارة الأصناف
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0645\u0648\u0631\u062F\u064A\u0646", // إدارة الموردين
            "\u0645\u062A\u0627\u0628\u0639\u0629 \u0627\u0644\u0634\u062D\u0646\u0627\u062A", // متابعة الشحنات
            "\u0627\u0644\u0625\u062F\u062E\u0627\u0644\u0627\u062A \u0627\u0644\u062C\u0645\u0631\u0643\u064A\u0629", // الإدخالات الجمركية
            "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u062A\u0643\u0627\u0644\u064A\u0641", // إدارة التكاليف
            "\u0627\u0644\u062A\u0642\u0627\u0631\u064A\u0631" // التقارير
        };

        JList<String> moduleList = new JList<>(modules);
        moduleList.setFont(arabicFont);
        moduleList.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        moduleList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        JScrollPane scrollPane = new JScrollPane(moduleList);
        scrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        scrollPane.setBorder(BorderFactory.createEmptyBorder(0, 20, 20, 20));
        sidePanel.add(scrollPane, BorderLayout.CENTER);

        return sidePanel;
    }

    /**
     * إنشاء شريط الحالة المحسن
     */
    private JPanel createEnhancedStatusBar(Font arabicFont) {
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusPanel.setBackground(new Color(248, 249, 250));
        statusPanel.setBorder(BorderFactory.createMatteBorder(1, 0, 0, 0, new Color(222, 226, 230)));
        statusPanel.setPreferredSize(new Dimension(0, 35));

        JLabel statusLeft = new JLabel(READY);
        statusLeft.setFont(arabicFont);
        statusLeft.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusLeft.setBorder(BorderFactory.createEmptyBorder(0, 15, 0, 0));

        statusLabel = new JLabel();
        statusLabel.setFont(arabicFont);
        statusLabel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        statusLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 15));

        statusPanel.add(statusLeft, BorderLayout.EAST);
        statusPanel.add(statusLabel, BorderLayout.WEST);

        return statusPanel;
    }

    /**
     * تبديل الوضع المظلم
     */
    private void toggleDarkMode() {
        try {
            FlatAnimatedLafChange.showSnapshot();

            if (isDarkMode) {
                FlatLightLaf.setup();
                isDarkMode = false;
            } else {
                FlatDarkLaf.setup();
                isDarkMode = true;
            }

            SwingUtilities.updateComponentTreeUI(mainFrame);
            FlatAnimatedLafChange.hideSnapshotWithAnimation();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * بدء الساعة الرقمية
     */
    private void startClock() {
        clockTimer = new Timer(1000, e -> {
            LocalDateTime now = LocalDateTime.now();
            String timeText = now.format(DateTimeFormatter.ofPattern("yyyy/MM/dd - HH:mm:ss"));
            statusLabel.setText("\u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645: admin | " + timeText); // المستخدم: admin
        });
        clockTimer.start();
    }

    /**
     * معالج تسجيل الدخول
     */
    private class LoginActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String username = usernameField.getText().trim();
            String password = new String(passwordField.getPassword());

            if (username.isEmpty() || password.isEmpty()) {
                JOptionPane.showMessageDialog(loginFrame,
                    "\u064A\u0631\u062C\u0649 \u0625\u062F\u062E\u0627\u0644 \u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645 \u0648\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631", // يرجى إدخال اسم المستخدم وكلمة المرور
                    "\u062E\u0637\u0623", // خطأ
                    JOptionPane.ERROR_MESSAGE);
                return;
            }

            statusLabel.setText("\u062C\u0627\u0631\u064A \u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644..."); // جاري تسجيل الدخول...

            // محاكاة تسجيل الدخول مع تأخير
            Timer loginTimer = new Timer(1500, event -> {
                if ("admin".equals(username) && "admin123".equals(password)) {
                    showEnhancedMainInterface();
                } else {
                    statusLabel.setText(READY);
                    JOptionPane.showMessageDialog(loginFrame,
                        "\u0627\u0633\u0645 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645 \u0623\u0648 \u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631 \u063A\u064A\u0631 \u0635\u062D\u064A\u062D\u0629", // اسم المستخدم أو كلمة المرور غير صحيحة
                        "\u062E\u0637\u0623 \u0641\u064A \u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644", // خطأ في تسجيل الدخول
                        JOptionPane.ERROR_MESSAGE);
                    passwordField.selectAll();
                    passwordField.requestFocus();
                }
            });
            loginTimer.setRepeats(false);
            loginTimer.start();
        }
    }
}