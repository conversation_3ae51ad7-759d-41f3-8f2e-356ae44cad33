import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;

/**
 * أداة استكشاف سريع للجداول Quick Table Explorer
 */
public class QuickTableExplorer {

    public static void main(String[] args) {
        QuickTableExplorer explorer = new QuickTableExplorer();
        explorer.exploreQuickly();
    }

    /**
     * استكشاف سريع للجداول
     */
    public void exploreQuickly() {
        String url = "*************************************";
        String username = "ias20251";
        String password = "ys123";

        try (Connection connection = DriverManager.getConnection(url, username, password)) {

            System.out.println("🔄 الاتصال بقاعدة البيانات IAS20251...");
            System.out.println("✅ تم الاتصال بنجاح!");
            System.out.println();

            // عرض جميع الجداول
            showAllTables(connection);

            // عرض الجداول الأكثر أهمية
            showImportantTables(connection);

        } catch (SQLException e) {
            System.err.println("❌ خطأ في الاتصال: " + e.getMessage());
        }
    }

    /**
     * عرض جميع الجداول
     */
    private void showAllTables(Connection connection) throws SQLException {
        System.out.println("📋 جميع الجداول في IAS20251:");
        System.out.println("================================");

        String query = """
                    SELECT table_name, num_rows
                    FROM all_tables
                    WHERE owner = 'IAS20251'
                    ORDER BY table_name
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            int count = 0;
            while (rs.next()) {
                String tableName = rs.getString("table_name");
                String numRows = rs.getString("num_rows");

                System.out.printf("%-30s %s صف\n", tableName,
                        numRows != null ? String.format("%,d", Integer.parseInt(numRows))
                                : "غير محدد");
                count++;
            }

            System.out.println("\n📊 إجمالي الجداول: " + count);
        }

        System.out.println("\n" + "=".repeat(50) + "\n");
    }

    /**
     * عرض الجداول المهمة مع تفاصيل
     */
    private void showImportantTables(Connection connection) throws SQLException {
        System.out.println("🎯 الجداول المهمة مع التفاصيل:");
        System.out.println("===============================");

        String[] importantTables = {"IAS_ITM_MST", // الأصناف الرئيسي
                "IAS_ITM_DTL", // تفاصيل الأصناف
                "IAS_CUSTOMER", // العملاء
                "IAS_VENDOR", // الموردين
                "IAS_INVOICE", // الفواتير
                "IAS_RECEIPT", // الإيصالات
                "IAS_PAYMENT", // المدفوعات
                "IAS_STOCK", // المخزون
                "IAS_TRANSACTION", // المعاملات
                "IAS_ACCOUNT" // الحسابات
        };

        for (String tableName : importantTables) {
            exploreTable(connection, tableName);
        }
    }

    /**
     * استكشاف جدول محدد
     */
    private void exploreTable(Connection connection, String tableName) {
        System.out.println("🔍 جدول: " + tableName);
        System.out.println("-".repeat(40));

        try {
            // عدد الصفوف
            String countQuery =
                    String.format("SELECT COUNT(*) as total FROM IAS20251.%s", tableName);
            try (PreparedStatement stmt = connection.prepareStatement(countQuery);
                    ResultSet rs = stmt.executeQuery()) {

                if (rs.next()) {
                    int total = rs.getInt("total");
                    System.out.printf("📊 عدد الصفوف: %,d\n", total);
                }
            }

            // عدد الأعمدة
            String columnsQuery = """
                        SELECT COUNT(*) as column_count
                        FROM all_tab_columns
                        WHERE owner = 'IAS20251' AND table_name = ?
                    """;

            try (PreparedStatement stmt = connection.prepareStatement(columnsQuery)) {
                stmt.setString(1, tableName);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        int columnCount = rs.getInt("column_count");
                        System.out.println("📋 عدد الأعمدة: " + columnCount);
                    }
                }
            }

            // الأعمدة الرئيسية
            showMainColumns(connection, tableName);

            // عينة من البيانات
            showSampleData(connection, tableName);

        } catch (SQLException e) {
            System.out.println("❌ الجدول غير موجود أو خطأ في الوصول: " + e.getMessage());
        }

        System.out.println();
    }

    /**
     * عرض الأعمدة الرئيسية
     */
    private void showMainColumns(Connection connection, String tableName) throws SQLException {
        String query = """
                    SELECT column_name, data_type, nullable
                    FROM all_tab_columns
                    WHERE owner = 'IAS20251' AND table_name = ?
                    AND column_id <= 10
                    ORDER BY column_id
                """;

        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, tableName);
            try (ResultSet rs = stmt.executeQuery()) {
                System.out.println("📝 الأعمدة الرئيسية:");

                while (rs.next()) {
                    String columnName = rs.getString("column_name");
                    String dataType = rs.getString("data_type");
                    String nullable = rs.getString("nullable");

                    System.out.printf("   %-25s %-15s %s\n", columnName, dataType,
                            "Y".equals(nullable) ? "(يقبل NULL)" : "(مطلوب)");
                }
            }
        }
    }

    /**
     * عرض عينة من البيانات
     */
    private void showSampleData(Connection connection, String tableName) throws SQLException {
        String query = String.format("SELECT * FROM IAS20251.%s WHERE ROWNUM <= 2", tableName);

        try (PreparedStatement stmt = connection.prepareStatement(query);
                ResultSet rs = stmt.executeQuery()) {

            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            System.out.println("📄 عينة من البيانات:");

            // عرض أسماء الأعمدة (أول 5 فقط)
            System.out.print("   ");
            for (int i = 1; i <= Math.min(columnCount, 5); i++) {
                System.out.printf("%-15s ", metaData.getColumnName(i));
            }
            if (columnCount > 5) {
                System.out.print("...");
            }
            System.out.println();

            // عرض البيانات
            int rowCount = 0;
            while (rs.next() && rowCount < 2) {
                System.out.print("   ");
                for (int i = 1; i <= Math.min(columnCount, 5); i++) {
                    Object value = rs.getObject(i);
                    String displayValue = value != null ? value.toString() : "NULL";
                    if (displayValue.length() > 12) {
                        displayValue = displayValue.substring(0, 12) + "...";
                    }
                    System.out.printf("%-15s ", displayValue);
                }
                if (columnCount > 5) {
                    System.out.print("...");
                }
                System.out.println();
                rowCount++;
            }

            if (rowCount == 0) {
                System.out.println("   (الجدول فارغ)");
            }

        } catch (SQLException e) {
            System.out.println("   ❌ خطأ في قراءة البيانات: " + e.getMessage());
        }
    }
}
