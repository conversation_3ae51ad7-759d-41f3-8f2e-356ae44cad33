import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * تحليل بنية جداول مجموعات الأصناف في قاعدة البيانات IAS20251
 * Analyze Item Group Tables Structure in IAS20251 Database
 */
public class AnalyzeItemGroupTables {
    
    public static void main(String[] args) {
        try {
            // تحميل Oracle JDBC driver
            Class.forName("oracle.jdbc.OracleDriver");
            System.out.println("✅ تم تحميل Oracle JDBC driver بنجاح");
            
            // الاتصال بقاعدة البيانات IAS20251
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ias20251", 
                "ys123"
            );
            
            System.out.println("✅ متصل بقاعدة البيانات IAS20251");
            
            // تحليل الجداول المطلوبة
            String[] tables = {
                "GROUP_DETAILS",
                "IAS_MAINSUB_GRP_DTL", 
                "IAS_SUB_GRP_DTL",
                "IAS_ASSISTANT_GROUP",
                "IAS_DETAIL_GROUP"
            };
            
            for (String tableName : tables) {
                System.out.println("\n" + "=".repeat(80));
                System.out.println("📊 تحليل جدول: " + tableName);
                System.out.println("=".repeat(80));
                
                analyzeTableStructure(conn, tableName);
                analyzeTableConstraints(conn, tableName);
                analyzeTableIndexes(conn, tableName);
                analyzeTableData(conn, tableName);
            }
            
            conn.close();
            System.out.println("\n🎉 تم تحليل جميع الجداول بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * تحليل بنية الجدول
     */
    private static void analyzeTableStructure(Connection conn, String tableName) {
        try {
            System.out.println("\n🔍 بنية الجدول:");
            
            String sql = """
                SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE,
                       NULLABLE, DATA_DEFAULT, COLUMN_ID
                FROM USER_TAB_COLUMNS 
                WHERE TABLE_NAME = ?
                ORDER BY COLUMN_ID
            """;
            
            PreparedStatement pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, tableName.toUpperCase());
            ResultSet rs = pstmt.executeQuery();
            
            System.out.printf("%-25s %-15s %-10s %-10s %-8s %-10s %-15s%n", 
                "COLUMN_NAME", "DATA_TYPE", "LENGTH", "PRECISION", "SCALE", "NULLABLE", "DEFAULT");
            System.out.println("-".repeat(100));
            
            while (rs.next()) {
                String columnName = rs.getString("COLUMN_NAME");
                String dataType = rs.getString("DATA_TYPE");
                String length = rs.getString("DATA_LENGTH");
                String precision = rs.getString("DATA_PRECISION");
                String scale = rs.getString("DATA_SCALE");
                String nullable = rs.getString("NULLABLE");
                String defaultValue = rs.getString("DATA_DEFAULT");
                
                if ("NUMBER".equals(dataType) && precision != null) {
                    if (scale != null && !scale.equals("0")) {
                        dataType = dataType + "(" + precision + "," + scale + ")";
                    } else {
                        dataType = dataType + "(" + precision + ")";
                    }
                } else if ("VARCHAR2".equals(dataType)) {
                    dataType = dataType + "(" + length + ")";
                }
                
                System.out.printf("%-25s %-15s %-10s %-10s %-8s %-10s %-15s%n", 
                    columnName, dataType, 
                    length != null ? length : "", 
                    precision != null ? precision : "",
                    scale != null ? scale : "",
                    nullable, 
                    defaultValue != null ? defaultValue.trim() : "");
            }
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحليل بنية الجدول " + tableName + ": " + e.getMessage());
        }
    }
    
    /**
     * تحليل قيود الجدول
     */
    private static void analyzeTableConstraints(Connection conn, String tableName) {
        try {
            System.out.println("\n🔒 قيود الجدول:");
            
            String sql = """
                SELECT c.CONSTRAINT_NAME, c.CONSTRAINT_TYPE, c.STATUS, c.VALIDATED,
                       cc.COLUMN_NAME, c.R_CONSTRAINT_NAME
                FROM USER_CONSTRAINTS c
                LEFT JOIN USER_CONS_COLUMNS cc ON c.CONSTRAINT_NAME = cc.CONSTRAINT_NAME
                WHERE c.TABLE_NAME = ?
                ORDER BY c.CONSTRAINT_TYPE, c.CONSTRAINT_NAME, cc.POSITION
            """;
            
            PreparedStatement pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, tableName.toUpperCase());
            ResultSet rs = pstmt.executeQuery();
            
            System.out.printf("%-30s %-5s %-25s %-10s %-10s%n", 
                "CONSTRAINT_NAME", "TYPE", "COLUMN_NAME", "STATUS", "VALIDATED");
            System.out.println("-".repeat(90));
            
            while (rs.next()) {
                String constraintName = rs.getString("CONSTRAINT_NAME");
                String constraintType = rs.getString("CONSTRAINT_TYPE");
                String columnName = rs.getString("COLUMN_NAME");
                String status = rs.getString("STATUS");
                String validated = rs.getString("VALIDATED");
                String rConstraintName = rs.getString("R_CONSTRAINT_NAME");
                
                String typeDesc = switch (constraintType) {
                    case "P" -> "PK";
                    case "R" -> "FK";
                    case "U" -> "UK";
                    case "C" -> "CK";
                    default -> constraintType;
                };
                
                System.out.printf("%-30s %-5s %-25s %-10s %-10s%n", 
                    constraintName, typeDesc, 
                    columnName != null ? columnName : "", 
                    status, validated);
                
                if (rConstraintName != null) {
                    System.out.printf("%-30s %-5s %-25s %-10s %-10s%n", 
                        "", "", "-> " + rConstraintName, "", "");
                }
            }
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحليل قيود الجدول " + tableName + ": " + e.getMessage());
        }
    }
    
    /**
     * تحليل فهارس الجدول
     */
    private static void analyzeTableIndexes(Connection conn, String tableName) {
        try {
            System.out.println("\n📇 فهارس الجدول:");
            
            String sql = """
                SELECT i.INDEX_NAME, i.INDEX_TYPE, i.UNIQUENESS, ic.COLUMN_NAME, ic.COLUMN_POSITION
                FROM USER_INDEXES i
                LEFT JOIN USER_IND_COLUMNS ic ON i.INDEX_NAME = ic.INDEX_NAME
                WHERE i.TABLE_NAME = ?
                ORDER BY i.INDEX_NAME, ic.COLUMN_POSITION
            """;
            
            PreparedStatement pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, tableName.toUpperCase());
            ResultSet rs = pstmt.executeQuery();
            
            System.out.printf("%-30s %-10s %-10s %-25s%n", 
                "INDEX_NAME", "TYPE", "UNIQUE", "COLUMN_NAME");
            System.out.println("-".repeat(80));
            
            while (rs.next()) {
                String indexName = rs.getString("INDEX_NAME");
                String indexType = rs.getString("INDEX_TYPE");
                String uniqueness = rs.getString("UNIQUENESS");
                String columnName = rs.getString("COLUMN_NAME");
                
                System.out.printf("%-30s %-10s %-10s %-25s%n", 
                    indexName, indexType, uniqueness, 
                    columnName != null ? columnName : "");
            }
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحليل فهارس الجدول " + tableName + ": " + e.getMessage());
        }
    }
    
    /**
     * تحليل بيانات الجدول
     */
    private static void analyzeTableData(Connection conn, String tableName) {
        try {
            System.out.println("\n📊 إحصائيات البيانات:");
            
            // عدد السجلات
            String countSql = "SELECT COUNT(*) as RECORD_COUNT FROM " + tableName;
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery(countSql);
            
            if (rs.next()) {
                int recordCount = rs.getInt("RECORD_COUNT");
                System.out.println("عدد السجلات: " + recordCount);
            }
            
            // عينة من البيانات (أول 3 سجلات)
            if (rs.getInt("RECORD_COUNT") > 0) {
                System.out.println("\nعينة من البيانات (أول 3 سجلات):");
                
                String sampleSql = "SELECT * FROM " + tableName + " WHERE ROWNUM <= 3";
                ResultSet sampleRs = stmt.executeQuery(sampleSql);
                ResultSetMetaData metaData = sampleRs.getMetaData();
                int columnCount = metaData.getColumnCount();
                
                // طباعة أسماء الأعمدة
                for (int i = 1; i <= columnCount; i++) {
                    System.out.printf("%-20s ", metaData.getColumnName(i));
                }
                System.out.println();
                System.out.println("-".repeat(columnCount * 21));
                
                // طباعة البيانات
                while (sampleRs.next()) {
                    for (int i = 1; i <= columnCount; i++) {
                        String value = sampleRs.getString(i);
                        if (value != null && value.length() > 18) {
                            value = value.substring(0, 15) + "...";
                        }
                        System.out.printf("%-20s ", value != null ? value : "NULL");
                    }
                    System.out.println();
                }
            }
            
        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحليل بيانات الجدول " + tableName + ": " + e.getMessage());
        }
    }
}
