<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
          http://maven.apache.org/xsd/settings-1.0.0.xsd">
    
    <!-- Local Repository -->
    <localRepository>${user.home}/.m2/repository</localRepository>
    
    <!-- Offline Mode -->
    <offline>false</offline>
    
    <!-- Plugin Groups -->
    <pluginGroups>
        <pluginGroup>org.apache.maven.plugins</pluginGroup>
        <pluginGroup>org.codehaus.mojo</pluginGroup>
        <pluginGroup>org.openjfx</pluginGroup>
    </pluginGroups>
    
    <!-- Mirrors for faster downloads -->
    <mirrors>
        <!-- <PERSON><PERSON> (China) -->
        <mirror>
            <id>aliyun-central</id>
            <name><PERSON>yun Central</name>
            <url>https://maven.aliyun.com/repository/central</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
        
        <!-- Maven Central Backup -->
        <mirror>
            <id>maven-central-backup</id>
            <name>Maven Central Backup</name>
            <url>https://repo1.maven.org/maven2</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
    </mirrors>
    
    <!-- Profiles -->
    <profiles>
        <profile>
            <id>default</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            
            <repositories>
                <repository>
                    <id>central</id>
                    <name>Maven Central Repository</name>
                    <url>https://repo1.maven.org/maven2</url>
                    <layout>default</layout>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                
                <repository>
                    <id>aliyun-central</id>
                    <name>Aliyun Central</name>
                    <url>https://maven.aliyun.com/repository/central</url>
                    <layout>default</layout>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                
                <repository>
                    <id>jcenter</id>
                    <name>JCenter</name>
                    <url>https://jcenter.bintray.com</url>
                    <layout>default</layout>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                
                <repository>
                    <id>sonatype-nexus-snapshots</id>
                    <name>Sonatype Nexus Snapshots</name>
                    <url>https://oss.sonatype.org/content/repositories/snapshots</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            
            <pluginRepositories>
                <pluginRepository>
                    <id>central</id>
                    <name>Maven Plugin Repository</name>
                    <url>https://repo1.maven.org/maven2</url>
                    <layout>default</layout>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                    <releases>
                        <updatePolicy>never</updatePolicy>
                    </releases>
                </pluginRepository>
                
                <pluginRepository>
                    <id>aliyun-central</id>
                    <name>Aliyun Central</name>
                    <url>https://maven.aliyun.com/repository/central</url>
                    <layout>default</layout>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
        
        <!-- Offline Profile -->
        <profile>
            <id>offline</id>
            <activation>
                <property>
                    <name>offline</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
    </profiles>
    
    <!-- Active Profiles -->
    <activeProfiles>
        <activeProfile>default</activeProfile>
    </activeProfiles>
</settings>
