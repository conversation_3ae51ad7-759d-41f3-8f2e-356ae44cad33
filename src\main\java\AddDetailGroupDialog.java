import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.ComponentOrientation;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.Frame;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JComponent;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JTextField;

/**
 * نافذة إضافة مجموعة تفصيلية جديدة Add Detail Group Dialog
 */
public class AddDetailGroupDialog extends JDialog {

    private Connection connection;
    private boolean saved = false;

    // مكونات الواجهة
    private JComboBox<String> mainGroupCombo;
    private JComboBox<String> mainSubGroupCombo;
    private JComboBox<String> subGroupCombo;
    private JComboBox<String> assistantGroupCombo;
    private JTextField detailCodeField;
    private JTextField arabicNameField;
    private JTextField englishNameField;
    private JTextField imageCodeField;
    private JCheckBox webSyncCheckBox;
    private JCheckBox activeCheckBox;

    private Font arabicFont = new Font("Tahoma", Font.PLAIN, 13);
    private Font arabicBoldFont = new Font("Tahoma", Font.BOLD, 13);

    public AddDetailGroupDialog(Frame parent, Connection connection) {
        super(parent, "إضافة مجموعة تفصيلية جديدة", true);
        this.connection = connection;

        initializeComponents();
        setupLayout();
        loadMainGroups();
        setDefaultValues();

        setSize(450, 550);
        setLocationRelativeTo(parent);
        setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
    }

    private void initializeComponents() {
        // إنشاء الحقول
        mainGroupCombo = new JComboBox<>();
        mainSubGroupCombo = new JComboBox<>();
        subGroupCombo = new JComboBox<>();
        assistantGroupCombo = new JComboBox<>();
        detailCodeField = new JTextField(15);
        arabicNameField = new JTextField(25);
        englishNameField = new JTextField(25);
        imageCodeField = new JTextField(15);

        // إنشاء صناديق الاختيار
        webSyncCheckBox = new JCheckBox("مزامنة الويب");
        activeCheckBox = new JCheckBox("نشط");

        // تطبيق الخط العربي
        JComponent[] components = {mainGroupCombo, mainSubGroupCombo, subGroupCombo,
                assistantGroupCombo, detailCodeField, arabicNameField, englishNameField,
                imageCodeField, webSyncCheckBox, activeCheckBox};
        for (JComponent component : components) {
            component.setFont(arabicFont);
            component.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        }

        // إضافة مستمعين
        mainGroupCombo.addActionListener(e -> loadMainSubGroups());
        mainSubGroupCombo.addActionListener(e -> loadSubGroups());
        subGroupCombo.addActionListener(e -> loadAssistantGroups());
        assistantGroupCombo.addActionListener(e -> suggestDetailCode());
    }

    private void setupLayout() {
        setLayout(new BorderLayout());

        JPanel mainPanel = new JPanel(new GridBagLayout());
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        mainPanel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;

        int row = 0;

        // المجموعة الرئيسية
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("المجموعة الرئيسية *:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(mainGroupCombo, gbc);
        row++;

        // المجموعة الفرعية
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("المجموعة الفرعية *:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(mainSubGroupCombo, gbc);
        row++;

        // المجموعة تحت فرعية
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("المجموعة تحت فرعية *:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(subGroupCombo, gbc);
        row++;

        // المجموعة المساعدة
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("المجموعة المساعدة *:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(assistantGroupCombo, gbc);
        row++;

        // كود المجموعة التفصيلية
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("كود المجموعة التفصيلية *:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(detailCodeField, gbc);
        row++;

        // الاسم العربي
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("الاسم العربي *:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(arabicNameField, gbc);
        row++;

        // الاسم الإنجليزي
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("الاسم الإنجليزي:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(englishNameField, gbc);
        row++;

        // كود الصورة
        gbc.gridx = 1;
        gbc.gridy = row;
        mainPanel.add(createLabel("كود الصورة:"), gbc);
        gbc.gridx = 0;
        mainPanel.add(imageCodeField, gbc);
        row++;

        // صناديق الاختيار
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        mainPanel.add(webSyncCheckBox, gbc);
        row++;

        gbc.gridy = row;
        mainPanel.add(activeCheckBox, gbc);

        add(mainPanel, BorderLayout.CENTER);
        add(createButtonPanel(), BorderLayout.SOUTH);
    }

    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(arabicBoldFont);
        label.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        return label;
    }

    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        JButton saveButton = new JButton("💾 حفظ");
        saveButton.setFont(arabicBoldFont);
        saveButton.setBackground(new Color(39, 174, 96));
        saveButton.setForeground(Color.WHITE);
        saveButton.setPreferredSize(new Dimension(100, 35));
        saveButton.addActionListener(e -> saveDetailGroup());

        JButton cancelButton = new JButton("❌ إلغاء");
        cancelButton.setFont(arabicBoldFont);
        cancelButton.setBackground(new Color(231, 76, 60));
        cancelButton.setForeground(Color.WHITE);
        cancelButton.setPreferredSize(new Dimension(100, 35));
        cancelButton.addActionListener(e -> dispose());

        panel.add(saveButton);
        panel.add(cancelButton);

        return panel;
    }

    private void loadMainGroups() {
        try {
            String sql =
                    "SELECT G_CODE, G_A_NAME FROM ERP_GROUP_DETAILS WHERE IS_ACTIVE = 1 ORDER BY G_CODE";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            mainGroupCombo.removeAllItems();
            mainGroupCombo.addItem("-- اختر المجموعة الرئيسية --");

            while (rs.next()) {
                String code = rs.getString("G_CODE");
                String name = rs.getString("G_A_NAME");
                mainGroupCombo.addItem(code + " - " + name);
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات الرئيسية: " + e.getMessage());
        }
    }

    private void loadMainSubGroups() {
        mainSubGroupCombo.removeAllItems();
        mainSubGroupCombo.addItem("-- اختر المجموعة الفرعية --");

        if (mainGroupCombo.getSelectedIndex() <= 0) {
            return;
        }

        try {
            String selectedItem = (String) mainGroupCombo.getSelectedItem();
            String mainGroupCode = selectedItem.split(" - ")[0];

            String sql =
                    "SELECT MNG_CODE, MNG_A_NAME FROM ERP_MAINSUB_GRP_DTL WHERE G_CODE = ? AND IS_ACTIVE = 1 ORDER BY MNG_CODE";
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, mainGroupCode);
            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                String code = rs.getString("MNG_CODE");
                String name = rs.getString("MNG_A_NAME");
                mainSubGroupCombo.addItem(code + " - " + name);
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات الفرعية: " + e.getMessage());
        }
    }

    private void loadSubGroups() {
        subGroupCombo.removeAllItems();
        subGroupCombo.addItem("-- اختر المجموعة تحت فرعية --");

        if (mainSubGroupCombo.getSelectedIndex() <= 0) {
            return;
        }

        try {
            String selectedItem = (String) mainSubGroupCombo.getSelectedItem();
            String mainSubGroupCode = selectedItem.split(" - ")[0];

            String sql =
                    "SELECT SUBG_CODE, SUBG_A_NAME FROM ERP_SUB_GRP_DTL WHERE MNG_CODE = ? AND IS_ACTIVE = 1 ORDER BY SUBG_CODE";
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, mainSubGroupCode);
            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                String code = rs.getString("SUBG_CODE");
                String name = rs.getString("SUBG_A_NAME");
                subGroupCombo.addItem(code + " - " + name);
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات تحت فرعية: " + e.getMessage());
        }
    }

    private void loadAssistantGroups() {
        assistantGroupCombo.removeAllItems();
        assistantGroupCombo.addItem("-- اختر المجموعة المساعدة --");

        if (subGroupCombo.getSelectedIndex() <= 0) {
            return;
        }

        try {
            String selectedItem = (String) subGroupCombo.getSelectedItem();
            String subGroupCode = selectedItem.split(" - ")[0];

            String sql =
                    "SELECT ASSISTANT_NO, ASSISTANT_A_NAME FROM ERP_ASSISTANT_GROUP WHERE SUBG_CODE = ? AND IS_ACTIVE = 1 ORDER BY ASSISTANT_NO";
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, subGroupCode);
            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                String code = rs.getString("ASSISTANT_NO");
                String name = rs.getString("ASSISTANT_A_NAME");
                assistantGroupCombo.addItem(code + " - " + name);
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في تحميل المجموعات المساعدة: " + e.getMessage());
        }
    }

    private void suggestDetailCode() {
        if (assistantGroupCombo.getSelectedIndex() <= 0) {
            return;
        }

        try {
            String selectedItem = (String) assistantGroupCombo.getSelectedItem();
            String assistantCode = selectedItem.split(" - ")[0];

            String sql =
                    """
                                SELECT ? || LPAD(NVL(MAX(TO_NUMBER(SUBSTR(DETAIL_NO, LENGTH(?) + 1))), 0) + 1, 2, '0')
                                FROM ERP_DETAIL_GROUP
                                WHERE ASSISTANT_NO = ? AND DETAIL_NO LIKE ? || '%'
                            """;

            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, assistantCode);
            pstmt.setString(2, assistantCode);
            pstmt.setString(3, assistantCode);
            pstmt.setString(4, assistantCode);

            ResultSet rs = pstmt.executeQuery();

            if (rs.next()) {
                String nextCode = rs.getString(1);
                // اقتراح الكود فقط إذا كان الحقل فارغاً
                if (detailCodeField.getText().trim().isEmpty()) {
                    detailCodeField.setText(nextCode);
                }
            } else {
                if (detailCodeField.getText().trim().isEmpty()) {
                    detailCodeField.setText(assistantCode + "01");
                }
            }

        } catch (SQLException e) {
            System.err.println("❌ خطأ في توليد كود المجموعة التفصيلية: " + e.getMessage());
        }
    }

    private void setDefaultValues() {
        activeCheckBox.setSelected(true);
        webSyncCheckBox.setSelected(false);
    }

    private void saveDetailGroup() {
        if (!validateInput()) {
            return;
        }

        try {
            String mainGroupItem = (String) mainGroupCombo.getSelectedItem();
            String mainGroupCode = mainGroupItem.split(" - ")[0];

            String mainSubGroupItem = (String) mainSubGroupCombo.getSelectedItem();
            String mainSubGroupCode = mainSubGroupItem.split(" - ")[0];

            String subGroupItem = (String) subGroupCombo.getSelectedItem();
            String subGroupCode = subGroupItem.split(" - ")[0];

            String assistantGroupItem = (String) assistantGroupCombo.getSelectedItem();
            String assistantGroupCode = assistantGroupItem.split(" - ")[0];

            String sql =
                    """
                                INSERT INTO ERP_DETAIL_GROUP
                                (G_CODE, MNG_CODE, SUBG_CODE, ASSISTANT_NO, DETAIL_NO, DET_I_CODE, DETAIL_A_NAME,
                                 DETAIL_E_NAME, SYNCHRNZ_TO_WEB_FLG, IS_ACTIVE, CREATED_BY, CREATED_DATE)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'USER', SYSDATE)
                            """;

            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, mainGroupCode);
            pstmt.setString(2, mainSubGroupCode);
            pstmt.setString(3, subGroupCode);
            pstmt.setString(4, assistantGroupCode);
            pstmt.setString(5, detailCodeField.getText().trim());
            pstmt.setString(6, imageCodeField.getText().trim());
            pstmt.setString(7, arabicNameField.getText().trim());
            pstmt.setString(8, englishNameField.getText().trim());
            pstmt.setInt(9, webSyncCheckBox.isSelected() ? 1 : 0);
            pstmt.setInt(10, activeCheckBox.isSelected() ? 1 : 0);

            pstmt.executeUpdate();
            connection.commit();

            saved = true;
            JOptionPane.showMessageDialog(this, "تم حفظ المجموعة التفصيلية بنجاح!", "نجح الحفظ",
                    JOptionPane.INFORMATION_MESSAGE);

            dispose();

        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }

            JOptionPane.showMessageDialog(this, "خطأ في حفظ المجموعة التفصيلية:\n" + e.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
        }
    }

    private boolean validateInput() {
        if (mainGroupCombo.getSelectedIndex() <= 0) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار المجموعة الرئيسية", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        if (mainSubGroupCombo.getSelectedIndex() <= 0) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار المجموعة الفرعية", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        if (subGroupCombo.getSelectedIndex() <= 0) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار المجموعة تحت فرعية", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        if (assistantGroupCombo.getSelectedIndex() <= 0) {
            JOptionPane.showMessageDialog(this, "يرجى اختيار المجموعة المساعدة", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        if (detailCodeField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "كود المجموعة التفصيلية مطلوب", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        if (arabicNameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "الاسم العربي مطلوب", "خطأ",
                    JOptionPane.ERROR_MESSAGE);
            return false;
        }

        return true;
    }

    public boolean isSaved() {
        return saved;
    }
}
