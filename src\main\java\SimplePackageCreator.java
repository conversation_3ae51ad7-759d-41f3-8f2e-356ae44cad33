import java.sql.*;

public class SimplePackageCreator {
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            System.out.println("🔗 الاتصال بـ SHIP_ERP...");
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            System.out.println("✅ تم الاتصال");
            
            // إنشاء جدول سجل العمليات
            System.out.println("📋 إنشاء جدول سجل العمليات...");
            try {
                Statement stmt = conn.createStatement();
                stmt.execute("DROP TABLE ERP_OPERATION_LOG CASCADE CONSTRAINTS");
                System.out.println("🗑️ تم حذف الجدول القديم");
            } catch (SQLException e) {
                System.out.println("ℹ️ الجدول غير موجود مسبقاً");
            }
            
            String createLogTable = """
                CREATE TABLE ERP_OPERATION_LOG (
                    log_id NUMBER PRIMARY KEY,
                    operation_type VARCHAR2(50) NOT NULL,
                    table_name VARCHAR2(100),
                    status VARCHAR2(20) NOT NULL,
                    message VARCHAR2(4000),
                    records_count NUMBER DEFAULT 0,
                    operation_date DATE DEFAULT SYSDATE,
                    username VARCHAR2(100) DEFAULT USER
                )
            """;
            
            Statement stmt = conn.createStatement();
            stmt.execute(createLogTable);
            System.out.println("✅ تم إنشاء جدول سجل العمليات");
            
            // إنشاء Sequence
            try {
                stmt.execute("DROP SEQUENCE ERP_LOG_SEQ");
            } catch (SQLException e) {
                // تجاهل الخطأ
            }
            
            stmt.execute("CREATE SEQUENCE ERP_LOG_SEQ START WITH 1 INCREMENT BY 1");
            System.out.println("✅ تم إنشاء Sequence");
            
            // إنشاء Package بسيط للاختبار
            System.out.println("📦 إنشاء Package ERP_INTEGRATION...");
            
            String packageSpec = """
                CREATE OR REPLACE PACKAGE ERP_INTEGRATION AS
                    
                    -- تسجيل العمليات
                    PROCEDURE log_operation(
                        p_operation_type VARCHAR2,
                        p_table_name VARCHAR2,
                        p_status VARCHAR2,
                        p_message VARCHAR2,
                        p_records_count NUMBER DEFAULT 0
                    );
                    
                    -- اختبار الاتصال
                    FUNCTION test_connection RETURN VARCHAR2;
                    
                END ERP_INTEGRATION;
            """;
            
            stmt.execute(packageSpec);
            System.out.println("✅ تم إنشاء Package Specification");
            
            String packageBody = """
                CREATE OR REPLACE PACKAGE BODY ERP_INTEGRATION AS
                    
                    -- تسجيل العمليات
                    PROCEDURE log_operation(
                        p_operation_type VARCHAR2,
                        p_table_name VARCHAR2,
                        p_status VARCHAR2,
                        p_message VARCHAR2,
                        p_records_count NUMBER DEFAULT 0
                    ) IS
                        PRAGMA AUTONOMOUS_TRANSACTION;
                    BEGIN
                        INSERT INTO ERP_OPERATION_LOG (
                            log_id, operation_type, table_name, status,
                            message, records_count, operation_date, username
                        ) VALUES (
                            ERP_LOG_SEQ.NEXTVAL, p_operation_type, p_table_name, p_status,
                            p_message, p_records_count, SYSDATE, USER
                        );
                        COMMIT;
                    EXCEPTION
                        WHEN OTHERS THEN
                            ROLLBACK;
                            -- تجاهل أخطاء التسجيل
                            NULL;
                    END log_operation;
                    
                    -- اختبار الاتصال
                    FUNCTION test_connection RETURN VARCHAR2 IS
                    BEGIN
                        RETURN 'SUCCESS: الاتصال يعمل بنجاح';
                    END test_connection;
                    
                END ERP_INTEGRATION;
            """;
            
            stmt.execute(packageBody);
            System.out.println("✅ تم إنشاء Package Body");
            
            // اختبار Package
            System.out.println("🧪 اختبار Package...");
            CallableStatement cs = conn.prepareCall("{ ? = call ERP_INTEGRATION.test_connection }");
            cs.registerOutParameter(1, Types.VARCHAR);
            cs.execute();
            String result = cs.getString(1);
            System.out.println("📋 نتيجة الاختبار: " + result);
            
            // تسجيل عملية الإنشاء
            CallableStatement logCs = conn.prepareCall("{ call ERP_INTEGRATION.log_operation(?, ?, ?, ?, ?) }");
            logCs.setString(1, "CREATE");
            logCs.setString(2, "ERP_INTEGRATION");
            logCs.setString(3, "SUCCESS");
            logCs.setString(4, "تم إنشاء Package ERP_INTEGRATION بنجاح");
            logCs.setInt(5, 1);
            logCs.execute();
            System.out.println("✅ تم تسجيل العملية");
            
            conn.close();
            System.out.println("🎉 تم إنشاء Package بنجاح!");
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
