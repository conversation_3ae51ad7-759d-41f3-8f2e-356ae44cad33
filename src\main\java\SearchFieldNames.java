import java.sql.*;

/**
 * البحث في جدول comments لمعرفة تسميات الحقول العربية
 */
public class SearchFieldNames {
    
    public static void main(String[] args) {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            System.out.println("✅ تم الاتصال بـ SHIP_ERP");
            
            // البحث عن تسميات الحقول المهمة
            searchFieldNames(conn);
            
            // عرض جميع التعليقات المتعلقة بالأصناف
            searchItemRelatedComments(conn);
            
            // عرض التعليقات حسب النطاقات
            showCommentsByRange(conn);
            
            conn.close();
            
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * البحث عن تسميات الحقول المهمة
     */
    private static void searchFieldNames(Connection conn) throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("🔍 البحث عن تسميات الحقول المهمة");
        System.out.println("=".repeat(80));
        
        String[] searchTerms = {
            "كود", "اسم", "تكلفة", "سعر", "نشط", "خدمة", "نقدي", "مجموعة",
            "وحدة", "باركود", "حجم", "وصف", "مصنع", "مورد", "بديل",
            "انتهاء", "دفعة", "تسلسل", "رئيسي", "بيع", "شراء", "مخزون"
        };
        
        Statement stmt = conn.createStatement();
        
        for (String term : searchTerms) {
            System.out.println("\n🔍 البحث عن: " + term);
            
            String searchQuery = "SELECT COMMENTS_NO, COMMENTS_NAME FROM COMMENTS WHERE UPPER(COMMENTS_NAME) LIKE UPPER('%" + term + "%') ORDER BY COMMENTS_NO";
            
            try {
                ResultSet rs = stmt.executeQuery(searchQuery);
                boolean found = false;
                
                while (rs.next()) {
                    if (!found) {
                        System.out.println("  ✅ تم العثور على:");
                        found = true;
                    }
                    
                    int commentNo = rs.getInt("COMMENTS_NO");
                    String commentName = rs.getString("COMMENTS_NAME");
                    
                    System.out.println("    " + commentNo + " - " + commentName);
                }
                
                if (!found) {
                    System.out.println("  ❌ لم يتم العثور على نتائج");
                }
                
                rs.close();
                
            } catch (SQLException e) {
                System.out.println("  ⚠️ خطأ في البحث: " + e.getMessage());
            }
        }
        
        stmt.close();
    }
    
    /**
     * البحث عن التعليقات المتعلقة بالأصناف
     */
    private static void searchItemRelatedComments(Connection conn) throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("📦 التعليقات المتعلقة بالأصناف");
        System.out.println("=".repeat(80));
        
        String[] itemTerms = {
            "صنف", "أصناف", "مادة", "مواد", "منتج", "منتجات", "سلعة", "سلع"
        };
        
        Statement stmt = conn.createStatement();
        
        for (String term : itemTerms) {
            System.out.println("\n🔍 البحث عن: " + term);
            
            String searchQuery = "SELECT COMMENTS_NO, COMMENTS_NAME FROM COMMENTS WHERE UPPER(COMMENTS_NAME) LIKE UPPER('%" + term + "%') ORDER BY COMMENTS_NO";
            
            try {
                ResultSet rs = stmt.executeQuery(searchQuery);
                boolean found = false;
                
                while (rs.next()) {
                    if (!found) {
                        System.out.println("  ✅ تم العثور على:");
                        found = true;
                    }
                    
                    int commentNo = rs.getInt("COMMENTS_NO");
                    String commentName = rs.getString("COMMENTS_NAME");
                    
                    System.out.println("    " + commentNo + " - " + commentName);
                }
                
                if (!found) {
                    System.out.println("  ❌ لم يتم العثور على نتائج");
                }
                
                rs.close();
                
            } catch (SQLException e) {
                System.out.println("  ⚠️ خطأ في البحث: " + e.getMessage());
            }
        }
        
        stmt.close();
    }
    
    /**
     * عرض التعليقات حسب النطاقات
     */
    private static void showCommentsByRange(Connection conn) throws SQLException {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("📊 التعليقات حسب النطاقات");
        System.out.println("=".repeat(80));
        
        int[] ranges = {19000, 19500, 20000, 20500, 21000, 21500, 22000};
        
        Statement stmt = conn.createStatement();
        
        for (int i = 0; i < ranges.length - 1; i++) {
            int startRange = ranges[i];
            int endRange = ranges[i + 1];
            
            System.out.println("\n📋 النطاق: " + startRange + " - " + endRange);
            
            String rangeQuery = "SELECT COMMENTS_NO, COMMENTS_NAME FROM COMMENTS WHERE COMMENTS_NO >= " + startRange + " AND COMMENTS_NO < " + endRange + " ORDER BY COMMENTS_NO";
            
            try {
                ResultSet rs = stmt.executeQuery(rangeQuery);
                int count = 0;
                
                while (rs.next() && count < 10) { // عرض أول 10 فقط
                    int commentNo = rs.getInt("COMMENTS_NO");
                    String commentName = rs.getString("COMMENTS_NAME");
                    
                    System.out.println("  " + commentNo + " - " + commentName);
                    count++;
                }
                
                if (count == 0) {
                    System.out.println("  ❌ لا توجد تعليقات في هذا النطاق");
                } else if (count == 10) {
                    System.out.println("  ... (عرض أول 10 فقط)");
                }
                
                rs.close();
                
            } catch (SQLException e) {
                System.out.println("  ⚠️ خطأ في الاستعلام: " + e.getMessage());
            }
        }
        
        stmt.close();
    }
    
    /**
     * البحث عن تعليق محدد برقمه
     */
    public static String getCommentByNumber(Connection conn, int commentNo) {
        try {
            PreparedStatement stmt = conn.prepareStatement("SELECT COMMENTS_NAME FROM COMMENTS WHERE COMMENTS_NO = ?");
            stmt.setInt(1, commentNo);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                String commentName = rs.getString("COMMENTS_NAME");
                rs.close();
                stmt.close();
                return commentName;
            }
            
            rs.close();
            stmt.close();
            
        } catch (SQLException e) {
            System.err.println("خطأ في البحث عن التعليق رقم " + commentNo + ": " + e.getMessage());
        }
        
        return null;
    }
}
