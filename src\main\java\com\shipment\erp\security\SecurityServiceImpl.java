package com.shipment.erp.security;

import com.shipment.erp.model.User;
import com.shipment.erp.model.AuditLog;
import com.shipment.erp.repository.AuditLogRepository;
import com.shipment.erp.util.PasswordUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * تطبيق خدمة الأمان
 */
@Service
public class SecurityServiceImpl implements SecurityService {

    private static final Logger logger = LoggerFactory.getLogger(SecurityServiceImpl.class);

    private static final String ENCRYPTION_ALGORITHM = "AES";
    private static final String ADMIN_ROLE = "مدير عام";
    private static final String SYSTEM_ADMIN_ROLE = "مدير نظام";
    private static final int MAX_LOGIN_ATTEMPTS = 5;
    private static final int BLOCK_DURATION_MINUTES = 30;

    @Autowired
    private AuditLogRepository auditLogRepository;

    // خريطة لتتبع محاولات تسجيل الدخول الفاشلة
    private final Map<String, LoginAttemptInfo> loginAttempts = new ConcurrentHashMap<>();
    
    // خريطة لتتبع عناوين IP المحظورة
    private final Map<String, LocalDateTime> blockedIps = new ConcurrentHashMap<>();
    
    // خريطة لتتبع رموز الجلسات
    private final Map<String, SessionInfo> sessionTokens = new ConcurrentHashMap<>();

    // مفتاح التشفير (يجب تخزينه بشكل آمن في الإنتاج)
    private final SecretKey encryptionKey;

    public SecurityServiceImpl() {
        this.encryptionKey = generateEncryptionKey();
    }

    @Override
    public boolean hasPermission(User user, String moduleName, String permissionType) {
        if (user == null || user.getRole() == null) {
            return false;
        }

        // المدير العام له جميع الصلاحيات
        if (isSystemAdmin(user)) {
            return true;
        }

        return user.hasPermission(moduleName, permissionType);
    }

    @Override
    public boolean canRead(User user, String moduleName) {
        return hasPermission(user, moduleName, PermissionType.READ.getCode());
    }

    @Override
    public boolean canWrite(User user, String moduleName) {
        return hasPermission(user, moduleName, PermissionType.WRITE.getCode());
    }

    @Override
    public boolean canDelete(User user, String moduleName) {
        return hasPermission(user, moduleName, PermissionType.DELETE.getCode());
    }

    @Override
    public boolean canPrint(User user, String moduleName) {
        return hasPermission(user, moduleName, PermissionType.PRINT.getCode());
    }

    @Override
    public boolean canExport(User user, String moduleName) {
        return hasPermission(user, moduleName, PermissionType.EXPORT.getCode());
    }

    @Override
    public boolean isAdmin(User user) {
        return user != null && user.getRole() != null && 
               (ADMIN_ROLE.equals(user.getRole().getName()) || isSystemAdmin(user));
    }

    @Override
    public boolean isSystemAdmin(User user) {
        return user != null && user.getRole() != null && 
               SYSTEM_ADMIN_ROLE.equals(user.getRole().getName());
    }

    @Override
    public void logAudit(User user, AuditLog.ActionType actionType, String tableName, Long recordId,
                        String oldValues, String newValues, String ipAddress, String userAgent) {
        try {
            AuditLog auditLog = new AuditLog(user, actionType, tableName, recordId);
            auditLog.setOldValues(oldValues);
            auditLog.setNewValues(newValues);
            auditLog.setIpAddress(ipAddress);
            auditLog.setUserAgent(userAgent);
            
            auditLogRepository.save(auditLog);
            
            logger.debug("تم تسجيل عملية التدقيق: {} - {} - {}", 
                user != null ? user.getUsername() : "system", actionType, tableName);
                
        } catch (Exception e) {
            logger.error("خطأ في تسجيل عملية التدقيق", e);
        }
    }

    @Override
    public void logSuccessfulLogin(User user, String ipAddress, String userAgent) {
        logAudit(user, AuditLog.ActionType.LOGIN, null, null, null, null, ipAddress, userAgent);
        
        // إزالة محاولات تسجيل الدخول الفاشلة
        String key = user.getUsername() + ":" + ipAddress;
        loginAttempts.remove(key);
    }

    @Override
    public void logFailedLogin(String username, String ipAddress, String userAgent) {
        // تسجيل المحاولة الفاشلة
        AuditLog auditLog = AuditLog.createLoginFailedLog(username, ipAddress, userAgent);
        auditLogRepository.save(auditLog);
        
        // تتبع محاولات تسجيل الدخول الفاشلة
        String key = username + ":" + ipAddress;
        LoginAttemptInfo attemptInfo = loginAttempts.computeIfAbsent(key, 
            k -> new LoginAttemptInfo());
        attemptInfo.incrementAttempts();
        
        // حظر IP إذا تجاوز الحد المسموح
        if (attemptInfo.getAttempts() >= MAX_LOGIN_ATTEMPTS) {
            blockIpTemporarily(ipAddress, BLOCK_DURATION_MINUTES);
            logger.warn("تم حظر عنوان IP {} بسبب محاولات تسجيل دخول فاشلة متكررة", ipAddress);
        }
    }

    @Override
    public void logLogout(User user, String ipAddress, String userAgent) {
        logAudit(user, AuditLog.ActionType.LOGOUT, null, null, null, null, ipAddress, userAgent);
    }

    @Override
    public PasswordStrengthResult checkPasswordStrength(String password) {
        PasswordUtil.PasswordStrength strength = PasswordUtil.checkPasswordStrength(password);
        
        int score = strength.getLevel();
        String feedback = generatePasswordFeedback(password, strength);
        
        return new PasswordStrengthResult(
            PasswordStrength.valueOf(strength.name()), 
            score, 
            feedback
        );
    }

    @Override
    public boolean isPasswordExpired(User user, int maxDaysOld) {
        if (user.getPasswordChangedDate() == null) {
            return true; // إذا لم يتم تسجيل تاريخ تغيير كلمة المرور
        }
        
        long daysSinceChange = ChronoUnit.DAYS.between(
            user.getPasswordChangedDate().toLocalDate(), 
            LocalDateTime.now().toLocalDate()
        );
        
        return daysSinceChange > maxDaysOld;
    }

    @Override
    public boolean needsPasswordChange(User user) {
        // التحقق من انتهاء صلاحية كلمة المرور (90 يوم)
        return isPasswordExpired(user, 90);
    }

    @Override
    public String encryptSensitiveData(String data) {
        try {
            Cipher cipher = Cipher.getInstance(ENCRYPTION_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, encryptionKey);
            byte[] encryptedData = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedData);
        } catch (Exception e) {
            logger.error("خطأ في تشفير البيانات", e);
            throw new SecurityException("فشل في تشفير البيانات", e);
        }
    }

    @Override
    public String decryptSensitiveData(String encryptedData) {
        try {
            Cipher cipher = Cipher.getInstance(ENCRYPTION_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, encryptionKey);
            byte[] decodedData = Base64.getDecoder().decode(encryptedData);
            byte[] decryptedData = cipher.doFinal(decodedData);
            return new String(decryptedData, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("خطأ في فك تشفير البيانات", e);
            throw new SecurityException("فشل في فك تشفير البيانات", e);
        }
    }

    @Override
    public String generateSessionToken(User user) {
        String token = generateRandomToken();
        SessionInfo sessionInfo = new SessionInfo(user.getId(), LocalDateTime.now());
        sessionTokens.put(token, sessionInfo);
        
        // تنظيف الرموز المنتهية الصلاحية
        cleanupExpiredTokens();
        
        return token;
    }

    @Override
    public boolean validateSessionToken(String token, User user) {
        SessionInfo sessionInfo = sessionTokens.get(token);
        if (sessionInfo == null) {
            return false;
        }
        
        // التحقق من انتهاء صلاحية الرمز (24 ساعة)
        if (sessionInfo.getCreatedAt().isBefore(LocalDateTime.now().minusHours(24))) {
            sessionTokens.remove(token);
            return false;
        }
        
        return sessionInfo.getUserId().equals(user.getId());
    }

    @Override
    public void invalidateSessionToken(String token) {
        sessionTokens.remove(token);
    }

    @Override
    public String getRealIpAddress(String forwardedFor, String remoteAddr) {
        if (forwardedFor != null && !forwardedFor.isEmpty()) {
            // أخذ أول IP في حالة وجود عدة IPs
            return forwardedFor.split(",")[0].trim();
        }
        return remoteAddr;
    }

    @Override
    public boolean isSuspiciousLoginAttempt(String username, String ipAddress) {
        String key = username + ":" + ipAddress;
        LoginAttemptInfo attemptInfo = loginAttempts.get(key);
        return attemptInfo != null && attemptInfo.getAttempts() >= 3;
    }

    @Override
    public void blockIpTemporarily(String ipAddress, int minutes) {
        LocalDateTime blockUntil = LocalDateTime.now().plusMinutes(minutes);
        blockedIps.put(ipAddress, blockUntil);
    }

    @Override
    public boolean isIpBlocked(String ipAddress) {
        LocalDateTime blockUntil = blockedIps.get(ipAddress);
        if (blockUntil == null) {
            return false;
        }
        
        if (blockUntil.isBefore(LocalDateTime.now())) {
            // انتهت فترة الحظر
            blockedIps.remove(ipAddress);
            return false;
        }
        
        return true;
    }

    /**
     * إنشاء مفتاح التشفير
     */
    private SecretKey generateEncryptionKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(ENCRYPTION_ALGORITHM);
            keyGenerator.init(256);
            return keyGenerator.generateKey();
        } catch (Exception e) {
            logger.error("خطأ في إنشاء مفتاح التشفير", e);
            // استخدام مفتاح افتراضي (غير آمن للإنتاج)
            byte[] key = "MySecretKey12345".getBytes(StandardCharsets.UTF_8);
            return new SecretKeySpec(key, ENCRYPTION_ALGORITHM);
        }
    }

    /**
     * إنشاء رمز عشوائي
     */
    private String generateRandomToken() {
        SecureRandom random = new SecureRandom();
        byte[] tokenBytes = new byte[32];
        random.nextBytes(tokenBytes);
        return Base64.getEncoder().encodeToString(tokenBytes);
    }

    /**
     * تنظيف الرموز المنتهية الصلاحية
     */
    private void cleanupExpiredTokens() {
        LocalDateTime cutoff = LocalDateTime.now().minusHours(24);
        sessionTokens.entrySet().removeIf(entry -> 
            entry.getValue().getCreatedAt().isBefore(cutoff));
    }

    /**
     * إنشاء ملاحظات حول قوة كلمة المرور
     */
    private String generatePasswordFeedback(String password, PasswordUtil.PasswordStrength strength) {
        StringBuilder feedback = new StringBuilder();
        feedback.append("قوة كلمة المرور: ").append(strength.getDescription()).append("\n");
        
        if (password.length() < 8) {
            feedback.append("- يجب أن تكون 8 أحرف على الأقل\n");
        }
        if (!password.matches(".*[A-Z].*")) {
            feedback.append("- يجب أن تحتوي على حرف كبير\n");
        }
        if (!password.matches(".*[a-z].*")) {
            feedback.append("- يجب أن تحتوي على حرف صغير\n");
        }
        if (!password.matches(".*[0-9].*")) {
            feedback.append("- يجب أن تحتوي على رقم\n");
        }
        if (!password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) {
            feedback.append("- يُنصح بإضافة رموز خاصة\n");
        }
        
        return feedback.toString();
    }

    /**
     * معلومات محاولة تسجيل الدخول
     */
    private static class LoginAttemptInfo {
        private int attempts = 0;
        private LocalDateTime lastAttempt = LocalDateTime.now();

        public void incrementAttempts() {
            this.attempts++;
            this.lastAttempt = LocalDateTime.now();
        }

        public int getAttempts() { return attempts; }
        public LocalDateTime getLastAttempt() { return lastAttempt; }
    }

    /**
     * معلومات الجلسة
     */
    private static class SessionInfo {
        private final Long userId;
        private final LocalDateTime createdAt;

        public SessionInfo(Long userId, LocalDateTime createdAt) {
            this.userId = userId;
            this.createdAt = createdAt;
        }

        public Long getUserId() { return userId; }
        public LocalDateTime getCreatedAt() { return createdAt; }
    }
}
