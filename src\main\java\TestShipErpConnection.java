import java.sql.*;

public class TestShipErpConnection {
    public static void main(String[] args) {
        try {
            System.out.println("🔗 محاولة الاتصال بـ SHIP_ERP...");
            Class.forName("oracle.jdbc.driver.OracleDriver");
            Connection conn = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            
            System.out.println("✅ نجح الاتصال بـ SHIP_ERP");
            
            // اختبار استعلام بسيط
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT USER FROM DUAL");
            if (rs.next()) {
                System.out.println("📊 المستخدم الحالي: " + rs.getString(1));
            }
            
            // عرض عدد الجداول
            rs = stmt.executeQuery("SELECT COUNT(*) FROM USER_TABLES");
            if (rs.next()) {
                System.out.println("📊 عدد الجداول: " + rs.getInt(1));
            }
            
            conn.close();
            
        } catch (Exception e) {
            System.out.println("❌ فشل الاتصال: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
