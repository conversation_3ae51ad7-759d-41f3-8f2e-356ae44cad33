/**
 * بيانات وحدة القياس
 */
public class UnitOfMeasureData {
    private String code;
    private String nameAr;
    private String nameEn;
    private String symbol;
    private boolean active;
    private String parentCode;
    private double conversionFactor;
    private boolean isBase;
    private String description;
    
    public UnitOfMeasureData(String code, String nameAr, String nameEn, String symbol, 
                           boolean active, String parentCode, double conversionFactor, 
                           boolean isBase, String description) {
        this.code = code;
        this.nameAr = nameAr;
        this.nameEn = nameEn;
        this.symbol = symbol;
        this.active = active;
        this.parentCode = parentCode;
        this.conversionFactor = conversionFactor;
        this.isBase = isBase;
        this.description = description;
    }
    
    // Getters
    public String getCode() { return code; }
    public String getNameAr() { return nameAr; }
    public String getNameEn() { return nameEn; }
    public String getSymbol() { return symbol; }
    public boolean isActive() { return active; }
    public String getParentCode() { return parentCode; }
    public double getConversionFactor() { return conversionFactor; }
    public boolean isBase() { return isBase; }
    public String getDescription() { return description; }
    
    // Setters
    public void setCode(String code) { this.code = code; }
    public void setNameAr(String nameAr) { this.nameAr = nameAr; }
    public void setNameEn(String nameEn) { this.nameEn = nameEn; }
    public void setSymbol(String symbol) { this.symbol = symbol; }
    public void setActive(boolean active) { this.active = active; }
    public void setParentCode(String parentCode) { this.parentCode = parentCode; }
    public void setConversionFactor(double conversionFactor) { this.conversionFactor = conversionFactor; }
    public void setBase(boolean base) { this.isBase = base; }
    public void setDescription(String description) { this.description = description; }
}
