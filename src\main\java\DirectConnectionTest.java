import java.sql.*;
import java.util.Properties;

/**
 * اختبار الاتصال المباشر بقاعدة البيانات
 * Direct Database Connection Test
 */
public class DirectConnectionTest {
    
    public static void main(String[] args) {
        System.out.println("🔄 بدء اختبار الاتصال المباشر...");
        System.out.println("=" + "=".repeat(50));
        
        // إعدادات الاتصال
        String url = "*************************************";
        String username = "ias20251";
        String password = "ys123";
        
        System.out.println("📋 إعدادات الاتصال:");
        System.out.println("   URL: " + url);
        System.out.println("   المستخدم: " + username);
        System.out.println("   كلمة المرور: " + password);
        System.out.println();
        
        // اختبار 1: فحص مكتبات Oracle
        testOracleLibraries();
        
        // اختبار 2: الاتصال المباشر
        testDirectConnection(url, username, password);
        
        // اختبار 3: الاتصال مع خصائص محسنة
        testEnhancedConnection(url, username, password);
        
        // اختبار 4: فحص الجداول
        testTableAccess(url, username, password);
    }
    
    /**
     * اختبار مكتبات Oracle
     */
    private static void testOracleLibraries() {
        System.out.println("🔍 اختبار مكتبات Oracle...");
        
        try {
            // اختبار تحميل تعريف Oracle
            Class.forName("oracle.jdbc.driver.OracleDriver");
            System.out.println("✅ تم تحميل تعريف Oracle JDBC بنجاح");
            
            // اختبار مكتبة orai18n
            try {
                Class.forName("oracle.i18n.text.OraCharsetMap");
                System.out.println("✅ مكتبة orai18n متاحة");
            } catch (ClassNotFoundException e) {
                System.out.println("⚠️ مكتبة orai18n غير متاحة - قد تحدث مشاكل مع الأحرف العربية");
            }
            
        } catch (ClassNotFoundException e) {
            System.out.println("❌ فشل في تحميل تعريف Oracle: " + e.getMessage());
            return;
        }
        
        System.out.println();
    }
    
    /**
     * اختبار الاتصال المباشر
     */
    private static void testDirectConnection(String url, String username, String password) {
        System.out.println("🔗 اختبار الاتصال المباشر...");
        
        try {
            Connection connection = DriverManager.getConnection(url, username, password);
            
            if (connection != null && !connection.isClosed()) {
                System.out.println("✅ نجح الاتصال المباشر!");
                
                // اختبار استعلام بسيط
                try (Statement stmt = connection.createStatement();
                     ResultSet rs = stmt.executeQuery("SELECT 1 FROM DUAL")) {
                    
                    if (rs.next()) {
                        System.out.println("✅ استعلام DUAL نجح");
                    }
                }
                
                // معلومات قاعدة البيانات
                DatabaseMetaData metaData = connection.getMetaData();
                System.out.println("📋 معلومات قاعدة البيانات:");
                System.out.println("   المنتج: " + metaData.getDatabaseProductName());
                System.out.println("   الإصدار: " + metaData.getDatabaseProductVersion());
                System.out.println("   المستخدم: " + metaData.getUserName());
                
                connection.close();
            } else {
                System.out.println("❌ فشل في إنشاء الاتصال");
            }
            
        } catch (SQLException e) {
            System.out.println("❌ خطأ في الاتصال المباشر:");
            System.out.println("   الرمز: " + e.getErrorCode());
            System.out.println("   الرسالة: " + e.getMessage());
            
            // تشخيص أخطاء شائعة
            if (e.getErrorCode() == 17002) {
                System.out.println("💡 مشكلة في الشبكة أو عنوان الخادم");
            } else if (e.getErrorCode() == 1017) {
                System.out.println("💡 اسم المستخدم أو كلمة المرور خطأ");
            } else if (e.getErrorCode() == 12505) {
                System.out.println("💡 اسم الخدمة (SID) خطأ");
            }
        }
        
        System.out.println();
    }
    
    /**
     * اختبار الاتصال مع خصائص محسنة
     */
    private static void testEnhancedConnection(String url, String username, String password) {
        System.out.println("⚙️ اختبار الاتصال مع خصائص محسنة...");
        
        try {
            Properties props = new Properties();
            props.setProperty("user", username);
            props.setProperty("password", password);
            props.setProperty("oracle.jdbc.ReadTimeout", "30000");
            props.setProperty("oracle.net.CONNECT_TIMEOUT", "30000");
            props.setProperty("oracle.jdbc.implicitStatementCacheSize", "20");
            
            Connection connection = DriverManager.getConnection(url, props);
            
            if (connection != null && !connection.isClosed()) {
                System.out.println("✅ نجح الاتصال المحسن!");
                connection.close();
            }
            
        } catch (SQLException e) {
            System.out.println("❌ خطأ في الاتصال المحسن: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * اختبار الوصول للجداول
     */
    private static void testTableAccess(String url, String username, String password) {
        System.out.println("📋 اختبار الوصول للجداول...");
        
        try (Connection connection = DriverManager.getConnection(url, username, password)) {
            
            // اختبار الوصول لجدول IAS_ITM_MST
            String query = "SELECT COUNT(*) as total FROM IAS20251.IAS_ITM_MST WHERE ROWNUM <= 1";
            
            try (PreparedStatement stmt = connection.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next()) {
                    System.out.println("✅ تم الوصول لجدول IAS_ITM_MST بنجاح");
                }
                
            } catch (SQLException e) {
                System.out.println("❌ فشل في الوصول لجدول IAS_ITM_MST:");
                System.out.println("   " + e.getMessage());
                
                // اختبار بديل - البحث عن الجداول المتاحة
                try {
                    DatabaseMetaData metaData = connection.getMetaData();
                    ResultSet tables = metaData.getTables(null, username.toUpperCase(), "%", new String[]{"TABLE"});
                    
                    System.out.println("📋 الجداول المتاحة للمستخدم " + username + ":");
                    int count = 0;
                    while (tables.next() && count < 10) {
                        System.out.println("   - " + tables.getString("TABLE_NAME"));
                        count++;
                    }
                    
                    if (count == 0) {
                        System.out.println("   لا توجد جداول متاحة");
                    }
                    
                } catch (SQLException e2) {
                    System.out.println("❌ فشل في الحصول على قائمة الجداول: " + e2.getMessage());
                }
            }
            
        } catch (SQLException e) {
            System.out.println("❌ فشل في اختبار الجداول: " + e.getMessage());
        }
        
        System.out.println();
        System.out.println("🏁 انتهى اختبار الاتصال");
    }
}
