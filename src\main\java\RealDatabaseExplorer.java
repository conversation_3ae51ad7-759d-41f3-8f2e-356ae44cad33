import java.sql.*;
import javax.swing.*;
import java.awt.*;
import javax.swing.table.DefaultTableModel;

/**
 * استكشاف قاعدة البيانات الحقيقية
 * Server: localhost
 * Database: orcl  
 * User: ship_erp
 * Password: ship_erp_password
 */
public class RealDatabaseExplorer extends JFrame {
    
    private Connection connection;
    private JTextArea logArea;
    private JTable tablesTable;
    private DefaultTableModel tablesModel;
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                new RealDatabaseExplorer().setVisible(true);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, "خطأ في بدء المستكشف: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    public RealDatabaseExplorer() throws Exception {
        connectToRealDatabase();
        initializeGUI();
        exploreDatabase();
    }
    
    /**
     * الاتصال بقاعدة البيانات الحقيقية
     */
    private void connectToRealDatabase() throws Exception {
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
            
            log("🔗 الاتصال بقاعدة البيانات الحقيقية...");
            log("📍 Server: localhost");
            log("📍 Database: orcl");
            log("📍 User: ship_erp");
            
            connection = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            
            log("✅ تم الاتصال بنجاح!");
            
            // معلومات قاعدة البيانات
            DatabaseMetaData metaData = connection.getMetaData();
            log("📋 معلومات قاعدة البيانات:");
            log("   - اسم قاعدة البيانات: " + metaData.getDatabaseProductName());
            log("   - إصدار قاعدة البيانات: " + metaData.getDatabaseProductVersion());
            log("   - اسم المستخدم: " + metaData.getUserName());
            
        } catch (Exception e) {
            log("❌ فشل الاتصال: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    private void initializeGUI() {
        setTitle("مستكشف قاعدة البيانات الحقيقية - SHIP_ERP");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        
        setLayout(new BorderLayout());
        
        // لوحة التحكم
        JPanel controlPanel = new JPanel(new FlowLayout());
        controlPanel.setBorder(BorderFactory.createTitledBorder("التحكم"));
        
        JButton refreshBtn = new JButton("تحديث الجداول");
        refreshBtn.addActionListener(e -> exploreDatabase());
        controlPanel.add(refreshBtn);
        
        JButton searchGroupsBtn = new JButton("البحث عن جداول المجموعات");
        searchGroupsBtn.addActionListener(e -> searchForGroupTables());
        controlPanel.add(searchGroupsBtn);
        
        JButton searchItemsBtn = new JButton("البحث عن جداول الأصناف");
        searchItemsBtn.addActionListener(e -> searchForItemTables());
        controlPanel.add(searchItemsBtn);
        
        JButton analyzeBtn = new JButton("تحليل شامل");
        analyzeBtn.addActionListener(e -> comprehensiveAnalysis());
        controlPanel.add(analyzeBtn);
        
        add(controlPanel, BorderLayout.NORTH);
        
        // جدول الجداول
        tablesModel = new DefaultTableModel(new String[]{"اسم الجدول", "النوع", "عدد الصفوف", "التعليق"}, 0);
        tablesTable = new JTable(tablesModel);
        tablesTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                showTableDetails();
            }
        });
        
        JScrollPane tablesScroll = new JScrollPane(tablesTable);
        tablesScroll.setBorder(BorderFactory.createTitledBorder("الجداول الموجودة"));
        
        // منطقة السجل
        logArea = new JTextArea(15, 80);
        logArea.setEditable(false);
        logArea.setFont(new Font("Arial", Font.PLAIN, 12));
        JScrollPane logScroll = new JScrollPane(logArea);
        logScroll.setBorder(BorderFactory.createTitledBorder("سجل الاستكشاف"));
        
        // تقسيم الشاشة
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT, tablesScroll, logScroll);
        splitPane.setDividerLocation(400);
        add(splitPane, BorderLayout.CENTER);
        
        log("🎉 تم تهيئة مستكشف قاعدة البيانات الحقيقية");
    }
    
    /**
     * استكشاف قاعدة البيانات
     */
    private void exploreDatabase() {
        try {
            log("\n🔍 استكشاف جداول قاعدة البيانات...");
            
            tablesModel.setRowCount(0);
            
            DatabaseMetaData metaData = connection.getMetaData();
            
            // الحصول على جميع الجداول
            ResultSet tables = metaData.getTables(null, "SHIP_ERP", "%", new String[]{"TABLE"});
            
            int tableCount = 0;
            while (tables.next()) {
                String tableName = tables.getString("TABLE_NAME");
                String tableType = tables.getString("TABLE_TYPE");
                String remarks = tables.getString("REMARKS");
                
                // الحصول على عدد الصفوف
                int rowCount = 0;
                try {
                    Statement stmt = connection.createStatement();
                    ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName);
                    if (rs.next()) {
                        rowCount = rs.getInt(1);
                    }
                    rs.close();
                    stmt.close();
                } catch (SQLException e) {
                    // تجاهل الأخطاء
                }
                
                tablesModel.addRow(new Object[]{tableName, tableType, rowCount, remarks});
                tableCount++;
            }
            tables.close();
            
            log("✅ تم العثور على " + tableCount + " جدول");
            
        } catch (SQLException e) {
            log("❌ خطأ في استكشاف قاعدة البيانات: " + e.getMessage());
        }
    }
    
    /**
     * البحث عن جداول المجموعات
     */
    private void searchForGroupTables() {
        log("\n🔍 البحث عن جداول المجموعات...");
        
        String[] searchTerms = {"GROUP", "GRP", "CATEGORY", "CAT", "CLASS"};
        boolean found = false;
        
        for (int i = 0; i < tablesModel.getRowCount(); i++) {
            String tableName = (String) tablesModel.getValueAt(i, 0);
            
            for (String term : searchTerms) {
                if (tableName.toUpperCase().contains(term)) {
                    found = true;
                    int rowCount = (Integer) tablesModel.getValueAt(i, 2);
                    log("📋 جدول محتمل للمجموعات: " + tableName + " (" + rowCount + " صف)");
                    
                    // فحص أعمدة الجدول
                    analyzeTableStructure(tableName);
                    break;
                }
            }
        }
        
        if (!found) {
            log("⚠️ لم يتم العثور على جداول مجموعات واضحة");
            log("💡 جرب البحث اليدوي في أسماء الجداول");
        }
    }
    
    /**
     * البحث عن جداول الأصناف
     */
    private void searchForItemTables() {
        log("\n🔍 البحث عن جداول الأصناف...");
        
        String[] searchTerms = {"ITEM", "ITM", "PRODUCT", "PROD", "MATERIAL", "MAT"};
        boolean found = false;
        
        for (int i = 0; i < tablesModel.getRowCount(); i++) {
            String tableName = (String) tablesModel.getValueAt(i, 0);
            
            for (String term : searchTerms) {
                if (tableName.toUpperCase().contains(term)) {
                    found = true;
                    int rowCount = (Integer) tablesModel.getValueAt(i, 2);
                    log("📋 جدول محتمل للأصناف: " + tableName + " (" + rowCount + " صف)");
                    
                    // فحص أعمدة الجدول
                    analyzeTableStructure(tableName);
                    break;
                }
            }
        }
        
        if (!found) {
            log("⚠️ لم يتم العثور على جداول أصناف واضحة");
            log("💡 جرب البحث اليدوي في أسماء الجداول");
        }
    }
    
    /**
     * تحليل شامل
     */
    private void comprehensiveAnalysis() {
        log("\n📊 تحليل شامل لقاعدة البيانات...");
        
        log("📋 إجمالي الجداول: " + tablesModel.getRowCount());
        
        // تحليل الجداول حسب عدد الصفوف
        log("\n📊 الجداول حسب عدد الصفوف:");
        for (int i = 0; i < tablesModel.getRowCount(); i++) {
            String tableName = (String) tablesModel.getValueAt(i, 0);
            int rowCount = (Integer) tablesModel.getValueAt(i, 2);
            
            if (rowCount > 0) {
                log("  " + tableName + ": " + rowCount + " صف");
            }
        }
        
        // البحث عن جداول مهمة
        log("\n🔍 البحث عن جداول مهمة...");
        searchForGroupTables();
        searchForItemTables();
    }
    
    /**
     * تحليل بنية جدول
     */
    private void analyzeTableStructure(String tableName) {
        try {
            log("  🔍 تحليل بنية الجدول: " + tableName);
            
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet columns = metaData.getColumns(null, "SHIP_ERP", tableName, null);
            
            log("    📋 الأعمدة:");
            while (columns.next()) {
                String columnName = columns.getString("COLUMN_NAME");
                String dataType = columns.getString("TYPE_NAME");
                int columnSize = columns.getInt("COLUMN_SIZE");
                String nullable = columns.getString("IS_NULLABLE");
                
                log("      - " + columnName + " (" + dataType + 
                    (columnSize > 0 ? "(" + columnSize + ")" : "") + 
                    ", " + (nullable.equals("YES") ? "NULL" : "NOT NULL") + ")");
            }
            columns.close();
            
            // عرض عينة من البيانات
            try {
                Statement stmt = connection.createStatement();
                ResultSet rs = stmt.executeQuery("SELECT * FROM " + tableName + " WHERE ROWNUM <= 3");
                
                log("    📄 عينة من البيانات:");
                ResultSetMetaData rsmd = rs.getMetaData();
                int columnCount = rsmd.getColumnCount();
                
                while (rs.next()) {
                    StringBuilder row = new StringBuilder("      ");
                    for (int i = 1; i <= columnCount; i++) {
                        String value = rs.getString(i);
                        if (value != null && value.length() > 15) {
                            value = value.substring(0, 15) + "...";
                        }
                        row.append(rsmd.getColumnName(i)).append("=").append(value).append(" | ");
                    }
                    log(row.toString());
                }
                rs.close();
                stmt.close();
            } catch (SQLException e) {
                log("      ⚠️ لا يمكن قراءة البيانات: " + e.getMessage());
            }
            
        } catch (SQLException e) {
            log("    ❌ خطأ في تحليل الجدول: " + e.getMessage());
        }
    }
    
    /**
     * عرض تفاصيل الجدول المحدد
     */
    private void showTableDetails() {
        int selectedRow = tablesTable.getSelectedRow();
        if (selectedRow == -1) return;
        
        String tableName = (String) tablesModel.getValueAt(selectedRow, 0);
        log("\n🔍 تفاصيل الجدول المحدد: " + tableName);
        
        analyzeTableStructure(tableName);
    }
    
    /**
     * تسجيل رسالة
     */
    private void log(String message) {
        SwingUtilities.invokeLater(() -> {
            logArea.append(message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
        System.out.println(message);
    }
}
