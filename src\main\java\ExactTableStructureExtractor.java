import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * استخراج البنية الحقيقية والدقيقة للجداول IAS_ITM_MST و IAS_ITM_DTL بالحرف الواحد - بدون أي اجتهاد
 * أو تعديل
 */
public class ExactTableStructureExtractor {

    private Connection ias20251Connection;
    private Connection shipErpConnection;

    public static void main(String[] args) {
        try {
            new ExactTableStructureExtractor().extractAndCreateExact();
        } catch (Exception e) {
            System.err.println("❌ خطأ: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public ExactTableStructureExtractor() throws Exception {
        initializeConnections();
    }

    /**
     * تهيئة الاتصالات
     */
    private void initializeConnections() throws Exception {
        Class.forName("oracle.jdbc.driver.OracleDriver");

        // اتصال IAS20251
        ias20251Connection = DriverManager.getConnection("*************************************",
                "ias20251", "ys123");
        System.out.println("✅ تم الاتصال بـ IAS20251");

        // اتصال SHIP_ERP
        shipErpConnection = DriverManager.getConnection("*************************************",
                "ship_erp", "ship_erp_password");
        System.out.println("✅ تم الاتصال بـ SHIP_ERP");
    }

    /**
     * استخراج وإنشاء الجداول بالضبط
     */
    public void extractAndCreateExact() throws SQLException {
        System.out.println("\n🔍 استخراج البنية الحقيقية للجداول...");

        // استخراج IAS_ITM_MST
        System.out.println("\n📋 استخراج بنية IAS_ITM_MST:");
        extractExactTableStructure("IAS_ITM_MST");

        // استخراج IAS_ITM_DTL
        System.out.println("\n📋 استخراج بنية IAS_ITM_DTL:");
        extractExactTableStructure("IAS_ITM_DTL");

        // إنشاء الجداول بالضبط
        createExactTables();
    }

    /**
     * استخراج بنية جدول بالضبط
     */
    private void extractExactTableStructure(String tableName) throws SQLException {
        System.out.println("🔍 فحص جدول: " + tableName);

        // استخدام استعلام مباشر بدلاً من DatabaseMetaData لتجنب مشكلة Stream
        String sql = """
                    SELECT
                        COLUMN_ID,
                        COLUMN_NAME,
                        DATA_TYPE,
                        DATA_LENGTH,
                        DATA_PRECISION,
                        DATA_SCALE,
                        NULLABLE,
                        DATA_DEFAULT
                    FROM ALL_TAB_COLUMNS
                    WHERE OWNER = 'IAS20251' AND TABLE_NAME = ?
                    ORDER BY COLUMN_ID
                """;

        PreparedStatement pstmt = ias20251Connection.prepareStatement(sql);
        pstmt.setString(1, tableName);
        ResultSet columns = pstmt.executeQuery();

        System.out.println("📋 الأعمدة الحقيقية:");
        int columnCount = 0;
        while (columns.next()) {
            columnCount++;
            int columnId = columns.getInt("COLUMN_ID");
            String columnName = columns.getString("COLUMN_NAME");
            String dataType = columns.getString("DATA_TYPE");
            int dataLength = columns.getInt("DATA_LENGTH");
            int dataPrecision = columns.getInt("DATA_PRECISION");
            int dataScale = columns.getInt("DATA_SCALE");
            String nullable = columns.getString("NULLABLE");
            String dataDefault = columns.getString("DATA_DEFAULT");

            System.out.printf("  %2d. %-25s %-15s", columnId, columnName, dataType);

            // تحديد حجم البيانات بدقة
            if (dataType.equals("VARCHAR2") || dataType.equals("CHAR")) {
                System.out.printf("(%d)", dataLength);
            } else if (dataType.equals("NUMBER")) {
                if (dataPrecision > 0) {
                    if (dataScale > 0) {
                        System.out.printf("(%d,%d)", dataPrecision, dataScale);
                    } else {
                        System.out.printf("(%d)", dataPrecision);
                    }
                }
            }

            System.out.printf(" %s", nullable.equals("Y") ? "NULL" : "NOT NULL");
            if (dataDefault != null && !dataDefault.trim().isEmpty()) {
                System.out.printf(" DEFAULT %s", dataDefault.trim());
            }
            System.out.println();
        }
        columns.close();
        pstmt.close();

        System.out.println("📊 إجمالي الأعمدة: " + columnCount);

        // الحصول على المفاتيح الأساسية باستخدام استعلام مباشر
        String pkSql = """
                    SELECT COLUMN_NAME, CONSTRAINT_NAME
                    FROM ALL_CONS_COLUMNS
                    WHERE OWNER = 'IAS20251' AND TABLE_NAME = ?
                    AND CONSTRAINT_NAME IN (
                        SELECT CONSTRAINT_NAME FROM ALL_CONSTRAINTS
                        WHERE OWNER = 'IAS20251' AND TABLE_NAME = ? AND CONSTRAINT_TYPE = 'P'
                    )
                    ORDER BY POSITION
                """;

        PreparedStatement pkStmt = ias20251Connection.prepareStatement(pkSql);
        pkStmt.setString(1, tableName);
        pkStmt.setString(2, tableName);
        ResultSet primaryKeys = pkStmt.executeQuery();

        System.out.println("🔑 المفاتيح الأساسية:");
        while (primaryKeys.next()) {
            String pkColumn = primaryKeys.getString("COLUMN_NAME");
            String pkName = primaryKeys.getString("CONSTRAINT_NAME");
            System.out.println("  - " + pkColumn + " (PK: " + pkName + ")");
        }
        primaryKeys.close();
        pkStmt.close();

        // الحصول على المفاتيح الخارجية باستخدام استعلام مباشر
        String fkSql =
                """
                            SELECT
                                acc.COLUMN_NAME,
                                ac.CONSTRAINT_NAME,
                                ac.R_CONSTRAINT_NAME,
                                acc2.TABLE_NAME AS REF_TABLE,
                                acc2.COLUMN_NAME AS REF_COLUMN
                            FROM ALL_CONSTRAINTS ac
                            JOIN ALL_CONS_COLUMNS acc ON ac.CONSTRAINT_NAME = acc.CONSTRAINT_NAME AND ac.OWNER = acc.OWNER
                            JOIN ALL_CONSTRAINTS ac2 ON ac.R_CONSTRAINT_NAME = ac2.CONSTRAINT_NAME AND ac.R_OWNER = ac2.OWNER
                            JOIN ALL_CONS_COLUMNS acc2 ON ac2.CONSTRAINT_NAME = acc2.CONSTRAINT_NAME AND ac2.OWNER = acc2.OWNER
                            WHERE ac.OWNER = 'IAS20251' AND ac.TABLE_NAME = ? AND ac.CONSTRAINT_TYPE = 'R'
                        """;

        PreparedStatement fkStmt = ias20251Connection.prepareStatement(fkSql);
        fkStmt.setString(1, tableName);
        ResultSet foreignKeys = fkStmt.executeQuery();

        System.out.println("🔗 المفاتيح الخارجية:");
        while (foreignKeys.next()) {
            String fkColumn = foreignKeys.getString("COLUMN_NAME");
            String pkTable = foreignKeys.getString("REF_TABLE");
            String pkColumn = foreignKeys.getString("REF_COLUMN");
            String fkName = foreignKeys.getString("CONSTRAINT_NAME");
            System.out.println(
                    "  - " + fkColumn + " → " + pkTable + "." + pkColumn + " (FK: " + fkName + ")");
        }
        foreignKeys.close();
        fkStmt.close();

        // عرض عينة من البيانات الحقيقية
        System.out.println("📄 عينة من البيانات الحقيقية:");
        Statement stmt = ias20251Connection.createStatement();
        ResultSet rs = stmt.executeQuery("SELECT * FROM " + tableName + " WHERE ROWNUM <= 2");

        ResultSetMetaData rsmd = rs.getMetaData();
        int colCount = rsmd.getColumnCount();

        // عرض أسماء الأعمدة
        System.out.print("  ");
        for (int i = 1; i <= colCount; i++) {
            System.out.printf("%-15s ", rsmd.getColumnName(i));
        }
        System.out.println();

        // عرض البيانات
        while (rs.next()) {
            System.out.print("  ");
            for (int i = 1; i <= colCount; i++) {
                String value = rs.getString(i);
                if (value != null && value.length() > 12) {
                    value = value.substring(0, 12) + "...";
                }
                System.out.printf("%-15s ", value != null ? value : "NULL");
            }
            System.out.println();
        }

        rs.close();
        stmt.close();
    }

    /**
     * إنشاء الجداول بالضبط كما هي في المصدر
     */
    private void createExactTables() throws SQLException {
        System.out.println("\n🏗️ إنشاء الجداول مطابقة تماماً للمصدر...");

        Statement stmt = shipErpConnection.createStatement();

        // حذف الجداول القديمة
        try {
            stmt.execute("DROP TABLE IAS_ITM_DTL CASCADE CONSTRAINTS");
            System.out.println("🗑️ تم حذف IAS_ITM_DTL القديم");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        try {
            stmt.execute("DROP TABLE IAS_ITM_MST CASCADE CONSTRAINTS");
            System.out.println("🗑️ تم حذف IAS_ITM_MST القديم");
        } catch (SQLException e) {
            // الجدول غير موجود
        }

        // إنشاء IAS_ITM_MST بالضبط
        createExactIasMstTable(stmt);

        // إنشاء IAS_ITM_DTL بالضبط
        createExactIasDtlTable(stmt);

        // إنشاء المفاتيح والقيود بالضبط
        createExactConstraints(stmt);

        stmt.close();

        System.out.println("✅ تم إنشاء الجداول مطابقة تماماً للمصدر!");
    }

    /**
     * إنشاء جدول IAS_ITM_MST بالضبط
     */
    private void createExactIasMstTable(Statement stmt) throws SQLException {
        System.out.println("🔨 إنشاء IAS_ITM_MST مطابق تماماً...");

        // الحصول على البنية الدقيقة باستخدام استعلام مباشر
        String sql = """
                    SELECT
                        COLUMN_ID,
                        COLUMN_NAME,
                        DATA_TYPE,
                        DATA_LENGTH,
                        DATA_PRECISION,
                        DATA_SCALE,
                        NULLABLE,
                        DATA_DEFAULT
                    FROM ALL_TAB_COLUMNS
                    WHERE OWNER = 'IAS20251' AND TABLE_NAME = 'IAS_ITM_MST'
                    ORDER BY COLUMN_ID
                """;

        PreparedStatement pstmt = ias20251Connection.prepareStatement(sql);
        ResultSet columns = pstmt.executeQuery();

        StringBuilder createSQL = new StringBuilder("CREATE TABLE IAS_ITM_MST (\n");
        boolean first = true;

        while (columns.next()) {
            if (!first) {
                createSQL.append(",\n");
            }
            first = false;

            String columnName = columns.getString("COLUMN_NAME");
            String dataType = columns.getString("DATA_TYPE");
            int dataLength = columns.getInt("DATA_LENGTH");
            int dataPrecision = columns.getInt("DATA_PRECISION");
            int dataScale = columns.getInt("DATA_SCALE");
            String nullable = columns.getString("NULLABLE");
            String defaultValue = columns.getString("DATA_DEFAULT");

            createSQL.append("  ").append(columnName).append(" ");

            // نوع البيانات بالضبط
            if (dataType.equals("VARCHAR2")) {
                createSQL.append("VARCHAR2(").append(dataLength).append(")");
            } else if (dataType.equals("NUMBER")) {
                if (dataPrecision > 0 && dataScale > 0) {
                    createSQL.append("NUMBER(").append(dataPrecision).append(",").append(dataScale)
                            .append(")");
                } else if (dataPrecision > 0) {
                    createSQL.append("NUMBER(").append(dataPrecision).append(")");
                } else {
                    createSQL.append("NUMBER");
                }
            } else if (dataType.equals("DATE")) {
                createSQL.append("DATE");
            } else if (dataType.equals("CHAR")) {
                createSQL.append("CHAR(").append(dataLength).append(")");
            } else {
                createSQL.append(dataType);
                if (dataLength > 0) {
                    createSQL.append("(").append(dataLength).append(")");
                }
            }

            // NULL/NOT NULL
            if (nullable.equals("N")) {
                createSQL.append(" NOT NULL");
            }

            // القيمة الافتراضية
            if (defaultValue != null && !defaultValue.trim().isEmpty()) {
                createSQL.append(" DEFAULT ").append(defaultValue);
            }
        }
        columns.close();
        pstmt.close();

        createSQL.append("\n)");

        System.out.println("📝 SQL للجدول IAS_ITM_MST:");
        System.out.println(createSQL.toString());

        stmt.execute(createSQL.toString());
        System.out.println("✅ تم إنشاء IAS_ITM_MST بنجاح");
    }

    /**
     * إنشاء جدول IAS_ITM_DTL بالضبط
     */
    private void createExactIasDtlTable(Statement stmt) throws SQLException {
        System.out.println("🔨 إنشاء IAS_ITM_DTL مطابق تماماً...");

        // الحصول على البنية الدقيقة باستخدام استعلام مباشر
        String sql = """
                    SELECT
                        COLUMN_ID,
                        COLUMN_NAME,
                        DATA_TYPE,
                        DATA_LENGTH,
                        DATA_PRECISION,
                        DATA_SCALE,
                        NULLABLE,
                        DATA_DEFAULT
                    FROM ALL_TAB_COLUMNS
                    WHERE OWNER = 'IAS20251' AND TABLE_NAME = 'IAS_ITM_DTL'
                    ORDER BY COLUMN_ID
                """;

        PreparedStatement pstmt = ias20251Connection.prepareStatement(sql);
        ResultSet columns = pstmt.executeQuery();

        StringBuilder createSQL = new StringBuilder("CREATE TABLE IAS_ITM_DTL (\n");
        boolean first = true;

        while (columns.next()) {
            if (!first) {
                createSQL.append(",\n");
            }
            first = false;

            String columnName = columns.getString("COLUMN_NAME");
            String dataType = columns.getString("DATA_TYPE");
            int dataLength = columns.getInt("DATA_LENGTH");
            int dataPrecision = columns.getInt("DATA_PRECISION");
            int dataScale = columns.getInt("DATA_SCALE");
            String nullable = columns.getString("NULLABLE");
            String defaultValue = columns.getString("DATA_DEFAULT");

            createSQL.append("  ").append(columnName).append(" ");

            // نوع البيانات بالضبط
            if (dataType.equals("VARCHAR2")) {
                createSQL.append("VARCHAR2(").append(dataLength).append(")");
            } else if (dataType.equals("NUMBER")) {
                if (dataPrecision > 0 && dataScale > 0) {
                    createSQL.append("NUMBER(").append(dataPrecision).append(",").append(dataScale)
                            .append(")");
                } else if (dataPrecision > 0) {
                    createSQL.append("NUMBER(").append(dataPrecision).append(")");
                } else {
                    createSQL.append("NUMBER");
                }
            } else if (dataType.equals("DATE")) {
                createSQL.append("DATE");
            } else if (dataType.equals("CHAR")) {
                createSQL.append("CHAR(").append(dataLength).append(")");
            } else {
                createSQL.append(dataType);
                if (dataLength > 0) {
                    createSQL.append("(").append(dataLength).append(")");
                }
            }

            // NULL/NOT NULL
            if (nullable.equals("N")) {
                createSQL.append(" NOT NULL");
            }

            // القيمة الافتراضية
            if (defaultValue != null && !defaultValue.trim().isEmpty()) {
                createSQL.append(" DEFAULT ").append(defaultValue);
            }
        }
        columns.close();
        pstmt.close();

        createSQL.append("\n)");

        System.out.println("📝 SQL للجدول IAS_ITM_DTL:");
        System.out.println(createSQL.toString());

        stmt.execute(createSQL.toString());
        System.out.println("✅ تم إنشاء IAS_ITM_DTL بنجاح");
    }

    /**
     * إنشاء المفاتيح والقيود بالضبط
     */
    private void createExactConstraints(Statement stmt) throws SQLException {
        System.out.println("🔗 إنشاء المفاتيح والقيود بالضبط...");

        try {
            // المفاتيح الأساسية للجدول الأول باستخدام استعلام مباشر
            String pkSql =
                    """
                                SELECT COLUMN_NAME, CONSTRAINT_NAME
                                FROM ALL_CONS_COLUMNS
                                WHERE OWNER = 'IAS20251' AND TABLE_NAME = 'IAS_ITM_MST'
                                AND CONSTRAINT_NAME IN (
                                    SELECT CONSTRAINT_NAME FROM ALL_CONSTRAINTS
                                    WHERE OWNER = 'IAS20251' AND TABLE_NAME = 'IAS_ITM_MST' AND CONSTRAINT_TYPE = 'P'
                                )
                                ORDER BY POSITION
                            """;

            PreparedStatement pkStmt = ias20251Connection.prepareStatement(pkSql);
            ResultSet primaryKeys = pkStmt.executeQuery();

            StringBuilder pkColumns = new StringBuilder();
            String pkName = null;
            boolean first = true;

            while (primaryKeys.next()) {
                if (!first) {
                    pkColumns.append(", ");
                }
                first = false;
                pkColumns.append(primaryKeys.getString("COLUMN_NAME"));
                if (pkName == null) {
                    pkName = primaryKeys.getString("CONSTRAINT_NAME");
                }
            }
            primaryKeys.close();
            pkStmt.close();

            if (pkColumns.length() > 0) {
                String pkSQL = "ALTER TABLE IAS_ITM_MST ADD CONSTRAINT "
                        + (pkName != null ? pkName : "PK_IAS_ITM_MST") + " PRIMARY KEY ("
                        + pkColumns.toString() + ")";
                stmt.execute(pkSQL);
                System.out.println("🔑 تم إنشاء المفتاح الأساسي لـ IAS_ITM_MST");
            }

            // المفاتيح الأساسية للجدول الثاني
            pkSql = """
                        SELECT COLUMN_NAME, CONSTRAINT_NAME
                        FROM ALL_CONS_COLUMNS
                        WHERE OWNER = 'IAS20251' AND TABLE_NAME = 'IAS_ITM_DTL'
                        AND CONSTRAINT_NAME IN (
                            SELECT CONSTRAINT_NAME FROM ALL_CONSTRAINTS
                            WHERE OWNER = 'IAS20251' AND TABLE_NAME = 'IAS_ITM_DTL' AND CONSTRAINT_TYPE = 'P'
                        )
                        ORDER BY POSITION
                    """;

            pkStmt = ias20251Connection.prepareStatement(pkSql);
            primaryKeys = pkStmt.executeQuery();

            pkColumns = new StringBuilder();
            pkName = null;
            first = true;

            while (primaryKeys.next()) {
                if (!first) {
                    pkColumns.append(", ");
                }
                first = false;
                pkColumns.append(primaryKeys.getString("COLUMN_NAME"));
                if (pkName == null) {
                    pkName = primaryKeys.getString("CONSTRAINT_NAME");
                }
            }
            primaryKeys.close();
            pkStmt.close();

            if (pkColumns.length() > 0) {
                String pkSQL = "ALTER TABLE IAS_ITM_DTL ADD CONSTRAINT "
                        + (pkName != null ? pkName : "PK_IAS_ITM_DTL") + " PRIMARY KEY ("
                        + pkColumns.toString() + ")";
                stmt.execute(pkSQL);
                System.out.println("🔑 تم إنشاء المفتاح الأساسي لـ IAS_ITM_DTL");
            }

            // المفاتيح الخارجية
            String fkSql =
                    """
                                SELECT
                                    acc.COLUMN_NAME,
                                    ac.CONSTRAINT_NAME,
                                    ac.R_CONSTRAINT_NAME,
                                    acc2.TABLE_NAME AS REF_TABLE,
                                    acc2.COLUMN_NAME AS REF_COLUMN
                                FROM ALL_CONSTRAINTS ac
                                JOIN ALL_CONS_COLUMNS acc ON ac.CONSTRAINT_NAME = acc.CONSTRAINT_NAME AND ac.OWNER = acc.OWNER
                                JOIN ALL_CONSTRAINTS ac2 ON ac.R_CONSTRAINT_NAME = ac2.CONSTRAINT_NAME AND ac.R_OWNER = ac2.OWNER
                                JOIN ALL_CONS_COLUMNS acc2 ON ac2.CONSTRAINT_NAME = acc2.CONSTRAINT_NAME AND ac2.OWNER = acc2.OWNER
                                WHERE ac.OWNER = 'IAS20251' AND ac.TABLE_NAME = 'IAS_ITM_DTL' AND ac.CONSTRAINT_TYPE = 'R'
                            """;

            PreparedStatement fkStmt = ias20251Connection.prepareStatement(fkSql);
            ResultSet foreignKeys = fkStmt.executeQuery();
            while (foreignKeys.next()) {
                String fkColumn = foreignKeys.getString("COLUMN_NAME");
                String pkTable = foreignKeys.getString("REF_TABLE");
                String pkColumn = foreignKeys.getString("REF_COLUMN");
                String fkName = foreignKeys.getString("CONSTRAINT_NAME");

                if (pkTable.equals("IAS_ITM_MST")) {
                    String fkSQL = "ALTER TABLE IAS_ITM_DTL ADD CONSTRAINT "
                            + (fkName != null ? fkName : "FK_IAS_ITM_DTL_MST") + " FOREIGN KEY ("
                            + fkColumn + ") REFERENCES IAS_ITM_MST(" + pkColumn + ")";
                    stmt.execute(fkSQL);
                    System.out.println("🔗 تم إنشاء المفتاح الخارجي: " + fkColumn + " → " + pkTable
                            + "." + pkColumn);
                }
            }
            foreignKeys.close();
            fkStmt.close();

        } catch (SQLException e) {
            System.out.println("⚠️ تحذير في إنشاء القيود: " + e.getMessage());
        }
    }
}
