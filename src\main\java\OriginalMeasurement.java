import java.util.Date;

/**
 * كلاس وحدة القياس الأصلي
 * مبني بالضبط على بنية جدول MEASUREMENT من قاعدة البيانات IAS20251
 * Original Measurement Entity based on IAS20251.MEASUREMENT table structure
 */
public class OriginalMeasurement {
    
    // الحقول الأساسية من جدول MEASUREMENT الأصلي
    private String measureCode;        // MEASURE_CODE VARCHAR2(10) NOT NULL
    private String measure;            // MEASURE VARCHAR2(100) NOT NULL
    private String measureFNm;         // MEASURE_F_NM VARCHAR2(100)
    private String measureCodeGb;      // MEASURE_CODE_GB VARCHAR2(10)
    private Integer measureType;       // MEASURE_TYPE NUMBER(1)
    private Integer measureWtType;     // MEASURE_WT_TYPE NUMBER(2)
    private Integer measureWtConn;     // MEASURE_WT_CONN NUMBER(1)
    private Double dfltSize;           // DFLT_SIZE NUMBER(22,4)
    private Integer allowUpd;          // ALLOW_UPD NUMBER(1)
    private Integer untSaleTyp;        // UNT_SALE_TYP NUMBER(2)
    
    // حقول التدقيق من الجدول الأصلي
    private Integer adUId;             // AD_U_ID NUMBER(5)
    private Date adDate;               // AD_DATE DATE
    private Integer upUId;             // UP_U_ID NUMBER(5)
    private Date upDate;               // UP_DATE DATE
    private Integer upCnt;             // UP_CNT NUMBER(10)
    private String adTrmnlNm;          // AD_TRMNL_NM VARCHAR2(50)
    private String upTrmnlNm;          // UP_TRMNL_NM VARCHAR2(50)
    
    // Constructors
    public OriginalMeasurement() {
        this.allowUpd = 1;
        this.measureType = 1;
        this.measureWtConn = 0;
        this.untSaleTyp = 3;
        this.adDate = new Date();
        this.upCnt = 0;
    }
    
    public OriginalMeasurement(String measureCode, String measure) {
        this();
        this.measureCode = measureCode;
        this.measure = measure;
    }
    
    // Getters and Setters
    public String getMeasureCode() {
        return measureCode;
    }
    
    public void setMeasureCode(String measureCode) {
        this.measureCode = measureCode;
    }
    
    public String getMeasure() {
        return measure;
    }
    
    public void setMeasure(String measure) {
        this.measure = measure;
    }
    
    public String getMeasureFNm() {
        return measureFNm;
    }
    
    public void setMeasureFNm(String measureFNm) {
        this.measureFNm = measureFNm;
    }
    
    public String getMeasureCodeGb() {
        return measureCodeGb;
    }
    
    public void setMeasureCodeGb(String measureCodeGb) {
        this.measureCodeGb = measureCodeGb;
    }
    
    public Integer getMeasureType() {
        return measureType;
    }
    
    public void setMeasureType(Integer measureType) {
        this.measureType = measureType;
    }
    
    public Integer getMeasureWtType() {
        return measureWtType;
    }
    
    public void setMeasureWtType(Integer measureWtType) {
        this.measureWtType = measureWtType;
    }
    
    public Integer getMeasureWtConn() {
        return measureWtConn;
    }
    
    public void setMeasureWtConn(Integer measureWtConn) {
        this.measureWtConn = measureWtConn;
    }
    
    public Double getDfltSize() {
        return dfltSize;
    }
    
    public void setDfltSize(Double dfltSize) {
        this.dfltSize = dfltSize;
    }
    
    public Integer getAllowUpd() {
        return allowUpd;
    }
    
    public void setAllowUpd(Integer allowUpd) {
        this.allowUpd = allowUpd;
    }
    
    public Integer getUntSaleTyp() {
        return untSaleTyp;
    }
    
    public void setUntSaleTyp(Integer untSaleTyp) {
        this.untSaleTyp = untSaleTyp;
    }
    
    public Integer getAdUId() {
        return adUId;
    }
    
    public void setAdUId(Integer adUId) {
        this.adUId = adUId;
    }
    
    public Date getAdDate() {
        return adDate;
    }
    
    public void setAdDate(Date adDate) {
        this.adDate = adDate;
    }
    
    public Integer getUpUId() {
        return upUId;
    }
    
    public void setUpUId(Integer upUId) {
        this.upUId = upUId;
    }
    
    public Date getUpDate() {
        return upDate;
    }
    
    public void setUpDate(Date upDate) {
        this.upDate = upDate;
    }
    
    public Integer getUpCnt() {
        return upCnt;
    }
    
    public void setUpCnt(Integer upCnt) {
        this.upCnt = upCnt;
    }
    
    public String getAdTrmnlNm() {
        return adTrmnlNm;
    }
    
    public void setAdTrmnlNm(String adTrmnlNm) {
        this.adTrmnlNm = adTrmnlNm;
    }
    
    public String getUpTrmnlNm() {
        return upTrmnlNm;
    }
    
    public void setUpTrmnlNm(String upTrmnlNm) {
        this.upTrmnlNm = upTrmnlNm;
    }
    
    @Override
    public String toString() {
        return measure != null ? measure : measureCode;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        OriginalMeasurement that = (OriginalMeasurement) obj;
        return measureCode != null ? measureCode.equals(that.measureCode) : that.measureCode == null;
    }
    
    @Override
    public int hashCode() {
        return measureCode != null ? measureCode.hashCode() : 0;
    }
    
    /**
     * الحصول على نص نوع وحدة القياس
     */
    public String getMeasureTypeText() {
        if (measureType == null) return "غير محدد";
        
        switch (measureType) {
            case 1: return "عادي";
            case 2: return "وزن";
            case 3: return "حجم";
            case 4: return "طول";
            case 5: return "مساحة";
            default: return "غير محدد";
        }
    }
    
    /**
     * الحصول على نص نوع البيع
     */
    public String getUntSaleTypText() {
        if (untSaleTyp == null) return "غير محدد";
        
        switch (untSaleTyp) {
            case 1: return "بيع بالقطعة";
            case 2: return "بيع بالوزن";
            case 3: return "بيع عادي";
            default: return "غير محدد";
        }
    }
    
    /**
     * التحقق من إمكانية التحديث
     */
    public boolean isUpdateAllowed() {
        return allowUpd != null && allowUpd == 1;
    }
}
