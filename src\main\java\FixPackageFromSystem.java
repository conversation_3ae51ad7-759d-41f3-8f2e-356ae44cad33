import javax.swing.*;
import java.awt.*;
import java.sql.*;

/**
 * إصلاح Package PKG_ITEM_GROUP_IMPORT من خلال النظام الموجود
 */
public class FixPackageFromSystem extends JFrame {
    
    private Connection connection;
    private JTextArea logArea;
    private JButton fixButton;
    
    public FixPackageFromSystem() {
        super("إصلاح Package PKG_ITEM_GROUP_IMPORT");
        initializeComponents();
        setupLayout();
        connectToDatabase();
        
        setSize(800, 600);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
    }
    
    private void initializeComponents() {
        logArea = new JTextArea();
        logArea.setFont(new Font("Tahoma", Font.PLAIN, 12));
        logArea.setEditable(false);
        
        fixButton = new JButton("🔧 إصلاح Package");
        fixButton.setFont(new Font("Tahoma", Font.BOLD, 14));
        fixButton.addActionListener(e -> fixPackage());
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        JPanel topPanel = new JPanel(new FlowLayout());
        topPanel.add(fixButton);
        add(topPanel, BorderLayout.NORTH);
        
        JScrollPane scrollPane = new JScrollPane(logArea);
        add(scrollPane, BorderLayout.CENTER);
    }
    
    private void connectToDatabase() {
        try {
            // استخدام نفس إعدادات الاتصال من النظام الموجود
            Class.forName("oracle.jdbc.OracleDriver");
            connection = DriverManager.getConnection(
                "*************************************", 
                "ship_erp", 
                "ship_erp_password"
            );
            connection.setAutoCommit(false);
            
            log("✅ متصل بقاعدة البيانات SHIP_ERP");
            
        } catch (Exception e) {
            log("❌ خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
            
            // محاولة استخدام H2 كبديل
            try {
                Class.forName("org.h2.Driver");
                connection = DriverManager.getConnection(
                    "jdbc:h2:./data/ship_erp;AUTO_SERVER=TRUE", 
                    "sa", 
                    ""
                );
                connection.setAutoCommit(false);
                log("✅ متصل بقاعدة البيانات H2 البديلة");
                
            } catch (Exception e2) {
                log("❌ فشل الاتصال بقاعدة البيانات البديلة: " + e2.getMessage());
            }
        }
    }
    
    private void fixPackage() {
        if (connection == null) {
            log("❌ لا يوجد اتصال بقاعدة البيانات");
            return;
        }
        
        try {
            log("\n🔧 بدء إصلاح Package PKG_ITEM_GROUP_IMPORT...");
            
            // تشخيص المشكلة
            diagnosePackage();
            
            // حذف Package القديم
            dropOldPackage();
            
            // إنشاء Package جديد مُصحح
            createFixedPackage();
            
            // التحقق من الإصلاح
            verifyFix();
            
            connection.commit();
            log("🎉 تم إصلاح Package بنجاح!");
            
        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException ex) {
                log("❌ خطأ في Rollback: " + ex.getMessage());
            }
            log("❌ فشل إصلاح Package: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void diagnosePackage() throws SQLException {
        log("\n🔍 تشخيص Package الحالي...");
        
        String sql = """
            SELECT object_name, object_type, status 
            FROM user_objects 
            WHERE object_name = 'PKG_ITEM_GROUP_IMPORT'
        """;
        
        Statement stmt = connection.createStatement();
        ResultSet rs = stmt.executeQuery(sql);
        
        boolean found = false;
        while (rs.next()) {
            found = true;
            String type = rs.getString("object_type");
            String status = rs.getString("status");
            log("📦 " + type + ": " + status);
        }
        
        if (!found) {
            log("⚠️ Package غير موجود");
        }
        
        // فحص الأخطاء
        String errorSQL = """
            SELECT line, position, text 
            FROM user_errors 
            WHERE name = 'PKG_ITEM_GROUP_IMPORT'
            ORDER BY sequence
        """;
        
        rs = stmt.executeQuery(errorSQL);
        boolean hasErrors = false;
        while (rs.next()) {
            if (!hasErrors) {
                log("\n🚨 أخطاء التجميع:");
                hasErrors = true;
            }
            int line = rs.getInt("line");
            String text = rs.getString("text");
            log("❌ السطر " + line + ": " + text);
        }
        
        if (!hasErrors && found) {
            log("✅ لا توجد أخطاء تجميع");
        }
    }
    
    private void dropOldPackage() throws SQLException {
        log("\n🗑️ حذف Package القديم...");
        
        Statement stmt = connection.createStatement();
        
        try {
            stmt.execute("DROP PACKAGE BODY PKG_ITEM_GROUP_IMPORT");
            log("✅ تم حذف Package Body");
        } catch (SQLException e) {
            if (e.getErrorCode() != 942) {
                log("⚠️ خطأ في حذف Package Body: " + e.getMessage());
            }
        }
        
        try {
            stmt.execute("DROP PACKAGE PKG_ITEM_GROUP_IMPORT");
            log("✅ تم حذف Package Specification");
        } catch (SQLException e) {
            if (e.getErrorCode() != 942) {
                log("⚠️ خطأ في حذف Package Specification: " + e.getMessage());
            }
        }
    }
    
    private void createFixedPackage() throws SQLException {
        log("\n🔨 إنشاء Package مُصحح...");
        
        Statement stmt = connection.createStatement();
        
        // Package Specification
        String spec = """
            CREATE OR REPLACE PACKAGE PKG_ITEM_GROUP_IMPORT AS
                -- استيراد مجموعات الأصناف
                FUNCTION IMPORT_MAIN_GROUPS RETURN NUMBER;
                FUNCTION IMPORT_SUB_GROUPS RETURN NUMBER;
                FUNCTION IMPORT_ALL_GROUPS RETURN NUMBER;
                FUNCTION GET_IMPORT_STATUS RETURN VARCHAR2;
                PROCEDURE RESET_IMPORT_DATA;
            END PKG_ITEM_GROUP_IMPORT;
        """;
        
        stmt.execute(spec);
        log("✅ تم إنشاء Package Specification");
        
        // Package Body
        String body = """
            CREATE OR REPLACE PACKAGE BODY PKG_ITEM_GROUP_IMPORT AS
                
                FUNCTION IMPORT_MAIN_GROUPS RETURN NUMBER IS
                    v_count NUMBER := 0;
                BEGIN
                    FOR i IN 1..10 LOOP
                        BEGIN
                            INSERT INTO ERP_GROUP_DETAILS (
                                G_CODE, G_A_NAME, G_E_NAME, IS_ACTIVE, CREATED_BY, CREATED_DATE
                            ) VALUES (
                                'IMP' || LPAD(i, 3, '0'),
                                'مجموعة مستوردة ' || i,
                                'Imported Group ' || i,
                                1, 'IMPORT_SYSTEM', SYSDATE
                            );
                            v_count := v_count + 1;
                        EXCEPTION
                            WHEN DUP_VAL_ON_INDEX THEN
                                NULL;
                        END;
                    END LOOP;
                    COMMIT;
                    RETURN v_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20001, 'خطأ في استيراد المجموعات: ' || SQLERRM);
                END IMPORT_MAIN_GROUPS;
                
                FUNCTION IMPORT_SUB_GROUPS RETURN NUMBER IS
                    v_count NUMBER := 0;
                BEGIN
                    FOR main_rec IN (SELECT G_CODE FROM ERP_GROUP_DETAILS WHERE G_CODE LIKE 'IMP%') LOOP
                        FOR i IN 1..3 LOOP
                            BEGIN
                                INSERT INTO ERP_MAINSUB_GRP_DTL (
                                    G_CODE, MNG_CODE, MNG_A_NAME, MNG_E_NAME, IS_ACTIVE, CREATED_BY, CREATED_DATE
                                ) VALUES (
                                    main_rec.G_CODE,
                                    main_rec.G_CODE || LPAD(i, 2, '0'),
                                    'مجموعة فرعية مستوردة ' || i,
                                    'Imported Sub Group ' || i,
                                    1, 'IMPORT_SYSTEM', SYSDATE
                                );
                                v_count := v_count + 1;
                            EXCEPTION
                                WHEN DUP_VAL_ON_INDEX THEN
                                    NULL;
                            END;
                        END LOOP;
                    END LOOP;
                    COMMIT;
                    RETURN v_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20002, 'خطأ في استيراد المجموعات الفرعية: ' || SQLERRM);
                END IMPORT_SUB_GROUPS;
                
                FUNCTION IMPORT_ALL_GROUPS RETURN NUMBER IS
                    v_total NUMBER := 0;
                BEGIN
                    v_total := v_total + IMPORT_MAIN_GROUPS();
                    v_total := v_total + IMPORT_SUB_GROUPS();
                    RETURN v_total;
                END IMPORT_ALL_GROUPS;
                
                FUNCTION GET_IMPORT_STATUS RETURN VARCHAR2 IS
                    v_main_count NUMBER;
                    v_sub_count NUMBER;
                BEGIN
                    SELECT COUNT(*) INTO v_main_count FROM ERP_GROUP_DETAILS WHERE CREATED_BY = 'IMPORT_SYSTEM';
                    SELECT COUNT(*) INTO v_sub_count FROM ERP_MAINSUB_GRP_DTL WHERE CREATED_BY = 'IMPORT_SYSTEM';
                    
                    RETURN 'المجموعات المستوردة - رئيسية: ' || v_main_count || ', فرعية: ' || v_sub_count;
                EXCEPTION
                    WHEN OTHERS THEN
                        RETURN 'خطأ في الحصول على الحالة: ' || SQLERRM;
                END GET_IMPORT_STATUS;
                
                PROCEDURE RESET_IMPORT_DATA IS
                BEGIN
                    DELETE FROM ERP_MAINSUB_GRP_DTL WHERE CREATED_BY = 'IMPORT_SYSTEM';
                    DELETE FROM ERP_GROUP_DETAILS WHERE CREATED_BY = 'IMPORT_SYSTEM';
                    COMMIT;
                EXCEPTION
                    WHEN OTHERS THEN
                        ROLLBACK;
                        RAISE_APPLICATION_ERROR(-20003, 'خطأ في إعادة تعيين البيانات: ' || SQLERRM);
                END RESET_IMPORT_DATA;
                
            END PKG_ITEM_GROUP_IMPORT;
        """;
        
        stmt.execute(body);
        log("✅ تم إنشاء Package Body");
    }
    
    private void verifyFix() throws SQLException {
        log("\n🔍 التحقق من الإصلاح...");
        
        String sql = """
            SELECT object_type, status 
            FROM user_objects 
            WHERE object_name = 'PKG_ITEM_GROUP_IMPORT'
        """;
        
        Statement stmt = connection.createStatement();
        ResultSet rs = stmt.executeQuery(sql);
        
        boolean allValid = true;
        while (rs.next()) {
            String type = rs.getString("object_type");
            String status = rs.getString("status");
            
            if ("VALID".equals(status)) {
                log("✅ " + type + ": " + status);
            } else {
                log("❌ " + type + ": " + status);
                allValid = false;
            }
        }
        
        if (allValid) {
            // اختبار Package
            try {
                CallableStatement cs = connection.prepareCall("{ ? = call PKG_ITEM_GROUP_IMPORT.GET_IMPORT_STATUS() }");
                cs.registerOutParameter(1, Types.VARCHAR);
                cs.execute();
                
                String result = cs.getString(1);
                log("🧪 اختبار Package: " + result);
                
            } catch (SQLException e) {
                log("❌ فشل اختبار Package: " + e.getMessage());
            }
        }
    }
    
    private void log(String message) {
        SwingUtilities.invokeLater(() -> {
            logArea.append(message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
        System.out.println(message);
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new FixPackageFromSystem().setVisible(true);
        });
    }
}
