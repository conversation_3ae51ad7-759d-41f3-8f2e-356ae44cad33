# 👨‍💻 دليل المطورين - جداول IAS_ITM_MST و IAS_ITM_DTL
## Developer Guide - IAS Tables Integration

---

## 🎯 **نظرة عامة:**

تم تطبيق فهم شامل لبنية جداول بيانات الأصناف في نافذة بيانات الأصناف الشاملة.

### **📊 هيكل الجداول:**

#### **IAS_ITM_MST (الجدول الرئيسي):**
```sql
-- 229 عمود، المفتاح الأساسي: I_CODE
-- يحتوي على البيانات الأساسية للأصناف
-- 4647 صنف إجمالي (4630 نشط)
```

#### **IAS_ITM_DTL (الجدول التفصيلي):**
```sql
-- 32 عمود، المفتاح المركب: (I_CODE, ITM_UNT)
-- يحتوي على تفاصيل الوحدات لكل صنف
-- 9108 تفصيل وحدة
```

#### **العلاقة:**
```sql
-- One-to-Many: صنف واحد ← عدة وحدات
-- مفتاح الربط: I_CODE
-- الوحدة الرئيسية: MAIN_UNIT = 1
```

---

## 🔗 **الاستعلامات المطبقة:**

### **1. استعلام قائمة الأصناف مع الوحدة الرئيسية:**
```java
String sql = """
    SELECT 
        m.I_CODE,
        m.I_NAME,
        m.I_E_NAME,
        m.G_CODE,
        m.PRIMARY_COST,
        m.ITEM_TYPE,
        m.INACTIVE,
        m.SERVICE_ITM,
        m.CASH_SALE,
        d.ITM_UNT as MAIN_UNIT,
        d.P_SIZE as PACKAGE_SIZE,
        d.BARCODE
    FROM IAS_ITM_MST m
    LEFT JOIN IAS_ITM_DTL d ON m.I_CODE = d.I_CODE AND d.MAIN_UNIT = 1
    WHERE m.INACTIVE = 0
    ORDER BY m.I_CODE
""";
```

### **2. استعلام تفاصيل الوحدات لصنف محدد:**
```java
String sql = """
    SELECT 
        I_CODE, ITM_UNT, P_SIZE, ITM_UNT_L_DSC, ITM_UNT_F_DSC,
        MAIN_UNIT, SALE_UNIT, PUR_UNIT, STOCK_UNIT, PRICE_UNIT,
        BARCODE, WEIGHT_UNIT, LVL_UNIT, INACTIVE
    FROM IAS_ITM_DTL 
    WHERE I_CODE = ?
    ORDER BY ITM_UNT
""";
```

### **3. استعلام جميع تفاصيل الوحدات:**
```java
String sql = """
    SELECT 
        I_CODE, ITM_UNT, P_SIZE, ITM_UNT_L_DSC, ITM_UNT_F_DSC,
        MAIN_UNIT, SALE_UNIT, PUR_UNIT, STOCK_UNIT, PRICE_UNIT,
        BARCODE, WEIGHT_UNIT, LVL_UNIT, INACTIVE
    FROM IAS_ITM_DTL 
    ORDER BY I_CODE, ITM_UNT
""";
```

---

## 🏗️ **التطبيق في الكود:**

### **1. الدوال الرئيسية:**

#### **تحميل قائمة الأصناف:**
```java
private void loadItemsData() {
    // استعلام مدمج بين IAS_ITM_MST و IAS_ITM_DTL
    // يعرض الأصناف مع وحداتها الرئيسية
}
```

#### **تحميل تفاصيل الوحدات:**
```java
private void loadUnitsDetails(DefaultTableModel tableModel, String itemCode) {
    // تحميل جميع وحدات صنف محدد من IAS_ITM_DTL
}

private void loadAllUnitsDetails(DefaultTableModel tableModel) {
    // تحميل جميع الوحدات من IAS_ITM_DTL
}
```

### **2. التبويبات المطبقة:**

#### **تبويب قائمة الأصناف:**
- **12 عمود:** 9 من IAS_ITM_MST + 3 من IAS_ITM_DTL
- **البيانات المدمجة:** الوحدة الرئيسية، حجم العبوة، الباركود

#### **تبويب تفاصيل الوحدات:**
- **14 عمود:** جميع الحقول المهمة من IAS_ITM_DTL
- **وظائف البحث:** بكود الصنف أو تحميل الكل

---

## 🎯 **نصائح للمطورين:**

### **1. عند إضافة استعلامات جديدة:**
```java
// استخدم LEFT JOIN للحفاظ على الأصناف بدون وحدات
// استخدم MAIN_UNIT = 1 للحصول على الوحدة الرئيسية فقط
// تأكد من ترتيب النتائج بـ ORDER BY I_CODE
```

### **2. عند التعامل مع البيانات:**
```java
// تحقق من NULL values خاصة في IAS_ITM_DTL
// استخدم rs.getInt("INACTIVE") == 0 للتحقق من النشاط
// استخدم PreparedStatement للاستعلامات بمعاملات
```

### **3. عند إضافة حقول جديدة:**
```java
// أضف الحقل في الاستعلام SQL
// أضف العمود في نموذج الجدول
// أضف عرض العمود في setPreferredWidth
// أضف البيانات في مصفوفة النتائج
```

---

## 📊 **الأداء والتحسين:**

### **1. الفهارس المستخدمة:**
- **I_CODE:** مفهرس في كلا الجدولين
- **MAIN_UNIT:** يساعد في تسريع البحث عن الوحدة الرئيسية

### **2. تحسينات الذاكرة:**
- **تحميل تدريجي:** البيانات تُحمل عند الحاجة
- **مسح البيانات:** قبل التحميل الجديد
- **إغلاق الموارد:** ResultSet و Statement

### **3. معالجة الأخطاء:**
```java
try {
    // الاستعلام والمعالجة
} catch (SQLException e) {
    System.err.println("❌ خطأ: " + e.getMessage());
    e.printStackTrace(); // للتشخيص التفصيلي
}
```

---

## 🚀 **التوسعات المستقبلية:**

### **1. إضافة جداول أخرى:**
- يمكن ربط جداول المجموعات والموردين
- تطبيق نفس النمط للجداول الأخرى

### **2. تحسين الواجهة:**
- إضافة فلاتر متقدمة
- تصدير البيانات إلى Excel
- طباعة التقارير

### **3. تحسين الأداء:**
- استخدام Connection Pooling
- تطبيق Pagination للبيانات الكبيرة
- إضافة Cache للاستعلامات المتكررة

---

## 🎉 **النتيجة:**

تم تطبيق **فهم شامل** لبنية جداول بيانات الأصناف مع:
- ✅ **استعلامات محسنة** تستغل العلاقات
- ✅ **عرض البيانات المترابطة** بشكل منطقي
- ✅ **كود قابل للصيانة** والتوسع
- ✅ **أداء محسن** مع معالجة الأخطاء

النظام الآن جاهز للتطوير والتوسع! 🚀
